import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { ManagerRes } from '../../../model/response/manager-res.model';
import { ManagerService } from '../../../core/services/manager.service';
import { ToastService } from '../../../core/services/toast.service';

import { AddManagerComponent } from './add-manager/add-manager.component';

import { LoadingComponent } from '../../common/loading/loading.component';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NotifyType } from '../../../constant/notify-type';

@Component({
  selector: 'app-managers',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    AddManagerComponent,
    LoadingComponent
  ],
  templateUrl: './managers.component.html',
  styleUrls: ['./managers.component.css']
})
export class ManagersComponent implements OnInit, OnDestroy {
  managers: ManagerRes[] = [];
  loading: boolean = false;
  showAddManagerPopup: boolean = false;

  // Search
  searchTerm: string = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private managerService: ManagerService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadManagers();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadManagers(): void {
    this.loading = true;
    const subscription = this.managerService.getManagers()
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.managers = response.data;
          } else {
            this.toastService.showToast(response.message || 'Failed to load managers', NotifyType.ERROR);
          }
        },
        error: (error) => {
          console.error('Error loading managers:', error);
          this.toastService.showToast(error.error?.message || 'Failed to load managers', NotifyType.ERROR);
        }
      });

    this.subscriptions.push(subscription);
  }

  get filteredManagers(): ManagerRes[] {
    return this.managers.filter(manager => {
      const matchesSearch = !this.searchTerm ||
        manager.login.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        manager.role.toLowerCase().includes(this.searchTerm.toLowerCase());

      return matchesSearch;
    });
  }

  openAddManagerPopup(): void {
    this.showAddManagerPopup = true;
  }

  closeAddManagerPopup(): void {
    this.showAddManagerPopup = false;
  }

  onManagerAdded(manager: ManagerRes): void {
    this.managers.unshift(manager);
    this.toastService.showToast('Manager added successfully', NotifyType.SUCCESS);
  }



  getRoleIcon(role: string): 'shield' | 'user-pen' | 'headset' | 'user' {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'shield';
      case 'moderator':
        return 'user-pen';
      case 'agent':
        return 'headset';
      default:
        return 'user';
    }
  }

  getRoleColor(role: string): string {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'text-blue-600 bg-blue-100';
      case 'moderator':
        return 'text-orange-600 bg-orange-100';
      case 'agent':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }


}
