<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <!-- Header with close button -->
      <div class="modal-header">
        <div class="flex items-center">
          <fa-icon [icon]="['fas', 'file-import']" class="text-[#0095f6] text-xl mr-3"></fa-icon>
          <h2 class="modal-title">{{ 'Configure imported services' }}</h2>
        </div>
        <button class="close-button" (click)="closePopup()">
          <fa-icon [icon]="['fas', 'xmark']" class="text-lg"></fa-icon>
        </button>
      </div>

      <!-- Provider information -->
      <div class="form-section" *ngIf="selectedProvider">
        <label class="form-label">{{ 'Provider' }}</label>
        <div class="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
          <div class="text-sm font-medium">{{ selectedProvider.name || 'Unknown Provider' }}</div>
          <div class="flex-1"></div>
          <div class="text-xs text-gray-500">ID: {{ selectedProvider.id }}</div>
        </div>
      </div>

      <!-- Category selection -->
      <div class="form-section">
        <label class="form-label">{{ 'Category' }}</label>
        <app-icon-dropdown
          [options]="categoryOptions"
          [selectedOption]="selectedCategoryOption"
          (selected)="onCategoryChange($event)"
          [customClassDropdown]="'bg-white border border-gray-300 rounded-lg'"
          [customClassButton]="'text-gray-700'">
        </app-icon-dropdown>
      </div>

      <!-- Extra price percentage -->
      <div class="form-section">
        <label class="form-label">{{ 'Global extra price in %' }}</label>
        <div class="flex items-center gap-2">
          <div class="text-sm text-gray-500">{{ 'Default percentage for all services' }}</div>
          <div class="flex-1"></div>
          <div class="price-input-container w-16">
            <input
              type="number"
              class="price-input text-sm py-1 px-2"
              [(ngModel)]="extraPricePercentage"
              (ngModelChange)="updateAllPercentages()"
              [disabled]="isLoading">
            <div class="percentage-symbol text-sm">%</div>
          </div>
        </div>
      </div>

      <!-- Services list -->
      <div class="services-container">
        <div *ngIf="isLoading" class="loading-indicator">
          <fa-icon [icon]="['fas', 'spinner']" [spin]="true" class="text-[#0095f6] text-2xl"></fa-icon>
          <div class="flex flex-col items-center mt-2">
            <p *ngIf="processedServices === 0">{{ 'Preparing to import services from ' + getProviderName() + '...' }}</p>
            <p *ngIf="processedServices > 0">{{ 'Importing service: ' + currentServiceName }}</p>
            <div class="progress-container mt-2 w-full max-w-md bg-gray-200 rounded-full h-2.5">
              <div class="progress-bar bg-[#0095f6] h-2.5 rounded-full"
                   [style.width]="(processedServices / totalServices * 100) + '%'"></div>
            </div>
            <p class="mt-2">{{ processedServices }} of {{ totalServices }} services imported</p>
            <div class="flex gap-4 mt-1">
              <span class="text-green-500">{{ successCount }} successful</span>
              <span class="text-red-500">{{ failedCount }} failed</span>
            </div>
          </div>
        </div>

        <div *ngIf="!isLoading && selectedServices.length === 0" class="empty-state">
          <fa-icon [icon]="['fas', 'info-circle']" class="text-gray-400 text-2xl mb-2"></fa-icon>
          <p>{{ 'No services selected' }}</p>
        </div>

        <div *ngIf="!isLoading && selectedServices.length > 0" class="service-list">
          <div class="service-header">
            <div class="service-column">{{ 'Service' }}</div>
            <div class="price-column">{{ 'Provider price' }}</div>
            <div class="arrow-column">
              <fa-icon [icon]="['fas', 'arrow-right']" class="text-gray-400"></fa-icon>
            </div>
            <div class="price-column">{{ 'Your price' }}</div>
            <div class="percentage-column">{{ 'Per-service %' }}</div>
          </div>

          <div *ngFor="let service of selectedServices" class="service-item">
            <div class="service-column">
              <div class="service-id">ID {{ service.id }}</div>
              <div class="service-name">{{ service.name }}</div>
            </div>
            <div class="price-column">${{ service.price }}</div>
            <div class="arrow-column">
              <fa-icon [icon]="['fas', 'arrow-right']" class="text-gray-400"></fa-icon>
            </div>
            <div class="price-column text-green-500">${{ calculateFinalPrice(service) }}</div>
            <div class="ml-2 percentage-column-item">
              <div class="price-input-container w-8">
                <input
                  type="number"
                  class="price-input-item text-xs "
                  [value]="getServicePercentage(service.id)"
                  (input)="handlePercentageInput(service.id, $event)">
                <div class="percentage-symbol text-xs">%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="action-container">
        <button
          class="back-button"
          [disabled]="isLoading"
          (click)="goBack()">
          <fa-icon [icon]="['fas', 'arrow-left']" class="mr-2"></fa-icon>
          {{ 'Back' }}
        </button>
        <button
          class="import-button"
          [disabled]="!selectedCategory || selectedServices.length === 0 || isLoading"
          [class.disabled]="!selectedCategory || selectedServices.length === 0 || isLoading"
          (click)="configureServices()">
          {{ isLoading ? 'Importing services...' : 'Import ' + selectedServices.length + ' services' }}
        </button>
      </div>
    </div>
  </div>
</div>
