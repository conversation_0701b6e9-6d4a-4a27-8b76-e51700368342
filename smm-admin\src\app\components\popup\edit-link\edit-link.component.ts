import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-edit-link',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './edit-link.component.html',
  styleUrl: './edit-link.component.css'
})
export class EditLinkComponent {
  @Input() link: string = '';
  @Output() close = new EventEmitter<void>();
  @Output() linkUpdated = new EventEmitter<string>();

  constructor() {}

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  saveLink(): void {
    if (this.link && this.link.trim()) {
      this.linkUpdated.emit(this.link.trim());
      this.onClose();
    }
  }
}
