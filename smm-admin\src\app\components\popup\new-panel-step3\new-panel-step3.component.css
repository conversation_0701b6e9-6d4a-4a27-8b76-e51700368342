.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: relative;
}

.back-button {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  color: var(--primary-color);
  font-weight: 500;
}

.back-button:hover {
  color: var(--primary-color-hover);
}

.icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  background-color: var(--primary-color);
  color: white;
}

.flag-icon {
  font-size: 1.25rem;
}

.header-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.description {
  color: #4b5563;
  font-size: 0.875rem;
  text-align: center;
}

.panel-info {
  width: 100%;
  margin-bottom: 1.5rem;
}

.panel-info > * + * {
  margin-top: 1rem;
}

.info-row {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.info-label {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.domain-value {
  color: #1f2937;
  font-weight: 500;
}

.currency-code {
  color: #1f2937;
  font-weight: 500;
}

.currency-name {
  color: #4b5563;
  font-size: 0.875rem;
}

.create-button {
  width: 100%;
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
}

.create-button:hover {
  background-color: var(--primary-color-hover);
}

.create-button:active {
  background-color: var(--primary-color-active);
}
