import { Injectable } from '@angular/core';
import { NotifyType } from '../../constant/notify-type';

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toasts: HTMLDivElement[] = [];

  constructor() {}

  /**
   * Show a toast notification
   * @param message The message to display
   * @param type The type of notification (success, warning, error)
   * @param duration The duration in milliseconds
   */
  showToast(message: string, type: string = NotifyType.SUCCESS, duration: number = 3000): void {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    
    // Add type-specific styling
    switch (type) {
      case NotifyType.SUCCESS:
        toast.classList.add('toast-success');
        break;
      case NotifyType.WARNING:
        toast.classList.add('toast-warning');
        break;
      case NotifyType.ERROR:
        toast.classList.add('toast-error');
        break;
      default:
        toast.classList.add('toast-success');
    }
    
    // Set content
    toast.textContent = message;
    
    // Add to document
    document.body.appendChild(toast);
    
    // Track toast
    this.toasts.push(toast);
    
    // Show with animation
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);
    
    // Remove after duration
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        document.body.removeChild(toast);
        this.toasts = this.toasts.filter(t => t !== toast);
      }, 300); // Wait for fade out animation
    }, duration);
  }

  /**
   * Show a success toast notification
   * @param message The message to display
   * @param duration The duration in milliseconds
   */
  showSuccess(message: string, duration: number = 3000): void {
    this.showToast(message, NotifyType.SUCCESS, duration);
  }

  /**
   * Show a warning toast notification
   * @param message The message to display
   * @param duration The duration in milliseconds
   */
  showWarning(message: string, duration: number = 3000): void {
    this.showToast(message, NotifyType.WARNING, duration);
  }

  /**
   * Show an error toast notification
   * @param message The message to display
   * @param duration The duration in milliseconds
   */
  showError(message: string, duration: number = 3000): void {
    this.showToast(message, NotifyType.ERROR, duration);
  }
}
