<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ isEditMode ? ('Edit category' | translate) : ('New category' | translate) }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <form [formGroup]="categoryForm" (ngSubmit)="onSubmit()" class="category-form">
        <!-- Title field -->
        <div class="form-group">
          <label for="name" class="form-label">{{ 'Title' | translate }}</label>
          <input
            type="text"
            id="name"
            formControlName="name"
            class="form-input"
            placeholder="My category #1"
            [ngClass]="{'input-error': categoryForm.get('name')?.invalid && categoryForm.get('name')?.touched}"
          >
          <div *ngIf="categoryForm.get('name')?.invalid && categoryForm.get('name')?.touched" class="error-message">
            {{ 'Name is required' | translate }}
          </div>
        </div>

        <!-- Platform selection -->
        <div class="form-group">
          <label class="form-label">{{ 'Platform' | translate }}</label>
          <app-icon-dropdown
            [options]="platformOptions"
            [selectedOption]="selectedPlatform"
            [placeholder]="'Select a platform' | translate"
            [customClassDropdown]="'bg-white rounded-lg border'"
            (selected)="onPlatformSelected($event)"
          ></app-icon-dropdown>
          <div *ngIf="categoryForm.get('platform_id')?.invalid && categoryForm.get('platform_id')?.touched" class="error-message">
            {{ 'Platform is required' | translate }}
          </div>
        </div>

        <!-- Error message -->
        <div *ngIf="errorMessage" class="error-alert">
          {{ errorMessage }}
        </div>

        <!-- Submit button -->
        <button
          type="submit"
          class="create-button"
          [disabled]="isSubmitting"
        >
          <span *ngIf="!isSubmitting">{{ isEditMode ? ('Update' | translate) : ('Create' | translate) }}</span>
          <span *ngIf="isSubmitting" class="loading-spinner-small"></span>
        </button>
      </form>
    </div>
  </div>
</div>
