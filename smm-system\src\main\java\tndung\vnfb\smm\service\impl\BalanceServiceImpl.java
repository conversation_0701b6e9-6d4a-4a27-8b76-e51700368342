package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.constant.enums.TransactionSource;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GTransaction;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.service.BalanceService;
import tndung.vnfb.smm.service.UserNotificationService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
@Service
@Slf4j
@RequiredArgsConstructor
public class BalanceServiceImpl implements BalanceService {

    private final GUserRepository gUserRepository;
    private final TransactionRepository transactionRepository;
    private final OrderRepository orderRepository;
    private final MessageSource messageSource;
    private final UserNotificationService userNotificationService;

    @Override
    @Transactional
    public GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note) {
        validateAmount(amount);

        log.info("Adding balance: userId={}, amount={}, source={}", user.getId(), amount, source);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.add(amount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create transaction record
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            amount, // Positive for addition
            newBalance,
            TransactionType.Bonus,
            source,
            note,
            null // no order
        );

        transactionRepository.save(transaction);

        log.info("Balance added successfully: userId={}, oldBalance={}, newBalance={}",
                savedUser.getId(), oldBalance, newBalance);

        // Send notification for balance addition
        sendBalanceNotification(savedUser, amount, true, source, note);

        return savedUser;
    }

    @Override
    @Transactional
    public GUser deductBalance(GUser user, BigDecimal amount,TransactionType type, TransactionSource source, String note) {
        validateAmount(amount);

        // Check sufficient balance
        if (!hasSufficientBalance(user, amount)) {
            log.warn("Insufficient balance: userId={}, currentBalance={}, requestedAmount={}",
                    user.getId(), user.getBalance(), amount);
            throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
        }

        log.info("Deducting balance: userId={}, amount={}, source={}", user.getId(), amount, source);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.subtract(amount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create transaction record
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            amount.negate(), // Negative for deduction
            newBalance,
                type,
            source,
            note,
            null // no order
        );

        transactionRepository.save(transaction);
        sendBalanceNotification(savedUser, amount, false, source, note);
        log.info("Balance deducted successfully: userId={}, oldBalance={}, newBalance={}",
                savedUser.getId(), oldBalance, newBalance);

        return savedUser;
    }

    @Override
    @Transactional
    public GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note, GOrder order) {
        validateAmount(amount);

        log.info("Adding balance: userId={}, amount={}, source={}", user.getId(), amount, source);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.add(amount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create transaction record
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            amount, // Positive for addition
            newBalance,
            TransactionType.Bonus,
            source,
            note,
            order
        );

        transactionRepository.save(transaction);

        log.info("Balance added successfully: userId={}, oldBalance={}, newBalance={}",
                savedUser.getId(), oldBalance, newBalance);

        // Send notification for balance addition with order
        sendBalanceNotification(savedUser, amount, true, source, note);

        return savedUser;
    }

    @Override
    @Transactional
    public GUser deductBalance(GUser user, BigDecimal amount, TransactionSource source, String note, GOrder order) {
        validateAmount(amount);

        // Check sufficient balance
        if (!hasSufficientBalance(user, amount)) {
            log.warn("Insufficient balance: userId={}, currentBalance={}, requestedAmount={}",
                    user.getId(), user.getBalance(), amount);
            throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
        }

        log.info("Deducting balance: userId={}, amount={}, source={}", user.getId(), amount, source);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.subtract(amount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create transaction record
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            amount.negate(), // Negative for deduction
            newBalance,
            TransactionType.Spent,
            source,
            note,
            order
        );

        transactionRepository.save(transaction);

        log.info("Balance deducted successfully: userId={}, oldBalance={}, newBalance={}",
                savedUser.getId(), oldBalance, newBalance);

        return savedUser;
    }

    @Override
    @Transactional
    public GUser processRefund(GOrder order) {
        List<OrderStatus> refundStatuses = Arrays.asList(OrderStatus.CANCELED, OrderStatus.PARTIAL, OrderStatus.FAILED);

        // Check if order status allows refund (CANCELED_WITHOUT_REFUND is explicitly excluded)
        if (!refundStatuses.contains(order.getStatus())) {
            log.warn("Order status does not allow refund: orderId={}, status={}", order.getId(), order.getStatus());
            return order.getUser();
        }

        if (OrderStatus.CANCELED.equals(order.getStatus())) {
            return processCanceledRefund(order);
        } else if (OrderStatus.PARTIAL.equals(order.getStatus())) {
            return processPartialRefund(order);
        } else if (OrderStatus.FAILED.equals(order.getStatus())) {
            return processFailedRefund(order);
        }

        return order.getUser();
    }

    /**
     * Process refund for canceled orders with intelligent calculation
     * Accounts for previous partial refunds to prevent over-refunding
     */
    private GUser processCanceledRefund(GOrder order) {
        // Check if refund already processed
        Optional<GTransaction> existingRefund = transactionRepository.findByOrderAndType(order, TransactionType.Refund);
        if (existingRefund.isPresent()) {
            log.info("Canceled refund already processed for order: {}", order.getId());
            return order.getUser();
        }

        // Calculate the correct refund amount accounting for previous partial refunds
        BigDecimal refundAmount = calculateCancelRefundAmount(order);

        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("No additional refund needed for canceled order: {} (all amount already refunded through partials)",
                    order.getId());
            return order.getUser();
        }

        String refundNote = String.format("Cancel refund for order #%d (after accounting for partials): %s",
                order.getId(), refundAmount);

        // Add balance directly to avoid self-invocation
        validateAmount(refundAmount);

        log.info("Adding balance for canceled refund: userId={}, amount={} (after partial adjustments)",
                order.getUser().getId(), refundAmount);

        // Update user balance
        BigDecimal oldBalance = order.getUser().getBalance();
        BigDecimal newBalance = oldBalance.add(refundAmount);
        order.getUser().setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(order.getUser());

        // Create transaction record
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            refundAmount, // Positive for addition
            newBalance,
            TransactionType.Refund,
            TransactionSource.CANCELED,
            refundNote,
            order
        );

        transactionRepository.save(transaction);

        log.info("Canceled refund processed: userId={}, oldBalance={}, newBalance={}, finalRefundAmount={}",
                savedUser.getId(), oldBalance, newBalance, refundAmount);

        // Send refund notification
        sendBalanceNotification(savedUser, refundAmount, true, TransactionSource.CANCELED, refundNote);

        // Update actual charge after cancel refund
        updateActualChargeAfterRefund(order);

        return savedUser;
    }

    /**
     * Process partial refund/deduction with correct business logic
     *
     * Business Rules:
     * 1. First partial: always refund for current remains
     * 2. Subsequent partials:
     *    - If remains increase: refund difference (positive amount)
     *    - If remains decrease: deduct difference (negative amount)
     */
    private GUser processPartialRefund(GOrder order) {
        if (order.getRemains() <= 0) {
            log.info("No remains to process for partial order: {}", order.getId());
            return order.getUser();
        }

        // Calculate balance adjustment amount (positive = refund, negative = deduct)
        BigDecimal adjustmentAmount = calculatePartialAdjustmentAmount(order);

        if (adjustmentAmount.compareTo(BigDecimal.ZERO) == 0) {
            log.info("No balance adjustment needed for partial order: {}", order.getId());
            return order.getUser();
        }

        GUser updatedUser;
        if (adjustmentAmount.compareTo(BigDecimal.ZERO) > 0) {
            // Positive amount = refund (first partial or remains increased)
            String refundNote = String.format("Partial refund for order #%d (remains: %d): %s",
                    order.getId(), order.getRemains(), adjustmentAmount);

            updatedUser = addBalanceWithCustomType(order.getUser(), adjustmentAmount,
                    TransactionSource.CANCELED, refundNote, order, TransactionType.Partial);

            log.info("Partial refund processed: orderId={}, remains={}, refundAmount={}",
                    order.getId(), order.getRemains(), adjustmentAmount);
        } else {
            // Negative amount = deduct (remains decreased)
            BigDecimal deductAmount = adjustmentAmount.abs();

            // Check if user has sufficient balance
            if (!hasSufficientBalance(order.getUser(), deductAmount)) {
                log.warn("Insufficient balance for partial deduction: userId={}, currentBalance={}, requiredAmount={}",
                        order.getUser().getId(), order.getUser().getBalance(), deductAmount);
                throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
            }

            String deductNote = String.format("Partial deduction for order #%d (remains: %d): %s",
                    order.getId(), order.getRemains(), deductAmount);

            updatedUser = deductBalanceWithCustomType(order.getUser(), deductAmount,
                    TransactionSource.REMOVE, deductNote, order, TransactionType.Partial);

            log.info("Partial deduction processed: orderId={}, remains={}, deductAmount={}",
                    order.getId(), order.getRemains(), deductAmount);
        }

        // Update actual charge after partial refund/deduction
        updateActualChargeAfterRefund(order);

        return updatedUser;
    }

    /**
     * Process refund for failed orders with intelligent calculation
     * Accounts for previous partial refunds to prevent over-refunding
     */
    private GUser processFailedRefund(GOrder order) {
        // Check if refund already processed
        Optional<GTransaction> existingRefund = transactionRepository.findByOrderAndType(order, TransactionType.Refund);
        if (existingRefund.isPresent()) {
            log.info("Failed refund already processed for order: {}", order.getId());
            return order.getUser();
        }

        // Calculate the correct refund amount accounting for previous partial refunds
        BigDecimal refundAmount = calculateCancelRefundAmount(order);

        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("No additional refund needed for failed order: {} (all amount already refunded through partials)",
                    order.getId());
            return order.getUser();
        }

        String refundNote = String.format("Failed order refund for order #%d (after accounting for partials): %s",
                order.getId(), refundAmount);

        // Add balance directly to avoid self-invocation
        validateAmount(refundAmount);

        log.info("Adding balance for failed refund: userId={}, amount={} (after partial adjustments)",
                order.getUser().getId(), refundAmount);

        // Update user balance
        BigDecimal oldBalance = order.getUser().getBalance();
        BigDecimal newBalance = oldBalance.add(refundAmount);
        order.getUser().setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(order.getUser());

        // Create transaction record
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            refundAmount, // Positive for addition
            newBalance,
            TransactionType.Refund,
            TransactionSource.CANCELED, // Use CANCELED for failed orders as they're both refund scenarios
            refundNote,
            order
        );

        transactionRepository.save(transaction);

        log.info("Failed refund processed: userId={}, oldBalance={}, newBalance={}, finalRefundAmount={}",
                savedUser.getId(), oldBalance, newBalance, refundAmount);

        // Send refund notification
        sendBalanceNotification(savedUser, refundAmount, true, TransactionSource.CANCELED, refundNote);

        // Update actual charge after failed refund
        updateActualChargeAfterRefund(order);

        return savedUser;
    }

    @Override
    public boolean hasSufficientBalance(GUser user, BigDecimal amount) {
        if (user == null || user.getBalance() == null || amount == null) {
            return false;
        }
        return user.getBalance().compareTo(amount) >= 0;
    }

    @Override
    @Transactional
    public GUser[] transferBalance(GUser fromUser, GUser toUser, BigDecimal amount, String note) {
        validateAmount(amount);

        // Check sufficient balance
        if (!hasSufficientBalance(fromUser, amount)) {
            log.warn("Insufficient balance for transfer: fromUserId={}, currentBalance={}, requestedAmount={}",
                    fromUser.getId(), fromUser.getBalance(), amount);
            throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
        }

        log.info("Transferring balance: fromUserId={}, toUserId={}, amount={}",
                fromUser.getId(), toUser.getId(), amount);

        // Deduct from sender
        BigDecimal fromOldBalance = fromUser.getBalance();
        BigDecimal fromNewBalance = fromOldBalance.subtract(amount);
        fromUser.setBalance(fromNewBalance);
        GUser updatedFromUser = gUserRepository.save(fromUser);

        // Create deduction transaction
        GTransaction deductTransaction = createTransaction(
            updatedFromUser.getId(),
            amount.negate(), // Negative for deduction
            fromNewBalance,
            TransactionType.Spent,
            TransactionSource.REMOVE,
            "Transfer to user " + toUser.getId() + ": " + note,
            null
        );
        transactionRepository.save(deductTransaction);

        // Add to receiver
        BigDecimal toOldBalance = toUser.getBalance();
        BigDecimal toNewBalance = toOldBalance.add(amount);
        toUser.setBalance(toNewBalance);
        GUser updatedToUser = gUserRepository.save(toUser);

        // Create addition transaction
        GTransaction addTransaction = createTransaction(
            updatedToUser.getId(),
            amount, // Positive for addition
            toNewBalance,
            TransactionType.Bonus,
            TransactionSource.BONUS,
            "Transfer from user " + fromUser.getId() + ": " + note,
            null
        );
        transactionRepository.save(addTransaction);

        log.info("Transfer completed: fromUserId={}, fromNewBalance={}, toUserId={}, toNewBalance={}",
                updatedFromUser.getId(), fromNewBalance, updatedToUser.getId(), toNewBalance);

        return new GUser[]{updatedFromUser, updatedToUser};
    }

    /**
     * Calculate partial adjustment amount with correct business logic
     *
     * Business Rules:
     * 1. First partial: refund for current remains (remains * price / 1000)
     * 2. Subsequent partials:
     *    - If remains decrease (30→20): deduct difference (30-20) * price / 1000 (negative amount)
     *    - If remains increase (20→30): refund difference (30-20) * price / 1000 (positive amount)
     *
     * Examples:
     * - quantity=50, first partial remains=30 → refund 30 * price / 1000
     * - second partial remains=20 → deduct (30-20) * price / 1000 (negative)
     * - third partial remains=25 → refund (25-20) * price / 1000 (positive)
     */
    private BigDecimal calculatePartialAdjustmentAmount(GOrder order) {
        if (order.getRemains() <= 0) {
            return BigDecimal.ZERO;
        }

        // Get all previous partial transactions for this order, ordered by creation date
        List<GTransaction> previousPartialTransactions = transactionRepository
                .findAllByOrderAndType(order, TransactionType.Partial);

        if (previousPartialTransactions.isEmpty()) {
            // First partial refund - refund for current remains
            BigDecimal refundAmount = BigDecimal.valueOf(order.getRemains())
                    .multiply(order.getPrice())
                    .divide(BigDecimal.valueOf(1000), RoundingMode.HALF_EVEN);

            log.info("First partial refund: orderId={}, remains={}, refundAmount={}",
                    order.getId(), order.getRemains(), refundAmount);

            return refundAmount;
        } else {
            // Subsequent partial - calculate difference from previous remains
            GTransaction lastTransaction = previousPartialTransactions.get(previousPartialTransactions.size() - 1);
            int previousRemains = extractRemainsFromTransactionNote(lastTransaction.getNote());

            // Fallback: if we can't extract from note, return zero to avoid errors
            if (previousRemains == -1) {
                log.warn("Could not extract previous remains from note, skipping adjustment for order: {}", order.getId());
                return BigDecimal.ZERO;
            }

            // Calculate difference:
            // Positive = remains increased (need to refund more)
            // Negative = remains decreased (need to deduct back)
            int remainsDifference = order.getRemains() - previousRemains;

            if (remainsDifference == 0) {
                log.info("No adjustment needed: orderId={}, previousRemains={}, currentRemains={}",
                        order.getId(), previousRemains, order.getRemains());
                return BigDecimal.ZERO;
            }

            BigDecimal adjustmentAmount = BigDecimal.valueOf(remainsDifference)
                    .multiply(order.getPrice())
                    .divide(BigDecimal.valueOf(1000), RoundingMode.HALF_EVEN);

            log.info("Subsequent partial adjustment: orderId={}, previousRemains={}, currentRemains={}, difference={}, adjustmentAmount={}",
                    order.getId(), previousRemains, order.getRemains(), remainsDifference, adjustmentAmount);

            return adjustmentAmount;
        }
    }

    /**
     * Extract remains value from transaction note
     * Note formats:
     * - "Partial refund for order #123 (remains: 30): 0.123"
     * - "Partial deduction for order #123 (remains: 30): 0.123"
     * Returns -1 if cannot extract
     */
    private int extractRemainsFromTransactionNote(String note) {
        if (note == null || note.isEmpty()) {
            return -1;
        }

        try {
            // Look for pattern "(remains: XX)"
            int remainsIndex = note.indexOf("(remains: ");
            if (remainsIndex == -1) {
                return -1;
            }

            int startIndex = remainsIndex + "(remains: ".length();
            int endIndex = note.indexOf(")", startIndex);
            if (endIndex == -1) {
                return -1;
            }

            String remainsStr = note.substring(startIndex, endIndex);
            return Integer.parseInt(remainsStr);
        } catch (Exception e) {
            log.warn("Failed to extract remains from transaction note: {}", note, e);
            return -1;
        }
    }

    /**
     * Add balance with custom transaction type
     */
    private GUser addBalanceWithCustomType(GUser user, BigDecimal amount, TransactionSource source,
                                         String note, GOrder order, TransactionType transactionType) {
        validateAmount(amount);

        log.info("Adding balance with custom type: userId={}, amount={}, source={}, type={}",
                user.getId(), amount, source, transactionType);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.add(amount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create transaction record with custom type
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            amount, // Positive for addition
            newBalance,
            transactionType, // Use custom transaction type
            source,
            note,
            order
        );

        transactionRepository.save(transaction);

        log.info("Balance added with custom type: userId={}, oldBalance={}, newBalance={}, type={}",
                savedUser.getId(), oldBalance, newBalance, transactionType);

        return savedUser;
    }

    /**
     * Deduct balance with custom transaction type
     */
    private GUser deductBalanceWithCustomType(GUser user, BigDecimal amount, TransactionSource source,
                                            String note, GOrder order, TransactionType transactionType) {
        validateAmount(amount);

        log.info("Deducting balance with custom type: userId={}, amount={}, source={}, type={}",
                user.getId(), amount, source, transactionType);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.subtract(amount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create transaction record with custom type (negative amount for deduction)
        GTransaction transaction = createTransaction(
            savedUser.getId(),
            amount.negate(), // Negative for deduction
            newBalance,
            transactionType, // Use custom transaction type
            source,
            note,
            order
        );

        transactionRepository.save(transaction);

        log.info("Balance deducted with custom type: userId={}, oldBalance={}, newBalance={}, type={}",
                savedUser.getId(), oldBalance, newBalance, transactionType);

        return savedUser;
    }

    /**
     * Create a transaction record
     */
    private GTransaction createTransaction(Long userId, BigDecimal change, BigDecimal balance,
                                        TransactionType type, TransactionSource source,
                                        String note, GOrder order) {
        GTransaction transaction = new GTransaction();
        transaction.setUserId(userId);
        transaction.setChange(change);
        transaction.setBalance(balance);
        transaction.setType(type);
        transaction.setSource(source);
        transaction.setNote(note);
        transaction.setOrder(order);

        return transaction;
    }

    @Override
    public BigDecimal calculateCancelRefundAmount(GOrder order) {
        // Get all previous partial transactions for this order
        List<GTransaction> previousPartialTransactions = transactionRepository
                .findAllByOrderAndType(order, TransactionType.Partial);

        if (previousPartialTransactions.isEmpty()) {
            // No previous partial refunds - refund the full charge amount
            log.info("No previous partial refunds found for order {}, refunding full charge: {}",
                    order.getId(), order.getCharge());
            return order.getCharge();
        }

        // Calculate total amount already refunded through partial transactions
        BigDecimal totalPartialRefunds = previousPartialTransactions.stream()
                .map(GTransaction::getChange)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate the remaining amount to refund
        // Cancel refund = Original charge - Total partial refunds already given
        BigDecimal cancelRefundAmount = order.getCharge().subtract(totalPartialRefunds);

        // Ensure we don't refund negative amounts (in case of over-refunding scenarios)
        BigDecimal finalRefundAmount = cancelRefundAmount.max(BigDecimal.ZERO);

        log.info("Cancel refund calculation for order {}: originalCharge={}, totalPartialRefunds={}, cancelRefundAmount={}",
                order.getId(), order.getCharge(), totalPartialRefunds, finalRefundAmount);

        return finalRefundAmount;
    }

    /**
     * Validate amount is positive
     */
    private void validateAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }
    }

    /**
     * Send balance notification to user
     */
    private void sendBalanceNotification(GUser user, BigDecimal amount, boolean isAddition,
                                       TransactionSource source, String note) {
        try {
            NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
            String formattedAmount = currencyFormat.format(amount);

            String title;
            String content;

            if (isAddition) {
                if (source == TransactionSource.BANK) {
                    title = "Nạp tiền thành công";
                    content = String.format("Tài khoản của bạn đã được nạp thêm %s. %s",
                            formattedAmount, note != null ? note : "");
                } else if (source == TransactionSource.CANCELED || source == TransactionSource.REFUND) {
                    title = "Hoàn tiền";
                    content = String.format("Bạn đã được hoàn %s. %s",
                            formattedAmount, note != null ? note : "");
                } else if (source == TransactionSource.BONUS) {
                    title = "Thưởng";
                    content = String.format("Bạn đã nhận được thưởng %s. %s",
                            formattedAmount, note != null ? note : "");
                } else {
                    title = "Số dư tăng";
                    content = String.format("Tài khoản của bạn đã được cộng thêm %s. %s",
                            formattedAmount, note != null ? note : "");
                }
            } else {
                title = "Số dư giảm";
                content = String.format("Tài khoản của bạn đã bị trừ %s. %s",
                        formattedAmount, note != null ? note : "");
            }

            // Create custom notification with title and content
            userNotificationService.createBalanceNotification(user, title + ": " + content);

        } catch (Exception e) {
            log.error("Failed to send balance notification for user {}: {}", user.getId(), e.getMessage());
            // Don't throw exception to avoid affecting the main transaction
        }
    }

    /**
     * Update actual charge after refund/partial operations
     * Calculates the actual amount the user paid after all refunds
     */
    private void updateActualChargeAfterRefund(GOrder order) {
        try {
            // Get all refund-related transactions for this order
            List<GTransaction> refundTransactions = transactionRepository
                    .findAllByOrderAndTypeIn(order, Arrays.asList(TransactionType.Refund, TransactionType.Partial));

            // Calculate total refunded amount (sum of positive changes)
            BigDecimal totalRefunded = refundTransactions.stream()
                    .map(GTransaction::getChange)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Actual charge = Original charge - Total refunded
            BigDecimal actualCharge = order.getCharge().subtract(totalRefunded);

            // Ensure actual charge is not negative
            actualCharge = actualCharge.max(BigDecimal.ZERO);

            // Update and save the order
            order.setActualCharge(actualCharge);
            orderRepository.save(order);

            log.info("Updated actual charge for order {}: originalCharge={}, totalRefunded={}, actualCharge={}",
                    order.getId(), order.getCharge(), totalRefunded, actualCharge);

        } catch (Exception e) {
            log.error("Failed to update actual charge for order {}: {}", order.getId(), e.getMessage());
            // Don't throw exception to avoid affecting the main transaction
        }
    }
}
