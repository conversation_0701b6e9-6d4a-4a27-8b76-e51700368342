<div class="menu-sidebar bg-white text-gray-800 p-3 shadow-sm rounded-lg mr-2" [ngClass]="{'open': isOpen}">
  <!-- Close button (mobile only) -->
  <button
    class="close-btn md:hidden absolute top-4 right-4 text-white z-10 w-8 h-8 flex items-center justify-center rounded-full bg-blue-600 hover:bg-blue-700"
    (click)="closeSidebar()">
    <fa-icon [icon]="['fas', 'times']" class="text-lg" class="text-lg"></fa-icon>
  </button>

  <div class="menu-content">
    <!-- Navigation Sections -->
    <div *ngFor="let section of settingsNavItems" class="mb-6">
      <h2 class="text-gray-500 text-xs font-semibold uppercase mb-2 px-2">{{ section.title }}</h2>

      <ul>
        <li *ngFor="let item of section.items" class="mb-0.5">
          <a [routerLink]="item.link"
          class="flex items-center gap-3 py-2 px-3 rounded-lg transition-colors"
          (click)="onMobileItemClick()"
          [ngClass]="{'bg-blue-100 text-blue-700': item.isActive, 'hover:bg-gray-100': !item.isActive}">
             <div class="w-6 h-6 flex items-center justify-center rounded-md" [ngClass]="getIconBackgroundColor(item.icon)">
              <fa-icon [icon]="['fas', item.icon]" class="text-xs text-white"></fa-icon>
            </div>
            <span class="text-sm font-medium">{{ item.label }}</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
