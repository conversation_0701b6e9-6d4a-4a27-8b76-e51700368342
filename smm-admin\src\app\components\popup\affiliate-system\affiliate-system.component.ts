import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-affiliate-system',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './affiliate-system.component.html',
  styleUrls: ['./affiliate-system.component.css']
})
export class AffiliateSystemComponent {
  @Output() close = new EventEmitter<void>();
  
  // Settings
  enableOnPanel: boolean = true;
  referralPercentage: number = 5;

  constructor() {}

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  saveSettings(): void {
    // Here you would implement the API call to save the settings
    console.log('Saving affiliate settings:', {
      enableOnPanel: this.enableOnPanel,
      referralPercentage: this.referralPercentage
    });
    
    // Show success message and close popup
    this.onClose();
  }
}
