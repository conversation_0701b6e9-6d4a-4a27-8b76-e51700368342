import { Component, OnInit, HostListener, ElementRef } from '@angular/core';
import { TenantService, TenantInfo } from '../../core/services/tenant.service';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToastService } from '../../core/services/toast.service';
import { NotifyType } from '../../constant/notify-type';
import { NewPanelStep1Component } from '../popup/new-panel-step1/new-panel-step1.component';
import { NewPanelStep2Component } from '../popup/new-panel-step2/new-panel-step2.component';
import { NewPanelStep3Component } from '../popup/new-panel-step3/new-panel-step3.component';
import { PanelService } from '../../core/services/panel.service';
import { ModalService } from '../../core/services/modal.service';
import { Router } from '@angular/router';

// Extend TenantInfo to include status
interface ExtendedTenantInfo extends TenantInfo {
  status: string;
}

@Component({
  selector: 'app-tenant-switcher',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  template: `
    <!-- Mobile backdrop -->
    <div
      *ngIf="isDropdownOpen"
      class="fixed inset-0 bg-black bg-opacity-25 z-[9998] md:hidden"
      (click)="isDropdownOpen = false"
    ></div>

    <div class="relative">
      <button
        (click)="toggleDropdown()"
        class="flex items-center space-x-1 px-3 py-2 rounded-md hover:bg-gray-100 bg-white text-gray-800 border border-gray-200"
      >
        <div class="flex flex-col items-start">
          <span class="text-sm font-medium">{{ currentTenant?.domain || 'Select Tenant' }}</span>
          <span
            *ngIf="currentTenant?.days_until_expiration !== undefined && currentTenant?.days_until_expiration !== null"
            class="text-xs"
            [ngClass]="getRemainingDaysClass(currentTenant?.days_until_expiration)"
          >
            {{ formatRemainingDays(currentTenant?.days_until_expiration) }}
          </span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div *ngIf="isDropdownOpen" class="tenant-dropdown absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-[9999] border border-gray-200 cursor-pointer">
        <div class="py-2">
          <div class="px-4 py-2 border-b border-gray-100 flex items-center justify-between">
            <span class="text-sm font-semibold text-gray-700">Panels</span>
            <div class="flex items-center space-x-2">
              <button
                class="flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 p-1"
                (click)="navigateToAllPanels()"
                title="Settings"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
              <button
                class="flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 p-1"
                (click)="createPanel()"
                title="Create Panel"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          </div>
          <div *ngFor="let tenant of accessibleTenants$ | async" class="px-3 py-2 hover:bg-gray-50">
            <div class="flex items-center justify-between" (click)="switchTenant(tenant)">
              <div class="flex items-center space-x-3 flex-1">
                <div class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <div
                    class="text-left text-sm font-medium text-gray-800 truncate"
                    [class.font-bold]="tenant.id === currentTenant?.id"
                  >
                    {{ tenant.domain }}
                  </div>
                  <div
                    *ngIf="tenant.days_until_expiration !== undefined && tenant.days_until_expiration !== null"
                    class="text-xs"
                    [ngClass]="getRemainingDaysClass(tenant.days_until_expiration)"
                  >
                    {{ formatRemainingDays(tenant.days_until_expiration) }}
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border"
                  [ngClass]="getStatusClass(tenant.status)"
                >
                  {{ getStatusLabel(tenant.status) }}
                </span>
                <button
                  *ngIf="shouldShowDeleteButton(tenant.status)"
                  class="text-gray-400 hover:text-gray-600"
                  (click)="deleteTenant(tenant, $event)"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
                <div
                  *ngIf="tenant.id === currentTenant?.id"
                  class="text-green-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      position: relative;
    }

    /* Ensure dropdown is always on top */
    .tenant-dropdown {
      position: absolute;
      z-index: 9999 !important;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Mobile specific adjustments */
    @media (max-width: 768px) {
      .tenant-dropdown {
        position: fixed !important;
        top: 60px !important;
        right: 10px !important;
        left: 10px !important;
        width: auto !important;
        max-width: none !important;
        z-index: 9999 !important;
        transform: none !important;
      }
    }

    /* Tablet adjustments */
    @media (max-width: 1024px) and (min-width: 769px) {
      .tenant-dropdown {
        right: 0 !important;
        width: 320px !important;
        z-index: 9999 !important;
      }
    }
  `]
})
export class TenantSwitcherComponent implements OnInit {
  isDropdownOpen = false;
  currentTenant: ExtendedTenantInfo | null = null;
  accessibleTenants$: Observable<ExtendedTenantInfo[]>;

  // Panel creation properties
  currentStep = 0; // 0 = no modal, 1-3 = steps
  domainName = '';
  isCreatingPanel = false;

  constructor(
    private tenantService: TenantService,
    private toastService: ToastService,
    private panelService: PanelService,
    private modalService: ModalService,
    private router: Router,
    private elementRef: ElementRef
  ) {
    this.accessibleTenants$ = this.tenantService.accessibleTenants$ as Observable<ExtendedTenantInfo[]>;

    // Load tenant information on component initialization
    this.tenantService.getAccessibleTenants().subscribe();
  }

  ngOnInit(): void {
    this.tenantService.currentTenant$.subscribe(tenantId => {
      if (tenantId) {
        // Find the tenant info from the accessible tenants list
        this.accessibleTenants$.subscribe(tenants => {
          this.currentTenant = tenants.find(t => t.id === tenantId) || null;
        });
      } else {
        this.currentTenant = null;
      }
    });
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event): void {
    if (this.isDropdownOpen && !this.elementRef.nativeElement.contains(event.target)) {
      this.isDropdownOpen = false;
    }
  }

  switchTenant(tenant: ExtendedTenantInfo): void {
    if (this.currentTenant && tenant.id === this.currentTenant.id) {
      this.isDropdownOpen = false;
      return;
    }

    this.tenantService.switchTenant(tenant.id).subscribe({
      next: () => {
        this.toastService.showToast(`Switched to tenant: ${tenant.domain}`, NotifyType.SUCCESS);
        this.isDropdownOpen = false;

        // Reload the page after switching tenant
        setTimeout(() => {
          window.location.reload();
        }, 300); // Short delay to ensure the toast message is visible
      },
      error: (error) => {
        console.error('Error switching tenant:', error);
        this.toastService.showToast('Failed to switch tenant', NotifyType.ERROR);
      }
    });
  }

  getStatusClass(status?: string): string {
    if (!status) return 'bg-gray-100 text-gray-800';

    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Expired':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'New':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Nginx80':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Configured':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  getStatusLabel(status?: string): string {
    if (!status) return 'Unknown';

    switch (status) {
      case 'Ready':
        return 'Active';
      case 'Failed':
        return 'Error';
      case 'Expired':
        return 'Expired';
      case 'New':
        return 'New';
      case 'Nginx80':
      case 'Configured':
        return 'Configuring';
      case 'Pending':
        return 'Pending';
      default:
        return status;
    }
  }

  formatRemainingDays(daysUntilExpiration?: number): string {
    if (daysUntilExpiration === undefined || daysUntilExpiration === null) {
      return '';
    }

    if (daysUntilExpiration < 0) {
      return `Expired ${Math.abs(daysUntilExpiration)} days ago`;
    } else if (daysUntilExpiration === 0) {
      return 'Expires today';
    } else if (daysUntilExpiration === 1) {
      return '1 day left';
    } else {
      return `${daysUntilExpiration} days left`;
    }
  }

  getRemainingDaysClass(daysUntilExpiration?: number): string {
    daysUntilExpiration = daysUntilExpiration ? Math.abs(daysUntilExpiration) : 0;
    if (daysUntilExpiration === undefined || daysUntilExpiration === null) {
      return 'text-gray-500';
    }

    if (daysUntilExpiration < 0) {
      return 'text-red-600';
    } else if (daysUntilExpiration <= 7) {
      return 'text-orange-600';
    } else  {
      return 'text-green-600';
    } 
  }

  shouldShowDeleteButton(status?: string): boolean {
    return status === 'Failed' || status === 'Expired';
  }

  deleteTenant(tenant: ExtendedTenantInfo, event: Event): void {
    event.stopPropagation();
    // Implement delete functionality here
    console.log('Delete tenant:', tenant);
    this.toastService.showToast(`Delete functionality not implemented yet for: ${tenant.domain}`, NotifyType.WARNING);
  }

  navigateToAllPanels(): void {
    // Close the dropdown
    this.isDropdownOpen = false;

    // Navigate to all-panels page
    this.router.navigate(['/panel/settings/all-panels']);
  }

  createPanel(): void {
    // Close the dropdown
    this.isDropdownOpen = false;

    // Reset domain name
    this.domainName = '';

    // Open the first step modal
    const modalRef = this.modalService.open(NewPanelStep1Component, {
      domain: this.domainName
    });

    // Subscribe to events
    modalRef.instance.close.subscribe(() => {
      this.modalService.close();
    });

    modalRef.instance.next.subscribe((domain: string) => {
      this.domainName = domain;
      this.openStep2Modal();
    });
  }

  openStep2Modal(): void {
    // Close previous modal
    this.modalService.close();

    // Open step 2 modal
    const modalRef = this.modalService.open(NewPanelStep2Component, {
      domain: this.domainName
    });

    // Subscribe to events
    modalRef.instance.close.subscribe(() => {
      this.modalService.close();
    });

    modalRef.instance.back.subscribe(() => {
      this.modalService.close();
      this.createPanel(); // Go back to step 1
    });

    modalRef.instance.next.subscribe(() => {
      this.openStep3Modal();
    });
  }

  openStep3Modal(): void {
    // Close previous modal
    this.modalService.close();

    // Open step 3 modal
    const modalRef = this.modalService.open(NewPanelStep3Component, {
      domain: this.domainName
    });

    // Subscribe to events
    modalRef.instance.close.subscribe(() => {
      this.modalService.close();
    });

    modalRef.instance.back.subscribe(() => {
      this.modalService.close();
      this.openStep2Modal(); // Go back to step 2
    });

    modalRef.instance.createPanel.subscribe((panelInfo: any) => {
      this.createPanelSubmit();
    });
  }

  createPanelSubmit(): void {
    this.isCreatingPanel = true;

    // Call the API to create the panel
    this.panelService.createPanel(this.domainName).subscribe({
      next: (response) => {
        this.toastService.showToast(`Panel created successfully for domain: ${this.domainName}`, NotifyType.SUCCESS);
        this.modalService.close();

        // Refresh the tenant list
        this.tenantService.getAccessibleTenants().subscribe();
      },
      error: (error) => {
        console.error('Error creating panel:', error);

        // Use error message with fallback to default message
        const errorMessage = error?.message || 'Failed to create panel';

        this.toastService.showToast(errorMessage, NotifyType.ERROR);
        this.isCreatingPanel = false;
      }
    });
  }
}
