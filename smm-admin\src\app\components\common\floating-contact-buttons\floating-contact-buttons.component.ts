import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { brandColors } from '../../../icons/icons.font-awesome-brands';
import { IntegrationsService } from '../../../core/services/integrations.service';
import { IntegrationPosition, IntegrationRes } from '../../../model/response/integration-res.model';

@Component({
  selector: 'app-floating-contact-buttons',
  standalone: true,
  imports: [CommonModule, IconsModule, TranslateModule],
  templateUrl: './floating-contact-buttons.component.html',
  styleUrls: ['./floating-contact-buttons.component.css']
})
export class FloatingContactButtonsComponent implements OnInit {
  brandColors = brandColors;

  // Integration data from API
  leftIntegrations: IntegrationRes[] = [];
  rightIntegrations: IntegrationRes[] = [];
  loading: boolean = false;

  constructor(
    private integrationsService: IntegrationsService
  ) {}

  ngOnInit(): void {
    this.loadIntegrations();
  }

  loadIntegrations(): void {
    this.loading = true;
    this.integrationsService.getForUser().subscribe({
      next: (integrations) => {
        // Filter integrations by position
        this.leftIntegrations = integrations.filter(
          integration => integration.active && integration.position === IntegrationPosition.LEFT
        );
        this.rightIntegrations = integrations.filter(
          integration => integration.active && integration.position === IntegrationPosition.RIGHT
        );
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading integrations:', error);
        this.loading = false;
      }
    });
  }

  getIntegrationIcon(key: string): any {
    switch (key.toLowerCase()) {
      case 'telegram':
        return ['fab', 'telegram-plane'];
      case 'whatsapp':
        return ['fab', 'whatsapp'];
      default:
        return ['fab', key.toLowerCase()];
    }
  }

  getIntegrationColor(key: string): string {
    const brandKey = key.toLowerCase();
    return brandColors[brandKey] || '#6c757d'; // Default color if not found
  }

  openIntegrationLink(integration: IntegrationRes): void {
    if (!integration.value) return;

    let url = '';

    switch (integration.key.toLowerCase()) {
      case 'telegram':
        // Handle Telegram link
        url = `https://t.me/${integration.value.replace('@', '')}`;
        break;
      case 'whatsapp':
        // Handle WhatsApp link
        const cleanNumber = integration.value.replace(/[\s+]/g, '');
        url = `https://wa.me/${cleanNumber}`;
        break;
      default:
        // For other integration types, use the value directly if it's a URL
        if (integration.value.startsWith('http')) {
          url = integration.value;
        } else {
          console.warn('Unknown integration type:', integration.key);
          return;
        }
    }

    // Open the URL in a new tab
    if (url) {
      window.open(url, '_blank');
    }
  }
}
