/**
 * Domain management controller
 */

const express = require('express');
const DomainService = require('../services/domain-service');
const { asyncHandler } = require('../middleware/error-handler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Add a domain by domain name
 * POST /domains/:domain
 */
router.post('/:domain', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  logger.info(`Received request to add domain: ${domain}`);

  const result = await DomainService.addDomain(domain);

  res.status(201).json(result);
}));

/**
 * Remove a domain
 * DELETE /domains/:name
 */
router.delete('/:name', asyncHandler(async (req, res) => {
  const domainName = req.params.name;
  logger.info(`Received request to remove domain: ${domainName}`);

  const result = await DomainService.removeDomain(domainName);

  res.json(result);
}));

/**
 * Refresh Nginx configuration for all domains
 * POST /domains/refresh
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  logger.info('Received request to refresh all domain configurations');

  const result = await DomainService.refreshAllConfigurations();

  res.json(result);
}));

/**
 * Suspend a domain by removing its Nginx configuration
 * POST /domains/:domain/suspend
 */
router.post('/:domain/suspend', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  logger.info(`Received request to suspend domain: ${domain}`);

  const result = await DomainService.suspendDomain(domain);

  res.json(result);
}));

module.exports = router;
