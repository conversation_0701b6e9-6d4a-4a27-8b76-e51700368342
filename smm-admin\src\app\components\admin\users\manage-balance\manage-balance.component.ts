import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { UserBalanceService, AddFundRequest } from '../../../../core/services/user-balance.service';
import { TransactionRes, TransactionSource, TransactionType } from '../../../../model/response/transaction.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-manage-balance',
  standalone: true,
  imports: [CommonModule, FormsModule, IconsModule, TranslateModule],
  templateUrl: './manage-balance.component.html',
  styleUrl: './manage-balance.component.css'
})
export class ManageBalanceComponent implements OnInit, OnD<PERSON>roy {
  @Input() userId: number = 0;
  @Input() userName: string = '';
  @Input() currentBalance: number = 0;
  @Output() close = new EventEmitter<void>();
  @Output() balanceUpdated = new EventEmitter<number>();

  amount: string = '';
  activeTab: 'payments' | 'orders' = 'payments';
  searchQuery: string = '';
  isLoading: boolean = false;

  // Transaction history data
  transactions: TransactionRes[] = [];

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };

  private subscriptions: Subscription[] = [];

  constructor(private userBalanceService: UserBalanceService) {}

  ngOnInit(): void {
    // Subscribe to loading state
    this.subscriptions.push(
      this.userBalanceService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to transactions data
    this.subscriptions.push(
      this.userBalanceService.transactions$.subscribe(transactions => {
        this.transactions = transactions;
      })
    );

    // Subscribe to pagination data
    this.subscriptions.push(
      this.userBalanceService.pagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Load transaction history
    this.loadTransactionHistory();
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadTransactionHistory(page: number = 0): void {
    const types = this.getTypesForActiveTab();
    this.userBalanceService.loadUserTransactions(this.userId, page, this.pagination.pageSize, types);
  }

  private getTypesForActiveTab(): string[] {
    if (this.activeTab === 'payments') {
      return ['Bonus', 'Deposit', 'Remove'];
    } else {
      return ['Spent', 'Refund', 'Partial'];
    }
  }

  addBalance(): void {
    if (!this.amount || isNaN(Number(this.amount)) || Number(this.amount) <= 0) {
      return;
    }

    const request: AddFundRequest = {
      amount: Number(this.amount),
      source: TransactionSource.BONUS,
      note: `Added by admin for user ${this.userId}`
    };

    this.userBalanceService.addFunds(this.userId, request).subscribe({
      next: (response) => {
        console.log('Balance added successfully:', response);
        // Update the current balance
        this.currentBalance += Number(this.amount);
        // Emit the updated balance
        this.balanceUpdated.emit(this.currentBalance);
        // Reload transaction history
        this.loadTransactionHistory();
        // Reset input
        this.amount = '';
      },
      error: (error) => {
        console.error('Error adding balance:', error);
      }
    });
  }

  removeBalance(): void {
    if (!this.amount || isNaN(Number(this.amount)) || Number(this.amount) <= 0) {
      return;
    }

    // For removing balance, we use a negative amount
    const request: AddFundRequest = {
      amount: -Number(this.amount),
      source: TransactionSource.REMOVE,
      note: `Removed by admin for user ${this.userId}`
    };

    this.userBalanceService.addFunds(this.userId, request).subscribe({
      next: (response) => {
        console.log('Balance removed successfully:', response);
        // Update the current balance
        this.currentBalance -= Number(this.amount);
        // Emit the updated balance
        this.balanceUpdated.emit(this.currentBalance);
        // Reload transaction history
        this.loadTransactionHistory();
        // Reset input
        this.amount = '';
      },
      error: (error) => {
        console.error('Error removing balance:', error);
      }
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }

  getFormattedAmount(transaction: TransactionRes): string {
    if (transaction.change > 0) {
      return `+$${transaction.change}`;
    } else {
      return `-$${Math.abs(transaction.change)}`;
    }
  }

  getTransactionType(transaction: TransactionRes): 'add' | 'remove' {
    return transaction.change > 0 ? 'add' : 'remove';
  }

  switchTab(tab: 'payments' | 'orders'): void {
    this.activeTab = tab;
    // Reload transaction history with new filter when switching tabs
    this.loadTransactionHistory(0);
  }

  closeModal(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close the modal if the user clicks on the overlay (outside the modal content)
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }

  loadNextPage(): void {
    if (this.pagination.pageNumber < this.pagination.totalPages - 1) {
      this.loadTransactionHistory(this.pagination.pageNumber + 1);
    }
  }

  loadPreviousPage(): void {
    if (this.pagination.pageNumber > 0) {
      this.loadTransactionHistory(this.pagination.pageNumber - 1);
    }
  }

  searchTransactions(): void {
    // In a real application, you would implement search functionality here
    console.log('Searching for:', this.searchQuery);
  }
}
