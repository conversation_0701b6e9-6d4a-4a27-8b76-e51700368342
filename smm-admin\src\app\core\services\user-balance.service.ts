import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize } from 'rxjs';
import { TransactionPageRes, TransactionRes, TransactionSource } from '../../model/response/transaction.model';
import { ConfigService } from './config.service';

export interface AddFundRequest {
  amount: number;
  source: TransactionSource | string;
  note: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserBalanceService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _transactions$ = new BehaviorSubject<TransactionRes[]>([]);
  private _pagination$ = new BehaviorSubject<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) { }

  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  get transactions$(): Observable<TransactionRes[]> {
    return this._transactions$.asObservable();
  }

  get pagination$(): Observable<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }> {
    return this._pagination$.asObservable();
  }

  /**
   * Add funds to a user's account
   * @param userId User ID
   * @param request Add fund request
   * @returns Observable of the transaction
   */
  addFunds(userId: number, request: AddFundRequest): Observable<TransactionRes> {
    this._loading$.next(true);
    return this.http.put<TransactionRes>(
      `${this.configService.apiUrl}/users/${userId}/add-fund`,
      request
    ).pipe(
      finalize(() => this._loading$.next(false))
    );
  }

  /**
   * Get user transactions
   * @param userId User ID (use 'me' for current user)
   * @param page Page number (0-based)
   * @param size Page size
   * @param types Optional array of transaction types to filter by
   * @returns Observable of paginated transactions
   */
  getUserTransactions(userId: string | number = 'me', page: number = 0, size: number = 10, types?: string[]): Observable<TransactionPageRes> {
    this._loading$.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    // Add types parameter if provided
    if (types && types.length > 0) {
      params = params.set('types', types.join(','));
    }

    return this.http.get<TransactionPageRes>(
      `${this.configService.apiUrl}/users/${userId}/transactions`,
      { params }
    ).pipe(
      finalize(() => this._loading$.next(false))
    );
  }

  /**
   * Load user transactions and update the BehaviorSubjects
   * @param userId User ID (use 'me' for current user)
   * @param page Page number (0-based)
   * @param size Page size
   * @param types Optional array of transaction types to filter by
   */
  loadUserTransactions(userId: string | number = 'me', page: number = 0, size: number = 10, types?: string[]): void {
    this.getUserTransactions(userId, page, size, types).subscribe({
      next: (response) => {
        this._transactions$.next(response.content);
        this._pagination$.next({
          pageNumber: response.pageable.page_number,
          pageSize: response.pageable.page_size,
          totalElements: response.total_elements,
          totalPages: response.total_pages
        });
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        this._transactions$.next([]);
      }
    });
  }
}
