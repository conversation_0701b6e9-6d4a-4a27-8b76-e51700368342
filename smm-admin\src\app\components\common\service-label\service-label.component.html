<!-- No service message -->
<div *ngIf="!service || !service.id" class="no-service-message text-center py-2 text-gray-500">
  {{ 'no_services_available' | translate }}
</div>

<!-- Service content when available -->
<ng-container *ngIf="service && service.id">
  <div *ngIf="lite; else full" class="header-container">
    <div class="flex items-center w-full overflow-hidden">
      <div class="flex-shrink-0">
        <app-social-icon *ngIf="service.icon" zClass="mr-2" [icon]="service.icon || 'facebook'"></app-social-icon>
      </div>
      <div class="overflow-hidden mr-8">
        <span class="lite-description" *ngIf="service.name?.startsWith('filter.'); else normalName">{{ service.name | translate }}</span>
        <ng-template #normalName>
          <span class="lite-description">{{service.name}}</span>
        </ng-template>
      </div>
    </div>
  </div>

  <ng-template #full>
    <div class="header-container">
      <app-social-icon *ngIf="service.icon" [icon]="service.icon || 'facebook'"></app-social-icon>

      <div class="badge">{{service.id}}</div>
      <span class="description" *ngIf="service.name?.startsWith('filter.'); else normalFullName">{{ service.name | translate }} - </span>
      <ng-template #normalFullName>
        <span class="description">{{service.name}} - </span>
      </ng-template>
      <div  class="flex items-center">
  
        <!-- Always show the final price -->
        <span class="description text-red-500">{{service.price}}</span>
      </div>
        <fa-icon *ngIf="service.api_service_id" [icon]="['fas', 'copy']" class="copy-icon" (click)="copyToClipboard(service.api_service_id, $event)" [fixedWidth]="true"></fa-icon>
    </div>
    <div class="tags-container">
      <app-tag-label *ngFor="let tag of service.labels" [color]="tag.type === 'Green' ? ' #289d35' : '#cf0616'" [text]="tag.text"></app-tag-label>
      <div *ngIf="getSpecialPriceCount() > 0"  class="flex items-center" >
        <span class="inline-flex items-center justify-center w-5 h-5 mr-2 bg-[#f97316] text-white rounded-full">
          <fa-icon [icon]="['fas', 'tag']" [size]="'xs'"></fa-icon>
        </span>
        <span  class="text-sm text-gray-900 font-medium">{{ getSpecialPriceCount() }} special prices</span>
      </div>
    </div>
  </ng-template>
</ng-container>