<div class="popup-overlay" (click)="onClose()">
  <div class="popup-container" (click)="$event.stopPropagation()">
    <div class="popup-header">
      <div class="header-icon">
        <fa-icon [icon]="['fas', 'globe']"></fa-icon>
      </div>
      <h2 class="popup-title">{{ 'Select Language' | translate }}</h2>
      <button class="close-button" (click)="onClose()">
        <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>

    <div class="popup-content">
      <div class="language-list">
        <div
          *ngFor="let language of unselectedLanguages"
          class="language-item"
          (click)="selectLanguage(language.code)">
          <div class="language-flag">
            <span [class]="language.flag" [title]="language.name"></span>
          </div>
          <div class="language-name">{{ language.name }}</div>
          <div class="language-arrow">
            <fa-icon [icon]="['fas', 'chevron-right']"></fa-icon>
          </div>
        </div>

        <div *ngIf="unselectedLanguages.length === 0" class="no-languages">
          <div class="no-languages-icon">
            <fa-icon [icon]="['fas', 'check-circle']"></fa-icon>
          </div>
          <div class="no-languages-text">{{ 'All languages have been selected' | translate }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
