import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { SuperPlatformRes } from '../../model/response/super-platform.model';

@Injectable({
  providedIn: 'root'
})
export class PlatformSelectionService {
  private platformSelectedSource = new Subject<SuperPlatformRes>();
  
  platformSelected$ = this.platformSelectedSource.asObservable();
  
  selectPlatform(platform: SuperPlatformRes) {
    this.platformSelectedSource.next(platform);
  }
}
