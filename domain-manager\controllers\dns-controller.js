/**
 * DNS management controller
 */

const express = require('express');
const dnsManager = require('../dns-manager');
const { asyncHandler } = require('../middleware/error-handler');
const logger = require('../utils/logger');
const config = require('../config/constants');

const router = express.Router();

/**
 * Add domain to DNS
 * POST /dns/:domain
 */
router.post('/:domain', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  const ip = req.body.ip || config.SERVER_IP;

  logger.info(`Adding domain ${domain} to DNS with IP ${ip}`);

  const result = await dnsManager.addDomain(domain, ip);

  res.status(201).json({
    message: `Domain ${domain} added to DNS successfully`,
    details: result
  });
}));

/**
 * Remove domain from DNS
 * DELETE /dns/:domain
 */
router.delete('/:domain', asyncHandler(async (req, res) => {
  const domain = req.params.domain;

  logger.info(`Removing domain ${domain} from DNS`);

  const result = await dnsManager.removeDomain(domain);

  res.json({
    message: `Domain ${domain} removed from DNS successfully`,
    details: result
  });
}));

/**
 * Reload DNS configuration
 * POST /dns/reload
 */
router.post('/reload', asyncHandler(async (req, res) => {
  logger.info('Reloading DNS configuration');

  await dnsManager.reloadConfig();

  res.json({
    message: 'DNS configuration reloaded successfully'
  });
}));

module.exports = router;
