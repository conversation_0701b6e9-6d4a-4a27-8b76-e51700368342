<div class="overlay-black">
  <div class="modal-container bg-white rounded-2xl shadow-lg w-[500px] max-w-full">
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-800">Add manager</h2>
      <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Form -->
    <div class="p-6">
      <form (ngSubmit)="addManager()">
        <!-- Login -->
        <div class="mb-4">
          <label for="login" class="block text-sm font-medium text-gray-700 mb-1">Login</label>
          <input
            type="text"
            id="login"
            name="login"
            [(ngModel)]="login"
            placeholder="agent9165"
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
            required
          >
        </div>

        <!-- Password -->
        <div class="mb-6">
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
          <input
            type="password"
            id="password"
            name="password"
            [(ngModel)]="password"
            placeholder="••••••••"
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
            required
          >
        </div>

        <!-- Role Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">Role</label>
          <div class="space-y-3">
            <div *ngFor="let role of roles"
                 class="flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors"
                 [ngClass]="{
                   'border-[var(--primary)] bg-[var(--primary-light)]': selectedRole === role.id && role.enabled,
                   'border-gray-200 bg-white hover:border-gray-300': selectedRole !== role.id && role.enabled,
                   'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed': !role.enabled
                 }"
                 (click)="selectRole(role.id)">
              <!-- Left side: Radio button and content -->
              <div class="flex items-center">
                <div class="w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3"
                     [ngClass]="{
                       'border-[var(--primary)] bg-[var(--primary)]': selectedRole === role.id && role.enabled,
                       'border-gray-300': selectedRole !== role.id || !role.enabled
                     }">
                  <div *ngIf="selectedRole === role.id && role.enabled" class="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <div class="flex-1">
                  <div class="flex items-center">
                    <span class="font-medium text-gray-900" [ngClass]="{'text-gray-500': !role.enabled}">
                      {{ role.name }}
                    </span>
                    <fa-icon *ngIf="role.id === 'Admin'" [icon]="['fas', 'check-circle']" class="ml-2 text-green-500"></fa-icon>
                  </div>
                  <p class="text-sm text-gray-600 mt-1" [ngClass]="{'text-gray-400': !role.enabled}">
                    {{ role.description }}
                  </p>
                </div>
              </div>

              <!-- Right side: Role Icon -->
              <div class="ml-4">
                <div *ngIf="role.id === 'Admin'" class="w-12 h-12 rounded-full flex items-center justify-center">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSIxMiIgeT0iMTIiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMTIgMUwyMSA1VjExQzIxIDE2LjU1IDE3LjE2IDIxLjc0IDEyIDIzQzYuODQgMjEuNzQgMyAxNi41NSAzIDExVjVMMTIgMVoiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik05IDEyTDExIDE0TDE1IDEwIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="Admin" class="w-12 h-12">
                </div>
                <div *ngIf="role.id === 'Moderator'" class="w-12 h-12 rounded-full flex items-center justify-center">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNGRjk1MDAiLz4KPHN2ZyB4PSIxMiIgeT0iMTIiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMjAgNkw5IDEyTDQgOSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNiIgY3k9IjMiIHI9IjIiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS41Ii8+CjxwYXRoIGQ9Ik0yIDEwVjlBMyAzIDAgMCAxIDUgNkgxMUEzIDMgMCAwIDEgMTQgOVYxMCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+Cjwvc3ZnPgo=" alt="Moderator" class="w-12 h-12">
                </div>
                <div *ngIf="role.id === 'Agent'" class="w-12 h-12 rounded-full flex items-center justify-center">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMxNkE2NEYiLz4KPHN2ZyB4PSIxMiIgeT0iMTIiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjMiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNiAyMVY5QTMgMyAwIDAgMSA5IDZIMTVBMyAzIDAgMCAxIDE4IDlWMjEiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik04IDE0SDE2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8cGF0aCBkPSJNOCAxN0gxNiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+Cjwvc3ZnPgo=" alt="Agent" class="w-12 h-12">
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Panel Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">Select accessible panels</label>
          <div class="space-y-2">
            <div *ngFor="let panel of panels" class="flex items-center">
              <input
                type="checkbox"
                [id]="panel.id"
                [checked]="panel.checked"
                (change)="togglePanel(panel.id)"
                class="w-4 h-4 text-[var(--primary)] bg-white border-gray-300 rounded focus:ring-[var(--primary)]"
              >
              <label [for]="panel.id" class="ml-2 text-sm text-gray-700 cursor-pointer">
                {{ panel.name }}
              </label>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-sm text-red-600">{{ errorMessage }}</p>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          [disabled]="loading"
          class="w-full bg-[var(--primary)] text-white py-3 px-4 rounded-lg font-medium hover:bg-[var(--primary-hover)] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span *ngIf="!loading">Add</span>
          <span *ngIf="loading" class="flex items-center justify-center">
            <fa-icon [icon]="['fas', 'spinner']" [spin]="true" class="mr-2"></fa-icon>
            Adding...
          </span>
        </button>
      </form>
    </div>
  </div>
</div>
