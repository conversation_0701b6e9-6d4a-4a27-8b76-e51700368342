import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, interval } from 'rxjs';
import { ConfigService } from './config.service';
import { PanelNotificationRes, PanelNotificationReq } from '../../model/response/panel-notification-res.model';


@Injectable({
  providedIn: 'root'
})
export class PanelNotificationService {
  private readonly apiUrl: string;
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.apiUrl = `${this.configService.apiUrl}/panel-notifications`;

    // Poll for unread count every 30 seconds
    interval(30000).subscribe(() => {
      this.refreshUnreadCount();
    });

    // Initial load
    this.refreshUnreadCount();
  }

  createNotification(req: PanelNotificationReq): Observable<PanelNotificationRes> {
    return this.http.post<PanelNotificationRes>(this.apiUrl, req);
  }

  getNotifications(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}?page=${page}&size=${size}`);
  }

  getUnreadNotifications(): Observable<PanelNotificationRes[]> {
    return this.http.get<PanelNotificationRes[]>(`${this.apiUrl}/unread`);
  }

  getUnreadCount(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/unread/count`);
  }

  markAsRead(id: number): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/${id}/read`, {});
  }

  markAllAsRead(): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/read-all`, {});
  }

  private refreshUnreadCount(): void {
    this.getUnreadCount().subscribe({
      next: (response) => {
     
          this.unreadCountSubject.next(response);
        
      },
      error: (error) => {
        console.error('Error fetching unread count:', error);
      }
    });
  }

  // Method to manually refresh unread count (e.g., after marking as read)
  public updateUnreadCount(): void {
    this.refreshUnreadCount();
  }
}
