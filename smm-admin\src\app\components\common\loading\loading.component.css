/* Loading spinner with primary color */
.loading-spinner {
  border-radius: 50%;
  border: 3px solid rgba(var(--primary-rgb, 48, 176, 199), 0.2);
  border-top-color: var(--primary, #30B0C7);
  animation: spin 1s ease-in-out infinite;
}

/* Size variants */
.loading-spinner.sm {
  width: 1.5rem;
  height: 1.5rem;
}

.loading-spinner.md {
  width: 2.5rem;
  height: 2.5rem;
}

.loading-spinner.lg {
  width: 4rem;
  height: 4rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
