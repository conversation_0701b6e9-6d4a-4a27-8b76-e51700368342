import { Injectable, ElementRef, Renderer2, RendererFactory2, ApplicationRef, ComponentFactoryResolver, Injector, EmbeddedViewRef } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

export interface DropdownPosition {
  top: number;
  left: number;
}

export interface DropdownOptions {
  width?: string;
  maxHeight?: string;
  minWidth?: string;
  placement?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  offset?: { x: number, y: number };
}

@Injectable({
  providedIn: 'root'
})
export class AdminDropdownService {
  private activeDropdownId = new BehaviorSubject<string | null>(null);
  private closeAllDropdowns$ = new Subject<void>();
  private excludeDropdownId$ = new BehaviorSubject<string | null>(null);
  private renderer: Renderer2;

  // Track all open dropdowns to manage them collectively
  private openDropdowns: Map<string, any> = new Map();

  // Observable that components can subscribe to
  public closeAllDropdowns = this.closeAllDropdowns$.asObservable();

  // Z-index counter to ensure newer dropdowns appear on top
  private zIndexCounter = 100000; // Very high starting z-index

  // Store dropdown elements by ID
  private dropdownElements: Map<string, HTMLElement> = new Map();

  // Variable to store the original position of the body
  private originalBodyPosition: string = '';
  private originalBodyTop: string = '';
  private originalBodyWidth: string = '';

  constructor(rendererFactory: RendererFactory2) {
    this.renderer = rendererFactory.createRenderer(null, null);

    // Add scroll event listener to update positions
    window.addEventListener('scroll', () => {
      // Only update positions if there are open dropdowns
      if (this.openDropdowns.size > 0) {
        this.updateAllDropdownPositions();
      }
    }, true);

    // Add resize event listener to update positions
    window.addEventListener('resize', () => {
      // Only update positions if there are open dropdowns
      if (this.openDropdowns.size > 0) {
        this.updateAllDropdownPositions();
      }
    });
  }

  getActiveDropdown() {
    return this.activeDropdownId.asObservable();
  }

  openDropdown(id: string, instance: any) {
    // If there's already an active dropdown and it's not the same instance,
    // close it before opening the new one
    if (this.openDropdowns.size > 0 && !this.openDropdowns.has(id)) {
      // Set the exclude ID to prevent the new dropdown from being closed
      this.excludeDropdownId$.next(id);

      // Close all other dropdowns
      this.closeAll();

      // Reset the exclude ID
      this.excludeDropdownId$.next(null);
    }

    // Set the new active dropdown
    this.activeDropdownId.next(id);
    this.openDropdowns.set(id, instance);
  }

  closeDropdown(id: string) {
    if (this.openDropdowns.has(id)) {
      this.openDropdowns.delete(id);
    }

    if (this.activeDropdownId.value === id) {
      this.activeDropdownId.next(null);
    }

    // Remove the dropdown element from the DOM if it exists
    if (this.dropdownElements.has(id)) {
      const dropdownElement = this.dropdownElements.get(id);
      if (dropdownElement && dropdownElement.parentElement === document.body) {
        document.body.removeChild(dropdownElement);
      }
      this.dropdownElements.delete(id);
    }
  }

  closeAll() {
    // Don't close the excluded dropdown
    const excludeId = this.excludeDropdownId$.value;

    // Create a copy of the keys to avoid modification during iteration
    const dropdownIds = Array.from(this.openDropdowns.keys());

    dropdownIds.forEach(id => {
      if (id !== excludeId) {
        const instance = this.openDropdowns.get(id);
        if (instance && typeof instance.close === 'function') {
          instance.close();
        } else {
          this.openDropdowns.delete(id);

          // Remove the dropdown element from the DOM if it exists
          if (this.dropdownElements.has(id)) {
            const dropdownElement = this.dropdownElements.get(id);
            if (dropdownElement && dropdownElement.parentElement === document.body) {
              document.body.removeChild(dropdownElement);
            }
            this.dropdownElements.delete(id);
          }
        }
      }
    });

    // Emit the close event for all subscribers
    this.closeAllDropdowns$.next();

    // Reset active dropdown if it's not the excluded one
    if (this.activeDropdownId.value !== excludeId) {
      this.activeDropdownId.next(null);
    }
  }

  calculatePosition(
    triggerElement: ElementRef | HTMLElement,
    dropdownElement: HTMLElement,
    options: DropdownOptions = {}
  ): DropdownPosition {
    const el = triggerElement instanceof ElementRef ? triggerElement.nativeElement : triggerElement;
    const rect = el.getBoundingClientRect();
    const dropdownRect = dropdownElement.getBoundingClientRect();

    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Default offset
    const offset = options.offset || { x: 0, y: 5 };

    // Default placement
    const placement = options.placement || 'bottom-left';

    let top = 0;
    let left = 0;

    // Calculate position based on placement
    switch (placement) {
      case 'bottom-left':
        top = rect.bottom + window.scrollY + offset.y;
        left = rect.left + window.scrollX + offset.x;
        break;
      case 'bottom-right':
        top = rect.bottom + window.scrollY + offset.y;
        left = rect.right + window.scrollX - dropdownRect.width + offset.x;
        break;
      case 'top-left':
        top = rect.top + window.scrollY - dropdownRect.height + offset.y;
        left = rect.left + window.scrollX + offset.x;
        break;
      case 'top-right':
        top = rect.top + window.scrollY - dropdownRect.height + offset.y;
        left = rect.right + window.scrollX - dropdownRect.width + offset.x;
        break;
    }

    // Adjust if dropdown would go off screen
    if (left + dropdownRect.width > viewportWidth) {
      left = viewportWidth - dropdownRect.width - 10;
    }

    if (left < 0) {
      left = 10;
    }

    // If dropdown would go off bottom of screen, position it above the trigger
    if (top + dropdownRect.height > viewportHeight + window.scrollY) {
      if (placement.startsWith('bottom')) {
        top = rect.top + window.scrollY - dropdownRect.height - offset.y;
      }
    }

    // If dropdown would go off top of screen, position it below the trigger
    if (top < window.scrollY) {
      if (placement.startsWith('top')) {
        top = rect.bottom + window.scrollY + offset.y;
      }
    }

    return { top, left };
  }

  updateDropdownPosition(
    id: string,
    triggerElement: ElementRef | HTMLElement,
    dropdownElement: HTMLElement,
    options: DropdownOptions = {}
  ) {
    if (!dropdownElement) return;

    // Store current scroll position
    const scrollX = window.scrollX || window.pageXOffset;
    const scrollY = window.scrollY || window.pageYOffset;

    // Store the dropdown element for future reference
    this.dropdownElements.set(id, dropdownElement);

    // Set position fixed immediately to prevent layout shifts
    this.renderer.setStyle(dropdownElement, 'position', 'fixed');

    // Ensure high z-index
    this.renderer.setStyle(dropdownElement, 'z-index', this.zIndexCounter.toString());
    this.zIndexCounter += 1;

    // Move the dropdown to the document body if it's not already there
    if (dropdownElement.parentElement !== document.body) {
      // Hide the dropdown before moving it to prevent flashing
      this.renderer.setStyle(dropdownElement, 'opacity', '0');
      document.body.appendChild(dropdownElement);

      // Restore scroll position after DOM manipulation
      window.scrollTo(scrollX, scrollY);
    }

    const el = triggerElement instanceof ElementRef ? triggerElement.nativeElement : triggerElement;
    const rect = el.getBoundingClientRect();

    // Apply width if specified
    if (options.width) {
      this.renderer.setStyle(dropdownElement, 'width', options.width);
    }

    // Apply min width if specified
    if (options.minWidth) {
      this.renderer.setStyle(dropdownElement, 'min-width', options.minWidth);
    }

    // Calculate the height based on the number of menu items
    this.calculateMenuHeight(dropdownElement);

    // Get viewport dimensions
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Get dropdown dimensions after applying styles
    const dropdownHeight = dropdownElement.offsetHeight;
    const dropdownWidth = dropdownElement.offsetWidth;

    // Calculate position based on placement
    const offset = options.offset || { x: 0, y: 5 };

    // Determine if there's enough space below the trigger
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;

    // Determine if the menu should appear above or below the trigger
    let placement = options.placement || 'bottom-right';

    // If there's not enough space below but enough space above, flip the placement
    if (spaceBelow < dropdownHeight && spaceAbove >= dropdownHeight) {
      if (placement === 'bottom-right') {
        placement = 'top-right';
      } else if (placement === 'bottom-left') {
        placement = 'top-left';
      }
    }

    // Calculate position based on the determined placement
    let top = 0;
    let left = 0;

    if (placement === 'bottom-right') {
      // Position to the right and below the trigger
      top = rect.bottom + offset.y;
      left = rect.right - dropdownWidth + offset.x;
    } else if (placement === 'bottom-left') {
      // Position to the left and below the trigger
      top = rect.bottom + offset.y;
      left = rect.left + offset.x;
    } else if (placement === 'top-right') {
      // Position to the right and above the trigger
      top = rect.top - dropdownHeight - offset.y;
      left = rect.right - dropdownWidth + offset.x;
    } else if (placement === 'top-left') {
      // Position to the left and above the trigger
      top = rect.top - dropdownHeight - offset.y;
      left = rect.left + offset.x;
    }

    // Adjust if dropdown would go off screen
    if (left + dropdownWidth > viewportWidth) {
      left = viewportWidth - dropdownWidth - 10; // 10px padding
    }

    if (left < 0) {
      left = 10; // 10px padding
    }

    // If dropdown would go off bottom of screen, position it above the trigger
    if (top + dropdownHeight > viewportHeight && placement.startsWith('bottom')) {
      top = rect.top - dropdownHeight - offset.y;
    }

    // If dropdown would go off top of screen, position it below the trigger
    if (top < 0 && placement.startsWith('top')) {
      top = rect.bottom + offset.y;
    }

    // Apply the calculated position
    this.renderer.setStyle(dropdownElement, 'top', `${top}px`);
    this.renderer.setStyle(dropdownElement, 'left', `${left}px`);

    // Make the dropdown visible again
    this.renderer.setStyle(dropdownElement, 'opacity', '1');
  }

  /**
   * Calculate the height of the menu based on the number of items
   * This eliminates the need for scrolling within the menu
   */
  private calculateMenuHeight(dropdownElement: HTMLElement) {
    // Remove any max-height constraint
    this.renderer.removeStyle(dropdownElement, 'max-height');
    this.renderer.removeStyle(dropdownElement, 'overflow-y');

    // First, let the menu render with its natural height to measure items correctly
    this.renderer.removeStyle(dropdownElement, 'height');

    // Count the number of menu items and dividers
    const menuItems = dropdownElement.querySelectorAll('.menu-item');
    const dividers = dropdownElement.querySelectorAll('.menu-divider');

    if (menuItems.length === 0) return;

    // Get the menu container (which contains all items)
    const menuContainer = dropdownElement.querySelector('.py-1') as HTMLElement;
    if (!menuContainer) return;

    // Get the actual height of the menu container with all its content
    const containerHeight = menuContainer.offsetHeight;

    // Add extra padding to ensure the last item is fully visible
    const extraPadding = 16; // Add 16px of extra padding at the bottom

    // Set the height of the dropdown to the container height plus extra padding
    this.renderer.setStyle(dropdownElement, 'height', `${containerHeight + extraPadding}px`);

    // Ensure the menu container takes up the full height
    this.renderer.setStyle(menuContainer, 'height', '100%');
  }

  private adjustDropdownPosition(dropdownElement: HTMLElement) {
    // Store current scroll position
    const scrollX = window.scrollX || window.pageXOffset;
    const scrollY = window.scrollY || window.pageYOffset;

    const rect = dropdownElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Adjust if dropdown extends beyond right edge of viewport
    if (rect.right > viewportWidth) {
      const overflowX = rect.right - viewportWidth;
      const newLeft = Math.max(0, rect.left - overflowX - 10); // 10px padding
      this.renderer.setStyle(dropdownElement, 'left', `${newLeft}px`);
    }

    // Adjust if dropdown extends beyond left edge of viewport
    if (rect.left < 0) {
      this.renderer.setStyle(dropdownElement, 'left', '10px'); // 10px padding
    }

    // Check if the dropdown is too tall for the viewport
    if (rect.height > viewportHeight) {
      // If the dropdown is taller than the viewport, resize it to fit
      const newHeight = viewportHeight - 20; // 10px padding top and bottom
      this.renderer.setStyle(dropdownElement, 'height', `${newHeight}px`);
      this.renderer.setStyle(dropdownElement, 'overflow-y', 'auto');

      // Recalculate position after resizing
      const newRect = dropdownElement.getBoundingClientRect();

      // Adjust top position to center the dropdown in the viewport
      if (newRect.top < 10) {
        this.renderer.setStyle(dropdownElement, 'top', '10px');
      } else if (newRect.bottom > viewportHeight - 10) {
        const newTop = viewportHeight - newRect.height - 10;
        this.renderer.setStyle(dropdownElement, 'top', `${newTop}px`);
      }
    } else {
      // Adjust if dropdown extends beyond bottom edge of viewport
      if (rect.bottom > viewportHeight) {
        // If there's not enough space below, try to position it above
        const triggerHeight = 40; // Approximate height of the trigger button
        const newTop = viewportHeight - rect.height - 10; // 10px padding

        // If positioning above would put it off the top of the screen, position at the top with padding
        if (newTop < 10) {
          this.renderer.setStyle(dropdownElement, 'top', '10px');
        } else {
          this.renderer.setStyle(dropdownElement, 'top', `${newTop}px`);
        }
      }

      // Adjust if dropdown extends beyond top edge of viewport
      if (rect.top < 0) {
        this.renderer.setStyle(dropdownElement, 'top', '10px'); // 10px padding
      }
    }

    // Restore scroll position after all adjustments
    window.scrollTo(scrollX, scrollY);
  }

  private updateAllDropdownPositions() {
    // Use requestAnimationFrame for better performance
    // This helps batch all DOM updates together
    requestAnimationFrame(() => {
      // Update all dropdown positions in a single frame
      this.openDropdowns.forEach((instance, id) => {
        if (instance && typeof instance.updatePosition === 'function') {
          instance.updatePosition();
        }
      });
    });
  }

  // Generate a unique ID for a dropdown
  generateDropdownId(prefix: string = 'admin-dropdown'): string {
    return `${prefix}-${Math.random().toString(36).substring(2, 9)}`;
  }
}
