.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.modal-body {
  @apply space-y-4;
}

.confirmation-text {
  @apply text-gray-700 mb-4;
}

.warning-box {
  @apply bg-red-50 border border-red-200 rounded-lg p-4 flex items-start mb-6;
}

.warning-icon {
  @apply text-red-500 mr-3 flex-shrink-0;
}

.warning-content {
  @apply text-sm text-gray-700;
}

.disable-form {
  @apply mt-6 space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)];
}

.form-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.input-error {
  @apply border-red-500;
}

.error-message {
  @apply text-red-500 text-xs;
}

.password-input-container {
  @apply relative;
}

.toggle-password {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700;
}

.form-actions {
  @apply flex justify-end mt-6 space-x-3;
}

.cancel-button {
  @apply bg-gray-100 text-gray-700 py-2 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium;
}

.disable-button {
  @apply bg-red-500 text-white py-2 px-6 rounded-lg hover:bg-red-600 transition-colors font-medium;
}

.disable-button:disabled {
  @apply opacity-70 cursor-not-allowed;
}

.loading-spinner-small {
  @apply inline-block w-4 h-4 border-2 border-white rounded-full;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
