# Spring @Transactional Self-Invocation Issue - Solution Guide

## 📋 Problem Description

In Spring Framework, **self-invocation** (a method within the target object calling another method of the target object) does not lead to an actual transaction at runtime. This is because Spring uses **proxy-based AOP** for transaction management.

### ❌ **What Doesn't Work:**

```java
@Service
public class MyService {
    
    @Transactional
    public void methodA() {
        // This call will NOT be transactional!
        methodB(); // Self-invocation - proxy is bypassed
    }
    
    @Transactional
    public void methodB() {
        // Transaction annotation is ignored
        // because it's called internally
    }
}
```

### 🔍 **Why This Happens:**

1. **Spring Proxy Mechanism**: Spring creates a proxy around your service
2. **External Calls Only**: Only external calls go through the proxy
3. **Internal Calls Bypass Proxy**: `this.methodB()` calls bypass the proxy entirely
4. **No Transaction Management**: @Transactional annotations are ignored for internal calls

## ✅ **Solutions Applied in Our Codebase**

### **Solution 1: Inline the Logic (Recommended)**

Instead of calling another @Transactional method, inline the logic directly:

#### **Before (Self-Invocation Problem):**
```java
@Override
@Transactional
public OrderRes updateOrderStatus(Long id, OrderStatus orderStatus) {
    // ... validation logic
    
    if (OrderStatus.PARTIAL.equals(orderStatus)) {
        return updateOrderToPartialWithRefund(order); // ❌ Self-invocation
    }
    
    // ... rest of logic
}

@Transactional // ❌ This won't work when called internally
public OrderRes updateOrderToPartialWithRefund(GOrder order) {
    // ... partial refund logic
}
```

#### **After (Fixed):**
```java
@Override
@Transactional
public OrderRes updateOrderStatus(Long id, OrderStatus orderStatus) {
    // ... validation logic
    
    if (OrderStatus.PARTIAL.equals(orderStatus)) {
        // ✅ Inline the logic directly
        log.info("Updating order to PARTIAL with refund: orderId={}, currentRemains={}", 
                order.getId(), order.getRemains());
        
        // Validate that order has remains to refund
        if (order.getRemains() <= 0) {
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }
        
        // Set order status to PARTIAL
        order.setStatus(OrderStatus.PARTIAL);
        GOrder savedOrder = orderRepository.save(order);
        
        // Process refund using BalanceService
        balanceService.processRefund(savedOrder);
        
        return orderMapper.toRes(savedOrder);
    }
    
    // ... rest of logic
}
```

### **Solution 2: Avoid Method Overloading with Self-Invocation**

#### **Before (Self-Invocation Problem):**
```java
@Override
@Transactional
public GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note) {
    return addBalance(user, amount, source, note, null); // ❌ Self-invocation
}

@Override
@Transactional
public GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note, GOrder order) {
    // ... actual logic
}
```

#### **After (Fixed):**
```java
@Override
@Transactional
public GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note) {
    // ✅ Inline the logic directly
    validateAmount(amount);
    
    log.info("Adding balance: userId={}, amount={}, source={}", user.getId(), amount, source);
    
    // Update user balance
    BigDecimal oldBalance = user.getBalance();
    BigDecimal newBalance = oldBalance.add(amount);
    user.setBalance(newBalance);
    
    // Save user first to get updated balance
    GUser savedUser = gUserRepository.save(user);
    
    // Create transaction record
    GTransaction transaction = createTransaction(
        savedUser.getId(), amount, newBalance, TransactionType.Bonus, source, note, null
    );
    
    transactionRepository.save(transaction);
    return savedUser;
}

@Override
@Transactional
public GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note, GOrder order) {
    // ✅ Separate implementation - no self-invocation
    validateAmount(amount);
    // ... complete logic here
}
```

## 🛠️ **Alternative Solutions (Not Used in Our Case)**

### **Solution A: Self-Injection**
```java
@Service
public class MyService {
    
    @Autowired
    private MyService self; // Inject proxy of itself
    
    @Transactional
    public void methodA() {
        self.methodB(); // ✅ Goes through proxy
    }
    
    @Transactional
    public void methodB() {
        // Transaction works correctly
    }
}
```

### **Solution B: ApplicationContext Lookup**
```java
@Service
public class MyService implements ApplicationContextAware {
    
    private ApplicationContext applicationContext;
    
    @Transactional
    public void methodA() {
        MyService proxy = applicationContext.getBean(MyService.class);
        proxy.methodB(); // ✅ Goes through proxy
    }
    
    @Transactional
    public void methodB() {
        // Transaction works correctly
    }
}
```

### **Solution C: @Async + CompletableFuture**
```java
@Service
public class MyService {
    
    @Transactional
    public void methodA() {
        CompletableFuture<Void> future = methodBAsync();
        future.join(); // Wait for completion
    }
    
    @Async
    @Transactional
    public CompletableFuture<Void> methodBAsync() {
        // Runs in separate thread with transaction
        return CompletableFuture.completedFuture(null);
    }
}
```

## 🎯 **Best Practices Applied**

### **1. Prefer Inlining for Simple Cases**
- When the called method is simple
- When it's only called from one place
- When the logic is closely related

### **2. Keep Transaction Boundaries Clear**
- Each @Transactional method should have a clear, single responsibility
- Avoid complex call chains within transactions
- Make transaction boundaries explicit and obvious

### **3. Use Service Composition for Complex Cases**
- Extract complex logic to separate services
- Use dependency injection instead of self-invocation
- Keep services focused and cohesive

## 📊 **Impact on Our Codebase**

### **Files Modified:**
1. **OrderServiceImpl.java**
   - Fixed `updateOrderStatus()` self-invocation
   - Inlined `updateOrderToPartialWithRefund()` logic

2. **BalanceServiceImpl.java**
   - Fixed `addBalance()` method overloading self-invocation
   - Fixed `deductBalance()` method overloading self-invocation
   - Fixed `transferBalance()` internal method calls
   - Fixed `processCanceledRefund()` self-invocation

### **Benefits Achieved:**
- ✅ **Proper Transaction Management**: All @Transactional annotations now work correctly
- ✅ **Data Consistency**: Database operations are properly wrapped in transactions
- ✅ **Error Handling**: Rollbacks work as expected when exceptions occur
- ✅ **Performance**: No unnecessary proxy overhead from self-invocations
- ✅ **Code Clarity**: Transaction boundaries are now explicit and clear

## ⚠️ **Detection and Prevention**

### **How to Detect Self-Invocation Issues:**
1. **Code Review**: Look for `this.methodName()` calls to @Transactional methods
2. **Testing**: Test rollback scenarios - they won't work with self-invocation
3. **Logging**: Enable transaction logging to see which methods are actually transactional
4. **Static Analysis**: Use tools like SonarQube to detect potential issues

### **Prevention Strategies:**
1. **Team Training**: Educate developers about Spring proxy limitations
2. **Code Standards**: Establish coding standards that discourage self-invocation
3. **Architecture Review**: Design services to minimize need for internal @Transactional calls
4. **Testing Strategy**: Always test transaction rollback scenarios

This solution ensures that all transaction management works correctly and provides a solid foundation for reliable data consistency in our application.
