import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize, tap } from 'rxjs';
import { ConfigService } from './config.service';
import { LoadingService } from './loading.service';
import { DashboardStatsRes } from '../../model/response/dashboard-stats.model';
import { TopServiceRes } from '../../model/response/top-services.model';
import { LatestActivityRes } from '../../model/response/latest-activity.model';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _dashboardStats$ = new BehaviorSubject<DashboardStatsRes | null>(null);
  private _topServices$ = new BehaviorSubject<TopServiceRes[]>([]);
  private _latestActivities$ = new BehaviorSubject<LatestActivityRes[]>([]);

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private loadingService: LoadingService
  ) {}

  // Observable getters
  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  get dashboardStats$(): Observable<DashboardStatsRes | null> {
    return this._dashboardStats$.asObservable();
  }

  get topServices$(): Observable<TopServiceRes[]> {
    return this._topServices$.asObservable();
  }

  get latestActivities$(): Observable<LatestActivityRes[]> {
    return this._latestActivities$.asObservable();
  }

  // Get dashboard statistics
  getDashboardStats(): Observable<DashboardStatsRes> {
    this._loading$.next(true);
    this.loadingService.show('Loading dashboard stats...');
    return this.http
      .get<DashboardStatsRes>(`${this.configService.apiUrl}/dashboard/stats`)
      .pipe(
        tap(stats => {
          this._dashboardStats$.next(stats);
        }),
        finalize(() => {
          this._loading$.next(false);
          this.loadingService.hide();
        })
      );
  }

  // Get top services
  getTopServices(): Observable<TopServiceRes[]> {
    this._loading$.next(true);
    this.loadingService.show('Loading top services...');
    return this.http
      .get<TopServiceRes[]>(`${this.configService.apiUrl}/dashboard/services/top`)
      .pipe(
        tap(services => {
          this._topServices$.next(services);
        }),
        finalize(() => {
          this._loading$.next(false);
          this.loadingService.hide();
        })
      );
  }

  // Get latest activities
  getLatestActivities(): Observable<LatestActivityRes[]> {
    this._loading$.next(true);
    this.loadingService.show('Loading latest activities...');
    return this.http
      .get<LatestActivityRes[]>(`${this.configService.apiUrl}/dashboard/activities/latest`)
      .pipe(
        tap(activities => {
          this._latestActivities$.next(activities);
        }),
        finalize(() => {
          this._loading$.next(false);
          this.loadingService.hide();
        })
      );
  }
}
