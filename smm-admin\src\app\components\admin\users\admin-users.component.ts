import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { ClickOutsideDirective } from '../../../shared/directives/click-outside.directive';
import { AdminMenuService, MenuAction } from '../../../core/services/admin-menu.service';
import { UserAdminService } from '../../../core/services/user-admin.service';
import { AccessAdminService } from '../../../core/services/access-admin.service';
import { TenantService } from '../../../core/services/tenant.service';
import { GUserSuperRes, CommonStatus } from '../../../model/response/g-user-super-res.model';
import { UserSearchReq } from '../../../model/request/user-search-req.model';
import { CreateTokenForUserReq } from '../../../model/request/create-token-for-user-req.model';
import { Subscription, forkJoin } from 'rxjs';
import { LoadingService } from '../../../core/services/loading.service';
import { ManageBalanceComponent } from './manage-balance/manage-balance.component';
import { EditUserComponent } from './edit-user/edit-user.component';
import { BanAccountComponent } from './ban-account/ban-account.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { SpecialPricesUserComponent } from '../../popup/special-prices-user/special-prices-user.component';
import { UserReferralsComponent } from '../../popup/user-referrals/user-referrals.component';
import { PaymentHistoryComponent } from './payment-history/payment-history.component';
import { ActivatedRoute } from '@angular/router';
import { AdminMenuComponent } from '../../common/admin-menu/admin-menu.component';
import { AdminDropdownDirective } from '../../../shared/directives/admin-dropdown.directive';
import { ToastService } from '../../../core/services/toast.service';
import { CurrencyService } from '../../../core/services/currency.service';

@Component({
  selector: 'app-admin-users',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    ClickOutsideDirective,
    ManageBalanceComponent,
    EditUserComponent,
    BanAccountComponent,
    ResetPasswordComponent,
    SpecialPricesUserComponent,
    UserReferralsComponent,
    PaymentHistoryComponent,
    AdminMenuComponent,
    AdminDropdownDirective
  ],
  templateUrl: './admin-users.component.html',
  styleUrls: ['./admin-users.component.css', '../common/admin-menu.css', '../common/admin-dropdown.css']
})
export class AdminUsersComponent implements OnInit, OnDestroy {
  users: GUserSuperRes[] = [];
  statusFilters = [
    { value: 'all', label: 'All', active: true },
    { value: CommonStatus.ACTIVATED, label: 'Active', active: false },
    { value: CommonStatus.DEACTIVATED, label: 'Deactivated', active: false }
  ];
  searchTerm: string = '';
  selectedStatus: string = 'all';
  selectAll: boolean = false;
  selectedUsers: number[] = [];
  activeActionMenu: number | null = null;
  menuPosition = { top: 0, left: 0 };
  menuActions: MenuAction[] = [];

  // Bulk action menu
  showBulkActionMenu: boolean = false;
  bulkMenuPosition = { top: 0, left: 0 };
  bulkMenuActions: MenuAction[] = [
    { id: 'ban-selected', label: 'Ban account', icon: 'ban', iconColor: 'text-red-500' },
    { id: 'unban-selected', label: 'Unban account', icon: 'check-circle', iconColor: 'text-green-500' },
    { id: 'delete-custom-prices', label: 'Delete custom prices', icon: 'trash', iconColor: 'text-red-500' }
  ];

  // Balance management popup
  showBalancePopup: boolean = false;
  selectedUserId: number = 0;
  selectedUserName: string = '';
  selectedUserBalance: number = 0;

  // Edit user popup
  showEditUserPopup: boolean = false;

  // Ban account popup
  showBanAccountPopup: boolean = false;

  // Reset password popup
  showResetPasswordPopup: boolean = false;

  // Special prices popup
  showSpecialPricesUserPopup: boolean = false;

  // User referrals popup
  showUserReferralsPopup: boolean = false;

  // Payment history popup
  showPaymentHistoryPopup: boolean = false;

  // Selected user data
  selectedUser: GUserSuperRes | null = null;
  selectedUserStatus: CommonStatus = CommonStatus.ACTIVATED;

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };

  isLoading = false;
  private subscriptions: Subscription[] = [];

  constructor(
    private adminMenuService: AdminMenuService,
    private userAdminService: UserAdminService,
    private accessAdminService: AccessAdminService,
    private tenantService: TenantService,
    private toastService: ToastService,
    private route: ActivatedRoute,
    private currencyService: CurrencyService
  ) {}

  ngOnInit() {
    // Get menu actions from service
    this.menuActions = this.adminMenuService.getUserMenuActions();

    // Load accessible tenants for login-as-user functionality
    this.tenantService.getAccessibleTenants().subscribe({
      next: (tenants) => {
        console.log('Accessible tenants loaded:', tenants);
      },
      error: (error) => {
        console.error('Error loading accessible tenants:', error);
      }
    });

    // Subscribe to users data
    this.subscriptions.push(
      this.userAdminService.users$.subscribe(users =>
        this.users = users

      )
    );

    // Subscribe to pagination data
    this.subscriptions.push(
      this.userAdminService.pagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.userAdminService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Check for search parameter in URL
    this.route.queryParams.subscribe(params => {
      if (params['search']) {
        this.searchTerm = params['search'];
        this.onSearch(); // Trigger search with the parameter
      } else {
        // Load initial data if no search parameter
        this.loadUsers();
      }
    });

    // Global click event listener no longer needed - handled by AdminMenuComponent
  }

  // Get filtered menu actions based on user status
  getMenuActionsForUser(user: GUserSuperRes): MenuAction[] {
    const baseActions = this.adminMenuService.getUserMenuActions();

    // Find the ban account action
    const banActionIndex = baseActions.findIndex(action => action.id === 'ban-account');

    if (banActionIndex !== -1) {
      // Create a copy of the actions array
      const actions = [...baseActions];

      // Replace the ban action based on user status
      if (user.status === CommonStatus.ACTIVATED) {
        actions[banActionIndex] = {
          id: 'ban-account',
          label: 'Ban account',
          icon: 'ban',
          iconColor: 'text-red-500'
        };
      } else {
        actions[banActionIndex] = {
          id: 'ban-account',
          label: 'Unban account',
          icon: 'check-circle',
          iconColor: 'text-green-500'
        };
      }

      return actions;
    }

    return baseActions;
  }

  loadUsers(page: number = 0): void {
    const filter: UserSearchReq = {};

    if (this.searchTerm) {
      filter.keyword = this.searchTerm;
    }

    if (this.selectedStatus !== 'all') {
      filter.status = this.selectedStatus;
    }

    this.userAdminService.loadUsers(filter, page, this.pagination.pageSize);
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Document click handler removed as it's now handled by the AdminMenuComponent

  toggleFilter(filter: any) {
    this.statusFilters.forEach(f => {
      f.active = f === filter;
    });
    this.selectedStatus = filter.value;
    this.loadUsers();
  }

  resetSearch() {
    this.searchTerm = '';
    this.selectedStatus = 'all';
    this.statusFilters.forEach(f => {
      f.active = f.value === 'all';
    });
    this.loadUsers();
  }

  toggleAllUsers() {
    if (this.selectAll) {
      this.selectedUsers = this.users.map(user => user.id);
    } else {
      this.selectedUsers = [];
    }
  }

  toggleUserSelection(userId: number) {
    const index = this.selectedUsers.indexOf(userId);
    if (index === -1) {
      this.selectedUsers.push(userId);
    } else {
      this.selectedUsers.splice(index, 1);
    }

    // Update selectAll checkbox state
    this.selectAll = this.selectedUsers.length === this.users.length;
  }

  isUserSelected(userId: number): boolean {
    return this.selectedUsers.includes(userId);
  }

  onSearch(): void {
    this.loadUsers();
  }

  // Menu handling methods removed as they're now handled by the AdminMenuComponent

  // Handle menu action click
  handleMenuAction(actionId: string, userId: number): void {
    console.log(`Action ${actionId} for user:`, userId);

    // Get the user to determine the current status for ban/unban action
    const user = this.users.find(u => u.id === userId);
    if (!user) {
      return;
    }

    switch (actionId) {
      case 'manage-balance':
        this.manageBalance(userId);
        break;
      case 'edit-user':
        this.editUser(userId);
        break;
      case 'user-orders':
        this.viewUserOrders(userId);
        break;
      case 'payments-history':
        this.viewPaymentsHistory(userId);
        break;
      case 'referral-system':
        this.viewReferralSystem(userId);
        break;
      case 'discount-prices':
        this.manageDiscountPrices(userId);
        break;
      case 'reset-password':
        this.resetPassword(userId);
        break;
      case 'login-as-user':
        this.loginAsUser(userId);
        break;
      case 'ban-account':
        // If user is already deactivated, unban directly without popup
        if (user.status === CommonStatus.DEACTIVATED) {
          this.unbanUser(userId);
        } else {
          // Otherwise show the ban popup
          this.banUser(userId);
        }
        break;
    }
  }

  // Action methods
  private manageBalance(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUserName = user.user_name;
      this.selectedUserBalance = user.balance;
      this.showBalancePopup = true;
    }
  }

  closeBalancePopup(): void {
    this.showBalancePopup = false;
  }

  updateUserBalance(newBalance: number): void {
    // Update the user's balance in the local users array
    const userIndex = this.users.findIndex(u => u.id === this.selectedUserId);
    if (userIndex !== -1) {
      this.users[userIndex].balance = newBalance;
      this.selectedUserBalance = newBalance;
    }
  }

  private editUser(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUser = user;
      this.showEditUserPopup = true;
    }
  }

  closeEditUserPopup(): void {
    this.showEditUserPopup = false;
  }

  updateUser(updatedUser: GUserSuperRes): void {
    // Update the user in the local users array
    const userIndex = this.users.findIndex(u => u.id === updatedUser.id);
    if (userIndex !== -1) {
      this.users[userIndex] = updatedUser;
    }
  }

  private viewUserOrders(userId: number): void {
    console.log('View orders for user:', userId);
    // Navigate to admin orders page with userId parameter
    window.location.href = '/panel/orders?userId=' + userId;
  }

  private viewPaymentsHistory(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUserName = user.user_name;
      this.showPaymentHistoryPopup = true;
    }
  }

  closePaymentHistoryPopup(): void {
    this.showPaymentHistoryPopup = false;
  }

  private viewReferralSystem(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUserName = user.user_name;
      this.selectedUser = user; // Set the selectedUser property to pass custom_referral_rate
      this.showUserReferralsPopup = true;
    }
  }

  closeUserReferralsPopup(): void {
    this.showUserReferralsPopup = false;
  }

  private manageDiscountPrices(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUser = user;
      this.selectedUserName = user.user_name;
      this.showSpecialPricesUserPopup = true;
    }
  }

  closeSpecialPricesUserPopup(): void {
    this.showSpecialPricesUserPopup = false;
  }

  onSpecialPriceAdded(specialPrices: any[]): void {
    console.log('Special prices added:', specialPrices);

    // Refresh the user list to get the latest data with updated special_price_count
    this.loadUsers(this.pagination.pageNumber);
  }

  private resetPassword(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUserName = user.user_name;
      this.showResetPasswordPopup = true;
    }
  }

  closeResetPasswordPopup(): void {
    this.showResetPasswordPopup = false;
  }

  private loginAsUser(userId: number): void {
    console.log('Login as user:', userId);

    // Get current tenant ID
    const currentTenantId = this.tenantService.currentTenantValue;
    if (!currentTenantId) {
      this.toastService.showError('No tenant information available');
      return;
    }

    // Find current tenant info from accessible tenants
    let accessibleTenants = this.tenantService.accessibleTenantsValue;
    let currentTenant = accessibleTenants.find(tenant => tenant.id === currentTenantId);

    if (!currentTenant || accessibleTenants.length === 0) {
      // If tenant info not found, try to load it first
      console.log('Loading tenant information...');
      this.tenantService.getAccessibleTenants().subscribe({
        next: (tenants) => {
          const tenant = tenants.find(t => t.id === currentTenantId);
          if (tenant) {
            this.performLoginAsUser(userId, tenant);
          } else {
            this.toastService.showError('Current tenant information not found');
          }
        },
        error: (error) => {
          console.error('Error loading tenant information:', error);
          this.toastService.showError('Failed to load tenant information');
        }
      });
      return;
    }

    this.performLoginAsUser(userId, currentTenant);
  }

  private performLoginAsUser(userId: number, currentTenant: any): void {
    // Prepare request
    const request: CreateTokenForUserReq = {
      user_id: userId
    };

    // Show loading state
    this.isLoading = true;

    // Call API to create token for user
    this.accessAdminService.createTokenForUser(request).subscribe({
      next: (tokenResponse) => {
        console.log('Token created successfully for user:', userId, tokenResponse);

        // Construct the URL with query parameters
        const targetUrl = this.buildTargetUrl(currentTenant.domain, tokenResponse);

        // Open in new tab
        window.open(targetUrl, '_blank');

        this.toastService.showSuccess(`Successfully logged in as user ${userId}`);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error creating token for user:', error);

        // Handle specific error messages
        let errorMessage = 'Failed to login as user';
        errorMessage = error?.message;

        this.toastService.showError(errorMessage);
        this.isLoading = false;
      }
    });
  }

  /**
   * Build target URL with token parameters
   */
  private buildTargetUrl(domain: string, tokenResponse: any): string {
    // Ensure domain has protocol

    let baseUrl = domain;
    if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
      baseUrl = `https://${baseUrl}`;
    }

    // Build query parameters
    const params = new URLSearchParams({
      accessToken: tokenResponse.access_token,
      clientId: tokenResponse.client_id,
      refreshToken: tokenResponse.refresh_token
    });

    return `${baseUrl}?${params.toString()}`;
  }

  private banUser(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (user) {
      this.selectedUserId = userId;
      this.selectedUserName = user.user_name;
      this.selectedUser = user;
      this.selectedUserStatus = user.status;
      this.showBanAccountPopup = true;
    }
  }

  private unbanUser(userId: number): void {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    // Call the API to activate the user (loading is handled by the service)
    this.userAdminService.activateUser(userId).subscribe({
      next: (response) => {
        console.log('User activated successfully:', response);

        // Update the user status in the local array
        this.updateUserStatus({
          userIds: [userId],
          status: CommonStatus.ACTIVATED
        });
      },
      error: (error) => {
        console.error('Error activating user:', error);
        // You could add a toast notification here for error feedback
      }
    });
  }

  closeBanAccountPopup(): void {
    this.showBanAccountPopup = false;
  }

  updateUserStatus(data: {userIds: number[], status: CommonStatus, reason?: string}): void {
    console.log('User status updated:', data);

    // Handle both single and multiple user updates
    data.userIds.forEach(userId => {
      // Update the user in the local users array
      const userIndex = this.users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        this.users[userIndex].status = data.status;

        // Update the selected user status if it's the currently selected user
        if (this.selectedUserId === userId) {
          this.selectedUserStatus = data.status;
        }
      }
    });

    // Clear selected users after bulk action
    this.selectedUsers = [];
    this.selectAll = false;

    // Refresh the user list to get the latest data
    this.loadUsers(this.pagination.pageNumber);
  }

  // Handle bulk action menu clicks
  handleBulkAction(actionId: string): void {
    console.log(`Bulk action ${actionId} for users:`, this.selectedUsers);

    switch (actionId) {
      case 'ban-selected':
        this.banSelectedUsers();
        break;
      case 'unban-selected':
        this.unbanSelectedUsers();
        break;
      case 'delete-custom-prices':
        this.deleteCustomPrices();
        break;
    }

    // Reset bulk action menu state
    this.showBulkActionMenu = false;
  }

  // Ban selected users
  private banSelectedUsers(): void {
    if (this.selectedUsers.length === 0) return;

    // Show the ban account popup with the selected users
    this.selectedUserId = 0; // Not used for bulk actions
    this.selectedUserName = ''; // Not used for bulk actions
    this.selectedUserStatus = CommonStatus.ACTIVATED; // Default to activated for bulk actions
    this.selectedUser = null; // Not used for bulk actions
    this.showBanAccountPopup = true;
  }

  // Unban selected users
  private unbanSelectedUsers(): void {
    if (this.selectedUsers.length === 0) return;

    // Show loading indicator
    this.isLoading = true;

    // Get selected users that are currently deactivated
    const usersToActivate = this.users
      .filter(user => this.selectedUsers.includes(user.id) && user.status === CommonStatus.DEACTIVATED)
      .map(user => user.id);

    if (usersToActivate.length === 0) {
      this.isLoading = false;
      return;
    }

    // Create an array of observables for each user to activate
    const requests = usersToActivate.map(userId =>
      this.userAdminService.activateUser(userId)
    );

    // Use forkJoin to execute all requests in parallel
    forkJoin(requests).subscribe({
      next: (responses) => {
        console.log('Users activated successfully:', responses);

        // Update the user status in the local array
        this.updateUserStatus({
          userIds: usersToActivate,
          status: CommonStatus.ACTIVATED
        });

        this.isLoading = false;
        this.showBulkActionMenu = false;
      },
      error: (error) => {
        console.error('Error activating users:', error);
        this.isLoading = false;
        this.showBulkActionMenu = false;
      }
    });
  }

  // Delete custom prices for selected users
  private deleteCustomPrices(): void {
    if (this.selectedUsers.length === 0) return;

    // Show loading indicator
    this.isLoading = true;

    // Create an array of observables for each user to delete custom prices for
    const requests = this.selectedUsers.map(userId =>
      this.userAdminService.deleteCustomDiscountByUser(userId)
    );

    // Use forkJoin to execute all requests in parallel
    forkJoin(requests).subscribe({
      next: (responses) => {
        console.log('Custom prices deleted successfully:', responses);

        // Refresh the user list to get the latest data
        this.loadUsers(this.pagination.pageNumber);

        // Clear selected users
        this.selectedUsers = [];
        this.selectAll = false;

        this.isLoading = false;
        this.showBulkActionMenu = false;
      },
      error: (error) => {
        console.error('Error deleting custom prices:', error);
        this.isLoading = false;
        this.showBulkActionMenu = false;
      }
    });
  }

  /**
   * Format balance using the common currency service
   */
  formatBalance(balance: number | undefined): string {
    return '$' +this.currencyService.formatBalance(balance);
  }
}
