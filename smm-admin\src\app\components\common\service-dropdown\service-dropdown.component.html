<div class="relative inline-block text-left w-full" [ngClass]="customClassDropdown" >

  <button class="dropdown-container rounded-xl " (click)="toggleDropdown($event)" [ngClass]="customClassButton">
    <app-service-label [lite]="lite" [service]="selectedOption"></app-service-label>
    <fa-icon class="absolute right-5 top-1/2 transform -translate-y-1/2 w-5 h-5"
      [icon]='["fas", "angle-down"]'></fa-icon>
  </button>


  <div *ngIf="isOpen"
    class="service-dropdown-menu-container fixed w-full bg-white border border-gray-200 rounded-lg shadow-lg z-[99999] max-h-96 overflow-y-auto"
    (click)="$event.stopPropagation()">
    <ul>
      <li *ngFor="let option of options" (click)="selectOption(option, $event)"
        class="px-4 py-2 cursor-pointer hover:bg-gray-100">

        <div class="p-2">
          <app-service-label [lite]="lite" [service]="option"></app-service-label>
        </div>
      </li>

    </ul>
  </div>

</div>

