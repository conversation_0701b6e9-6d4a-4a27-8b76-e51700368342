-- Add available_currencies column to tenant table
ALTER TABLE tenant ADD COLUMN IF NOT EXISTS available_currencies VARCHAR(200) DEFAULT 'USD';

-- Update existing tenants to have USD as default currency
UPDATE tenant SET available_currencies = 'USD' WHERE available_currencies IS NULL;

-- Insert some sample currencies if they don't exist
INSERT INTO currency (code, name, symbol, exchange_rate, base_currency) VALUES
('USD', 'US Dollar', '$', 1.00, true),
('EUR', 'Euro', '€', 0.85, false),
('GBP', 'British Pound', '£', 0.73, false),
('JPY', 'Japanese Yen', '¥', 110.00, false),
('CAD', 'Canadian Dollar', 'C$', 1.25, false),
('AUD', 'Australian Dollar', 'A$', 1.35, false),
('CHF', 'Swiss Franc', 'CHF', 0.92, false),
('CNY', 'Chinese Yuan', '¥', 6.45, false),
('SEK', 'Swedish Krona', 'kr', 8.60, false),
('NZD', 'New Zealand Dollar', 'NZ$', 1.42, false),
('MXN', 'Mexican Peso', '$', 20.50, false),
('SGD', 'Singapore Dollar', 'S$', 1.35, false),
('HKD', 'Hong Kong Dollar', 'HK$', 7.80, false),
('NOK', 'Norwegian Krone', 'kr', 8.50, false),
('VND', 'Vietnamese Dong', '₫', 23000.00, false)
ON CONFLICT (code) DO NOTHING;
