import { Injectable } from '@angular/core';
import { AdminServiceService } from './admin-service.service';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';

import { IconName } from '@fortawesome/fontawesome-svg-core';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root'
})
export class ServiceManagementService {
  constructor(private adminService: AdminServiceService, private toastService: ToastService) {}

  /**
   * Converts a service to GService format for compatibility with existing components
   * @param service The service to convert
   * @param icon The icon to use
   * @returns The converted GService
   */
  

  /**
   * Checks if a service is enabled
   * @param service The service to check
   * @returns True if the service is enabled, false otherwise
   */
  isServiceEnabled(service: SuperGeneralSvRes): boolean {
    return service.status === 'ACTIVATED';
  }

  /**
   * Toggles the status of a service
   * @param service The service to toggle
   * @param isChecked Whether the service should be enabled
   * @param callback Callback function to execute after completion
   */
  toggleServiceStatus(service: SuperGeneralSvRes, isChecked: boolean, callback?: () => void): void {
    const newStatus = isChecked ? 'ACTIVATED' : 'DEACTIVATED';
    console.log(`Toggle service status to ${newStatus}:`, service);

    if (isChecked) {
      // Activate the service
      this.adminService.activateService(service.id).subscribe({
        next: (updatedService) => {
          console.log('Service activated successfully:', updatedService);
          if (callback) callback();
        },
        error: (error) => {
          console.error('Error activating service:', error);
          // Revert the UI state in case of error
          service.status = 'DEACTIVATED';
        }
      });
    } else {
      // Deactivate the service
      this.adminService.deactivateService(service.id).subscribe({
        next: (updatedService) => {
          console.log('Service deactivated successfully:', updatedService);
          if (callback) callback();
        },
        error: (error) => {
          console.error('Error deactivating service:', error);
          // Revert the UI state in case of error
          service.status = 'ACTIVATED';
        }
      });
    }
  }

  /**
   * Duplicates a service
   * @param service The service to duplicate
   * @param callback Callback function to execute after completion
   */
  duplicateService(service: SuperGeneralSvRes, callback?: () => void): void {
    this.adminService.duplicateService(service.id).subscribe({
      next: (duplicatedService) => {
        console.log('Service duplicated successfully:', duplicatedService);
        if (callback) callback();
      },
      error: (error) => {
        console.error('Error duplicating service:', error);
      }
    });
  }

  /**
   * Disables all selected services
   * @param selectedServices The selected service IDs
   * @param callback Callback function to execute after completion
   */
  disableAllSelected(selectedServices: Set<number>, callback?: (completedCount: number, errorCount: number) => void): void {
    const selectedServiceIds = Array.from(selectedServices);
    if (selectedServiceIds.length === 0) {
      console.log('No services selected');
      if (callback) callback(0, 0);
      return;
    }

    // Track completed operations
    let completedCount = 0;
    let errorCount = 0;

    // Process each service
    selectedServiceIds.forEach(serviceId => {
      this.adminService.deactivateService(serviceId).subscribe({
        next: () => {
          completedCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === selectedServiceIds.length) {
            console.log(`Deactivated ${completedCount} services, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        },
        error: (error) => {
          console.error('Error deactivating service:', error);
          errorCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === selectedServiceIds.length) {
            console.log(`Deactivated ${completedCount} services, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        }
      });
    });
  }

  /**
   * Enables all selected services
   * @param selectedServices The selected service IDs
   * @param callback Callback function to execute after completion
   */
  enableAllSelected(selectedServices: Set<number>, callback?: (completedCount: number, errorCount: number) => void): void {
    const selectedServiceIds = Array.from(selectedServices);
    if (selectedServiceIds.length === 0) {
      console.log('No services selected');
      if (callback) callback(0, 0);
      return;
    }

    // Track completed operations
    let completedCount = 0;
    let errorCount = 0;

    // Process each service
    selectedServiceIds.forEach(serviceId => {
      this.adminService.activateService(serviceId).subscribe({
        next: () => {
          completedCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === selectedServiceIds.length) {
            console.log(`Activated ${completedCount} services, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        },
        error: (error) => {
          console.error('Error activating service:', error);
          errorCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === selectedServiceIds.length) {
            console.log(`Activated ${completedCount} services, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        }
      });
    });
  }

  /**
   * Extracts domain name from URL
   * @param url The URL to extract from
   * @returns The extracted domain name
   */
  extractDomainName(url: string): string {
    if (!url) return '';

    // Remove protocol (http://, https://)
    let domain = url.replace(/^https?:\/\//, '');

    // Get only the domain part (before any path)
    domain = domain.split('/')[0];

    // Remove www. if present
    domain = domain.replace(/^www\./, '');

    return domain;
  }

  /**
   * Copies text to clipboard
   * @param text The text to copy
   * @param element The element that triggered the copy
   */
  copyToClipboard(text: string, element: HTMLElement): void {
    navigator.clipboard.writeText(text)
      .then(() => {
        // Show a temporary success indicator
        const originalColor = element.style.color;

        // Change color to indicate success
        element.style.color = '#10b981'; // Green color

        // Reset after a short delay
        setTimeout(() => {
          element.style.color = originalColor;
        }, 1000);
        this.toastService.showSuccess('Copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy text: ', err);
        this.toastService.showError('Failed to copy text');
      });
  }
}
