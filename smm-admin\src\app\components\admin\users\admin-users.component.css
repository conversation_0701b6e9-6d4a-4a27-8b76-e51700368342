.layout-container {
  @apply max-w-full mx-auto;
}

/* Status badge styling */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

/* Table styling */
table {
  @apply w-full text-sm text-left text-gray-300;
}

thead {
  @apply text-xs text-gray-400 ;
}

th, td {
  @apply px-6 py-3;
}

tbody tr {
  @apply border-b border-gray-800;
}

/* Filter tabs */
.filter-tab {
  @apply inline-block p-4 border-transparent rounded-t-lg;
}

.filter-tab.active {
  @apply text-[var(--primary)] border-b-2 border-[var(--primary)];
}

.filter-tab:not(.active) {
  @apply text-gray-400 hover:border-b-2 hover:text-gray-200 hover:border-gray-700;
}

/* Search box */
.search-box {
  @apply relative w-full;
}

/* Action menu dropdown */
.dropdown-menu {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dropdown-menu a {
  color: #374151;
  transition: background-color 0.2s;
}

.dropdown-menu a:hover {
  background-color: #f3f4f6;
}

.dropdown-menu .py-1.divide-y.divide-gray-100 a {
  border-bottom: 1px solid #e5e7eb;
}

.dropdown-menu .py-1.divide-y.divide-gray-100 a:last-child {
  border-bottom: none;
}

/* Mobile card view styling */
.mobile-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.mobile-card-header {
  @apply flex justify-between items-center p-4 border-b border-gray-100;
}

.mobile-card-body {
  @apply p-4 space-y-3;
}

.mobile-card-footer {
  @apply px-4 py-3 bg-gray-50 flex justify-end;
}

.mobile-card-item {
  @apply flex items-center;
}

.mobile-card-icon {
  @apply text-gray-400 w-5;
}

.mobile-card-text {
  @apply ml-2 text-sm text-gray-700;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layout-container {
    @apply px-4;
  }

  /* Adjust pagination for mobile */
  .pagination-container {
    @apply flex-col space-y-4;
  }

  .pagination-container > div {
    @apply w-full;
  }

  /* Ensure action menu appears in the right position on mobile */
  .dropdown-menu {
    max-width: 90vw;
    right: 5vw;
    left: auto !important;
  }
}
