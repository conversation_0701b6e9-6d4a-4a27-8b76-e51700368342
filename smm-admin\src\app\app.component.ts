import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule, isPlatformBrowser, Location } from '@angular/common';
import { LanguageService } from './core/services/language.service';
import { TranslateModule } from '@ngx-translate/core';
import { AuthUtilsService } from './core/services/auth-utils.service';
import { LoadingService } from './core/services/loading.service';
import { filter, take } from 'rxjs/operators';
import { LoadingComponent } from './components/common/loading/loading.component';
import { GlobalLoadingComponent } from './components/common/global-loading/global-loading.component';
import { DesignSettingsService } from './core/services/design-settings.service';
// import { AppAssetsService } from './core/services/app-assets.service';


@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterModule,
    CommonModule,
    TranslateModule,
   // LoadingComponent,
    GlobalLoadingComponent
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent implements OnInit {
  isTranslationsLoaded = false;
  isInitialized = false;
  isAuthChecked = false;
  isBrowser: boolean;

  constructor(
   // public languageService: LanguageService,
    private authUtils: AuthUtilsService,
    private location: Location,
  //  private appAssetsService: AppAssetsService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngOnInit() {
    // Subscribe to the translations loaded state
    // this.languageService.translationsLoaded$.subscribe(loaded => {
    //   this.isTranslationsLoaded = loaded;
    // });

    // Check auth status on initialization
    if (this.isBrowser) {
      this.checkAuthStatus();

      // Initialize theme - this will apply the saved primary color if any
      //this.initializeTheme();
    }
  }

  /**
   * Initialize theme settings
   */
  private initializeTheme(): void {
    if (!this.isBrowser) return;

    // Apply the current settings to the interface
   // this.themeService.updateInterfaceStyles();

    // Then, fetch from API to verify if settings match
    // this.themeService.fetchAndApplyThemeFromAPI().subscribe({
    //   next: (settings) => {
    //     console.log('Theme settings fetched from API:', settings);

    //     // Initialize app assets (logo and favicon)
    //     this.appAssetsService.loadDesignSettings();
    //   },
    //   error: (error) => {
    //     console.error('Error fetching theme settings from API:', error);
    //   }
    // });
  }

  private checkAuthStatus() {
    this.isAuthChecked = true;
    console.log('App Component - Checking auth status');

    try {
      // Get the current URL
      const currentUrl = this.location.path();
      console.log('App Component - Current URL:', currentUrl);

      // Check if user is authenticated and token is valid
      const isAuthenticated = this.authUtils.isAuthenticated();
      const isTokenValid = !this.authUtils.isTokenExpired();
      console.log('App Component - Is authenticated:', isAuthenticated);
      console.log('App Component - Is token valid:', isTokenValid);

      // If on auth pages and already authenticated, redirect
      if (currentUrl.includes('/auth/')) {
        console.log('App Component - On auth page, checking authentication');

        if (isAuthenticated && isTokenValid) {
          console.log('App Component - User is authenticated on auth page, redirecting');
          const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/panel/dashboard';
          console.log('App Component - Redirecting to:', redirectUrl);
          window.location.href = redirectUrl;
        } else {
          console.log('App Component - User is not authenticated or token is expired, staying on auth page');
        }
      }
      // If on protected pages but not authenticated, redirect to login
      else if (!currentUrl.includes('/error/') && !isAuthenticated) {
        console.log('App Component - User is not authenticated on protected page, redirecting to login');

        // Store the current URL for redirect after login
        localStorage.setItem('redirectAfterLogin', currentUrl || '/panel/dashboard');

        // Redirect to login page
        window.location.href = '/auth/login';
      }
      // If token is expired, try to refresh it
      else if (isAuthenticated && !isTokenValid) {
        console.log('App Component - Token is expired, attempting to refresh');
        // The JWT interceptor will handle token refresh when API requests are made
      }
    } catch (error) {
      console.error('App Component - Error checking auth status:', error);
    }
  }
}
