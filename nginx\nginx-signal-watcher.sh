#!/bin/sh
# Nginx Signal Watcher Script
# This script monitors the /etc/nginx/signals directory for signal files
# and performs the requested actions (e.g., reload Nginx)

SIGNAL_DIR="/etc/nginx/signals"
PROCESSED_DIR="${SIGNAL_DIR}/processed"
LOG_FILE="/var/log/nginx/signal-watcher.log"

# Create directories if they don't exist
mkdir -p "${SIGNAL_DIR}"
mkdir -p "${PROCESSED_DIR}"
touch "${LOG_FILE}"

log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_FILE}"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

process_signal_file() {
  local signal_file="$1"
  local filename=$(basename "${signal_file}")
  
  log "Processing signal file: ${filename}"
  
  # Read the signal file content
  if [ -f "${signal_file}" ]; then
    # Check if it's a reload signal
    if echo "${filename}" | grep -q "reload"; then
      log "Reloading Nginx configuration..."
      
      # Test the configuration first
      nginx -t >> "${LOG_FILE}" 2>&1
      if [ $? -eq 0 ]; then
        # Reload Nginx
        nginx -s reload >> "${LOG_FILE}" 2>&1
        if [ $? -eq 0 ]; then
          log "Nginx reloaded successfully"
        else
          log "Failed to reload Nginx"
        fi
      else
        log "Nginx configuration test failed, not reloading"
      fi
    else
      log "Unknown signal type: ${filename}"
    fi
    
    # Move the processed file to the processed directory
    mv "${signal_file}" "${PROCESSED_DIR}/${filename}.$(date '+%s')"
    log "Signal file processed and moved to processed directory"
  fi
}

cleanup_old_files() {
  # Remove processed files older than 7 days
  find "${PROCESSED_DIR}" -type f -mtime +7 -delete
  
  # Log the cleanup
  log "Cleaned up old processed signal files"
}

log "Starting Nginx signal watcher..."

# Main loop
while true; do
  # Check for signal files
  for signal_file in "${SIGNAL_DIR}"/*.signal; do
    if [ -f "${signal_file}" ]; then
      process_signal_file "${signal_file}"
    fi
  done
  
  # Cleanup old files once a day (every 86400 seconds)
  if [ $(date '+%H:%M') = "00:00" ]; then
    cleanup_old_files
  fi
  
  # Sleep for a short time before checking again
  sleep 5
done
