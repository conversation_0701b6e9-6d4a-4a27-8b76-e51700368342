import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SpecialPriceRes } from '../../../model/response/special-price-res.model';
import { DiscountType } from '../../../model/request/custom-discount-service-req.model';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ToastService } from '../../../core/services/toast.service';
import { LiteDropdownComponent } from '../../common/lite-dropdown/lite-dropdown.component';
import { forkJoin, Observable } from 'rxjs';

@Component({
  selector: 'app-new-special-prices',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent
  ],
  templateUrl: './new-special-prices.component.html',
  styleUrls: ['./new-special-prices.component.css']
})
export class NewSpecialPricesComponent implements OnInit {
  @Input() selectedServices: Set<number> = new Set<number>();
  @Input() selectedService: SuperGeneralSvRes | null = null;
  @Input() editMode = false;
  @Input() specialPriceToEdit: SpecialPriceRes | null = null;
  @Input() fromUserView = false; // New property to indicate if opened from user special prices view
  @Input() userIdFromParent: number | null = null; // User ID passed from parent component
  @Output() close = new EventEmitter<void>();
  @Output() specialPriceAdded = new EventEmitter<SpecialPriceRes[]>();

  userId: string = '';
  discountType: string = 'Percentage discount';
  newPrice: number | null = null;
  isSubmitting = false;
  errorMessage: string = '';

  // For service dropdown in edit mode
  allServices: SuperGeneralSvRes[] = [];
  selectedServiceId: number | null = null;
  serviceOptions: string[] = [];
  currentServicePrice: number | null = null;

  discountTypes = [
    'Percentage discount',
    'Fix price'
  ];

  constructor(
    private adminService: AdminServiceService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    // Load all services for the dropdown
    this.loadServices();

    // If user ID is provided from parent component, use it
    if (this.userIdFromParent !== null) {
      this.userId = this.userIdFromParent.toString();
    }

    // If in edit mode and we have a special price to edit, initialize the form with its values
    if (this.editMode && this.specialPriceToEdit) {
      console.log('Initializing edit mode with special price:', this.specialPriceToEdit);

      // Set the discount type based on the special price type
      const discountType = this.specialPriceToEdit.type ||
                          (this.specialPriceToEdit.discount_type === 'FIXED' ? DiscountType.FIXED : DiscountType.PERCENT);
      this.discountType = discountType === DiscountType.FIXED ? 'Fix price' : 'Percentage discount';

      // Set the price value
      this.newPrice = this.specialPriceToEdit.customDiscount !== undefined ?
                     this.specialPriceToEdit.customDiscount :
                     this.specialPriceToEdit.discount_value;

      // Set the user ID if available
      const userId = this.specialPriceToEdit.userId || (this.specialPriceToEdit.user && this.specialPriceToEdit.user.id);
      if (userId) {
        this.userId = userId.toString();
      }

      // Set the selected service ID and update the dropdown
      this.initializeServiceSelection();

      // Set the initial selected option for the dropdown
      if (this.specialPriceToEdit.service) {
        const serviceId = this.specialPriceToEdit.service.id;
        const serviceName = this.specialPriceToEdit.service.name;
        this.selectedOption = `${serviceId}: ${serviceName}`;
      }
    } else if (this.selectedService && 'id' in this.selectedService) {
      // If not in edit mode but we have a selected service, set it as the selected service
      this.selectedServiceId = this.selectedService.id;

      // Set the current service price immediately if available
      if ('price' in this.selectedService) {
        this.currentServicePrice = this.selectedService.price;
      }

      // Also try to find the service in allServices and update the price if found
      const serviceId = this.selectedService.id;
      const selectedService = this.allServices.find(s => s.id === serviceId);
      if (selectedService) {
        this.currentServicePrice = selectedService.price;
      }
    } else if (this.fromUserView) {
      // If opened from user view, we need to show the service dropdown
      // but don't need to set a default service
      console.log('Initializing from user view mode');
    }

    // For create mode (not edit mode), ensure discount type is set to default
    if (!this.editMode) {
      this.discountType = 'Percentage discount';
    }
  }

  // Property to store the selected option for the dropdown
  selectedOption: string = '';

  /**
   * Initializes the service selection dropdown based on the special price to edit
   */
  private initializeServiceSelection(): void {
    if (!this.specialPriceToEdit) return;

    // Get the service ID from the special price
    const serviceId = this.specialPriceToEdit.serviceId ||
                     (this.specialPriceToEdit.service && this.specialPriceToEdit.service.id);

    if (!serviceId) return;

    console.log('Setting selected service ID:', serviceId);
    this.selectedServiceId = serviceId;

    // Find the service in allServices
    const service = this.allServices.find(s => s.id === serviceId);

    if (service) {
      console.log('Found service in allServices:', service);
      // Create the service option string
      const serviceOption = `${service.id}: ${service.name}`;

      // Move this service to the beginning of the options array to make it the default selection
      this.serviceOptions = [
        serviceOption,
        ...this.serviceOptions.filter(option => option !== serviceOption)
      ];

      // Set the current price
      this.currentServicePrice = service.price;
    } else {
      console.log('Service not found in allServices, using service from specialPriceToEdit');

      // If the service is not found in allServices but we have it in specialPriceToEdit.service,
      // use that information to create a service option
      if (this.specialPriceToEdit.service) {
        const serviceFromSpecialPrice = this.specialPriceToEdit.service;
        const serviceOption = `${serviceFromSpecialPrice.id}: ${serviceFromSpecialPrice.name}`;

        // Add this service to the options
        this.serviceOptions = [
          serviceOption,
          ...this.serviceOptions
        ];

        // Set the current price
        this.currentServicePrice = serviceFromSpecialPrice.price;
      }
    }
  }

  /**
   * Loads all services for the dropdown
   */
  loadServices(): void {
    // First check if we already have services in the cache
    this.allServices = this.adminService.getServiceValue();

    if (this.allServices && this.allServices.length > 0) {
      console.log('Using cached services:', this.allServices.length);
      this.updateServiceOptions();
      this.ensureCurrentPriceIsSet();
    } else {
      // If no cached services, fetch them from the API
      console.log('Fetching services from API...');
      this.adminService.getPlatformsWithServices().subscribe({
        next: () => {
          this.allServices = this.adminService.getServiceValue();
          console.log('Loaded services from API:', this.allServices.length);
          this.updateServiceOptions();
          this.ensureCurrentPriceIsSet();
        },
        error: (error) => {
          console.error('Error loading services:', error);
          this.toastService.showError(error?.message || 'Failed to load services');
          // Set empty options array
          this.serviceOptions = [];
        }
      });
    }
  }

  /**
   * Updates the service options for the dropdown based on the loaded services
   */
  private updateServiceOptions(): void {
    // Create options for the dropdown - use format "ID: Name" for display
    this.serviceOptions = this.allServices.map(service =>
      `${service.id}: ${service.name}`
    );

    // If we're in edit mode and have a special price to edit with a service,
    // make sure that service is included in the options even if it's not in allServices
    if (this.editMode && this.specialPriceToEdit && this.specialPriceToEdit.service) {
      const serviceId = this.specialPriceToEdit.service.id;
      const serviceName = this.specialPriceToEdit.service.name;

      // Check if this service is already in the options
      const serviceOption = `${serviceId}: ${serviceName}`;
      if (!this.serviceOptions.includes(serviceOption)) {
        console.log('Adding service from specialPriceToEdit to options:', serviceOption);
        this.serviceOptions.unshift(serviceOption);
      }
    }

    // If we have a selected service, make sure it's in the options
    if (this.selectedService && 'id' in this.selectedService && this.selectedService.id) {
      const serviceId = this.selectedService.id;
      const serviceName = this.selectedService.name;
      const serviceOption = `${serviceId}: ${serviceName}`;

      if (!this.serviceOptions.includes(serviceOption)) {
        console.log('Adding selected service to options:', serviceOption);
        this.serviceOptions.unshift(serviceOption);
      }

      // Set as selected option
      this.selectedOption = serviceOption;
      this.selectedServiceId = serviceId;

      // Update current service price - try to get from allServices first, then from selectedService
      const serviceFromAll = this.allServices.find(s => s.id === serviceId);
      if (serviceFromAll) {
        this.currentServicePrice = serviceFromAll.price;
      } else if ('price' in this.selectedService) {
        this.currentServicePrice = this.selectedService.price;
      }

      console.log('Set current service price:', this.currentServicePrice);
    }
  }

  /**
   * Ensures that current service price is set when we have a selected service
   */
  private ensureCurrentPriceIsSet(): void {
    // If we already have a current price, no need to do anything
    if (this.currentServicePrice !== null) {
      return;
    }

    // If we have a selected service ID, try to find its price
    if (this.selectedServiceId) {
      const service = this.allServices.find(s => s.id === this.selectedServiceId);
      if (service) {
        this.currentServicePrice = service.price;
        console.log('Set current service price from selectedServiceId:', this.currentServicePrice);
      }
    }
    // If we have a selected service object, use its price
    else if (this.selectedService && 'id' in this.selectedService) {
      if ('price' in this.selectedService) {
        this.currentServicePrice = this.selectedService.price;
        console.log('Set current service price from selectedService:', this.currentServicePrice);
      } else {
        // Try to find the service in allServices
        const service = this.allServices.find(s => s.id === this.selectedService!.id);
        if (service) {
          this.currentServicePrice = service.price;
          console.log('Set current service price from allServices lookup:', this.currentServicePrice);
        }
      }
    }
  }

  /**
   * Handles service selection from dropdown
   * @param option The selected option string
   */
  onServiceSelected(option: string): void {
    // Extract the ID from the option string (format: "ID: Name")
    const idStr = option.split(':')[0];
    const serviceId = parseInt(idStr, 10);

    if (!isNaN(serviceId)) {
      this.selectedServiceId = serviceId;

      // Find the service in allServices and get its price
      const selectedService = this.allServices.find(s => s.id === serviceId);
      if (selectedService) {
        this.currentServicePrice = selectedService.price;
      } else {
        this.currentServicePrice = null;
      }
    }
  }

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  toggleDiscountType(type: string): void {
    this.discountType = type;
  }

  saveSpecialPrice(): void {
    // Validate required fields
    if (this.newPrice === null) {
      this.errorMessage = 'Please enter a price value';
      return;
    }

    // For fromUserView mode, we need a selected service
    if (this.fromUserView && !this.selectedServiceId) {
      this.errorMessage = 'Please select a service';
      return;
    }

    // For normal mode (not fromUserView and not editMode), we need a user ID
    if (!this.fromUserView && !this.editMode && !this.userId) {
      this.errorMessage = 'Please enter a user ID';
      return;
    }

    // For fromUserView mode, make sure we have a userIdFromParent
    if (this.fromUserView && this.userIdFromParent === null) {
      this.errorMessage = 'User ID is missing';
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    // Map the discount type to the API enum value
    const apiDiscountType = this.discountType === 'Fix price' ? DiscountType.FIXED : DiscountType.PERCENT;

    // Validate discount value
    if (this.discountType === 'Percentage discount' && (this.newPrice < 0 || this.newPrice > 100)) {
      this.errorMessage = 'Percentage discount must be between 0 and 100';
      this.isSubmitting = false;
      return;
    }

    // Handle edit mode (update existing special price)
    if (this.editMode && this.specialPriceToEdit) {
      // Validate that a service is selected
      if (!this.selectedServiceId) {
        this.errorMessage = 'Please select a service';
        this.isSubmitting = false;
        return;
      }

      this.adminService.updateSpecialPrice(
        this.specialPriceToEdit.id,
        apiDiscountType,
        this.newPrice!,
        this.selectedServiceId!
      ).subscribe({
        next: (result) => {
          this.isSubmitting = false;
          this.specialPriceAdded.emit([result]);
          this.onClose();
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = error.message || 'Failed to update special price. Please try again.';
          console.error('Error updating special price:', error);
        }
      });
      return;
    }

    // Get the user ID - either from parent component or from input field
    let userIdNumber: number;
    if (this.userIdFromParent !== null) {
      userIdNumber = this.userIdFromParent;
    } else {
      userIdNumber = parseInt(this.userId, 10);
      if (isNaN(userIdNumber)) {
        this.errorMessage = 'User ID must be a valid number';
        this.isSubmitting = false;
        return;
      }
    }

    // Create an array of observables for each service
    const requests: Observable<SpecialPriceRes>[] = [];

    // If in fromUserView mode, use the selected service from dropdown
    if (this.fromUserView && this.selectedServiceId) {
      requests.push(
        this.adminService.addCustomDiscount(
          userIdNumber,
          this.selectedServiceId,
          apiDiscountType,
          this.newPrice!
        )
      );
    }
    // If selectedServices is empty but we have a selectedService, use that
    else if (this.selectedServices.size === 0 && this.selectedService) {
      requests.push(
        this.adminService.addCustomDiscount(
          userIdNumber,
          this.selectedService.id,
          apiDiscountType,
          this.newPrice!
        )
      );
    } else {
      // For each selected service, create a request
      Array.from(this.selectedServices).forEach(serviceId => {
        requests.push(
          this.adminService.addCustomDiscount(
            userIdNumber,
            serviceId,
            apiDiscountType,
            this.newPrice!
          )
        );
      });
    }

    // Execute all requests in parallel
    forkJoin(requests).subscribe({
      next: (results) => {
        this.isSubmitting = false;
        this.specialPriceAdded.emit(results);
        this.onClose();
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = error.message || 'Failed to add special prices. Please try again.';
        console.error('Error adding special prices:', error);
      }
    });
  }
}
