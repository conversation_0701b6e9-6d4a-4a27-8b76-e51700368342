package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.request.TenantLanguageSettingsReq;
import tndung.vnfb.smm.dto.response.TenantLanguageSettingsRes;
import tndung.vnfb.smm.dto.response.TenantDefaultLanguageRes;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.service.TenantSettingsService;

import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantSettingsServiceImpl implements TenantSettingsService {

    private final TenantService tenantService;

    @Override
    public TenantLanguageSettingsRes getLanguageSettings() {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Getting language settings for tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        // Parse available languages from comma-separated string
        List<String> availableLanguages = Arrays.asList(
                tenant.getAvailableLanguages() != null ?
                tenant.getAvailableLanguages().split(",") :
                new String[]{"vi", "en"}
        );

        return new TenantLanguageSettingsRes(
                tenant.getDefaultLanguage() != null ? tenant.getDefaultLanguage() : "vi",
                tenant.getId(),
                tenant.getDomain(),
                availableLanguages
        );
    }

    @Override
    @Transactional
    public TenantLanguageSettingsRes updateLanguageSettings(TenantLanguageSettingsReq request) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Updating language settings for tenant: {} with language: {}", tenantId, request.getDefaultLanguage());

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        // Validate that default language is in available languages list
        if (!request.getAvailableLanguages().contains(request.getDefaultLanguage())) {
            throw new RuntimeException("Default language must be in available languages list");
        }

        tenant.setDefaultLanguage(request.getDefaultLanguage());
        tenant.setAvailableLanguages(String.join(",", request.getAvailableLanguages()));
        Tenant savedTenant = tenantService.save(tenant);

        log.info("Updated language settings for tenant: {} - default: '{}', available: '{}'",
                tenantId, request.getDefaultLanguage(), request.getAvailableLanguages());

        // Parse available languages for response
        List<String> availableLanguages = Arrays.asList(
                savedTenant.getAvailableLanguages() != null ?
                savedTenant.getAvailableLanguages().split(",") :
                new String[]{"vi", "en", "cn"}
        );

        return new TenantLanguageSettingsRes(
                savedTenant.getDefaultLanguage(),
                savedTenant.getId(),
                savedTenant.getDomain(),
                availableLanguages
        );
    }

    @Override
    public TenantDefaultLanguageRes getTenantDefaultLanguage() {
        String tenantId = TenantContext.getSiteTenant();
        log.debug("Getting default language for tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        String defaultLanguage = tenant.getDefaultLanguage() != null ? tenant.getDefaultLanguage() : "vi";
        return new TenantDefaultLanguageRes(defaultLanguage);
    }

    @Override
    public List<String> getTenantAvailableLanguages() {
        String tenantId = TenantContext.getSiteTenant();
        log.debug("Getting available languages for tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found: " + tenantId));

        // Parse available languages from comma-separated string
        return Arrays.asList(
                tenant.getAvailableLanguages() != null ?
                tenant.getAvailableLanguages().split(",") :
                new String[]{"vi", "en"}
        );
    }
}
