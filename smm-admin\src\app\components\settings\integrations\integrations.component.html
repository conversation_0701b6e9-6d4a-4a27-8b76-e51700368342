<div class="integrations-settings">
  <!-- Header -->
  <div class="settings-header">
    <h1 class="settings-title">Services integrations</h1>
    <p class="settings-description">Connect your platform with external services</p>
  </div>

  <!-- Integrations List -->
  <div class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon icon="plug" class="section-icon"></fa-icon>
        Available Integrations
      </h2>
    </div>

    <div class="card-content">
      <!-- Loading State -->
      <app-loading *ngIf="loading" [message]="'Loading integrations...'"></app-loading>

      <!-- Integrations List -->
      <div *ngIf="!loading" class="integrations-list">
        <!-- Integration Items -->
        <div *ngFor="let integration of integrations" class="integration-item">
          <div class="integration-info">
            <div class="integration-icon">
              <fa-icon [icon]="getIntegrationIcon(integration.type)" [styles]="{'font-weight': 'fab'}" class="icon"></fa-icon>
            </div>
            <div class="integration-details">
              <h3 class="integration-name">{{ getIntegrationName(integration.type) }}</h3>
              <p class="integration-status" *ngIf="integration.enabled">
                <span class="status-badge connected">Connected</span>
                <span *ngIf="integration.username" class="ml-2 text-ellipsis overflow-hidden">
                  <!-- Format based on integration type -->
                  <ng-container *ngIf="integration.type.toLowerCase() === 'telegram' && !integration.username.startsWith('@')">
                    &#64;{{ integration.username }}
                  </ng-container>
                  <ng-container *ngIf="integration.type.toLowerCase() === 'telegram' && integration.username.startsWith('@')">
                    {{ integration.username }}
                  </ng-container>
                  <ng-container *ngIf="integration.type.toLowerCase() !== 'telegram'">
                    {{ integration.username }}
                  </ng-container>
                </span>
              </p>
              <p class="integration-status" *ngIf="!integration.enabled">
                <span class="status-badge disconnected">Not connected</span>
              </p>
            </div>
          </div>
          <div class="integration-actions">
            <button
              *ngIf="integration.enabled"
              class="edit-button"
              (click)="openConfigPopup(integration)">
              Edit
            </button>
            <button
              class="connect-button"
              [ngClass]="{'disconnect-button': integration.enabled}"
              (click)="connectIntegration(integration.type)">
              {{ integration.enabled ? 'Disconnect' : 'Connect' }}
            </button>
          </div>
        </div>

        <!-- No Integrations Message -->
        <div *ngIf="integrations.length === 0" class="no-integrations">
          <p>No integrations available at the moment.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Integration Config Popup -->
<app-integration-config
  *ngIf="showConfigPopup && selectedIntegration"
  [integration]="selectedIntegration"
  (close)="closeConfigPopup()"
  (save)="saveIntegrationConfig($event)">
</app-integration-config>

<!-- Disconnect Confirmation Modal -->
<app-disconnect-confirmation
  *ngIf="showDeleteConfirmation && integrationToDisconnect"
  [itemName]="getIntegrationName(integrationToDisconnect.type)"
  [isLoading]="isDisconnecting"
  (close)="closeDeleteConfirmation()"
  (confirm)="confirmDisconnectIntegration()">
</app-disconnect-confirmation>
