.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-container {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.popup-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  background: #fafafa;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--primary, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 16px;
  font-size: 18px;
}

.popup-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.popup-content {
  max-height: 60vh;
  overflow-y: auto;
}

.language-list {
  padding: 8px 0;
}

.language-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f8fafc;
}

.language-item:hover {
  background-color: #f8fafc;
}

.language-item:last-child {
  border-bottom: none;
}

.language-flag {
  width: 28px;
  height: 20px;
  margin-right: 16px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.language-flag span {
  width: 100%;
  height: 100%;
  display: block;
}

.language-name {
  flex: 1;
  font-weight: 500;
  color: #111827;
  font-size: 0.95rem;
}

.language-arrow {
  color: #9ca3af;
  font-size: 0.875rem;
  transition: color 0.2s;
}

.language-item:hover .language-arrow {
  color: #6b7280;
}

.no-languages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  text-align: center;
}

.no-languages-icon {
  width: 48px;
  height: 48px;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 16px;
  font-size: 20px;
}

.no-languages-text {
  color: #6b7280;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Custom scrollbar */
.popup-content::-webkit-scrollbar {
  width: 6px;
}

.popup-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.popup-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.popup-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
