
<div class="user-menu-container">
    <!-- Standard Header Style -->
    <div *ngIf="currentHeaderStyle === 'standard'" class="notification cursor-pointer" (click)="toggleMenu()">
        <fa-icon [icon]="faBell" class="md:text-lg text-sm text-[var(--primary)]"></fa-icon>
    </div>

    <!-- Compact Header Style -->
    <div *ngIf="currentHeaderStyle === 'compact'" class="notification-compact cursor-pointer" (click)="toggleMenu()">
        <fa-icon [icon]="faBell" class="md:text-sm text-xs text-gray-600"></fa-icon>
    </div>

    <!-- Modern Header Style -->
    <div *ngIf="currentHeaderStyle === 'modern'" class="notification-modern cursor-pointer" (click)="toggleMenu()">
        <fa-icon [icon]="faBell" class="md:text-lg text-sm text-blue-500"></fa-icon>
    </div>

    <!-- Minimal Header Style -->
    <div *ngIf="currentHeaderStyle === 'minimal'" class="notification-minimal cursor-pointer" (click)="toggleMenu()">
        <fa-icon [icon]="faBell" class="md:text-sm text-xs text-gray-500"></fa-icon>
    </div>

    <div class="notification-container" [class.show]="isOpen">
        <!-- Triangle pointer -->
        <div class="triangle-pointer"></div>

        <div class="notification-box">
          <div class="notification-header">
            <span class="header-text">Thông báo</span>
          </div>
          <div *ngFor="let notification of notifications.slice(1)"
               class="notification-item">
            <div class="notification-content">
              <span class="message-text">{{notification.message}}</span>
            </div>
          </div>
        </div>
      </div>
</div>
<div class="overlay" *ngIf="isOpen" (click)="toggleMenu()"></div>
