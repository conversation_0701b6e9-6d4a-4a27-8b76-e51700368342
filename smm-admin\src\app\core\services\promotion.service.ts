import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ConfigService } from './config.service';

export interface PromotionReq {
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  promotion_percentage: number;
  min_deposit_amount?: number;
  max_reward_amount?: number;
}

export interface PromotionRes {
  id: number;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  active: boolean;
  created_at: string;
  promotion_percentage?: number;
  min_deposit_amount?: number;
  max_reward_amount?: number;
}

@Injectable({
  providedIn: 'root'
})
export class PromotionService {
  private apiUrl: string;
  private _promotions$ = new BehaviorSubject<PromotionRes[]>([]);

  promotions$ = this._promotions$.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.apiUrl = `${this.configService.apiUrl}/promotions`;
  }

  /**
   * Get all promotions
   */
  getPromotions(): Observable<PromotionRes[]> {
    return this.http.get<PromotionRes[]>(this.apiUrl).pipe(
      tap(promotions => {
        this._promotions$.next(promotions);
      })
    );
  }

  /**
   * Get promotion by ID
   */
  getPromotionById(id: number): Observable<PromotionRes> {
    return this.http.get<PromotionRes>(`${this.apiUrl}/${id}`);
  }

  /**
   * Create a new promotion
   */
  createPromotion(promotion: PromotionReq): Observable<PromotionRes> {
    return this.http.post<PromotionRes>(this.apiUrl, promotion).pipe(
      tap(() => {
        // Refresh the promotions list
        this.getPromotions().subscribe();
      })
    );
  }

  /**
   * Update an existing promotion
   */
  updatePromotion(id: number, promotion: PromotionReq): Observable<PromotionRes> {
    return this.http.put<PromotionRes>(`${this.apiUrl}/${id}`, promotion).pipe(
      tap(() => {
        // Refresh the promotions list
        this.getPromotions().subscribe();
      })
    );
  }

  /**
   * Delete a promotion
   */
  deletePromotion(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`).pipe(
      tap(() => {
        // Refresh the promotions list
        this.getPromotions().subscribe();
      })
    );
  }
}
