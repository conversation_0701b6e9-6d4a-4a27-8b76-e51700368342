import { Component, Input, Output, EventEmitter, OnInit, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-date-range-picker',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './date-range-picker.component.html',
  styleUrl: './date-range-picker.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DateRangePickerComponent),
      multi: true
    }
  ]
})
export class DateRangePickerComponent implements OnInit, ControlValueAccessor {
  @Input() containerClass: string = '';
  @Output() dateRangeChanged = new EventEmitter<{ startDate: Date | null, endDate: Date | null }>();

  // Private properties for the dates
  private _startDate: Date | null = null;
  private _endDate: Date | null = null;

  // Getters and setters for the dates
  get startDate(): Date | null {
    return this._startDate;
  }

  set startDate(value: Date | null) {
    this._startDate = value;
    this.onChange({ startDate: this._startDate, endDate: this._endDate });
  }

  get endDate(): Date | null {
    return this._endDate;
  }

  set endDate(value: Date | null) {
    this._endDate = value;
    this.onChange({ startDate: this._startDate, endDate: this._endDate });
  }

  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  calendarDays: { date: Date, isCurrentMonth: boolean, isSelected: boolean, isInRange: boolean, isToday: boolean }[][] = [];
  currentMonth: number = 0;
  currentYear: number = 0;
  selectingStart: boolean = true;
  showCalendar: boolean = false;

  startDateStr: string = '';
  endDateStr: string = '';

  ngOnInit(): void {
    const today = new Date();
    this.currentMonth = today.getMonth();
    this.currentYear = today.getFullYear();
    this.updateCalendar();
    this.formatDateStrings();
  }

  formatDateStrings(): void {
    this.startDateStr = this.startDate ? this.formatDate(this.startDate) : 'Select start date';
    this.endDateStr = this.endDate ? this.formatDate(this.endDate) : 'Select end date';
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  toggleCalendar(): void {
    this.showCalendar = !this.showCalendar;
    if (this.showCalendar) {
      this.updateCalendar();
    }
  }

  updateCalendar(): void {
    this.calendarDays = [];

    const firstDay = new Date(this.currentYear, this.currentMonth, 1);
    const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);

    let startingDayOfWeek = firstDay.getDay();
    const totalDays = lastDay.getDate();

    let day = 1;
    const today = new Date();

    for (let i = 0; i < 6; i++) {
      const week: { date: Date, isCurrentMonth: boolean, isSelected: boolean, isInRange: boolean, isToday: boolean }[] = [];

      for (let j = 0; j < 7; j++) {
        if (i === 0 && j < startingDayOfWeek) {
          // Previous month days
          const prevMonthDate = new Date(this.currentYear, this.currentMonth, -startingDayOfWeek + j + 1);
          week.push({
            date: prevMonthDate,
            isCurrentMonth: false,
            isSelected: this.isDateSelected(prevMonthDate),
            isInRange: this.isDateInRange(prevMonthDate),
            isToday: this.isToday(prevMonthDate)
          });
        } else if (day > totalDays) {
          // Next month days
          const nextMonthDate = new Date(this.currentYear, this.currentMonth + 1, day - totalDays);
          week.push({
            date: nextMonthDate,
            isCurrentMonth: false,
            isSelected: this.isDateSelected(nextMonthDate),
            isInRange: this.isDateInRange(nextMonthDate),
            isToday: this.isToday(nextMonthDate)
          });
          day++;
        } else {
          // Current month days
          const currentDate = new Date(this.currentYear, this.currentMonth, day);
          week.push({
            date: currentDate,
            isCurrentMonth: true,
            isSelected: this.isDateSelected(currentDate),
            isInRange: this.isDateInRange(currentDate),
            isToday: this.isToday(currentDate)
          });
          day++;
        }
      }

      this.calendarDays.push(week);

      if (day > totalDays && i < 5) {
        break;
      }
    }
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  }

  isDateSelected(date: Date): boolean {
    return !!(this.startDate && this.isSameDate(date, this.startDate)) ||
           !!(this.endDate && this.isSameDate(date, this.endDate));
  }

  isDateInRange(date: Date): boolean {
    if (!this.startDate || !this.endDate) return false;
    return date > this.startDate && date < this.endDate;
  }

  isSameDate(date1: Date, date2: Date | null): boolean {
    if (!date2) return false;
    return date1.getDate() === date2.getDate() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getFullYear() === date2.getFullYear();
  }

  prevMonth(): void {
    if (this.currentMonth === 0) {
      this.currentMonth = 11;
      this.currentYear--;
    } else {
      this.currentMonth--;
    }
    this.updateCalendar();
  }

  nextMonth(): void {
    if (this.currentMonth === 11) {
      this.currentMonth = 0;
      this.currentYear++;
    } else {
      this.currentMonth++;
    }
    this.updateCalendar();
  }

  selectDate(date: Date): void {
    if (this.selectingStart) {
      this._startDate = new Date(date);
      this._endDate = new Date(date);
      this.selectingStart = false;
    } else {
      if (this._startDate && date < this._startDate) {
        this._endDate = new Date(this._startDate);
        this._startDate = new Date(date);
      } else {
        this._endDate = new Date(date);
      }
      this.selectingStart = true;
      this.formatDateStrings();
      this.dateRangeChanged.emit({
        startDate: this._startDate,
        endDate: this._endDate
      });
      this.onChange({
        startDate: this._startDate,
        endDate: this._endDate
      });
      this.onTouched();
    }
    this.updateCalendar();
  }

  applyDateRange(): void {
    this.showCalendar = false;
    console.log('Date range picker - Applying date range:', { startDate: this._startDate, endDate: this._endDate });
    this.dateRangeChanged.emit({ startDate: this._startDate, endDate: this._endDate });
    this.onChange({ startDate: this._startDate, endDate: this._endDate });
    this.onTouched();
  }

  // ControlValueAccessor methods
  writeValue(value: { startDate: Date | null, endDate: Date | null } | null): void {
    if (value) {
      this._startDate = value.startDate ? new Date(value.startDate) : null;
      this._endDate = value.endDate ? new Date(value.endDate) : null;
      this.formatDateStrings();
      this.updateCalendar();
    } else {
      this._startDate = null;
      this._endDate = null;
      this.formatDateStrings();
      this.updateCalendar();
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    // Implement if needed
  }
}
