import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize, switchMap, tap } from 'rxjs';
import { UpdateLogItem, UpdateLogsPageRes } from '../../model/response/update-logs-res.model';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class UpdateService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _updateLogs$ = new BehaviorSubject<UpdateLogItem[]>([]);
  private _pagination$ = new BehaviorSubject<any>({
    pageNumber: 0,
    pageSize: 20,
    totalElements: 0,
    totalPages: 0
  });
  private _search$ = new Subject<string>();
  private _filter$ = new Subject<string>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Set up subscription for search
    this._search$
      .pipe(
        tap(() => this._loading$.next(true)),
        switchMap(keyword => this.searchUpdateLogs(keyword))
      )
      .subscribe((result: UpdateLogsPageRes) => {
        this._updateLogs$.next(result.content);
        this._pagination$.next({
          pageNumber: result.pageable.page_number,
          pageSize: result.pageable.page_size,
          totalElements: result.total_elements,
          totalPages: result.total_pages
        });
        this._loading$.next(false);
      });

    // Set up subscription for filter
    this._filter$
      .pipe(
        tap(() => this._loading$.next(true)),
        switchMap(status => this.filterUpdateLogs(status))
      )
      .subscribe((result: UpdateLogsPageRes) => {
        this._updateLogs$.next(result.content);
        this._pagination$.next({
          pageNumber: result.pageable.page_number,
          pageSize: result.pageable.page_size,
          totalElements: result.total_elements,
          totalPages: result.total_pages
        });
        this._loading$.next(false);
      });
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get updateLogs$(): BehaviorSubject<UpdateLogItem[]> {
    return this._updateLogs$;
  }

  get pagination$(): BehaviorSubject<any> {
    return this._pagination$;
  }

  /**
   * Trigger a search for update logs
   * @param keyword The search keyword
   */
  search(keyword: string): void {
    this._search$.next(keyword);
  }

  /**
   * Trigger a filter for update logs
   * @param status The status to filter by
   */
  filter(status: string): void {
    this._filter$.next(status);
  }

  /**
   * Get all update logs
   */
  getUpdateLogs(page: number = 0, size: number = 20): Observable<UpdateLogsPageRes> {
    this._loading$.next(true);
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http
      .get<UpdateLogsPageRes>(`${this.configService.apiUrl}/update-logs`, { params })
      .pipe(
        tap(result => {
          this._updateLogs$.next(result.content);
          this._pagination$.next({
            pageNumber: result.pageable.page_number,
            pageSize: result.pageable.page_size,
            totalElements: result.total_elements,
            totalPages: result.total_pages
          });
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Search update logs by keyword
   * @param keyword The search keyword
   */
  searchUpdateLogs(keyword: string, page: number = 0, size: number = 20): Observable<UpdateLogsPageRes> {
    let params = new HttpParams()
      .set('keyword', keyword)
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http
      .get<UpdateLogsPageRes>(`${this.configService.apiUrl}/update-logs`, { params });
  }

  /**
   * Filter update logs by status
   * @param status The status to filter by
   */
  filterUpdateLogs(status: string, page: number = 0, size: number = 20): Observable<UpdateLogsPageRes> {
    let params = new HttpParams()
      .set('status', status)
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http
      .get<UpdateLogsPageRes>(`${this.configService.apiUrl}/update-logs`, { params });
  }

  /**
   * Get update logs with both keyword and status filters
   * @param keyword The search keyword
   * @param status The status to filter by
   */
  getUpdateLogsWithFilters(keyword: string, status: string, page: number = 0, size: number = 20): Observable<UpdateLogsPageRes> {
    this._loading$.next(true);
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (keyword) {
      params = params.set('keyword', keyword);
    }

    if (status && status !== 'ALL') {
      params = params.set('status', status);
    }

    return this.http
      .get<UpdateLogsPageRes>(`${this.configService.apiUrl}/update-logs`, { params })
      .pipe(
        tap(result => {
          this._updateLogs$.next(result.content);
          this._pagination$.next({
            pageNumber: result.pageable.page_number,
            pageSize: result.pageable.page_size,
            totalElements: result.total_elements,
            totalPages: result.total_pages
          });
        }),
        finalize(() => this._loading$.next(false))
      );
  }
}
