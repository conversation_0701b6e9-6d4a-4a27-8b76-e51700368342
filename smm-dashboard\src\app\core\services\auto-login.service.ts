import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from './auth.service';
import { UserRes } from '../../model/response/user-res.model';
import { Tokens } from '../../model/tokens.model';

@Injectable({
  providedIn: 'root'
})
export class AutoLoginService {

  constructor(
    private router: Router,
    private authService: AuthService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  /**
   * Check for auto-login parameters in URL and perform auto-login if present
   * This should be called on app initialization
   */
  checkAndPerformAutoLogin(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      // Get current URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const accessToken = urlParams.get('accessToken');
      const clientId = urlParams.get('clientId');
      const refreshToken = urlParams.get('refreshToken');

      // Check if all required parameters are present
      if (accessToken && clientId && refreshToken) {
        console.log('Auto-login parameters detected, performing auto-login...');
        
        // Perform auto-login
        this.performAutoLogin(accessToken, clientId, refreshToken);
      }
    } catch (error) {
      console.error('Error checking auto-login parameters:', error);
    }
  }

  /**
   * Perform auto-login with provided tokens
   */
  private performAutoLogin(accessToken: string, clientId: string, refreshToken: string): void {
    try {
      // Create tokens object
      const tokens: Tokens = {
        access_token: accessToken,
        client_id: clientId,
        refresh_token: refreshToken
      };

      // Create user object with tokens
      // We'll extract user info from the JWT token
      const userInfo = this.extractUserInfoFromToken(accessToken);
      
      const user: UserRes = {
        id: userInfo.userId,
        user_name: userInfo.username || 'User',
        email: userInfo.email || '',
        balance: 0, // Will be updated when user data is fetched
        tokens: tokens,
        avatar: '',
        name: '',
        phone: '',
        preferred_currency: { code: 'USD', symbol: '$', exchange_rate: 1 },
        mfa_enabled: false,
        api_key: '',
        created_at: '',
        last_login_at: '',
        total_order: 0
        //roles: userInfo.roles || ['ROLE_USER']
      };

      // Update auth service with the user
      this.authService.updateUser(user);

      // Clean URL by removing query parameters
      this.cleanUrl();

      // Redirect to dashboard
      console.log('Auto-login successful, redirecting to dashboard...');
      this.router.navigate(['/dashboard/new']);

    } catch (error) {
      console.error('Error performing auto-login:', error);
      // If auto-login fails, redirect to login page
      this.router.navigate(['/auth/login']);
    }
  }

  /**
   * Extract user information from JWT token
   */
  private extractUserInfoFromToken(token: string): any {
    try {
      // Decode JWT token (simple base64 decode of payload)
      const payload = token.split('.')[1];
      const decodedPayload = atob(payload);
      const tokenData = JSON.parse(decodedPayload);

      return {
        userId: tokenData.sub || tokenData.user_id,
        username: tokenData.user_name || tokenData.username,
        email: tokenData.email,
        roles: tokenData.roles || tokenData.authorities || ['ROLE_USER'],
        tenantId: tokenData.tenant_id
      };
    } catch (error) {
      console.error('Error extracting user info from token:', error);
      return {
        userId: null,
        username: 'User',
        email: '',
        roles: ['ROLE_USER']
      };
    }
  }

  /**
   * Clean URL by removing query parameters
   */
  private cleanUrl(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      // Get current URL without query parameters
      const url = window.location.protocol + '//' + 
                  window.location.host + 
                  window.location.pathname;
      
      // Update URL without reloading the page
      window.history.replaceState({}, document.title, url);
    } catch (error) {
      console.error('Error cleaning URL:', error);
    }
  }
}
