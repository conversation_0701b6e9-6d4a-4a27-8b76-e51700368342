package tndung.vnfb.smm.service;

import tndung.vnfb.smm.entity.UserTenant;

public interface UserTenantService {
    UserTenant save(Long userid, String tenantId);

    /**
     * Check if a user has access to a specific tenant
     *
     * @param userId The user ID
     * @param tenantId The tenant ID
     * @return true if the user has access, false otherwise
     */
    boolean hasAccessToTenant(Long userId, String tenantId);

    UserTenant findByUserId(Long userId);
}
