import { OrderStatus } from "../constant/order-status";




export interface Product {
  tag: string;
  imageUrl: string;
  title: string;
  description: string;
  price: string;
  stock: string;
  country: string;
}








  export interface Order {
    id: string;
    time: string;
    user: string;
    link: string;


    price: number;
    priceLabel: string;
    serviceId: string;
    description: string;
    quantity: number;
    totalAmount: string;
    flowAmount: string;
    status: OrderStatus;

    charge: number;
    remains: number;
    start_count: number;


    isChecked: boolean;
  }
