<div class="promotions-container">
  <div class="promotions-header">
    <h1 class="promotions-title">{{ 'Danh sách khuyến mãi' | translate }}</h1>
    <button class="add-promotion-button" (click)="createNewPromotion()">
      <fa-icon [icon]="['fas', 'plus']"></fa-icon>
      {{ 'Thêm khuyến mãi' | translate }}
    </button>
  </div>

  <!-- Loading and Empty States -->
  <app-loading *ngIf="isLoading" [message]="'Loading promotions...' | translate"></app-loading>


  <!-- Desktop View: Promotions Table -->
  <div class="promotions-table-container desktop-only">
    <table class="promotions-table">
      <thead>
        <tr>
          <th>{{ 'Tên khuyến mãi' | translate }}</th>
          <th>{{ 'Ngày bắt đầu' | translate }}</th>
          <th>{{ 'Ngày kết thúc' | translate }}</th>
          <th>{{ 'Trạng thái' | translate }}</th>
          <th>{{ 'Thao tác' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let promotion of getCurrentPagePromotions()">
          <td>{{ promotion.name }}</td>
          <td>{{ promotion.start_date | date:'dd/MM/yyyy' }}</td>
          <td>{{ promotion.end_date | date:'dd/MM/yyyy' }}</td>
          <td>
            <span class="status-badge" [ngClass]="getStatusClass(promotion)">
              {{ getStatusText(promotion) }}
            </span>
          </td>
          <td class="actions-cell">
            <button class="action-button edit-button" (click)="editPromotion(promotion.id)">
              <fa-icon [icon]="['fas', 'edit']"></fa-icon>
            </button>
            <button class="action-button delete-button" (click)="confirmDelete(promotion.id)">
              <fa-icon [icon]="['fas', 'trash']"></fa-icon>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="!isLoading && getCurrentPagePromotions().length === 0" class="empty-container">
    {{ 'No promotions found' | translate }}
  </div>
  <!-- Mobile View: Promotion Cards -->
  <div class="mobile-only promotion-cards-container" *ngIf="!isLoading && getCurrentPagePromotions().length > 0">
    <div class="promotion-card" *ngFor="let promotion of getCurrentPagePromotions()">
      <div class="promotion-card-header">
        <h3 class="promotion-card-title">{{ promotion.name }}</h3>
        <span class="status-badge mobile-badge" [ngClass]="getStatusClass(promotion)">
          {{ getStatusText(promotion) }}
        </span>
      </div>
      <div class="promotion-card-content">
        <div class="promotion-card-info">
          <div class="info-item">
            <span class="info-label">{{ 'Ngày bắt đầu' | translate }}:</span>
            <span class="info-value">{{ promotion.start_date | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ 'Ngày kết thúc' | translate }}:</span>
            <span class="info-value">{{ promotion.end_date | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>
        <div class="promotion-card-actions">
          <button class="action-button edit-button" (click)="editPromotion(promotion.id)">
            <fa-icon [icon]="['fas', 'edit']"></fa-icon>
          </button>
          <button class="action-button delete-button" (click)="confirmDelete(promotion.id)">
            <fa-icon [icon]="['fas', 'trash']"></fa-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalPages > 1">
    <div class="pagination">
      <button
        class="pagination-button"
        [disabled]="currentPage === 1"
        (click)="goToPage(currentPage - 1)">
        <fa-icon [icon]="['fas', 'chevron-left']"></fa-icon>
      </button>

      <button
        *ngFor="let page of [].constructor(totalPages); let i = index"
        class="pagination-button"
        [class.active]="currentPage === i + 1"
        (click)="goToPage(i + 1)">
        {{ i + 1 }}
      </button>

      <button
        class="pagination-button"
        [disabled]="currentPage === totalPages"
        (click)="goToPage(currentPage + 1)">
        <fa-icon [icon]="['fas', 'chevron-right']"></fa-icon>
      </button>
    </div>
  </div>

  <!-- Confirmation Popup -->
  <app-delete-confirmation
    *ngIf="showConfirmationPopup"
    [title]="'Delete Promotion' | translate"

    (confirm)="deletePromotion()"
    (cancel)="closeConfirmationPopup()">
  </app-delete-confirmation>
</div>
