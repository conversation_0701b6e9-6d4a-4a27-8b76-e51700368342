.page-container {
  @apply w-full max-w-7xl mx-auto py-2;
}

.content-container {
  @apply bg-white rounded-lg shadow-sm !p-0;
}

.page-header {
  @apply flex justify-between items-center mb-4 pb-3 border-b border-gray-200;
}

.page-title {
  @apply text-2xl font-semibold text-[var(--gray-800)];
}

.back-button {
  @apply p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
}

.form-container {
  @apply max-w-2xl mx-auto;
}

.notification-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.content-header {
  @apply flex items-center justify-between mb-2;
}

.html-toggle-container {
  @apply flex items-center;
}

.html-toggle-label {
  @apply text-xs font-medium text-[var(--gray-700)] mr-2;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)];
}

.form-input {
  @apply w-full px-4 py-3 bg-[#f5f7fc] border border-transparent rounded-lg focus:border-[#30B0C7] focus:ring-1 focus:ring-[#30B0C7] outline-none text-[var(--gray-800)];
}

.content-textarea {
  min-height: 200px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
}

.html-textarea {
  font-family: monospace;
  white-space: pre;
  tab-size: 2;
}

.html-container {
  width: 100%;
  margin-bottom: 0.5rem;
}

/* Quill Editor Styles */
.quill-container {
  margin-bottom: 0.5rem;
  width: 100%;
  display: block;
}

:host ::ng-deep quill-editor {
  display: block;
  width: 100%;
}

:host ::ng-deep .full-width-editor {
  width: 100% !important;
  display: block !important;
}

:host ::ng-deep .ql-editor-wrapper {
  width: 100% !important;
  display: block !important;
}

:host ::ng-deep .quill-editor-full-width {
  width: 100% !important;
  display: block !important;
}

:host ::ng-deep .quill-editor-full-width .ql-container,
:host ::ng-deep .quill-editor-full-width .ql-toolbar {
  width: 100% !important;
}

/* Error state */
.quill-error ::ng-deep .ql-container,
.quill-error ::ng-deep .ql-toolbar {
  border-color: #ef4444 !important;
}

/* Custom Quill styles */
:host ::ng-deep .ql-toolbar.ql-snow {
  background-color: #f5f7fc;
  border: 1px solid #e2e8f0;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  font-family: inherit;
  width: 100%;
}

:host ::ng-deep .ql-container.ql-snow {
  background-color: #f5f7fc;
  border: 1px solid #e2e8f0;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  font-family: inherit;
  width: 100%;
}

:host ::ng-deep .ql-editor {
  min-height: 150px;
  font-family: inherit;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
}

/* Fix for Quill SVG icons */
:host ::ng-deep .ql-snow .ql-stroke {
  stroke: #333;
  stroke-width: 1.5;
}

:host ::ng-deep .ql-toolbar.ql-snow .ql-formats {
  display: inline-block;
  vertical-align: middle;
  margin-right: 15px;
}

:host ::ng-deep .ql-snow .ql-fill {
  fill: #333;
}

:host ::ng-deep .ql-snow .ql-picker {
  color: #333;
}

/* Active state */
:host ::ng-deep .ql-snow .ql-toolbar button.ql-active,
:host ::ng-deep .ql-snow .ql-toolbar button:hover {
  color: #30B0C7;
}

:host ::ng-deep .ql-snow .ql-toolbar button.ql-active .ql-stroke,
:host ::ng-deep .ql-snow .ql-toolbar button:hover .ql-stroke {
  stroke: #30B0C7;
}

:host ::ng-deep .ql-snow .ql-toolbar button.ql-active .ql-fill,
:host ::ng-deep .ql-snow .ql-toolbar button:hover .ql-fill {
  fill: #30B0C7;
}

.input-error {
  @apply border-red-500 focus:border-red-500 focus:ring-red-500;
}

.disabled-input {
  @apply bg-gray-100 text-gray-500 cursor-not-allowed;
}

.validation-error {
  @apply text-red-500 text-xs mt-1;
}

/* Toggle Switch */
.toggle-container {
  @apply flex items-center justify-between;
}

.toggle-label {
  @apply text-sm font-medium text-[var(--gray-700)];
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch.small {
  width: 36px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e9eaec;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch.small .toggle-slider:before {
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
}

input:checked + .toggle-slider {
  background-color: #30B0C7;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

.toggle-switch.small input:checked + .toggle-slider:before {
  transform: translateX(16px);
}

.help-text {
  @apply text-xs text-gray-500 mt-1;
}

/* Auto Dismiss Container */
.auto-dismiss-container {
  @apply space-y-3;
}

.hours-input-container {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200;
}

.hours-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.hours-input-wrapper {
  @apply flex items-center space-x-2 max-w-xs;
}

.hours-input {
  @apply flex-1 px-3 py-2 bg-white border border-gray-300 rounded-md focus:border-[#30B0C7] focus:ring-1 focus:ring-[#30B0C7] outline-none text-gray-800 transition-colors;
  min-width: 80px;
}

.hours-input:disabled {
  @apply bg-gray-100 text-gray-500 cursor-not-allowed border-gray-200;
}

.hours-unit {
  @apply text-sm font-medium text-gray-600;
}

/* Expiry Options */
.expiry-options {
  @apply flex flex-col space-y-3 mt-3;
}

.radio-option {
  @apply flex items-center cursor-pointer transition-all duration-200 p-3 rounded-md hover:bg-gray-50 border border-transparent;
}

.radio-option:hover {
  @apply border-gray-200 shadow-sm;
}

.radio-option.active {
  @apply bg-blue-50 border-blue-200;
}

/* Hide the default radio button */
.radio-option input[type="radio"] {
  @apply absolute opacity-0;
  height: 0;
  width: 0;
}

/* Custom radio button */
.radio-checkmark {
  @apply relative inline-block mr-3 rounded-full border-2 border-gray-300 bg-white transition-all duration-200;
  height: 20px;
  width: 20px;
}

/* The dot inside the radio button */
.radio-checkmark:after {
  content: "";
  @apply absolute rounded-full bg-[#30B0C7] opacity-0 transition-all duration-200;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  height: 10px;
  width: 10px;
}

/* When the radio button is checked */
.radio-option input[type="radio"]:checked ~ .radio-checkmark {
  @apply border-[#30B0C7];
}

.radio-option input[type="radio"]:checked ~ .radio-checkmark:after {
  @apply opacity-100;
  transform: translate(-50%, -50%) scale(1);
}

/* Radio option label */
.radio-label {
  @apply text-sm font-medium text-gray-700 transition-all duration-200;
}

.radio-option input[type="radio"]:checked ~ .radio-label {
  @apply text-[#30B0C7] font-semibold;
}

/* Add icon to radio option */
.radio-icon {
  @apply ml-auto text-gray-400 opacity-0 transition-all duration-200;
}

.radio-option:hover .radio-icon {
  @apply opacity-50;
}

.radio-option input[type="radio"]:checked ~ .radio-icon {
  @apply text-[#30B0C7] opacity-100;
}

/* Focus styles for accessibility */
.radio-option input[type="radio"]:focus ~ .radio-checkmark {
  @apply ring-2 ring-offset-2 ring-[#30B0C7]/30;
}

/* Description text for radio options */
.radio-description {
  @apply block text-xs text-gray-500 mt-1 ml-7;
  max-width: 90%;
}

.date-time-inputs {
  @apply flex space-x-4 mt-4 ml-7;
}

.date-input, .time-input {
  @apply flex-1 bg-white border border-gray-200 rounded-lg shadow-sm p-3 transition-all duration-200;
}

.date-input:hover, .time-input:hover {
  @apply border-blue-200 shadow;
}

.date-input:focus, .time-input:focus {
  @apply border-[#30B0C7] shadow ring-1 ring-[#30B0C7]/20 outline-none;
}

.date-time-label {
  @apply block text-xs text-gray-500 mb-1 font-medium;
}

.date-time-container {
  @apply flex flex-col;
}

/* Time Period Input */
.time-period-input {
  @apply flex flex-col mt-4 ml-7;
}

.light-theme-container {
  @apply flex items-center space-x-2 bg-white rounded-lg p-3 border border-gray-200 shadow-sm transition-all duration-200;
  max-width: 300px;
}

.light-theme-container:hover {
  @apply border-blue-200 shadow;
}

.light-theme-container:focus-within {
  @apply border-[#30B0C7] shadow ring-1 ring-[#30B0C7]/20;
}

.light-theme-input {
  @apply bg-white text-black border-0 p-2 w-full focus:outline-none;
  max-width: 150px;
  font-size: 16px;
}

.time-period-label {
  @apply text-sm text-gray-700 font-medium;
}

.calculated-date {
  @apply mt-3 text-gray-600 text-xs flex items-center;
}

.calculated-date fa-icon {
  @apply text-[#30B0C7] mr-1;
}

/* Preview Section */
.preview-toggle {
  @apply flex justify-end mt-4;
}

.preview-button {
  @apply text-[#30B0C7] text-sm font-medium flex items-center gap-1 hover:underline;
}

.preview-section {
  @apply mt-4 p-4 bg-[#f5f7fc] rounded-lg;
}

.preview-title {
  @apply text-sm font-medium text-[var(--gray-700)] mb-2;
}

/* Popup Preview Styles */
.popup-preview {
  @apply bg-white rounded-lg shadow-md overflow-hidden;
  width: 100%;
  border: 1px solid #e2e8f0;
}

.popup-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
  background-color: #f8fafc;
}

.popup-title {
  @apply font-medium text-[var(--gray-800)];
}

.popup-close-button {
  @apply text-gray-500 hover:text-gray-700 focus:outline-none;
  background: none;
  border: none;
  font-size: 16px;
}

.popup-content {
  @apply flex flex-col w-full;
}

.popup-body {
  @apply p-4 text-sm text-[var(--gray-700)] w-full;
  min-height: 100px;
}

.popup-footer {
  @apply flex justify-end p-3 border-t border-gray-200;
  background-color: #f8fafc;
}

.popup-ok-button {
  @apply px-4 py-2 bg-[#30B0C7] text-white rounded-md hover:bg-[#2599a8] focus:outline-none;
}

/* Rich text preview styles */
.popup-body ::ng-deep p {
  margin-bottom: 0.5rem;
}

.popup-body ::ng-deep ul,
.popup-body ::ng-deep ol {
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.popup-body ::ng-deep ul {
  list-style-type: disc;
}

.popup-body ::ng-deep ol {
  list-style-type: decimal;
}

.popup-body ::ng-deep a {
  color: #30B0C7;
  text-decoration: underline;
}

.popup-body ::ng-deep strong {
  font-weight: bold;
}

.popup-body ::ng-deep em {
  font-style: italic;
}

/* Submit Button */
.save-button {
  @apply w-full bg-[#30B0C7] text-white font-medium py-3 px-4 rounded-lg hover:bg-[#2599a8] active:bg-[#1e7f8a] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed mt-6;
}

/* Read-only type display in edit mode */
.readonly-type-display {
  @apply bg-gray-50 rounded-lg p-4 mt-2 border border-gray-200;
}

.readonly-type-value {
  @apply flex items-center text-sm font-medium text-gray-700;
}

.readonly-type-description {
  @apply text-xs text-gray-500 mt-1 ml-6;
}
