<div class="relative inline-block text-left w-full ">
    <div>
      <button
        type="button"
        (click)="toggleDropdown()"
        class="inline-flex w-full justify-between border bg-white rounded-lg bg-transparent px-4 py-2 text-sm font-medium h-12 items-center"
      >
        {{ selectedOption }}
        <fa-icon [icon]='["fas", "angle-up"]'></fa-icon>


      </button>
    </div>

    <div
      *ngIf="isOpen"
      class="admin-dropdown-menu-container fixed w-full rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-[99999]"
    >
      <div class="py-1">
        <a
          *ngFor="let option of options"
          (click)="selectOption(option)"
          class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer"
        >
          {{ option }}
        </a>
      </div>
    </div>
  </div>