<!-- Full screen overlay -->
<div *ngIf="fullScreen" class="fixed inset-0 bg-white bg-opacity-90 flex justify-center items-center z-50">
  <div class="flex flex-col items-center">
    <div class="loading-spinner" [ngClass]="{'sm': size === 'sm', 'md': size === 'md', 'lg': size === 'lg'}"></div>
    <div *ngIf="message" class="mt-4 text-gray-800 font-medium">{{ message }}</div>
  </div>
</div>

<!-- Container overlay -->
<div *ngIf="overlay && !fullScreen" class="absolute inset-0 flex justify-center items-center z-10"
     [ngClass]="{'bg-white bg-opacity-70': !transparent, 'bg-transparent': transparent}">
  <div class="flex flex-col items-center">
    <div class="loading-spinner" [ngClass]="{'sm': size === 'sm', 'md': size === 'md', 'lg': size === 'lg'}"></div>
    <div *ngIf="message" class="mt-2 text-gray-800 font-medium">{{ message }}</div>
  </div>
</div>

<!-- Inline spinner -->
<div *ngIf="!overlay && !fullScreen" class="flex justify-center items-center">
  <div class="flex flex-col items-center">
    <div class="loading-spinner" [ngClass]="{'sm': size === 'sm', 'md': size === 'md', 'lg': size === 'lg'}"></div>
    <div *ngIf="message" class="mt-2 text-gray-800 font-medium">{{ message }}</div>
  </div>
</div>
