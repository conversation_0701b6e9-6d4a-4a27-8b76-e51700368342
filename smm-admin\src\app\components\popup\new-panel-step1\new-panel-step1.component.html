<div class="overlay-black" (click)="onOverlayClick($event)">
  <div class="modal-container">
    <div class="modal-content">
      <!-- Icon -->
      <div class="icon-container">
        <div class="icon-circle">
          <fa-icon [icon]="['fas', 'ellipsis']" class="dots-icon"></fa-icon>
        </div>
      </div>

      <!-- Header -->
      <div class="header-section">
        <h1 class="title">Attach your domain</h1>
        <p class="description">
          If you dont have a domain, you can register it in any domain service, for example 
          <a href="https://name.com" target="_blank" class="domain-link">name.com</a>
        </p>
      </div>

      <!-- Domain Input -->
      <div class="input-section">
        <input 
          type="text" 
          class="domain-input" 
          placeholder="Type your domain" 
          [(ngModel)]="domain"
          (keyup.enter)="onNext()"
        />
      </div>

      <!-- Action Button -->
      <button 
        type="button" 
        class="next-button" 
        [disabled]="!domain || !domain.trim()" 
        (click)="onNext()"
      >
        Next
      </button>
    </div>
  </div>
</div>
