import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AdminServiceService } from './admin-service.service';
import { SuperCategoryRes } from '../../model/response/super-category.model';
import { ExtendedCategoryRes } from '../../model/extended/extended-category.model';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  constructor(private adminService: AdminServiceService) {}

  /**
   * Creates a category object with platform information
   * @param category The base category
   * @param platformName The platform name
   * @param platformIcon The platform icon
   * @returns Extended category with platform info
   */
  createCategoryWithPlatformInfo(
    category: SuperCategoryRes,
    platformName: string,
    platformIcon: string
  ): ExtendedCategoryRes {
    return {
      ...category,
      platformName: platformName,
      platformIcon: platformIcon,
      isAllPlatforms: false,
      isAllCategories: false
    };
  }

  /**
   * Edits a category
   * @param category The category to edit
   */
  editCategory(category: ExtendedCategoryRes): SuperCategoryRes {
    return {
      id: category.id,
      name: category.name,
      description: category.description || '',
      image: category.image || '',
      icon: category.icon || '',
      platformName: category.platformName,
      platformIcon: category.platformIcon,
      sort: category.sort,
      hide: category.hide,
      services: category.services,
      status: category.status || ''
    };
  }

  /**
   * Disables all services in a category
   * @param category The category to disable
   * @param callback Callback function to execute after completion
   */
  disableCategory(category: ExtendedCategoryRes, callback?: (completedCount: number, errorCount: number) => void): void {
    if (!category || !category.services || category.services.length === 0) {
      console.log('No services in category');
      if (callback) callback(0, 0);
      return;
    }

    // Track completed operations
    let completedCount = 0;
    let errorCount = 0;
    const totalServices = category.services.length;

    // Process each service in the category
    category.services.forEach(service => {
      this.adminService.deactivateService(service.id).subscribe({
        next: () => {
          completedCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === totalServices) {
            console.log(`Deactivated ${completedCount} services in category, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        },
        error: (error) => {
          console.error('Error deactivating service:', error);
          errorCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === totalServices) {
            console.log(`Deactivated ${completedCount} services in category, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        }
      });
    });
  }

  /**
   * Enables all services in a category
   * @param category The category to enable
   * @param callback Callback function to execute after completion
   */
  enableCategory(category: ExtendedCategoryRes, callback?: (completedCount: number, errorCount: number) => void): void {
    if (!category || !category.services || category.services.length === 0) {
      console.log('No services in category');
      if (callback) callback(0, 0);
      return;
    }

    // Track completed operations
    let completedCount = 0;
    let errorCount = 0;
    const totalServices = category.services.length;

    // Process each service in the category
    category.services.forEach(service => {
      this.adminService.activateService(service.id).subscribe({
        next: () => {
          completedCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === totalServices) {
            console.log(`Activated ${completedCount} services in category, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        },
        error: (error) => {
          console.error('Error activating service:', error);
          errorCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === totalServices) {
            console.log(`Activated ${completedCount} services in category, ${errorCount} errors`);
            if (callback) callback(completedCount, errorCount);
          }
        }
      });
    });
  }

  /**
   * Sorts a category by price (Low to High)
   * @param category The category to sort
   * @param callback Callback function to execute after completion
   */
  sortCategoryByPriceLowToHigh(category: ExtendedCategoryRes, callback?: () => void): void {
    this.adminService.sortCategoryByPriceLowToHigh(category.id).subscribe({
      next: () => {
        console.log('Category sorted by price (Low to High) successfully');
        if (callback) callback();
      },
      error: (error) => {
        console.error('Error sorting category by price (Low to High):', error);
      }
    });
  }

  /**
   * Sorts a category by price (High to Low)
   * @param category The category to sort
   * @param callback Callback function to execute after completion
   */
  sortCategoryByPriceHighToLow(category: ExtendedCategoryRes, callback?: () => void): void {
    this.adminService.sortCategoryByPriceHighToLow(category.id).subscribe({
      next: () => {
        console.log('Category sorted by price (High to Low) successfully');
        if (callback) callback();
      },
      error: (error) => {
        console.error('Error sorting category by price (High to Low):', error);
      }
    });
  }

  /**
   * Swaps the sort order of two categories
   * @param sourceCategory The source category
   * @param targetCategory The target category
   * @param callback Callback function to execute after completion
   */
  swapCategorySort(sourceCategory: ExtendedCategoryRes, targetCategory: ExtendedCategoryRes, callback?: () => void): void {
    console.log(`Swapping categories: ${sourceCategory.id} (${sourceCategory.name}) and ${targetCategory.id} (${targetCategory.name})`);

    this.adminService.swapCategorySort(sourceCategory.id, targetCategory.id).subscribe({
      next: () => {
        console.log('Category sort order updated successfully');
        if (callback) callback();
      },
      error: (error) => {
        console.error('Error updating category sort order:', error);
      }
    });
  }

  /**
   * Updates the servicesByCategory in AdminServiceService to match the new category order
   * @param orderedCategories The ordered categories
   */
  updateServicesByCategoryOrder(orderedCategories: ExtendedCategoryRes[]): void {
    try {
      // Get the current servicesByCategory from AdminServiceService
      const currentServicesByCategory = {...this.adminService.getServicesByCategoryValue()};

      // Create a new object with the same keys but in the order of orderedCategories
      const orderedServicesByCategory: { [categoryId: string]: SuperGeneralSvRes[] } = {};

      // Add categories in the new order
      orderedCategories.forEach(category => {
        const categoryId = category.id.toString();
        if (currentServicesByCategory[categoryId]) {
          orderedServicesByCategory[categoryId] = currentServicesByCategory[categoryId];
        }
      });

      // Add any remaining categories that might not be in displayCategories
      Object.keys(currentServicesByCategory).forEach(categoryId => {
        if (!orderedServicesByCategory[categoryId]) {
          orderedServicesByCategory[categoryId] = currentServicesByCategory[categoryId];
        }
      });

      // Update the servicesByCategory in AdminServiceService
      this.adminService.updateServicesByCategory(orderedServicesByCategory);

      console.log('Updated servicesByCategory order in AdminServiceService');
    } catch (error) {
      console.error('Error updating servicesByCategory order:', error);
    }
  }

  /**
   * Updates category visibility in all relevant places (UI only)
   * @param categoryId The category ID
   * @param hide Whether to hide the category
   * @param displayCategories The display categories array
   * @param currentCategory The current category
   * @param allPlatforms All platforms
   * @returns Updated display categories and current category
   */
  updateCategoryVisibility(
    categoryId: number,
    hide: boolean,
    displayCategories: ExtendedCategoryRes[],
    currentCategory: ExtendedCategoryRes | null,
    allPlatforms: any[]
  ): { displayCategories: ExtendedCategoryRes[], currentCategory: ExtendedCategoryRes | null } {
    // Update in displayCategories
    if (displayCategories.length > 0) {
      const categoryIndex = displayCategories.findIndex(c => c.id === categoryId);
      if (categoryIndex !== -1) {
        displayCategories[categoryIndex].hide = hide;
      }
    }

    // Update in currentCategory
    if (currentCategory && currentCategory.id === categoryId) {
      currentCategory.hide = hide;
    }

    // Update in allPlatforms (for consistency when switching views)
    allPlatforms.forEach(platform => {
      const categoryIndex = platform.categories.findIndex((c: any) => c.id === categoryId);
      if (categoryIndex !== -1) {
        platform.categories[categoryIndex].hide = hide;
      }
    });

    return { displayCategories, currentCategory };
  }
}
