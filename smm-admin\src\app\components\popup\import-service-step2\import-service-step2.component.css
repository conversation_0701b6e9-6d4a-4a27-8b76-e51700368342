.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-3xl mx-auto;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
}

.modal-title {
  @apply text-xl font-semibold text-gray-800;
}

.close-button {
  @apply p-2 rounded-full text-gray-500 hover:text-gray-700;
}

.form-section {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-select {
  @apply w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-700 appearance-none;
  padding-right: 2.5rem;
}

.select-icon {
  @apply absolute right-0 top-0 h-full px-3 flex items-center pointer-events-none text-gray-500;
}

.price-input-container {
  @apply relative w-24;
}

.price-input {
  @apply w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-700 text-right;
  padding-right: 2rem;
}

.price-input-item {
  @apply w-full px-2 py-1 bg-white border border-gray-300 rounded-lg text-gray-700 text-right;
  padding-right: 1rem;
}

.percentage-symbol {
  @apply absolute right-0 top-0 h-full px-3 flex items-center pointer-events-none text-gray-500;
}

.services-container {
  @apply mt-4 border border-gray-200 rounded-lg overflow-hidden;
  max-height: 300px;
  overflow-y: auto;
}

.loading-indicator {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.empty-state {
  @apply flex flex-col items-center justify-center p-8 text-center text-gray-500;
}

.service-list {
  @apply divide-y divide-gray-200;
}

.service-header {
  @apply flex items-center p-3 bg-gray-50 font-medium text-sm text-gray-700;
}

.service-item {
  @apply flex items-center p-3 hover:bg-gray-50;
}

.service-column {
  @apply flex-1;
}

.price-column {
  @apply w-24 text-right font-medium;
}

.arrow-column {
  @apply w-10 flex justify-center;
}

.percentage-column {
  @apply w-24 flex justify-center;
}

.percentage-column-item {
  @apply w-16 flex justify-center;
}


.service-id {
  @apply text-sm font-medium text-gray-900;
}

.service-name {
  @apply text-sm text-gray-500;
}

.percentage-badge {
  @apply bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded;
}

.action-container {
  @apply mt-6 flex justify-between gap-4;
}

.back-button {
  @apply py-3 px-4 bg-gray-100 text-gray-700 font-medium text-sm rounded-lg transition-colors duration-300 flex items-center;
}

.back-button:hover {
  @apply bg-gray-200;
}

.import-button {
  @apply flex-1 py-3 px-4 bg-[#0095f6] text-white font-medium text-sm rounded-lg transition-colors duration-300;
}

.import-button:hover {
  @apply bg-blue-600;
}

.import-button:active {
  @apply bg-blue-700;
}

.import-button.disabled {
  @apply bg-gray-300 text-gray-500 cursor-not-allowed;
}
