import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, interval } from 'rxjs';
import { ConfigService } from './config.service';
import { UserNotificationRes, UserNotificationReq } from '../../model/response/user-notification-res.model';

@Injectable({
  providedIn: 'root'
})
export class UserNotificationService {
  private readonly apiUrl: string;
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.apiUrl = `${this.configService.apiUrl}/user-notifications`;

    // Poll for unread count every 30 seconds
    interval(30000).subscribe(() => {
      this.refreshUnreadCount();
    });

    // Initial load
    this.refreshUnreadCount();
  }

  createNotification(req: UserNotificationReq): Observable<UserNotificationRes> {
    return this.http.post<UserNotificationRes>(this.apiUrl, req);
  }

  getNotifications(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}?page=${page}&size=${size}`);
  }

  getUnreadNotifications(): Observable<UserNotificationRes[]> {
    return this.http.get<UserNotificationRes[]>(`${this.apiUrl}/unread`);
  }

  getUnreadCount(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/unread/count`);
  }

  markAsRead(id: number): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/${id}/read`, {});
  }

  markAllAsRead(): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/read-all`, {});
  }

  private refreshUnreadCount(): void {
    this.getUnreadCount().subscribe({
      next: (response) => {
        this.unreadCountSubject.next(response);
      },
      error: (error) => {
        console.error('Error fetching unread count:', error);
      }
    });
  }

  // Method to manually refresh unread count (e.g., after marking as read)
  public updateUnreadCount(): void {
    this.refreshUnreadCount();
  }
}
