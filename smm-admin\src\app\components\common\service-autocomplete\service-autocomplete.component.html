<div *ngIf="isVisible" class="absolute w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-96 overflow-y-auto">
  <ul>
    <li *ngFor="let service of services; let i = index" (click)="selectService(service)"
      class="px-4 py-2 cursor-pointer"
      [ngClass]="{'bg-gray-100': i === highlightedIndex, 'hover:bg-gray-50': i !== highlightedIndex}">
      <div class="p-2">
        <app-service-label [service]="service"></app-service-label>
      </div>
    </li>
    <li *ngIf="services.length === 0" class="px-4 py-4 text-center text-gray-500">
      Không tìm thấy dịch vụ. Nếu tìm theo ID, hãy nhập chính xác ID.
    </li>
  </ul>
</div>
