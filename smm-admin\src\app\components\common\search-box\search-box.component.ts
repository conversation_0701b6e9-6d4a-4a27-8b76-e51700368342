import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-search-box',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './search-box.component.html',
  styleUrl: './search-box.component.css'
})
export class SearchBoxComponent {
  @Input() placeholder: string = 'Tìm kiếm';
  @Input() buttonText: string = 'Tìm kiếm';
  @Input() buttonIcon: 'search' | 'edit' | 'none' = 'none';
  @Input() buttonPosition: 'left' | 'right' = 'right';
  @Input() showButtonText: boolean = true;
  
  // CSS customization inputs
  @Input() containerClass: string = '';
  @Input() inputClass: string = 'bg-white';
  @Input() buttonClass: string = 'bg-cyan-500 text-white font-medium';

  @Output() searchEvent = new EventEmitter<string>();

  searchValue: string = '';

  search(): void {
    this.searchEvent.emit(this.searchValue);
  }
}
