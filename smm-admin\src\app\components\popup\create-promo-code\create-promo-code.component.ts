import { Component, EventEmitter, HostListener, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { VoucherService } from '../../../core/services/voucher.service';
import { VoucherReq, VoucherType } from '../../../model/request/voucher-req.model';
import { VoucherRes } from '../../../model/response/voucher-res.model';
import { LiteDropdownComponent } from '../../common/lite-dropdown/lite-dropdown.component';

@Component({
  selector: 'app-create-promo-code',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent
  ],
  templateUrl: './create-promo-code.component.html',
  styleUrls: ['./create-promo-code.component.css']
})
export class CreatePromoCodeComponent {
  @Output() close = new EventEmitter<void>();
  @Output() voucherCreated = new EventEmitter<VoucherRes>();

  // Form data
  promoCodeTypes: string[] = ['Top up balance', 'Discount for order'];
  selectedType: string = 'Top up balance';
  promoCode: string = '';
  discountValue: number = 0;
  activationsCount: number = 1;

  // Map display names to enum values
  private typeMap: { [key: string]: VoucherType } = {
    'Top up balance': VoucherType.TOP_UP,
    'Discount for order': VoucherType.ORDER
  };

  // Error handling
  errorMessage: string = '';
  isSubmitting: boolean = false;

  constructor(private voucherService: VoucherService) {}

  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    this.onClose();
  }

  onClose(): void {
    this.close.emit();
  }

  onSelectType(type: string): void {
    this.selectedType = type;
  }

  generateRandomCode(): void {
    this.promoCode = this.voucherService.generateRandomCode();
  }

  onSubmit(): void {
    // Validate form
    if (!this.promoCode) {
      this.errorMessage = 'Please enter a promo code';
      return;
    }

    if (this.discountValue <= 0) {
      this.errorMessage = 'Please enter a valid discount amount';
      return;
    }

    // For Order type (percentage), validate that the value is between 0 and 100
    if (this.typeMap[this.selectedType] === VoucherType.ORDER && this.discountValue > 100) {
      this.errorMessage = 'Discount percentage cannot exceed 100%';
      return;
    }

    if (this.activationsCount <= 0) {
      this.errorMessage = 'Please enter a valid activations count';
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const voucherReq: VoucherReq = {
      code: this.promoCode,
      discount_value: this.discountValue,
      usage_limit: this.activationsCount,
      type: this.typeMap[this.selectedType]
    };

    this.voucherService.createVoucher(voucherReq).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.voucherCreated.emit(response);
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = error.message || 'Failed to create promo code';
        console.error('Error creating voucher:', error);
      }
    });
  }
}
