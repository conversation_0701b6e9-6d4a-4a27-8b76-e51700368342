/**
 * Common CSS Classes
 * This file contains common layout and utility classes used throughout the application
 */

/* Layout Utilities */
.flex-center {
  @apply flex items-center justify-center;
}

.flex-between {
  @apply flex items-center justify-between;
}

.flex-col-center {
  @apply flex flex-col items-center;
}

.responsive-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.responsive-flex {
  @apply flex flex-col md:flex-row gap-4;
}

/* Spacing Utilities */
.section-padding {
  @apply p-6;
}

.content-gap {
  @apply gap-4;
}

/* Container Styles */
.content-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.home-container {
  @apply flex bg-inherit w-full;
}

.layout-container {
  @apply p-6 bg-white rounded-2xl !max-w-full overflow-x-auto;
}

.z-card-container {
  @apply flex flex-col w-full max-w-[1611px] p-6 px-4 bg-white rounded-lg;
}

/* Text Styles */
.text-body {
  @apply font-roboto text-sm font-medium leading-6 text-gray-700;
}

.text-note {
  @apply text-xs text-gray-500;
}

.link-text {
  @apply text-[var(--primary)];
}

.welcome-message {
  @apply font-roboto text-sm font-medium leading-6 text-gray-700 my-4;
}

.link {
  @apply text-[var(--link-color)] text-sm;
}

.link-primary {
  @apply text-blue-500 hover:text-blue-600 transition-colors duration-200;
}

/* Icon Styles */
.icon-wrapper {
  @apply w-14 h-14 rounded-full bg-[var(--primary-light)] flex items-center justify-center;
}

.icon-circle {
  @apply w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center;
}

/* Interactive Elements */
.clickable {
  @apply cursor-pointer transition-colors duration-200 hover:bg-gray-100;
}

/* Button Styles */
.button-base {
  @apply flex items-center px-4 py-2 rounded-lg transition-all duration-200;
  height: var(--button-height);
}

.button-primary {
  @apply bg-[#D6EFF4] text-[var(--primary)] font-semibold;
}

.button-secondary {
  @apply bg-[var(--background)] text-[var(--gray-800)] font-medium border border-[var(--gray-200)] hover:bg-gray-100;
}

.button-icon {
  @apply flex items-center justify-center;
  width: 46px;
  height: 46px;
}

/* Form Elements */
.form-input {
  @apply w-full px-4 py-2 bg-[var(--background)] rounded-lg border border-transparent focus:border-[var(--primary)] outline-none;
  height: var(--input-height);
}

.form-label {
  @apply text-sm font-semibold text-gray-700 mb-2;
}

.input-popup {
  @apply border border-[var(--admin-primary)] rounded-lg p-2 w-full;
}

.z-text-base {
  @apply w-full bg-[var(--background)] rounded-lg py-3 text-[var(--secondary)] text-sm border-none;
}

/* Tag Styles */
.tag {
  @apply bg-[var(--primary)] text-white px-2.5 py-1 rounded-2xl text-sm;
}

/* Amount Styles */
.flow-amount {
  @apply flex items-center gap-1;
}

.amount {
  @apply px-2 py-1 rounded-2xl text-xs;
}

.amount.red {
  @apply bg-[var(--error-light)] text-[var(--error)];
}

.amount.yellow {
  @apply bg-[var(--warning-light)] text-[var(--warning)];
}

.amount.green {
  @apply bg-[var(--success-light)] text-[var(--success)];
}

.divider {
  @apply text-[var(--secondary)];
}

/* Overlay Styles */
.overlay {
  @apply fixed top-0 left-0 right-0 bottom-0 bg-transparent z-[var(--z-modal)];
}

.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

/* Responsive Styles */
@media (max-width: 768px) {
  .button-base {
    @apply rounded-md;
  }

  .search-box {
    width: 100%;
  }
}
