import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'timeFormat',
  standalone: true
})
export class TimeFormatPipe implements PipeTransform {
  transform(value: number): string {
    if (isNaN(value) || value < 0) {
      return 'Invalid time';
    }

    const hours = Math.floor(value / 3600);
    const minutes = Math.floor((value % 3600) / 60);

    let result = '';
    if (hours > 0) {
      result += `${hours} giờ `;
    }
    if (minutes > 0 || hours === 0) {
      result += `${minutes} phút`;
    }

    return result.trim();
  }
}
