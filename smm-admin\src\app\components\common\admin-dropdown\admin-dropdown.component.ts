import { Component, ElementRef, HostListener, Renderer2 } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-admin-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './admin-dropdown.component.html',
  styleUrl: './admin-dropdown.component.css'
})
export class AdminDropdownComponent {
  isOpen = false;
  selectedOption: string = 'Select an option';
  options: string[] = ['Option 1', 'Option 2', 'Option 3', 'Option 4'];
  private dropdownMenuElement: HTMLElement | null = null;

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Check if the click is outside the dropdown
    const target = event.target as HTMLElement;
    const isInsideDropdown = this.elementRef.nativeElement.contains(target);
    const isInsideDropdownMenu = this.dropdownMenuElement && this.dropdownMenuElement.contains(target);

    if (!isInsideDropdown && !isInsideDropdownMenu) {
      this.isOpen = false;
      this.removeDropdownFromDOM();
    }
  }

  @HostListener('window:resize')
  onWindowResize() {
    if (this.isOpen) {
      this.updateDropdownPosition();
    }
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    if (this.isOpen) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        this.updateDropdownPosition();
      });
    }
  }

  toggleDropdown() {
    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      // Add a small delay to ensure the dropdown is rendered before positioning
      setTimeout(() => {
        this.updateDropdownPosition();
      }, 0);
    } else {
      this.removeDropdownFromDOM();
    }
  }

  selectOption(option: string) {
    this.selectedOption = option;
    this.isOpen = false;
    this.removeDropdownFromDOM();
  }

  private updateDropdownPosition() {
    if (!this.isOpen) return;

    // Get the button element
    const buttonElement = this.elementRef.nativeElement.querySelector('button');
    if (!buttonElement) return;

    // Get the dropdown menu element
    const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.admin-dropdown-menu-container');
    if (!dropdownMenuElement) return;

    // Store reference to the dropdown menu for click detection
    this.dropdownMenuElement = dropdownMenuElement;

    // Get the button's position
    const buttonRect = buttonElement.getBoundingClientRect();

    // Calculate the position for the dropdown
    const top = buttonRect.bottom;
    const left = buttonRect.left;
    const width = buttonRect.width;

    // Set the position and width of the dropdown
    this.renderer.setStyle(dropdownMenuElement, 'position', 'fixed');
    this.renderer.setStyle(dropdownMenuElement, 'top', `${top}px`);
    this.renderer.setStyle(dropdownMenuElement, 'left', `${left}px`);
    this.renderer.setStyle(dropdownMenuElement, 'width', `${width}px`);
    this.renderer.setStyle(dropdownMenuElement, 'z-index', '99999');

    // Check if the dropdown would go off the bottom of the screen
    const dropdownHeight = dropdownMenuElement.offsetHeight;
    const viewportHeight = window.innerHeight;

    if (top + dropdownHeight > viewportHeight) {
      // Position the dropdown above the button instead
      const newTop = buttonRect.top - dropdownHeight;
      if (newTop >= 0) {
        this.renderer.setStyle(dropdownMenuElement, 'top', `${newTop}px`);
      } else {
        // If there's not enough space above either, limit the height and add scrolling
        this.renderer.setStyle(dropdownMenuElement, 'top', '10px');
        this.renderer.setStyle(dropdownMenuElement, 'max-height', `${viewportHeight - 20}px`);
        this.renderer.setStyle(dropdownMenuElement, 'overflow-y', 'auto');
      }
    }
  }

  private removeDropdownFromDOM() {
    this.dropdownMenuElement = null;
  }
}
