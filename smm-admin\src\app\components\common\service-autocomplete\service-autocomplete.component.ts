import { Component, Input, Output, EventEmitter, HostListener, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ServiceLabelComponent } from '../service-label/service-label.component';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';

@Component({
  selector: 'app-service-autocomplete',
  standalone: true,
  imports: [CommonModule, ServiceLabelComponent],
  templateUrl: './service-autocomplete.component.html',
  styleUrl: './service-autocomplete.component.css'
})
export class ServiceAutocompleteComponent {
  @Input() set services(value: SuperGeneralSvRes[]) {
    this._services = value;
    // Reset highlighted index when services change
    this.highlightedIndex = -1;
  }
  get services(): SuperGeneralSvRes[] {
    return this._services;
  }

  private _services: SuperGeneralSvRes[] = [];
  @Input() isVisible: boolean = false;
  @Output() selected = new EventEmitter<SuperGeneralSvRes>();

  // Track the currently highlighted item
  highlightedIndex: number = -1;

  constructor(private elementRef: ElementRef) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Check if the click is outside the autocomplete
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isVisible = false;
    }
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    // Only handle keyboard events when the autocomplete is visible
    if (!this.isVisible || this.services.length === 0) {
      return;
    }

    // Handle arrow down
    if (event.key === 'ArrowDown') {
      this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.services.length - 1);
      event.preventDefault();
    }

    // Handle arrow up
    if (event.key === 'ArrowUp') {
      this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
      event.preventDefault();
    }

    // Handle Enter key
    if (event.key === 'Enter' && this.highlightedIndex >= 0) {
      this.selectService(this.services[this.highlightedIndex]);
      event.preventDefault();
    }

    // Handle Escape key
    if (event.key === 'Escape') {
      this.isVisible = false;
      event.preventDefault();
    }
  }

  selectService(service: SuperGeneralSvRes): void {
    this.selected.emit(service);
    this.isVisible = false;
    this.highlightedIndex = -1;
  }
}
