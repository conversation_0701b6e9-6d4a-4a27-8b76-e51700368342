import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../../icons/icons.module';
import { ToggleSwitchComponent } from '../../../common/toggle-switch/toggle-switch.component';
import { ToastService } from '../../../../core/services/toast.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { NotificationRes } from '../../../../model/response/notification-res.model';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { Subscription } from 'rxjs';
import { DeleteConfirmationComponent } from '../../delete-confirmation/delete-confirmation.component';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    IconsModule,
    ToggleSwitchComponent,
    DeleteConfirmationComponent
  ],
  templateUrl: './notifications.component.html',
  styleUrl: './notifications.component.css'
})
export class NotificationsComponent implements OnInit, OnDestroy {
  popupNotifications: NotificationRes[] = [];
  fixedNotifications: NotificationRes[] = [];
  loading = false;
  errorMessage = '';

  // Content expansion tracking
  expandedContents: { [key: number]: boolean } = {};

  // Delete confirmation
  showDeleteConfirmation = false;
  notificationToDelete: NotificationRes | null = null;
  notificationTypeToDelete: 'popup' | 'fixed' = 'popup';
  isDeleting = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private toastService: ToastService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    // Subscribe to loading state
    this.subscriptions.push(
      this.notificationService.loading$.subscribe(loading => {
        this.loading = loading;
      })
    );

    // Subscribe to popup notifications
    this.subscriptions.push(
      this.notificationService.popupNotifications$.subscribe(notifications => {
        this.popupNotifications = notifications;
      })
    );

    // Subscribe to fixed notifications
    this.subscriptions.push(
      this.notificationService.fixedNotifications$.subscribe(notifications => {
        this.fixedNotifications = notifications;
      })
    );

    // Load notifications
    this.loadNotifications();
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadNotifications(): void {
    console.log('NotificationsComponent - Loading notifications');
    this.errorMessage = '';
    this.notificationService.getAllNotifications().subscribe({
      next: (notifications) => {
        console.log('NotificationsComponent - Notifications loaded successfully:', notifications);
        console.log('NotificationsComponent - Popup notifications count:', this.popupNotifications.length);
        console.log('NotificationsComponent - Fixed notifications count:', this.fixedNotifications.length);

        if (notifications.length === 0) {
          this.errorMessage = 'No notifications were returned from the API.';
        }
      },
      error: (error) => {
        console.error('Error loading notifications:', error);
        this.errorMessage = `Error loading notifications: ${error.message || 'Unknown error'}`;
        this.toastService.showToast('Failed to load notifications', 'error');
      }
    });
  }

  toggleNotificationStatus(notification: NotificationRes): void {
    const newStatus = !notification.show;
    console.log(`NotificationsComponent - Toggling notification ${notification.id} to show=${newStatus}`);

    // Check if this is a Fixed notification being activated and if another Fixed notification is already active
    if (notification.type === 'Fixed' && newStatus === true) {
      const activeFixedNotification = this.fixedNotifications.find(n => n.id !== notification.id && n.show === true);

      if (activeFixedNotification) {
        // Prevent activation and show warning message
        console.log('NotificationsComponent - Cannot activate more than one Fixed notification');
        this.toastService.showToast('Only one Fixed notification can be active at a time', 'warning');

        // Force refresh the notifications to ensure toggle switch returns to original state
        this.loadNotifications();
        return; // Exit early without making the API call
      }
    }

    this.notificationService.toggleNotificationStatus(notification.id, newStatus).subscribe({
      next: (updatedNotification) => {
        console.log('NotificationsComponent - Notification toggled successfully:', updatedNotification);
        const statusMessage = newStatus ? 'activated' : 'deactivated';
        this.toastService.showSuccess(`Notification ${statusMessage} successfully`);
      },
      error: (error) => {
        console.error('Error toggling notification status:', error);
        this.errorMessage = `Error toggling notification status: ${error.message || 'Unknown error'}`;
        this.toastService.showToast('Failed to update notification status', 'error');

        // Force refresh the notifications to ensure toggle switch returns to original state on error
        this.loadNotifications();
      }
    });
  }

  deleteNotification(type: 'popup' | 'fixed', notification: NotificationRes): void {
    console.log(`NotificationsComponent - Preparing to delete notification ${notification.id}`);

    // Set the notification to delete and show the confirmation popup
    this.notificationToDelete = notification;
    this.notificationTypeToDelete = type;
    this.showDeleteConfirmation = true;
  }

  // Close delete confirmation dialog
  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.notificationToDelete = null;
    this.isDeleting = false;
  }

  // Confirm delete notification
  confirmDeleteNotification(): void {
    if (!this.notificationToDelete) {
      return;
    }

    const id = this.notificationToDelete.id;
    this.isDeleting = true;

    console.log(`NotificationsComponent - Deleting notification ${id}`);

    this.notificationService.deleteNotification(id).subscribe({
      next: (response) => {
        console.log('NotificationsComponent - Notification deleted successfully:', response);
        this.toastService.showSuccess('Notification deleted successfully');
        this.closeDeleteConfirmation();
      },
      error: (error) => {
        console.error('Error deleting notification:', error);
        this.errorMessage = `Error deleting notification: ${error.message || 'Unknown error'}`;
        this.toastService.showToast('Failed to delete notification', 'error');
        this.isDeleting = false;
      }
    });
  }

  addNewNotification(): void {
    this.router.navigate(['/panel/settings/interaction/new']);
  }

  addPopupNotification(): void {
    this.router.navigate(['/panel/settings/interaction/new'], {
      queryParams: { type: 'Popup' }
    });
  }

  addFixedNotification(): void {
    this.router.navigate(['/panel/settings/interaction/new'], {
      queryParams: { type: 'Fixed' }
    });
  }

  editNotification(notification: NotificationRes): void {
    console.log(`NotificationsComponent - Editing notification ${notification.id}`);
    this.router.navigate(['/panel/settings/interaction/edit', notification.id]);
  }

  // Add a test notification for debugging
  addTestNotification(): void {
    // Create a test notification
    const testNotification: NotificationRes = {
      id: Date.now(), // Use timestamp as a temporary ID
      content: 'This is a test notification created for debugging purposes.',
      type: 'Popup',
      only_for_customers: false,
      offer_ending_type: 'Never',
      offer_end_date: null,
      show: true,
      title: 'Test Notification'
    };

    // Add it to the popup notifications
    this.popupNotifications = [...this.popupNotifications, testNotification];

    console.log('Added test notification:', testNotification);
    this.toastService.showSuccess('Test notification added');
  }

  getNotificationTypeIcon(type: string): IconName {
    // Map API notification types to icon types
    switch (type) {
      case 'Popup':
        return 'bell' as IconName;
      case 'Fixed':
        return 'thumbtack' as IconName;
      default:
        return 'bell' as IconName;
    }
  }

  getNotificationTypeClass(type: string): string {
    // Map API notification types to CSS classes
    switch (type) {
      case 'Popup':
        return 'bg-blue-100 text-blue-700';
      case 'Fixed':
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  }

  formatDate(dateString: string | null): string {
    if (!dateString || dateString === 'Never') return 'Never';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Helper method to determine if a notification is active
  isNotificationActive(notification: NotificationRes): boolean {
    return notification.show;
  }

  /**
   * Check if content is long enough to need truncation
   * @param content HTML content to check
   * @returns true if content is long enough to need truncation
   */
  isContentLong(content: string): boolean {
    // Simple check based on character length
    // You could implement more sophisticated checks if needed
    return content.length > 100;
  }

  /**
   * Toggle content expansion state for a notification
   * @param id Notification ID
   */
  toggleContentExpansion(id: number): void {
    this.expandedContents[id] = !this.expandedContents[id];
  }
}
