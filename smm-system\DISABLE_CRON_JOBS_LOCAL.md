# Disable Cron Jobs in Local Environment

## 🎯 Overview

Trong môi trường local development, các cron jobs có thể gây ra nhiều vấn đề:
- Tạo ra nhiều log không cần thiết
- Gọi API external services liên tục
- Tiêu tốn tài nguyên hệ thống
- Gây khó khăn trong debugging

Đã implement 2 cách để tắt tất cả cron jobs trong môi trường local.

## 🔧 Cách 1: Tắt Toàn Bộ Scheduling (Khuyến nghị)

### Configuration trong `application-local.yml`:

```yaml
spring:
  # Disable all scheduled tasks in local environment
  task:
    scheduling:
      enabled: false
```

### Ưu điểm:
- ✅ Tắt tất cả scheduled tasks với 1 dòng config
- ✅ Không cần modify code
- ✅ Dễ dàng enable/disable
- ✅ Không ảnh hưởng đến production

### Nhược điểm:
- ❌ Tắt tất cả, kh<PERSON><PERSON> thể chọn lọc từng job

## 🔧 Cách 2: Profile-Based Conditional Scheduling

### Đã thêm `@Profile("!local")` cho tất cả scheduler classes:

1. **TenantScheduler** - Xử lý tenants mới và SSL certificates
2. **GServiceScheduler** - Đồng bộ services từ providers  
3. **SubscriptionScheduler** - Xử lý subscription và auto-renewal
4. **CurrencyScheduler** - Đồng bộ tiền tệ
5. **OrderJob** - Cập nhật trạng thái orders

### Example Implementation:
```java
@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!local") // Disable in local environment
public class TenantScheduler {
    // Scheduler sẽ không được load khi profile = "local"
}
```

### Ưu điểm:
- ✅ Kiểm soát chi tiết từng scheduler
- ✅ Có thể enable lại từng cái nếu cần test
- ✅ Rõ ràng trong code

### Nhược điểm:
- ❌ Phải modify nhiều files
- ❌ Dễ quên khi thêm scheduler mới

## 📊 Danh Sách Các Cron Jobs Đã Tắt

| Scheduler | Frequency | Description | Status |
|-----------|-----------|-------------|---------|
| **TenantScheduler** | 15 phút | Xử lý tenants mới và SSL | ❌ Disabled |
| **GServiceScheduler** | 3 phút | Đồng bộ services từ providers | ❌ Disabled |
| **SubscriptionScheduler** | Hàng ngày + 3 phút | Auto-renewal và expired tenants | ❌ Disabled |
| **CurrencyScheduler** | 12 giờ | Đồng bộ tỷ giá tiền tệ | ❌ Disabled |
| **OrderJob** | 25 phút | Cập nhật trạng thái orders | ❌ Disabled |

## 🚀 Cách Sử Dụng

### Để tắt tất cả cron jobs (Khuyến nghị):
1. Đảm bảo `spring.profiles.active=local` trong application.yml
2. Cấu hình `spring.task.scheduling.enabled=false` đã có trong application-local.yml
3. Restart application

### Để enable lại một số jobs cụ thể:
1. Comment out `@Profile("!local")` trong scheduler class cần enable
2. Restart application

### Để enable tất cả jobs:
1. Set `spring.task.scheduling.enabled=true` hoặc comment out dòng này
2. Restart application

## 🧪 Verification

### Kiểm tra logs khi start application:

**Khi disabled:**
```
INFO  - No scheduled tasks found (scheduling disabled)
```

**Khi enabled:**
```
INFO  - Starting scheduled task to process new tenants
INFO  - Running scheduled service synchronization  
INFO  - Starting scheduled task: Update expired tenants
```

### Test Commands:
```bash
# Check if schedulers are loaded
curl http://localhost:8080/actuator/scheduledtasks

# Check application properties
curl http://localhost:8080/actuator/configprops | grep scheduling
```

## ⚠️ Lưu Ý Quan Trọng

1. **Production Environment**: Đảm bảo các cron jobs vẫn hoạt động bình thường trong production
2. **Testing**: Nếu cần test scheduler logic, có thể call method trực tiếp thay vì chờ schedule
3. **Memory**: Tắt schedulers giúp giảm memory usage trong local development
4. **Database**: Một số jobs có thể modify database, tắt chúng giúp tránh data inconsistency

## 🔄 Rollback

Để enable lại tất cả cron jobs:

1. **Cách 1**: Set `spring.task.scheduling.enabled=true` trong application-local.yml
2. **Cách 2**: Remove `@Profile("!local")` từ các scheduler classes
3. Restart application

## ✅ Best Practices

1. **Luôn tắt cron jobs trong local development** để tránh side effects
2. **Sử dụng Cách 1** (config-based) cho đơn giản
3. **Document rõ ràng** khi thêm scheduler mới
4. **Test schedulers** bằng cách call method trực tiếp trong unit tests
5. **Monitor logs** để đảm bảo schedulers không chạy trong local

Với cấu hình này, môi trường local sẽ sạch sẽ và không có background jobs chạy liên tục! 🎉
