import { Pipe, PipeTransform } from '@angular/core';
import { CurrencyService } from '../services/currency.service';

@Pipe({
  name: 'currencyConvert',
  standalone: true
})
export class CurrencyConvertPipe implements PipeTransform {
  constructor(private currencyService: CurrencyService) {}

  transform(value: number | undefined | null, showSymbol: boolean = true): string {
    if (value === null || value === undefined || isNaN(value as number)) {
      return '';
    }

    const currency = this.currencyService.currencyValue;
    if (!currency || !currency.exchange_rate) {
      // If no currency is set, just format with default $ symbol
      const formattedValue = value.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 3,
        useGrouping: true
      }).replace(/,/g, '.');

      return showSymbol ? `${formattedValue} $` : formattedValue;
    }

    // Convert the value using the exchange rate
    const convertedValue = value * currency.exchange_rate;

    // Format the number with thousand separators
    const formattedValue = convertedValue.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 3,
      useGrouping: true
    }).replace(/,/g, '.');

    // Add the currency symbol if requested
    if (showSymbol && currency.symbol) {
      return `${formattedValue} ${currency.symbol}`;
    }

    return formattedValue;
  }
}
