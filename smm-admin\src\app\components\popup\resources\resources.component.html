<div class="overlay-black" >
    <div class="service-container">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Add Service</h2>
          <button type="button" class="close-button" (click)="onClose()">
            <fa-icon [icon]="['fas', 'xmark']" class="text-gray-700"></fa-icon>
          </button>
        </div>


        <div class="service-list">
          <div *ngFor="let service of resources"
               class="service-item hover:bg-gray-100 cursor-pointer"
               (click)="onServiceClick(service)">
            <div class="service-content">
              <div class="icon-wrapper">
                <fa-icon [icon]="service.iconArray" [ngClass]="service.iconClass"></fa-icon>
              </div>

              <div class="text-content">
                <h3 class="title">{{service.title}}</h3>
                <p class="description">{{service.description}}</p>
              </div>
            </div>

            <div class="right-icon-wrapper">
              <fa-icon [icon]="['fas', 'angle-right']" class="right-icon"></fa-icon>
            </div>
          </div>
        </div>
      </div>
</div>
