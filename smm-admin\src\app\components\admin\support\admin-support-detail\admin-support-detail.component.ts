import { Component, EventEmitter, Input, OnInit, OnDestroy, Output, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../../icons/icons.module';
import { TicketRes } from '../../../../model/response/ticket-res.model';
import { TicketService } from '../../../../core/services/ticket.service';
import { TicketStatusComponent } from '../../../common/ticket-status/ticket-status.component';
import { TicketStatusSelectComponent } from '../../../common/ticket-status-select/ticket-status-select.component';
import { LoadingComponent } from '../../../common/loading/loading.component';

@Component({
  selector: 'app-admin-support-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    TicketStatusComponent,
    TicketStatusSelectComponent,
    LoadingComponent
  ],
  templateUrl: './admin-support-detail.component.html',
  styleUrl: './admin-support-detail.component.css'
})
export class AdminSupportDetailComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() ticketId: number | null = null;
  @Output() close = new EventEmitter<void>();

  ticket: TicketRes | null = null;
  isLoading = false;
  replyMessage = '';
  statusOptions = ['Pending', 'Accept', 'Closed', 'Solved'];
  selectedStatus: string | null = null; // Initialize as null to prevent default selection
  firstRepliedBy: string = 'customer'; // Default to customer as the initial sender

  constructor(
    private ticketService: TicketService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (this.ticketId) {
      this.loadTicket();
    }
  }

  ngAfterViewInit(): void {
    // Run change detection once after view initialization
    this.cdr.detectChanges();
  }

  loadTicket(): void {
    if (!this.ticketId) return;

    this.isLoading = true;
    this.ticketService.getTicketById(this.ticketId).subscribe({
      next: (ticket) => {
        this.ticket = ticket;
        this.selectedStatus = ticket.status;

        // Set the firstRepliedBy to 'customer' by default (for the initial message)
        this.firstRepliedBy = 'customer';

        // If there are replies, check if we need to update firstRepliedBy
        if (ticket.replies && ticket.replies.length > 0) {
          // If the first reply is not from 'customer', we'll use that as our firstRepliedBy
          if (ticket.replies[0].replied_by !== 'customer') {
            this.firstRepliedBy = ticket.replies[0].replied_by;
          }
        }

        this.isLoading = false;
        // Run change detection after data is loaded
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading ticket:', error);
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }

  sendReply(): void {
    if (!this.ticketId || !this.replyMessage.trim()) return;

    this.isLoading = true;
    this.ticketService.replyToTicket(this.ticketId, this.replyMessage).subscribe({
      next: (reply) => {
        // Add the reply to the ticket
        if (this.ticket && this.ticket.replies) {
          // If this is the first reply and it's not from the customer, update firstRepliedBy
          if (this.ticket.replies.length === 0 && reply.replied_by !== 'customer') {
            this.firstRepliedBy = reply.replied_by;
          }
          this.ticket.replies.push(reply);
        } else if (this.ticket) {
          // If this is the first reply and it's not from the customer, update firstRepliedBy
          if (reply.replied_by !== 'customer') {
            this.firstRepliedBy = reply.replied_by;
          }
          this.ticket.replies = [reply];
        }

        this.replyMessage = '';
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error sending reply:', error);
        this.isLoading = false;
      }
    });
  }

  updateStatus(): void {
    if (!this.ticketId || !this.selectedStatus || !this.ticket) return;

    this.isLoading = true;
    this.ticketService.updateTicketStatus(this.ticketId, this.selectedStatus).subscribe({
      next: (updatedTicket) => {
        console.log(`Updated ticket ${this.ticketId} status to ${this.selectedStatus}`);
        if (this.ticket) this.ticket.status = updatedTicket.status;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error updating ticket status:', error);
        this.isLoading = false;
      }
    });
  }

  getFormattedDate(dateString: string): string {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes} ${year}-${month}-${day}`;
  }

  ngOnDestroy(): void {
    // Clean up resources when component is destroyed
  }
}
