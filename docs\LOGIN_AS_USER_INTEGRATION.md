# Login as User Integration

## Tổng quan

Tính năng "Login as User" cho phép admin đăng nhập vào hệ thống với tư cách là một user cụ thể mà không cần biết mật khẩu của user đó. Tính năng này được tích hợp giữa `@smm-admin` (admin panel) và `@smm-dashboard` (user dashboard).

## Luồng hoạt động

```mermaid
sequenceDiagram
    participant Admin as Admin Panel
    participant API as Backend API
    participant Dashboard as User Dashboard
    participant Browser as Browser Tab

    Admin->>Admin: Click "Login as User" in menu
    Admin->>API: POST /v1/access/create-token {userId, tenantId}
    API->>API: Validate tenant access & user permissions
    API->>Admin: Return {accessToken, clientId, refreshToken}
    Admin->>Browser: Open new tab with domain + query params
    Browser->>Dashboard: Load dashboard with tokens in URL
    Dashboard->>Dashboard: Auto-detect tokens & perform login
    Dashboard->>Dashboard: Clean URL & redirect to /dashboard/new
```

## Backend Implementation

### 1. API Endpoint

**File:** `smm-system/src/main/java/tndung/vnfb/smm/controller/AccessController.java`

```java
@PostMapping("/create-token")
@PreAuthorize("hasAnyRole('ROLE_PANEL')")
public ApiResponseEntity<TokenPairDto> createTokenForUser(@RequestBody @Valid CreateTokenForUserReq request) {
    TokenPairDto tokenPair = accessService.createTokenForUser(request.getUserId(), request.getTenantId());
    return ApiResponseEntity.success(tokenPair);
}
```

### 2. Service Implementation

**File:** `smm-system/src/main/java/tndung/vnfb/smm/service/impl/AccessServiceImpl.java`

Kiểm tra:
- Tenant context có được thiết lập không
- User có thuộc tenant hiện tại không (qua bảng `user_tenant`)
- User có quyền truy cập tenant được chỉ định không

### 3. Security Validations

- **@TenantAccessCheck**: Annotation AOP để kiểm tra quyền truy cập tenant
- **TenantContext**: Kiểm tra tenant hiện tại
- **UserTenantRepository**: Kiểm tra user có thuộc tenant không

## Frontend Implementation

### 1. Admin Panel (@smm-admin)

**File:** `smm-admin/src/app/components/admin/users/admin-users.component.ts`

```typescript
private loginAsUser(userId: number): void {
  const currentTenant = this.tenantService.currentTenantValue;
  const request: CreateTokenForUserReq = {
    userId: userId,
    tenantId: currentTenant.id
  };

  this.accessAdminService.createTokenForUser(request).subscribe({
    next: (tokenResponse) => {
      const targetUrl = this.buildTargetUrl(currentTenant.domain, tokenResponse);
      window.open(targetUrl, '_blank');
    }
  });
}
```

### 2. User Dashboard (@smm-dashboard)

**File:** `smm-dashboard/src/app/core/services/auto-login.service.ts`

```typescript
checkAndPerformAutoLogin(): void {
  const urlParams = new URLSearchParams(window.location.search);
  const accessToken = urlParams.get('accessToken');
  const clientId = urlParams.get('clientId');
  const refreshToken = urlParams.get('refreshToken');

  if (accessToken && clientId && refreshToken) {
    this.performAutoLogin(accessToken, clientId, refreshToken);
  }
}
```

## URL Format

Khi admin click "Login as User", một tab mới sẽ mở với URL:

```
https://tenant-domain.com?accessToken=<token>&clientId=<client>&refreshToken=<refresh>
```

### Ví dụ:
```
https://autovnfb.dev?accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&clientId=550e8400-e29b-41d4-a716-446655440000&refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Database Tables

### user_tenant
Lưu trữ mối quan hệ giữa user và tenant:
```sql
CREATE TABLE user_tenant (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_tenant (user_id, tenant_id)
);
```

### user_tenant_access
Lưu trữ quyền truy cập của user đối với các tenant:
```sql
CREATE TABLE user_tenant_access (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_tenant_access (user_id, tenant_id)
);
```

## Error Handling

### Backend Errors
- `PANEL_NOT_CONFIGURED`: Không có tenant trong context
- `TENANT_ACCESS_DENIED`: User không thuộc tenant hoặc không có quyền truy cập
- `USER_NOT_FOUND`: User không tồn tại

### Frontend Errors
- Toast notification hiển thị lỗi chi tiết
- Fallback về trang login nếu auto-login thất bại
- Loading state trong quá trình xử lý

## Security Considerations

1. **Token Validation**: Tokens được validate ở backend trước khi tạo
2. **Tenant Isolation**: Chỉ có thể tạo token cho user thuộc tenant hiện tại
3. **URL Cleaning**: Query parameters được xóa sau khi auto-login thành công
4. **Role-based Access**: Chỉ user có role `ROLE_PANEL` mới có thể sử dụng API
5. **Audit Logging**: Tất cả hoạt động được ghi log để audit

## Testing

### Manual Testing
1. Mở admin panel và navigate đến Users management
2. Click vào menu action của một user
3. Click "Login as User"
4. Verify tab mới mở với URL chứa tokens
5. Verify auto-login thành công và redirect đến dashboard

### Test File
Sử dụng `smm-dashboard/examples/auto-login-test.html` để test các scenario khác nhau.

## Troubleshooting

### Common Issues

1. **"No tenant information available"**
   - Kiểm tra tenant service có hoạt động không
   - Verify token hiện tại có chứa tenant info

2. **"Failed to login as user"**
   - Kiểm tra user có thuộc tenant hiện tại không
   - Verify API permissions và role

3. **Auto-login không hoạt động**
   - Kiểm tra URL có chứa đủ 3 parameters không
   - Verify AutoLoginService được inject đúng cách

### Debug Steps

1. Check browser console cho errors
2. Verify network requests trong DevTools
3. Check backend logs cho validation errors
4. Verify database records trong user_tenant table

## Files Modified/Created

### Backend
- `AccessController.java` - Added create-token endpoint
- `AccessService.java` - Added createTokenForUser method
- `AccessServiceImpl.java` - Implementation with security checks
- `CreateTokenForUserReq.java` - Request DTO
- `UserTenantRepository.java` - Added tenant validation methods

### Frontend (Admin)
- `admin-users.component.ts` - Added loginAsUser implementation
- `access-admin.service.ts` - Service for API calls
- `create-token-for-user-req.model.ts` - Request model
- `token-pair-res.model.ts` - Response model

### Frontend (Dashboard)
- `auto-login.service.ts` - Auto-login logic
- `app.component.ts` - Integration with auto-login service

### Documentation
- `API_CREATE_TOKEN_FOR_USER.md` - API documentation
- `auto-login-test.html` - Test scenarios
- `LOGIN_AS_USER_INTEGRATION.md` - This file
