<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-url {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Auto Login Test for SMM Dashboard</h1>
    
    <div class="warning">
        <strong>Note:</strong> This is a test page to simulate the auto-login functionality. 
        In production, these URLs would be generated by the admin panel when clicking "Login as User".
    </div>

    <div class="test-section">
        <h2>Test Scenario 1: Valid Tokens</h2>
        <p>This simulates a successful login with valid tokens from the admin panel.</p>
        <div class="test-url" id="testUrl1">
            https://yourdomain.com?accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.example&clientId=test-client-123&refreshToken=refresh-token-example
        </div>
        <button onclick="testAutoLogin(1)">Test Auto Login</button>
    </div>

    <div class="test-section">
        <h2>Test Scenario 2: Missing Parameters</h2>
        <p>This simulates what happens when some parameters are missing.</p>
        <div class="test-url" id="testUrl2">
            https://yourdomain.com?accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjMiLCJ1c2VyX25hbWUiOiJ0ZXN0dXNlciJ9.example&clientId=test-client-123
        </div>
        <button onclick="testAutoLogin(2)">Test Missing RefreshToken</button>
    </div>

    <div class="test-section">
        <h2>Test Scenario 3: Normal Dashboard Access</h2>
        <p>This simulates normal dashboard access without auto-login parameters.</p>
        <div class="test-url" id="testUrl3">
            https://yourdomain.com/dashboard/new
        </div>
        <button onclick="testAutoLogin(3)">Test Normal Access</button>
    </div>

    <div class="test-section">
        <h2>How It Works</h2>
        <ol>
            <li><strong>Admin Panel:</strong> When clicking "Login as User", the admin panel calls the API to create tokens for the user</li>
            <li><strong>URL Generation:</strong> The admin panel constructs a URL with the tokens as query parameters</li>
            <li><strong>New Tab:</strong> The URL opens in a new tab pointing to the tenant's domain</li>
            <li><strong>Auto Login:</strong> The dashboard detects the tokens in the URL and automatically logs the user in</li>
            <li><strong>Redirect:</strong> After successful auto-login, the user is redirected to /dashboard/new</li>
            <li><strong>Clean URL:</strong> The query parameters are removed from the URL for security</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>API Integration</h2>
        <p><strong>Admin Panel API Call:</strong></p>
        <pre>
POST /v1/access/create-token
{
  "userId": 123,
  "tenantId": "tenant-uuid-optional"
}

Response:
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "clientId": "client-uuid"
  }
}
        </pre>
    </div>

    <script>
        function testAutoLogin(scenario) {
            let url;
            switch(scenario) {
                case 1:
                    url = document.getElementById('testUrl1').textContent.trim();
                    break;
                case 2:
                    url = document.getElementById('testUrl2').textContent.trim();
                    break;
                case 3:
                    url = document.getElementById('testUrl3').textContent.trim();
                    break;
            }
            
            // Replace yourdomain.com with localhost for testing
            url = url.replace('https://yourdomain.com', 'http://localhost:4200');
            
            console.log('Opening URL:', url);
            window.open(url, '_blank');
        }

        // Display current URL parameters for debugging
        function displayCurrentParams() {
            const params = new URLSearchParams(window.location.search);
            const accessToken = params.get('accessToken');
            const clientId = params.get('clientId');
            const refreshToken = params.get('refreshToken');
            
            if (accessToken || clientId || refreshToken) {
                const paramDiv = document.createElement('div');
                paramDiv.className = 'test-section';
                paramDiv.innerHTML = `
                    <h2>Current URL Parameters</h2>
                    <p><strong>Access Token:</strong> ${accessToken || 'Not present'}</p>
                    <p><strong>Client ID:</strong> ${clientId || 'Not present'}</p>
                    <p><strong>Refresh Token:</strong> ${refreshToken || 'Not present'}</p>
                `;
                document.body.insertBefore(paramDiv, document.body.firstChild);
            }
        }

        // Check for parameters on page load
        displayCurrentParams();
    </script>
</body>
</html>
