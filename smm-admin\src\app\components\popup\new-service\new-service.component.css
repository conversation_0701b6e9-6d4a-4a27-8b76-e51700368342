.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  padding: 20px;
  overflow-y: auto; /* Enable scrolling on the overlay */
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.close-btn {
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  transform: rotate(90deg);
}

.section {
  @apply w-full;
}

.section-title {
  @apply text-sm font-medium uppercase mb-2 text-gray-700;
}

.service-type-box {
  @apply flex justify-between items-center p-4 rounded-lg;
}

.change-btn {
  @apply text-[var(--primary)] text-sm font-medium px-4 py-1.5 border border-[var(--primary)] rounded-lg hover:bg-[var(--primary)] hover:text-white transition-colors;
}

.change-btn-dark {
  @apply text-sm font-medium py-1.5 transition-colors;
}

.field-container {
  @apply w-full;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent;
}

.form-input-dark {
  @apply w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0095f6] focus:border-transparent;
}

/* Light-themed input styles for toggle options */
.toggle-input {
  @apply w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.dropdown-container {
  @apply w-full;
}

.toggle-section {
  @apply flex flex-col gap-3;
}

.toggle-item {
  @apply flex justify-between items-center p-3 bg-[#f5f7fc] rounded-lg;
}

.toggle-label {
  @apply text-sm font-medium;
}

.input-with-checkbox {
  @apply flex items-center;
}

.input-with-checkbox .form-input {
  @apply rounded-r-none;
}

.checkbox-container {
  @apply flex items-center justify-center w-10 h-[42px] bg-[#f5f7fc] border border-l-0 border-gray-200 rounded-r-lg;
}

.checkbox {
  @apply w-4 h-4 text-[var(--primary)] rounded focus:ring-[var(--primary)];
}

.save-btn {
  @apply w-full py-3 bg-[var(--primary)] text-white font-medium rounded-lg hover:bg-[var(--primary-hover)] transition-colors;
}

.quality-btn {
  @apply px-4 py-2 bg-white text-gray-700 rounded-lg border border-gray-300 hover:bg-gray-100 transition-colors;
}

.quality-btn-active {
  @apply px-4 py-2 bg-[#0095f6] text-white rounded-lg border border-[#0095f6] transition-colors;
}

/* Layout container styles */
.service-layout-container {
  width: 700px;
  margin: 20px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


/* Remove spinner arrows from number inputs */
.no-spinner::-webkit-inner-spin-button,
.no-spinner::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
.no-spinner {
  -moz-appearance: textfield;
}

/* Custom checkbox style */
.checkbox-label {
  appearance: none;
  background-color: #fff;
  margin: 0;
  font: inherit;
  color: currentColor;
  width: 1.5em;
  height: 1.5em;
  border: 1px solid #ccc;
  border-radius: 0.25em;
  transform: translateY(-0.075em);
  display: grid;
  place-content: center;
  cursor: pointer;
}

.checkbox-label::before {
  content: "";
  width: 0.75em;
  height: 0.75em;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em var(--primary, #0095f6);
  transform-origin: center;
  clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
}

.checkbox-label:checked::before {
  transform: scale(1);
  background-color: white;
}

.checkbox-label:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
