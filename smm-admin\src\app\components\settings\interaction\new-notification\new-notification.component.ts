import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { Router, ActivatedRoute } from '@angular/router';
import { ToastService } from '../../../../core/services/toast.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { QuillEditorComponent } from 'ngx-quill';

import { NotificationReq, NotificationRes } from '../../../../model/response/notification-res.model';
import { NotificationService } from '../../../../core/services/notification.service';
import { IconsModule } from '../../../../icons/icons.module';
import { NotificationHomeComponent } from '../../../notification-home/notification-home.component';
import { NotificationPopupContentComponent } from '../../../popup/notification-popup-content/notification-popup-content.component';



@Component({
  selector: 'app-new-notification',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconsModule,
    QuillEditorComponent,
    NotificationPopupContentComponent,
    NotificationHomeComponent
],
  templateUrl: './new-notification.component.html',
  styleUrl: './new-notification.component.css'
})
export class NewNotificationComponent implements OnInit {
  notificationForm: FormGroup;
  isSubmitting = false;
  showPreview = false;
  isBrowser: boolean;
  htmlMode = false;
  showFullForm = true; // Controls whether to show full form or simplified version
  hasTypeFromQuery = false; // Controls whether type was set via query param

  // Edit mode properties
  isEditMode = false;
  notificationId: number | null = null;
  notification: NotificationRes | null = null;
  pageTitle = 'Create New Notification';
  submitButtonText = 'Create Notification';

  // Quill editor configuration
  quillModules = {
    toolbar: [
       ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
  ['blockquote', 'code-block'],
  ['link', 'image'],

  [{ 'header': 1 }, { 'header': 2 }],               // custom button values
  [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'list': 'check' }],
  [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
  [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
  [{ 'direction': 'rtl' }],                         // text direction

  [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

  [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
  [{ 'font': [] }],
  [{ 'align': [] }],

  ['clean']
    ],
    clipboard: {
      matchVisual: false
    }
  };

  // Track calculated end date
  calculatedEndDate: Date | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private sanitizer: DomSanitizer,
    private notificationService: NotificationService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.notificationForm = this.fb.group({
      title: [''], // Will add validation conditionally
      content: ['', [Validators.required]],
      autoDismiss: [false],
      dismissHours: [24], // Default 24 hours
      type: ['Popup'], // Default to Popup type
      expiryOption: ['never'],
      expiryDate: [''],
      expiryTime: [''],
      timePeriod: ['12'] // Default time period in hours
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode by looking for an ID in the route
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id && !isNaN(Number(id))) {
        this.notificationId = Number(id);
        this.isEditMode = true;
        this.submitButtonText = 'Update Notification';
        this.loadNotificationData(this.notificationId);
      }
    });

    // Check for query params to set notification type and form mode
    this.route.queryParams.subscribe(queryParams => {
      if (queryParams['type']) {
        const type = queryParams['type'];
        this.notificationForm.patchValue({ type: type });
        this.hasTypeFromQuery = true; // Mark that type was set via query param

        // Set form mode and page title based on type
        if (type === 'Fixed') {
          this.showFullForm = false; // Show simplified form for Fixed
          this.pageTitle = 'New Fixed Notification';
          this.updateTitleValidation(false); // Remove title validation for Fixed
        } else {
          this.showFullForm = true; // Show full form for Popup
          this.pageTitle = 'New Popup Notification';
          this.updateTitleValidation(true); // Add title validation for Popup
        }
      } else {
        this.hasTypeFromQuery = false; // No type from query param
        this.pageTitle = 'New Notification'; // Default title
      }
    });

    // Initialize form with default values
    if (this.isBrowser) {
      // Reset HTML mode to ensure we start in Quill mode
      this.htmlMode = false;

      // Set a default value for the content field to ensure Quill initializes properly
      setTimeout(() => {
        // Force a re-render of the editor
        const content = this.notificationForm.get('content')?.value || '';
        this.notificationForm.get('content')?.setValue(content);

        // Add a class to the Quill editor container to ensure it takes the full width
        const quillEditorElement = document.querySelector('.ql-container');
        if (quillEditorElement) {
          quillEditorElement.classList.add('full-width-editor');
        }
      }, 100);

      // Subscribe to expiryOption changes to handle the time period calculation
      this.notificationForm.get('expiryOption')?.valueChanges.subscribe(value => {
        if (value === 'period') {
          // When "After some time" is selected, calculate the end date
          this.onTimePeriodChange();
        }
      });

      // Subscribe to type changes to handle title field behavior
      this.notificationForm.get('type')?.valueChanges.subscribe(type => {
        if (type === 'Fixed') {
          // When type is Fixed, clear the title field, disable it, and remove validation
          this.notificationForm.get('title')?.setValue('');
          this.notificationForm.get('title')?.disable();
          this.notificationForm.get('title')?.clearValidators();
          this.notificationForm.get('title')?.updateValueAndValidity();
        } else {
          // When type is Popup, re-enable the title field and add back validation
          this.notificationForm.get('title')?.enable();
          this.notificationForm.get('title')?.setValidators([Validators.required]);
          this.notificationForm.get('title')?.updateValueAndValidity();
        }
      });

      // Check initial type value to set correct title field state
      const initialType = this.notificationForm.get('type')?.value;
      if (initialType === 'Fixed') {
        this.notificationForm.get('title')?.setValue('');
        this.notificationForm.get('title')?.disable();
        this.notificationForm.get('title')?.clearValidators();
        this.notificationForm.get('title')?.updateValueAndValidity();
      }
    }
  }

  /**
   * Load notification data for editing
   */
  loadNotificationData(id: number): void {
    console.log(`Loading notification data for ID: ${id}`);

    // Use the NotificationService to get the notification by ID
    this.notificationService.getNotificationById(id).subscribe({
      next: (notification) => {
        console.log('Notification loaded successfully:', notification);

        if (notification && notification.id) {
          this.notification = notification;
          this.populateForm(notification);

          // Disable the type control in edit mode
          this.notificationForm.get('type')?.disable();
        } else {
          console.error('Invalid notification data:', notification);
          this.toastService.showError('Failed to load notification data');
          this.router.navigate(['/panel/settings/interaction']);
        }
      },
      error: (error) => {
        console.error('Error loading notification:', error);
        this.toastService.showError('Failed to load notification data');
        this.router.navigate(['/panel/settings/interaction']);
      }
    });
  }

  /**
   * Populate the form with notification data
   */
  populateForm(notification: NotificationRes): void {
    console.log('Populating form with notification:', notification);

    try {
      // Determine the expiry option based on offer_ending_type
      let expiryOption = 'never';
      let expiryDate = '';
      let expiryTime = '';
      let timePeriod = '12';

      if (notification.offer_ending_type === 'Period' && notification.offer_end_date) {
        expiryOption = 'date';
        const date = new Date(notification.offer_end_date);
        expiryDate = this.formatDateForInput(date);
        expiryTime = this.formatTimeForInput(date);
      } else if (notification.offer_ending_type === 'After_Some_Time' && notification.offer_end_date) {
        expiryOption = 'period';
        // Calculate hours from now to the end date
        const endDate = new Date(notification.offer_end_date);
        const now = new Date();
        const diffHours = Math.round((endDate.getTime() - now.getTime()) / (1000 * 60 * 60));
        timePeriod = Math.max(1, diffHours).toString(); // Ensure it's at least 1 hour
        this.calculatedEndDate = endDate;
      }

      // Update the form values
      this.notificationForm.patchValue({
        title: notification.title || '',
        content: notification.content || '',
        onlyForCustomers: notification.only_for_customers || false,
        type: notification.type || 'Popup',
        expiryOption: expiryOption,
        expiryDate: expiryDate,
        expiryTime: expiryTime,
        timePeriod: timePeriod,
        autoDismiss: notification.auto_dismiss || false,
        dismissHours: notification.dismiss_hours || 24
      });

      // Set form mode and page title based on notification type in edit mode
      if (notification.type === 'Fixed') {
        this.showFullForm = false; // Show simplified form for Fixed
        this.pageTitle = 'Edit Fixed Notification';
        this.updateTitleValidation(false); // Remove title validation for Fixed
      } else {
        this.showFullForm = true; // Show full form for Popup
        this.pageTitle = 'Edit Popup Notification';
        this.updateTitleValidation(true); // Add title validation for Popup
      }

      // If the notification type is Fixed, handle the title field
      if (notification.type === 'Fixed') {
        // Clear the title field, disable it, and remove validation
        this.notificationForm.get('title')?.setValue('');
        this.notificationForm.get('title')?.disable();
        this.notificationForm.get('title')?.clearValidators();
        this.notificationForm.get('title')?.updateValueAndValidity();
      } else {
        // Ensure title field is enabled and has validation for Popup type
        this.notificationForm.get('title')?.enable();
        this.notificationForm.get('title')?.setValidators([Validators.required]);
        this.notificationForm.get('title')?.updateValueAndValidity();
      }

      console.log('Form populated successfully');
    } catch (error) {
      console.error('Error populating form:', error);
      this.toastService.showError('Error populating form with notification data');
    }
  }

  /**
   * Format a date for the date input field (YYYY-MM-DD)
   */
  formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Format a time for the time input field (HH:MM)
   */
  formatTimeForInput(date: Date): string {
    return date.toTimeString().substring(0, 5);
  }

  /**
   * Calculate the offer_end_date based on the time period input
   */
  onTimePeriodChange(): void {
    const timePeriod = this.notificationForm.get('timePeriod')?.value;
    if (timePeriod && !isNaN(Number(timePeriod))) {
      // Calculate the end date based on the current time plus the specified hours
      const hours = Number(timePeriod);
      const endDate = new Date();
      endDate.setHours(endDate.getHours() + hours);

      // Store the calculated end date
      this.calculatedEndDate = endDate;

      console.log(`Calculated end date: ${endDate.toISOString()} (${hours} hours from now)`);
    }
  }

  goBack(): void {
    this.router.navigate(['/panel/settings/interaction']);
  }



  togglePreview(): void {
    this.showPreview = !this.showPreview;
  }

  updateTitleValidation(isRequired: boolean): void {
    const titleControl = this.notificationForm.get('title');
    if (titleControl) {
      if (isRequired) {
        titleControl.setValidators([Validators.required]);
        titleControl.enable();
      } else {
        titleControl.clearValidators();
        titleControl.setValue(''); // Clear title value for Fixed type
        titleControl.disable();
      }
      titleControl.updateValueAndValidity();
    }
  }

  getSanitizedContent(): SafeHtml {
    if (!this.isBrowser) {
      return '';
    }
    const content = this.notificationForm.get('content')?.value || 'Notification content will appear here.';
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }

  toggleHtmlMode(): void {
    // Store the current content
    const currentContent = this.notificationForm.get('content')?.value || '';

    // Toggle the mode immediately
    this.htmlMode = !this.htmlMode;

    // Force change detection and update the view
    setTimeout(() => {
      // Set the content back to ensure it's preserved across mode changes
      this.notificationForm.get('content')?.setValue(currentContent);

      // If switching to HTML mode, focus the textarea
      if (this.htmlMode) {
        const textarea = document.querySelector('.html-textarea') as HTMLTextAreaElement;
        if (textarea) {
          textarea.focus();
        }
      }
    }, 50);
  }

  onSubmit(): void {
    if (this.notificationForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.notificationForm.controls).forEach(key => {
        const control = this.notificationForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    // Get form values - use getRawValue() to include disabled controls (like the type field in edit mode)
    const formValues = this.notificationForm.getRawValue();

    // Determine offer_ending_type and offer_end_date based on expiryOption
    let offer_ending_type: 'Never' | 'After_Some_Time' | 'Period' = 'Never';
    let offer_end_date: string | null = null;

    if (formValues.expiryOption === 'date') {
      offer_ending_type = 'Period';
      // Combine date and time
      if (formValues.expiryDate && formValues.expiryTime) {
        const dateStr = formValues.expiryDate;
        const timeStr = formValues.expiryTime;
        const dateTime = new Date(`${dateStr}T${timeStr}`);
        offer_end_date = dateTime.toISOString();
      }
    } else if (formValues.expiryOption === 'period') {
      offer_ending_type = 'After_Some_Time';
      // Use the calculated end date
      if (this.calculatedEndDate) {
        offer_end_date = this.calculatedEndDate.toISOString();
      }
    }

    // For Fixed notifications, ensure title is empty
    const title = formValues.type === 'Fixed' ? '' : (formValues.title || '');

    // Prepare notification data for API
    const notificationReq: NotificationReq = {
      content: formValues.content || '',
      type: formValues.type || 'Popup',
      only_for_customers: formValues.onlyForCustomers || false,
      offer_ending_type: offer_ending_type,
      offer_end_date: offer_end_date || undefined,
      show: this.notification?.show || true,
      title: title,
      auto_dismiss: formValues.autoDismiss || false,
      dismiss_hours: formValues.dismissHours || 24
    };

    console.log('Submitting notification:', notificationReq);

    // Check if this is a Fixed notification being activated
    if (notificationReq.type === 'Fixed' && notificationReq.show === true) {
      // Get the current value of fixedNotifications$ instead of subscribing to it
      const fixedNotifications = this.notificationService['_fixedNotifications$']?.value || [];

      // Check if there's an active fixed notification (excluding the current one if in edit mode)
      const activeFixedNotification = fixedNotifications.some(notification =>
        notification.show && (!this.isEditMode || !this.notificationId || notification.id !== this.notificationId)
      );

      if (activeFixedNotification) {
        // Prevent activation and show warning message
        console.log('NewNotificationComponent - Cannot activate more than one Fixed notification');
        this.toastService.showToast('Only one Fixed notification can be active at a time', 'warning');
        this.isSubmitting = false;
        return;
      } else {
        // Proceed with create/update
        this.proceedWithSubmission(notificationReq);
      }
    } else {
      // Not a Fixed notification or not being activated, proceed normally
      this.proceedWithSubmission(notificationReq);
    }
  }

  /**
   * Proceed with submission after validation
   */
  private proceedWithSubmission(notificationReq: NotificationReq): void {
    if (this.isEditMode && this.notificationId) {
      // Update existing notification
      this.updateNotification(this.notificationId, notificationReq);
    } else {
      // Create new notification
      this.createNotification(notificationReq);
    }
  }

  /**
   * Create a new notification
   */
  createNotification(notificationReq: NotificationReq): void {
    // Use the NotificationService to create the notification
    this.notificationService.createNotification(notificationReq).subscribe({
      next: (response: any) => {
        this.isSubmitting = false;
        console.log('Notification created successfully:', response);

        // Show success message
        this.toastService.showSuccess('Notification created successfully');

        // Navigate back to the notifications list
        this.router.navigate(['/panel/settings/interaction']);
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error creating notification:', error);

        // Show error message
        this.toastService.showError('Failed to create notification. Please try again.');
      }
    });
  }

  /**
   * Update an existing notification
   */
  updateNotification(id: number, notificationReq: NotificationReq): void {
    // Use the NotificationService to update the notification
    this.notificationService.updateNotification(id, notificationReq).subscribe({
      next: (response: any) => {
        this.isSubmitting = false;
        console.log('Notification updated successfully:', response);

        // Show success message
        this.toastService.showSuccess('Notification updated successfully');

        // Navigate back to the notifications list
        this.router.navigate(['/panel/settings/interaction']);
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error updating notification:', error);

        // Show error message
        this.toastService.showError('Failed to update notification. Please try again.');
      }
    });
  }
}
