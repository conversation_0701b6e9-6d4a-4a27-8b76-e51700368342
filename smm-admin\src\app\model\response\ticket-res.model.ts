export interface TicketReply {
  content: string;
  replied_by: string;
  created_at: string;
}

export interface TicketRes {
  id: number;
  description: string;
  subject: string;
  status: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  replies?: TicketReply[];
}

export interface TicketPageRes {
  content: TicketRes[];
  pageable: {
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    offset: number;
    page_number: number;
    page_size: number;
    unpaged: boolean;
    paged: boolean;
  };
  last: boolean;
  total_elements: number;
  total_pages: number;
  size: number;
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  first: boolean;
  number_of_elements: number;
  empty: boolean;
}
