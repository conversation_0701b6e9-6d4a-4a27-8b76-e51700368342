.menu-sidebar {
  @apply w-full transition-all duration-300 ease-in-out;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu-content {
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  height: auto;
}

/* Desktop styles */
@media (min-width: 769px) {
  .menu-sidebar {
    position: sticky;
    top: 5rem; /* Adjusted to account for header height */
    z-index: 20;
    height: 100%;
    min-height: calc(100vh - 150px); /* Ensure minimum height matches content */
    overflow-y: visible;
  }

  /* Hide the close button on desktop */
  .close-btn {
    display: none;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .menu-sidebar {
    width: 100%;
    height: auto;
    box-shadow: none;
    border-radius: 0;
  }

  /* Hide the close button in the sidebar since we're using the one in the header */
  .close-btn {
    display: none;
  }
}
