# Cancel Refund Logic - Intelligent Calculation

## 🎯 Problem Solved

**Issue:** <PERSON>hi cancel order, logic cũ chỉ hoàn lại toàn bộ `order.getCharge()` mà không tính đến các partial refunds trước đó, dẫn đến việc hoàn hố tiền cho user.

**Solution:** Tính toán lại tất cả partial refunds trước đó theo order ID, chỉ hoàn lại số tiền thực tế mà user đã chi tiêu.

## 📊 Detailed Example Scenarios

### Scenario 1: Cancel Order Without Previous Partials

**Initial Order:**
```
Order ID: 12345
Quantity: 100 items
Price: $10.00 per 1000 items
Total Charge: $1.00
Status: IN_PROGRESS
Previous Partials: None
```

**Cancel Action:**
```
Cancel refund calculation:
- Original charge: $1.00
- Total partial refunds: $0.00
- Cancel refund amount: $1.00 - $0.00 = $1.00
```

**Result:**
- User gets refunded: $1.00 (full amount)
- Transaction: type='Refund', change=+$1.00

### Scenario 2: Cancel Order With Previous Partials

**Order State Before Cancel:**
```
Order ID: 12345
Quantity: 100 items
Price: $10.00 per 1000 items
Total Charge: $1.00
Status: PARTIAL
Previous Partial Transactions:
  1. Partial refund (remains: 30): +$0.30
  2. Partial deduction (remains: 40): -$0.10
  Total partial refunds: $0.30 - $0.10 = $0.20
```

**Cancel Action:**
```
Cancel refund calculation:
- Original charge: $1.00
- Total partial refunds: $0.20
- Cancel refund amount: $1.00 - $0.20 = $0.80
```

**Result:**
- User gets refunded: $0.80 (remaining amount)
- Transaction: type='Refund', change=+$0.80
- Total refunded to user: $0.20 (partials) + $0.80 (cancel) = $1.00 ✅

### Scenario 3: Cancel Order Where All Amount Already Refunded

**Order State Before Cancel:**
```
Order ID: 12345
Quantity: 100 items
Price: $10.00 per 1000 items
Total Charge: $1.00
Status: PARTIAL
Previous Partial Transactions:
  1. Partial refund (remains: 100): +$1.00
  Total partial refunds: $1.00
```

**Cancel Action:**
```
Cancel refund calculation:
- Original charge: $1.00
- Total partial refunds: $1.00
- Cancel refund amount: $1.00 - $1.00 = $0.00
```

**Result:**
- User gets refunded: $0.00 (nothing additional)
- No transaction created
- Total refunded to user: $1.00 (already refunded through partials) ✅

## 🔧 Technical Implementation

### New Method: `calculateCancelRefundAmount()`

```java
@Override
public BigDecimal calculateCancelRefundAmount(GOrder order) {
    // Get all previous partial transactions
    List<GTransaction> previousPartialTransactions = transactionRepository
            .findAllByOrderAndType(order, TransactionType.Partial);

    if (previousPartialTransactions.isEmpty()) {
        // No previous partials - refund full charge
        return order.getCharge();
    }

    // Calculate total already refunded through partials
    BigDecimal totalPartialRefunds = previousPartialTransactions.stream()
            .map(GTransaction::getChange)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    // Cancel refund = Original charge - Total partial refunds
    BigDecimal cancelRefundAmount = order.getCharge().subtract(totalPartialRefunds);

    // Ensure no negative refunds
    return cancelRefundAmount.max(BigDecimal.ZERO);
}
```

### Updated `processCanceledRefund()` Logic

```java
private GUser processCanceledRefund(GOrder order) {
    // Check if already processed
    Optional<GTransaction> existingRefund = transactionRepository
            .findByOrderAndType(order, TransactionType.Refund);
    if (existingRefund.isPresent()) {
        return order.getUser();
    }

    // Calculate correct refund amount
    BigDecimal refundAmount = calculateCancelRefundAmount(order);
    
    if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
        // Nothing to refund - all already refunded through partials
        return order.getUser();
    }

    // Process the refund...
}
```

## ✅ Benefits

1. **Prevents Over-Refunding:** Never refunds more than user actually paid
2. **Accurate Calculations:** Accounts for all previous partial transactions
3. **Complete Audit Trail:** All refunds properly logged
4. **Edge Case Handling:** Handles scenarios where all amount already refunded
5. **Business Logic Compliance:** Ensures financial accuracy

## 🛡️ Safety Features

1. **Duplicate Prevention:** Checks for existing cancel refunds
2. **Negative Amount Protection:** Never refunds negative amounts
3. **Transaction Logging:** All operations logged for audit
4. **Balance Validation:** Ensures proper balance calculations

## 🧪 Testing Scenarios

```java
// Test 1: Cancel without partials
GOrder order1 = createOrder(100, new BigDecimal("10.00")); // $1.00 charge
BigDecimal refund1 = balanceService.calculateCancelRefundAmount(order1);
// Expected: $1.00

// Test 2: Cancel with partials
GOrder order2 = createOrderWithPartials(); // $1.00 charge, $0.30 partial refunds
BigDecimal refund2 = balanceService.calculateCancelRefundAmount(order2);
// Expected: $0.70

// Test 3: Cancel when fully refunded
GOrder order3 = createFullyRefundedOrder(); // $1.00 charge, $1.00 partial refunds
BigDecimal refund3 = balanceService.calculateCancelRefundAmount(order3);
// Expected: $0.00
```

This implementation ensures that cancel refunds are calculated accurately and prevent over-refunding users while maintaining complete financial audit trails.
