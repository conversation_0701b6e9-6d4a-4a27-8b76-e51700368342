package tndung.vnfb.smm.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tndung.vnfb.smm.dto.PanelNotificationReq;
import tndung.vnfb.smm.dto.PanelNotificationRes;
import tndung.vnfb.smm.entity.Tenant;

import java.util.List;

public interface PanelNotificationService {

    // Create notifications
    PanelNotificationRes createNotification(PanelNotificationReq req);

    // Get notifications
    Page<PanelNotificationRes> getNotifications(Pageable pageable);
    List<PanelNotificationRes> getUnreadNotifications();
    long getUnreadCount();

    // Mark as read
    void markAsRead(Long notificationId);
    void markAllAsRead();

    // System notifications for tenant setup
    void createTenantSetupSuccessNotification(Tenant tenant);
    void createTenantSetupFailureNotification(Tenant tenant, String errorMessage);

    // Renewal notifications
    void sendRenewalNotification(Tenant tenant);
    void sendExpirationNotification(Tenant tenant);
    void createRenewalReminderNotification(Tenant tenant);

    // Redis operations for read status
    boolean isNotificationReadByUser(Long userId, Long notificationId);
    void markNotificationAsReadByUser(Long userId, Long notificationId);
}
