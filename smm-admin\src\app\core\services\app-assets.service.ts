import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';
import { DesignSettingsService } from './design-settings.service';
import { DesignSettingsRes } from '../../model/response/design-settings-res.model';

const DESIGN_SETTINGS_STORAGE_KEY = 'app_design_settings';

/**
 * Service for managing application assets like logo and favicon
 */
@Injectable({
  providedIn: 'root'
})
export class AppAssetsService {
  private _logoUrl$ = new BehaviorSubject<string>('assets/images/logo.png');
  private _faviconUrl$ = new BehaviorSubject<string>('assets/images/favicon.ico');

  // Public observables
  public logoUrl$ = this._logoUrl$.asObservable();
  public faviconUrl$ = this._faviconUrl$.asObservable();

  constructor(
    private designSettingsService: DesignSettingsService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initializeAssets();
  }

  /**
   * Initialize assets from localStorage or fetch from server
   */
  private initializeAssets(): void {
    // Try to get settings from localStorage first
    if (isPlatformBrowser(this.platformId)) {
      const storedSettings = localStorage.getItem(DESIGN_SETTINGS_STORAGE_KEY);
      if (storedSettings) {
        try {
          const settings: DesignSettingsRes = JSON.parse(storedSettings);
          this.updateAssets(settings);
        } catch (e) {
          console.error('Error parsing stored settings:', e);
        }
      }
    }

    // Only fetch the latest settings from server if not on error page
    const isOnErrorPage = isPlatformBrowser(this.platformId) && window.location.pathname.startsWith('/error/');
    if (!isOnErrorPage) {
      this.loadDesignSettings();
    } else {
      console.log('Skipping design settings fetch - on error page');
    }
  }

  /**
   * Load design settings from server
   */
  public loadDesignSettings(): void {
    this.designSettingsService.getDesignSettings().subscribe({
      next: (settings) => {
        this.updateAssets(settings);
      },
      error: (error) => {
        console.error('Error loading design settings:', error);
      }
    });
  }

  /**
   * Update assets based on design settings
   */
  private updateAssets(settings: DesignSettingsRes): void {
    if (settings.logo?.url) {
      this._logoUrl$.next(settings.logo.url);
    }

    if (settings.favicon?.url) {
      this._faviconUrl$.next(settings.favicon.url);
      this.updateFaviconLink(settings.favicon.url);
    }
  }

  /**
   * Update favicon link in the document head
   */
  private updateFaviconLink(url: string): void {
    if (isPlatformBrowser(this.platformId)) {
      // Don't update favicon on admin pages
      if (window.location.pathname.startsWith('/panel')) {
        return;
      }

      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (favicon) {
        favicon.href = url;
      } else {
        const newFavicon = document.createElement('link');
        newFavicon.rel = 'icon';
        newFavicon.href = url;
        document.head.appendChild(newFavicon);
      }
    }
  }

  /**
   * Get current logo URL
   */
  public getLogoUrl(): string {
    return this._logoUrl$.getValue();
  }

  /**
   * Get current favicon URL
   */
  public getFaviconUrl(): string {
    return this._faviconUrl$.getValue();
  }
}
