import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ProviderRes } from '../../../model/response/provider-res.model';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-add-provider',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    FaIconComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './add-provider.component.html',
  styleUrl: './add-provider.component.css'
})
export class AddProviderComponent implements OnInit {
  @Input() provider: ProviderRes | null = null;
  @Input() changeKeyMode: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() providerAdded = new EventEmitter<ProviderRes>();

  // Form fields
  url: string = '';
  secretKey: string = '';

  isEdit: boolean = false;
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor(private adminService: AdminServiceService) {}

  ngOnInit(): void {
    if (this.provider) {
      this.isEdit = true;
      this.url = this.provider.url;
      this.secretKey = this.provider.secret_key;
    }
  }

  closePopup(): void {
    this.close.emit();
  }

  saveProvider(): void {
    this.isLoading = true;
    this.errorMessage = '';

    if (this.changeKeyMode && this.provider) {
      // Change API key mode
      this.adminService.changeProviderApiKey(this.provider.id, this.secretKey)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            // Update the provider object with the new API key
            if (this.provider) {
              const updatedProvider: ProviderRes = {
                ...this.provider,
                secret_key: this.secretKey
              };
              this.providerAdded.emit(updatedProvider);
              this.closePopup();
            }
          },
          error: (error) => {
            console.error('Error changing API key:', error);
            this.errorMessage = 'Failed to change API key. Please try again.';
          }
        });
    } else {
      // Add new provider mode
      this.adminService.addProvider(this.url, this.secretKey)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (provider) => {
            this.providerAdded.emit(provider);
            this.closePopup();
          },
          error: (error) => {
            console.error('Error adding provider:', error);
            this.errorMessage = 'Failed to add provider. Please check the URL and API key.';
          }
        });
    }
  }
}
