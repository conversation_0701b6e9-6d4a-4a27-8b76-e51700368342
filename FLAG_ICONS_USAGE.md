# Flag Icons Usage Guide

This project uses [flag-icons](https://github.com/lipis/flag-icons) CDN for displaying country flags instead of local image files.

## Setup

The flag-icons CSS is already included in both projects via CDN:

```html
<!-- In index.html -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css" />
```

## Usage

### Basic Flag Display

Use the `fi` class with the country code:

```html
<!-- Vietnam flag -->
<span class="fi fi-vn"></span>

<!-- US flag -->
<span class="fi fi-us"></span>

<!-- China flag -->
<span class="fi fi-cn"></span>
```

### With Custom Sizing

Add custom CSS classes for sizing:

```html
<!-- Small flag -->
<span class="fi fi-vn w-4 h-3 rounded"></span>

<!-- Medium flag -->
<span class="fi fi-us w-6 h-4 rounded"></span>

<!-- Large flag -->
<span class="fi fi-cn w-8 h-6 rounded"></span>
```

### In Angular Components

#### Language Dropdown Example

```typescript
// Component
allLanguages = [
  { flag: 'fi fi-vn', code: 'vi' }, // Vietnam flag
  { flag: 'fi fi-us', code: 'en' }, // US flag for English
  { flag: 'fi fi-cn', code: 'cn' }  // China flag
];
```

```html
<!-- Template -->
<span [class]="selectedFlag" class="w-6 h-4 rounded"></span>
```

#### Language Settings Example

```typescript
// Component
allLanguages = [
  { code: 'vi', name: 'Tiếng Việt', flag: 'fi fi-vn' },
  { code: 'en', name: 'English', flag: 'fi fi-us' },
  { code: 'cn', name: 'China', flag: 'fi fi-cn' }
];
```

```html
<!-- Template -->
<div class="language-flag">
  <span [class]="language.flag" [title]="language.name"></span>
</div>
```

## Available Country Codes

Common country codes used in this project:

- `fi-vn` - Vietnam
- `fi-us` - United States
- `fi-cn` - China
- `fi-gb` - United Kingdom
- `fi-fr` - France
- `fi-de` - Germany
- `fi-jp` - Japan
- `fi-kr` - South Korea

For a complete list, visit: https://github.com/lipis/flag-icons

## Styling Tips

### Rounded Corners
```html
<span class="fi fi-vn rounded"></span>      <!-- Small radius -->
<span class="fi fi-vn rounded-md"></span>   <!-- Medium radius -->
<span class="fi fi-vn rounded-lg"></span>   <!-- Large radius -->
<span class="fi fi-vn rounded-full"></span> <!-- Circular -->
```

### Responsive Sizing
```html
<span class="fi fi-vn w-4 h-3 md:w-6 md:h-4"></span>
```

### With Shadow
```html
<span class="fi fi-vn w-6 h-4 rounded shadow-sm"></span>
```

## Migration from Local Images

### Before (using local images):
```typescript
allLanguages = [
  { flag: 'assets/images/vi.png', code: 'vi' },
  { flag: 'assets/images/us.svg', code: 'en' }
];
```

```html
<img [src]="selectedFlag" alt="Selected language" class="w-6 h-4">
```

### After (using flag-icons):
```typescript
allLanguages = [
  { flag: 'fi fi-vn', code: 'vi' },
  { flag: 'fi fi-us', code: 'en' }
];
```

```html
<span [class]="selectedFlag" class="w-6 h-4 rounded"></span>
```

## Benefits

1. **No local files**: Reduces bundle size by removing flag image files
2. **Consistent quality**: All flags have the same style and quality
3. **Easy maintenance**: No need to manage flag image files
4. **Better performance**: CDN delivery with caching
5. **More flags available**: Access to 250+ country flags
6. **Responsive**: Vector-based flags that scale perfectly

## Components Updated

The following components have been updated to use flag-icons:

### smm-dashboard:
- `lang-dropdown.component` - Main language dropdown in header
- `layout-auth.component` - Language selector in auth pages

### smm-admin:
- `language-selector.component` - Language selection popup
- `language-settings.component` - Language settings management
- `layout-auth.component` - Language selector in auth pages

## Files Removed

The following local flag image files have been removed:
- `smm-dashboard/src/assets/images/vi.png`
- `smm-dashboard/src/assets/images/us.svg`
- `smm-dashboard/src/assets/images/cn.svg`
- `smm-admin/src/assets/images/vi.png`
- `smm-admin/src/assets/images/us.svg`
- `smm-admin/src/assets/images/cn.svg`
