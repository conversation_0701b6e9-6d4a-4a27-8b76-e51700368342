/**
 * CSS Variables
 * This file contains only CSS variables used throughout the application
 */

:root {
  /* Colors - Primary */
  --primary: #3B82F6; /* Tailwind blue-500 */
  --primary-hover: #2563EB; /* Tailwind blue-600 */
  --primary-active: #1D4ED8; /* Tailwind blue-700 */
  --primary-light: #DBEAFE; /* Tailwind blue-100 */

  /* Colors - Secondary */
  --secondary: #586280;

  /* Colors - Background */
  --background: #f5f7fc;
  --white: #ffffff;

  /* Colors - Gray Scale */
  --gray-100: #f8f8f8;
  --gray-200: #e9eaec;
  --gray-300: #dae4fc;
  --gray-400: #dae4fc;
  --gray-500: #dae4fc;
  --gray-600: #dae4fc;
  --gray-700: #262b38;
  --gray-800: #111827;

  /* Colors - Status */
  --success: #289d35;
  --success-light: #e4fbe1;
  --warning: #f6a733;
  --warning-light: #fff5e7;
  --error: #cf0616;
  --error-light: #ffdede;

  /* Colors - Special */
  --side-bar-text: #BFBFBF;
  --admin-primary: #30B0C7;
  --link-color: #6197ff;

  /* Typography */
  --font-family-inter: 'Inter', sans-serif;
  --font-family-roboto: 'Roboto', sans-serif;
  --font-family-manrope: 'Manrope', sans-serif;

  /* Spacing */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 1000px;

  /* Component Heights */
  --input-height: 46px;
  --button-height: 46px;
  --search-box-width: 400px;
  --dropdown-width: 200px;

  /* Z-index */
  --z-dropdown: 10;
  --z-modal: 999;
}
