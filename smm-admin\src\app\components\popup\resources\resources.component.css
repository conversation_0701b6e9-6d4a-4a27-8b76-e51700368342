.overlay-black {
    @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.service-container {
    position: relative;
    width: 438px;
    padding: 24px;
    background: #ffffff;
    border-radius: 16px;
  }

  .close-button {
    position: absolute;
    top: 16px;
    right: 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: background-color 0.2s;
    font-size: 18px;
  }

  .close-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .frame-image {
    position: absolute;
    top: -29px;
    right: -20px;
    width: 66px;
    height: 66px;
  }

  .service-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px;
    background: #f5f7fc;
    border-radius: 8px;
    transition: background-color 0.2s;
  }

  .service-content {
    display: flex;
    gap: 16px;
    align-items: flex-start;
  }

  .icon-wrapper {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: #e2e8f0;
  }

  .service-icon {
    font-size: 20px;
  }

  .text-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .title {
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: #000000;
    margin: 0;
  }

  .description {
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 113%;
    color: #586280;
    margin: 0;
  }

  .right-icon-wrapper {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-icon {
    font-size: 18px;
    color: #586280;
  }

  @media (max-width: 480px) {
    .service-container {
      width: 100%;
      padding: 16px;
    }

    .service-item {
      padding: 12px;
    }

    .text-content {
      gap: 2px;
    }
  }

