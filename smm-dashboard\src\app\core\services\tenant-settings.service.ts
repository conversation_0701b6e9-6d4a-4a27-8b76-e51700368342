import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class TenantSettingsService {
 // private readonly apiUrl = `${environment.apiUrl}/tenant-settings`;

  constructor(private http: HttpClient, private configService: ConfigService) {}

  /**
   * Get tenant default language (public endpoint, no auth required)
   */
  getTenantDefaultLanguage(): Observable<{default_language: string}> {
    return this.http.get<{default_language: string}>(`${this.configService.apiUrl}/tenant-settings/language/default`);
  }

  /**
   * Get tenant available languages (public endpoint, no auth required)
   */
  getTenantAvailableLanguages(): Observable<string[]> {
    return this.http.get<string[]>(`${this.configService.apiUrl}/tenant-settings/language/available`);
  }
}
