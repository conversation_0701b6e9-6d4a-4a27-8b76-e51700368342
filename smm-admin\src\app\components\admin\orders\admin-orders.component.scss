.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 0;

  .page-numbers {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .page-nav {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.5rem;
      height: 2.5rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.375rem;
      background-color: #fff;
      cursor: pointer;

      &:hover:not(:disabled) {
        background-color: #f3f4f6;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      img {
        width: 1rem;
        height: 1rem;
      }
    }

    .page-num {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 2.5rem;
      height: 2.5rem;
      padding: 0 0.75rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.375rem;
      background-color: #fff;
      font-size: 0.875rem;
      cursor: pointer;

      &:hover:not(.active) {
        background-color: #f3f4f6;
      }

      &.active {
        background-color: var(--primary);
        color: #fff;
        border-color: var(--primary);
      }
    }
  }

  .page-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: #4b5563;

    .show-entries {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      select {
        padding: 0.375rem 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        background-color: #fff;
        font-size: 0.875rem;
        color: #4b5563;
        cursor: pointer;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }
      }
    }
  }
}

// Loading styles moved to common loading component

// Action menu styles
.menu-container {
  position: relative;
  display: inline-block;

  .action-menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;

    // For table view (circular button)
    &:not(.px-2) {
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
    }

    &:hover {
      background-color: #f3f4f6;
    }
  }

  .dropdown-menu {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    z-index: 100; // Ensure high z-index
    position: fixed; // Use fixed positioning to avoid table overflow issues

    a {
      transition: background-color 0.2s;

      &:hover {
        background-color: #f9fafb;
      }

      &:active {
        background-color: #f3f4f6;
      }
    }
  }
}

// Card view specific styles
.grid-cols-1.gap-4 {
  .menu-container {
    .action-menu-button {
      width: 2rem !important;
      height: 2rem !important;
      border-radius: 50% !important;
      background-color: #f9fafb;

      &:hover {
        background-color: #f3f4f6;
      }
    }

    .dropdown-menu {
      right: 0;
      left: auto;
      z-index: 100;
    }
  }
}

// Bulk action menu styles
.bulk-action-menu {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  background-color: white;
  z-index: 100; // Ensure high z-index
  position: fixed; // Use fixed positioning to avoid table overflow issues

  a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #374151;
    font-size: 0.875rem;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9fafb;
    }

    &:active {
      background-color: #f3f4f6;
    }

    svg {
      margin-right: 0.75rem;
      width: 1rem;
      height: 1rem;
    }
  }
}

// Bulk action button styles
.bulk-action-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }
}

// Card view specific bulk action styles
.grid-cols-1.gap-4 {
  .bulk-action-menu {
    right: 0;
    left: auto;
    z-index: 100;
  }
}

.bulk-action-button {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: white;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }

  &:focus {
    outline: none;
  }

  span {
    font-size: 0.75rem;
    color: #2563eb;
    font-weight: 500;
  }
}

// Provider info styles
.provider-info {
  position: relative;

  .provider-name {
    color: #3b82f6;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;

    &.manual {
      color: #6b7280;
      cursor: default;
    }

    .icon {
      margin-left: 0.25rem;
      width: 1rem;
      height: 1rem;
    }
  }

  .provider-tooltip {
    position: absolute;
    z-index: 50;
    top: 100%;
    left: 0;
    margin-top: 0.5rem;
    width: 16rem;
    padding: 0.75rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    .tooltip-row {
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .value {
        font-weight: 500;
        word-break: break-all;
      }
    }
  }
}
