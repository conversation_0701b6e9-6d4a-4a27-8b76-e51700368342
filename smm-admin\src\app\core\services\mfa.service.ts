import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface MfaGenerateResponse {

    secret_key: string;
    image: string;
  
}

export interface MfaVerifyRequest {
  password: string;
  secret_key: string;
  code: string;
}

export interface MfaDisableRequest {
  password: string;
}

@Injectable({
  providedIn: 'root'
})
export class MfaService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Generate MFA QR code and secret key
   * @returns Observable with MFA generation response
   */
  generateMfa(): Observable<MfaGenerateResponse> {
    return this.http.get<MfaGenerateResponse>(`${this.configService.apiUrl}/access/mfa/generate`);
  }

  /**
   * Verify MFA code and enable 2FA
   * @param data MFA verification data
   * @returns Observable with verification response
   */
  verifyMfa(data: MfaVerifyRequest): Observable<any> {
    return this.http.put(`${this.configService.apiUrl}/access/mfa/verify`, data);
  }

  /**
   * Disable MFA
   * @param data MFA disable data
   * @returns Observable with disable response
   */
  disableMfa(data: MfaDisableRequest): Observable<any> {
    return this.http.put(`${this.configService.apiUrl}/access/mfa/disabled`, data);
  }
}
