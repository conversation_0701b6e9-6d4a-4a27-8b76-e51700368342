<div class="general-settings">
  <div class="settings-header">
    <h1 class="settings-title">{{ 'General Settings' | translate }}</h1>
    <p class="settings-description">{{ 'Configure your site settings' | translate }}</p>
  </div>

  <form [formGroup]="generalForm">
    <div class="settings-section">
      <h2 class="section-title">{{ 'Extra features' | translate }}</h2>

      <!-- Features Section -->
      <div class="features-list">
        <!-- Services Settings -->
        <div class="feature-item">
          <div class="feature-icon services-icon">
            <fa-icon [icon]="['fas', 'cog']"></fa-icon>
          </div>
          <div class="feature-content">
            <div class="feature-name">{{ 'Services settings' | translate }}</div>
          </div>
          <div class="feature-action">
            <fa-icon [icon]="['fas', 'angle-right']"></fa-icon>
          </div>
        </div>

        <!-- Language -->
        <div class="feature-item" (click)="openLanguageSettingsPopup()">
          <div class="feature-icon language-icon">
            <fa-icon [icon]="['fas', 'language']"></fa-icon>
          </div>
          <div class="feature-content">
            <div class="feature-name">{{ 'Language' | translate }}</div>
            <div class="feature-status active">{{ 'Default Language Settings' | translate }}</div>
          </div>
          <div class="feature-action">
            <fa-icon [icon]="['fas', 'angle-right']"></fa-icon>
          </div>
        </div>

        <!-- Promotions -->
        <div class="feature-item" [routerLink]="['/panel/settings/promotions']">
          <div class="feature-icon discount-icon">
            <fa-icon [icon]="['fas', 'percentage']"></fa-icon>
          </div>
          <div class="feature-content">
            <div class="feature-name">{{ 'Khuyến mãi' | translate }}</div>
            <div class="feature-status active">{{ 'Active' | translate }}</div>
          </div>
          <div class="feature-action">
            <fa-icon [icon]="['fas', 'angle-right']"></fa-icon>
          </div>
        </div>


        <div class="feature-item" (click)="openAffiliateSystemPopup()">
          <div class="feature-icon affiliate-icon">
            <fa-icon [icon]="['fas', 'user-friends']"></fa-icon>
          </div>
          <div class="feature-content">
            <div class="feature-name">{{ 'Affiliate system' | translate }}</div>
            <div class="feature-status active">{{ 'Active' | translate }}</div>
          </div>
          <div class="feature-action">
            <fa-icon [icon]="['fas', 'angle-right']"></fa-icon>
          </div>
        </div>

        <!-- Top-10 Users -->
        <!-- <div class="feature-item">
          <div class="feature-icon top-users-icon">
            <fa-icon [icon]="['fas', 'star']"></fa-icon>
          </div>
          <div class="feature-content">
            <div class="feature-name">{{ 'Top-10 users' | translate }}</div>
            <div class="feature-status active">{{ 'Active' | translate }}</div>
          </div>
          <div class="feature-action">
            <fa-icon [icon]="['fas', 'angle-right']"></fa-icon>
          </div>
        </div> -->
      </div>
    </div>

    <div class="settings-section">
      <h2 class="section-title">{{ 'Convertation' | translate }}</h2>

      <!-- Currency Settings -->
      <div class="currency-setting" (click)="openCurrencySettingsPopup()">
        <div class="setting-label">{{ 'Currency' | translate }}</div>
        <div class="setting-value">
          <span>{{ 'Primary currency:' | translate }}</span>
          <span class="currency-code">USD</span>
          <fa-icon [icon]="['fas', 'angle-right']" class="currency-arrow"></fa-icon>
        </div>
      </div>
    </div>




  </form>

  <!-- Affiliate System Popup -->
  <app-affiliate-system
    *ngIf="showAffiliateSystemPopup"
    (close)="closeAffiliateSystemPopup()">
  </app-affiliate-system>

  <!-- Language Settings Popup -->
  <app-language-settings
    *ngIf="showLanguageSettingsPopup"
    (close)="closeLanguageSettingsPopup()">
  </app-language-settings>

  <!-- Currency Settings Popup -->
  <app-currency-settings
    *ngIf="showCurrencySettingsPopup"
    (close)="closeCurrencySettingsPopup()">
  </app-currency-settings>
</div>
