import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

import { SvgIconComponent } from '../svg-icon/svg-icon.component';


@Component({
  selector: 'app-search-input',
  standalone: true,
  imports: [ SvgIconComponent, CommonModule ],
  templateUrl: './search-input.component.html',
  styleUrl: './search-input.component.css'
})
export class SearchInputComponent {
  @Input() placeholder: string = '';
  @Input() buttonText: string = '';
  @Input() icon: boolean = true;

  onSearch() {
    console.log('Search button clicked');
  }
}
