import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthUtilsService } from '../services/auth-utils.service';
import { isPlatformBrowser } from '@angular/common';
import { PLATFORM_ID } from '@angular/core';
import { Location } from '@angular/common';

export const newOrderGuard: CanActivateFn = (route, state) => {
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);
  const platformId = inject(PLATFORM_ID);
  const location = inject(Location);
  const isBrowser = isPlatformBrowser(platformId);

  // Skip auth check for login and register routes
  const currentPath = location.path();
  if (currentPath.includes('/auth/login') || currentPath.includes('/auth/register')) {
    return true;
  }

  // Check if user is authenticated
  if (!authUtils.isAuthenticated() || authUtils.isTokenExpired()) {
    // Store the intended URL for redirection after login (only in browser)
    if (isBrowser) {
      // Don't store login or register URLs
      if (!state.url.includes('/auth/login') && !state.url.includes('/auth/register')) {
        localStorage.setItem('redirectAfterLogin', state.url);
      }
    }

    // Redirect to login page with return url
    router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  // Additional checks specific to new order functionality could be added here
  // For example, checking if the user has sufficient balance or permissions

  return true;
};
