.layout-container {
  @apply max-w-full mx-auto;
}

/* Status badge styling */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

/* Table styling */
table {
  @apply w-full text-sm text-left text-gray-300;
}

thead {
  @apply text-xs text-gray-400 uppercase;
}

th, td {
  @apply px-6 py-3;
}

tbody tr {
  @apply border-b border-gray-800;
}

/* Filter tabs */
.filter-tab {
  @apply inline-block p-4 border-transparent rounded-t-lg;
}

.filter-tab.active {
  @apply text-[var(--primary)] border-b-2 border-[var(--primary)];
}

.filter-tab:not(.active) {
  @apply text-gray-400 hover:border-b-2 hover:text-gray-200 hover:border-gray-700;
}

/* Search box */
.search-box {
  @apply relative w-full;
}

.search-input {
  @apply w-full h-full px-4 py-2 bg-gray-800 text-white border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.search-button {
  @apply absolute right-0 top-0 h-full px-4 bg-[var(--primary)] text-white rounded-r-lg hover:bg-opacity-90;
}

/* Filter button */
.filter-button {
  @apply px-5 h-full text-sm font-medium text-white border border-gray-700 rounded-lg hover:bg-gray-800 focus:ring-2 focus:ring-gray-700 flex items-center;
}

/* Action buttons and menu */
.action-button {
  @apply p-1 rounded-full hover:bg-gray-700 transition-colors;
}

.edit-button {
  @apply text-[var(--primary)] hover:text-[var(--primary-dark)];
}

.delete-button {
  @apply text-red-500 hover:text-red-700;
}

/* Action menu dropdown */
.dropdown-menu {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dropdown-menu a {
  color: #374151;
  transition: background-color 0.2s;
}

.dropdown-menu a:hover {
  background-color: #f3f4f6;
}

.dropdown-menu .py-1.divide-y.divide-gray-100 a {
  border-bottom: 1px solid #e5e7eb;
}

.dropdown-menu .py-1.divide-y.divide-gray-100 a:last-child {
  border-bottom: none;
}

/* Card view styles */
.tag {
  @apply inline-block bg-gray-100 text-gray-700 text-xs px-1 py-0.5 rounded;
}

/* Pagination styles */
.pagination {
  @apply flex justify-between items-center mt-6 mb-4;
}

.page-numbers {
  @apply flex items-center gap-2;
}

.page-nav {
  @apply w-8 h-8 border border-gray-200 rounded-lg flex items-center justify-center cursor-pointer bg-white;
}

.page-nav:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.page-num {
  @apply w-8 h-8 border border-gray-200 rounded-lg bg-white text-xs text-gray-700 cursor-pointer flex items-center justify-center;
}

.page-num.active {
  @apply bg-[var(--primary)] text-white border-[var(--primary)];
}

.page-info {
  @apply text-sm text-gray-500 flex items-center gap-4;
}

.show-entries {
  @apply flex items-center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layout-container {
    @apply px-2;
  }

  th, td {
    @apply px-2 py-2;
  }

  .filter-tab {
    @apply p-2;
  }

  /* Auto-switch to card view on mobile */
  .viewMode-toggle {
    @apply hidden;
  }

  /* Header and filter adjustments */
  .mb-6.flex.flex-row.w-full.gap-4 {
    @apply flex-col;
  }

  .mb-6.flex.flex-row.w-full.gap-4 > div:first-child {
    @apply mb-2;
  }

  /* Pagination adjustments for mobile */
  .pagination {
    @apply flex-col items-start;
  }

  .page-info {
    @apply mt-4 w-full flex-col items-start gap-2;
  }

  /* Mobile-specific table cell widths (when table view is used) */
  table th, table td {
    @apply px-2 py-2 text-sm overflow-hidden text-ellipsis;
  }

  /* Make sure links don't overflow */
  td a.text-blue-600 {
    @apply block whitespace-nowrap overflow-hidden text-ellipsis max-w-full text-xs;
  }

  /* Financial details on mobile */
  td .flex.flex-col div {
    @apply text-xs;
  }
}
