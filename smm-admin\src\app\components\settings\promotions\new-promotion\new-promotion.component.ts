import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../../icons/icons.module';
import { PromotionService, PromotionReq, PromotionRes } from '../../../../core/services/promotion.service';
import { ToastService } from '../../../../core/services/toast.service';

@Component({
  selector: 'app-new-promotion',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './new-promotion.component.html',
  styleUrl: './new-promotion.component.css'
})
export class NewPromotionComponent implements OnInit {
  promotionForm: FormGroup;
  isSubmitting = false;
  
  // Edit mode properties
  isEditMode = false;
  promotionId: number | null = null;
  promotion: PromotionRes | null = null;
  pageTitle = 'Tạo khuyến mãi mới';
  submitButtonText = 'Tạo khuyến mãi';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private promotionService: PromotionService
  ) {
    this.promotionForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', [Validators.required]],
      start_date: ['', [Validators.required]],
      end_date: ['', [Validators.required]],
      promotion_percentage: ['', [Validators.required, Validators.min(0), Validators.max(100)]]
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode by looking for an ID in the route
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id && !isNaN(Number(id))) {
        this.promotionId = Number(id);
        this.isEditMode = true;
        this.pageTitle = 'Chỉnh sửa khuyến mãi';
        this.submitButtonText = 'Cập nhật khuyến mãi';
        this.loadPromotionData(this.promotionId);
      }
    });

    // Set default dates if not in edit mode
    if (!this.isEditMode) {
      const today = new Date();
      const nextMonth = new Date();
      nextMonth.setMonth(today.getMonth() + 1);
      
      this.promotionForm.patchValue({
        start_date: this.formatDateForInput(today),
        end_date: this.formatDateForInput(nextMonth)
      });
    }
  }

  /**
   * Load promotion data for editing
   */
  loadPromotionData(id: number): void {
    this.promotionService.getPromotionById(id).subscribe({
      next: (promotion) => {
        if (promotion && promotion.id) {
          this.promotion = promotion;
          this.populateForm(promotion);
        } else {
          this.toastService.showError('Failed to load promotion data');
          this.router.navigate(['/panel/settings/promotions']);
        }
      },
      error: (error) => {
        console.error('Error loading promotion:', error);
        this.toastService.showError(error.message);
        this.router.navigate(['/panel/settings/promotions']);
      }
    });
  }

  /**
   * Populate the form with promotion data
   */
  populateForm(promotion: PromotionRes): void {
    const startDate = promotion.start_date ? this.formatDateForInput(new Date(promotion.start_date)) : '';
    const endDate = promotion.end_date ? this.formatDateForInput(new Date(promotion.end_date)) : '';
    
    this.promotionForm.patchValue({
      name: promotion.name || '',
      description: promotion.description || '',
      start_date: startDate,
      end_date: endDate,
      promotion_percentage: promotion.promotion_percentage || 0
    });
  }

  /**
   * Format a date for the date input field (YYYY-MM-DD)
   */
  formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Go back to promotions list
   */
  goBack(): void {
    this.router.navigate(['/panel/settings/promotions']);
  }

  /**
   * Submit the form
   */
  onSubmit(): void {
    if (this.promotionForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.promotionForm.controls).forEach(key => {
        const control = this.promotionForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    // Get form values
    const formValues = this.promotionForm.value;

    // Format dates to ISO string
    const startDate = new Date(formValues.start_date);
    const endDate = new Date(formValues.end_date);
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    // Prepare promotion data for API
    const promotionReq: PromotionReq = {
      name: formValues.name,
      description: formValues.description,
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      promotion_percentage: formValues.promotion_percentage
    };

    if (this.isEditMode && this.promotionId) {
      // Update existing promotion
      this.updatePromotion(this.promotionId, promotionReq);
    } else {
      // Create new promotion
      this.createPromotion(promotionReq);
    }
  }

  /**
   * Create a new promotion
   */
  createPromotion(promotionReq: PromotionReq): void {
    this.promotionService.createPromotion(promotionReq).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.toastService.showSuccess('Promotion created successfully');
        this.router.navigate(['/panel/settings/promotions']);
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error creating promotion:', error);
        this.toastService.showError(error.message);
      }
    });
  }

  /**
   * Update an existing promotion
   */
  updatePromotion(id: number, promotionReq: PromotionReq): void {
    this.promotionService.updatePromotion(id, promotionReq).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.toastService.showSuccess('Promotion updated successfully');
        this.router.navigate(['/panel/settings/promotions']);
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error updating promotion:', error);
        this.toastService.showError('Failed to update promotion. Please try again.');
      }
    });
  }
}
