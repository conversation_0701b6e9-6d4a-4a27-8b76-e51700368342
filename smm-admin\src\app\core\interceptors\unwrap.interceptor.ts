import { HttpHandlerFn, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export function unwrapInterceptorFn(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<any> {
  return next(req).pipe(
    map(event => {
      if (event instanceof HttpResponse && event.body && typeof event.body === 'object' && 'data' in event.body) {
        return event.clone({ body: (event.body as any).data });
      }
      return event;
    })
  );
}
