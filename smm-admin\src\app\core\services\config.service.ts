import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { environment } from '../../../environments/environment';

interface TenantConfig {
  tenantId: string;
  domain: string;
  apiUrl: string;
  theme?: string;
  logoUrl?: string;
  companyName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private _apiUrl: string;
  private _tenantConfig: TenantConfig | null = null;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this._apiUrl = this.getApiUrl();
    this._tenantConfig = this.getTenantConfig();
    console.log('ConfigService initialized with API URL:', this._apiUrl);
    if (this._tenantConfig) {
      console.log('Tenant configuration loaded:', this._tenantConfig);
    }
  }

  /**
   * Get tenant configuration from window object
   */
  private getTenantConfig(): TenantConfig | null {
    if (isPlatformBrowser(this.platformId)) {
      const tenantConfig = (window as any).TENANT_CONFIG;
      if (tenantConfig) {
        return tenantConfig;
      }
    }
    return null;
  }

  /**
   * Get the API URL from various sources with fallbacks
   */
  private getApiUrl(): string {
    // First check for tenant-specific API URL
    if (isPlatformBrowser(this.platformId)) {
      // Check for tenant config
      const tenantConfig = (window as any).TENANT_CONFIG;
      if (tenantConfig && tenantConfig.apiUrl) {
        console.log('Using API URL from tenant configuration:', tenantConfig.apiUrl);
        return tenantConfig.apiUrl;
      }

      // Check for window.API_URL (set by server.ts)
      const windowApiUrl = (window as any).API_URL;
      if (windowApiUrl) {
        console.log('Using API URL from window object:', windowApiUrl);
        return windowApiUrl;
      }

      // Check for X-Tenant-API-URL header (set by Nginx)
      const tenantApiUrl = this.getTenantApiUrlFromHeaders();
      if (tenantApiUrl) {
        console.log('Using API URL from headers:', tenantApiUrl);
        return tenantApiUrl;
      }
    }

    // In server environment
    if (isPlatformServer(this.platformId)) {
      // Check for API_URL in server environment
      // We'll use a different approach that doesn't rely on direct process.env access
      const serverApiUrl = this.getServerApiUrl();
      if (serverApiUrl) {
        console.log('Using API URL from server environment:', serverApiUrl);
        return serverApiUrl;
      }
    }

    // Fallback to environment.ts
    console.log('Using API URL from environment.ts:', environment.apiUrl);
    return environment.apiUrl;
  }

  /**
   * Get tenant API URL from headers (if available)
   */
  private getTenantApiUrlFromHeaders(): string | null {
    if (isPlatformBrowser(this.platformId) && document) {
      // Some browsers expose response headers through meta tags
      const apiUrlMeta = document.querySelector('meta[name="x-tenant-api-url"]');
      if (apiUrlMeta) {
        const apiUrl = apiUrlMeta.getAttribute('content');
        if (apiUrl) {
          return apiUrl;
        }
      }
    }
    return null;
  }

  /**
   * Get API URL from server environment
   * This is a safer approach than directly accessing process.env
   */
  private getServerApiUrl(): string | null {
    // In a real implementation, this would get the API URL from the server
    // without directly accessing process.env
    // For now, we'll return null and fall back to environment.apiUrl
    return null;
  }

  /**
   * Get the current domain name
   */
  get currentDomain(): string {
    if (isPlatformBrowser(this.platformId)) {
      return window.location.hostname;
    }
    return '';
  }

  /**
   * Get the API URL
   */
  get apiUrl(): string {
    return this._apiUrl;
  }

  /**
   * Get the tenant configuration
   */
  get tenantConfig(): TenantConfig | null {
    return this._tenantConfig;
  }

  /**
   * Get the tenant ID
   */
  get tenantId(): string | null {
    return this._tenantConfig?.tenantId || null;
  }

  /**
   * Get the tenant theme
   */
  get theme(): string {
    return this._tenantConfig?.theme || 'default';
  }

  /**
   * Get the tenant logo URL
   */
  get logoUrl(): string | null {
    return this._tenantConfig?.logoUrl || null;
  }

  /**
   * Get the tenant company name
   */
  get companyName(): string {
    return this._tenantConfig?.companyName || 'SMM System';
  }

  /**
   * Check if the application is running in production mode
   */
  get isProduction(): boolean {
    return environment.production;
  }
}
