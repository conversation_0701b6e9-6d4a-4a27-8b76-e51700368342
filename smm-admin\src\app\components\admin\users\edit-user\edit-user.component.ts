import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { GUserSuperRes } from '../../../../model/response/g-user-super-res.model';
import { EditUserReq } from '../../../../model/request/edit-user-req.model';
import { UserAdminService } from '../../../../core/services/user-admin.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-edit-user',
  standalone: true,
  imports: [CommonModule, FormsModule, IconsModule, TranslateModule],
  templateUrl: './edit-user.component.html',
  styleUrl: './edit-user.component.css'
})
export class EditUserComponent implements OnInit {
  @Input() userId: number = 0;
  @Input() user: GUserSuperRes | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() userUpdated = new EventEmitter<GUserSuperRes>();

  // Form fields
  username: string = '';
  email: string = '';
  phone: string = '';

  // Loading state
  loading: boolean = false;
  errorMessage: string = '';

  constructor(private userAdminService: UserAdminService) {}

  ngOnInit(): void {
    if (this.user) {
      this.username = this.user.user_name;
      this.email = this.user.email || '';
      this.phone = this.user.phone || '';
    }
  }

  saveUser(): void {
    if (!this.user) return;

    this.loading = true;
    this.errorMessage = '';

    // Create the request object according to the EditUserReq model
    const userReq: EditUserReq = {
      email: this.email,
      phone: this.phone
    };

    // Call the API to update the user
    this.userAdminService.editUser(this.userId, userReq)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (updatedUser) => {
          console.log('User updated successfully:', updatedUser);
          this.userUpdated.emit(updatedUser);
          this.closeModal();
        },
        error: (error) => {
          console.error('Error updating user:', error);
          this.errorMessage = error?.message || 'Failed to update user. Please try again.';
        }
      });
  }

  closeModal(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close the modal if the user clicks on the overlay (outside the modal content)
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }
}
