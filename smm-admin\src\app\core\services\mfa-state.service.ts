import { Injectable } from '@angular/core';

/**
 * Service for securely storing MFA state during the authentication flow
 * This service keeps sensitive data in memory only and not in browser storage
 */
@Injectable({
  providedIn: 'root'
})
export class MfaStateService {
  private _username: string = '';
  private _password: string = '';
  private _loginFirstFactor: string = '';
  private _returnUrl: string = '';

  /**
   * Store MFA information securely in memory
   */
  storeMfaInfo(username: string, password: string, loginFirstFactor: string, returnUrl: string): void {
    this._username = username;
    this._password = password;
    this._loginFirstFactor = loginFirstFactor;
    this._returnUrl = returnUrl;
  }

  /**
   * Get stored username
   */
  get username(): string {
    return this._username;
  }

  /**
   * Get stored password
   */
  get password(): string {
    return this._password;
  }

  /**
   * Get stored login first factor
   */
  get loginFirstFactor(): string {
    return this._loginFirstFactor;
  }

  /**
   * Get stored return URL
   */
  get returnUrl(): string {
    return this._returnUrl;
  }

  /**
   * Clear all stored MFA information
   * Should be called after successful authentication or when abandoning the MFA flow
   */
  clearMfaInfo(): void {
    this._username = '';
    this._password = '';
    this._loginFirstFactor = '';
    this._returnUrl = '';
  }
}
