import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { VoucherService } from '../../../core/services/voucher.service';
import { VoucherRes } from '../../../model/response/voucher-res.model';
import { CreatePromoCodeComponent } from '../../popup/create-promo-code/create-promo-code.component';
import { UIStateService } from '../../../core/services/ui-state.service';
import { VoucherType } from '../../../model/request/voucher-req.model';
import { DeleteConfirmationComponent } from '../../settings/delete-confirmation/delete-confirmation.component';
import { CurrencyService } from '../../../core/services/currency.service';

@Component({
  selector: 'app-promo-codes',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconsModule,
    CreatePromoCodeComponent,
    DeleteConfirmationComponent
  ],
  templateUrl: './promo-codes.component.html',
  styleUrls: ['./promo-codes.component.css']
})
export class PromoCodesComponent implements OnInit {
  vouchers: VoucherRes[] = [];
  loading = false;
  showCreateModal = false;

  // Delete confirmation
  showDeleteConfirmation = false;
  voucherToDelete: VoucherRes | null = null;
  isDeleting = false;

  constructor(
    private voucherService: VoucherService,
    private uiStateService: UIStateService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit(): void {
    this.loadVouchers();

    // Subscribe to loading state
    this.voucherService.loading$.subscribe(loading => {
      this.loading = loading;
    });
  }

  loadVouchers(): void {
    this.voucherService.getAllVouchers().subscribe({
      next: (data) => {
        this.vouchers = data;
      },
      error: (error) => {
        console.error('Error loading vouchers:', error);
      }
    });
  }

  openCreateModal(): void {
    this.showCreateModal = true;
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
  }

  onVoucherCreated(voucher: VoucherRes): void {
    this.vouchers.push(voucher);
    this.closeCreateModal();
  }

  deleteVoucher(voucher: VoucherRes): void {
    // Show confirmation dialog
    this.voucherToDelete = voucher;
    this.showDeleteConfirmation = true;
  }

  // Close delete confirmation dialog
  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.voucherToDelete = null;
    this.isDeleting = false;
  }

  // Confirm delete voucher
  confirmDeleteVoucher(): void {
    if (!this.voucherToDelete) {
      return;
    }

    this.isDeleting = true;

    this.voucherService.deleteVoucher(this.voucherToDelete.id).subscribe({
      next: () => {
        // Remove the voucher from the list
        this.vouchers = this.vouchers.filter(v => v.id !== this.voucherToDelete?.id);
        this.closeDeleteConfirmation();
        this.uiStateService.showToast('Promo code deleted successfully', 'SUCCESS');
      },
      error: (error) => {
        console.error('Error deleting voucher:', error);
        this.isDeleting = false;
      }
    });
  }

  copyToClipboard(code: string): void {
    navigator.clipboard.writeText(code).then(() => {
      // Show success toast
      this.uiStateService.showToast('Promo code copied to clipboard', 'SUCCESS');
    });
  }

  /**
   * Get the formatted discount value with the appropriate symbol
   */
  getFormattedDiscount(voucher: VoucherRes): string {
    if (voucher.type === VoucherType.ORDER) {
      return `${voucher.discount_value}%`;
    } else {
      return `$${this.currencyService.formatBalance(voucher.discount_value)}`;
    }
  }
}
