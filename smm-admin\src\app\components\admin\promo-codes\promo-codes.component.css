.promo-container {
  @apply max-w-full mx-auto md:p-6  ;
}

/* Table styling */
.table-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead {
  background-color: #f9fafb;
}

th {
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

tbody tr {
  transition: background-color 0.2s;
}

tbody tr:hover {
  background-color: #f9fafb;
}

td {
  padding: 16px 24px;
  white-space: nowrap;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  color: #374151;
}

/* Status badges */
.status-badge {
  @apply px-2 py-1 text-xs font-medium;
  border-radius: 2px;
}

.status-active {
  @apply bg-green-100 text-green-800 border border-green-200;
}

.status-used {
  @apply bg-gray-100 text-gray-800 border border-gray-200;
}

/* Mobile styles */
@media (max-width: 767px) {
  .layout-container {
    @apply p-3;
  }

  /* Mobile card styling */
  .bg-white.rounded-lg.shadow-sm {
    @apply border border-gray-200;
  }

  /* Improve spacing in mobile cards */
  .grid-cols-2 {
    @apply gap-y-3;
  }

  /* Improve touch targets for mobile */
  button {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Ensure text is readable on mobile */
  .font-medium {
    @apply text-sm;
  }

  /* Ensure proper spacing between cards */
  .mb-4 {
    @apply mb-3;
  }
}
