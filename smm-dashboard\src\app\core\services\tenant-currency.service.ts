import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';
import { Currency } from '../../model/response/currency-res.model';

@Injectable({
  providedIn: 'root'
})
export class TenantCurrencyService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  getAvailableCurrencies(): Observable<Currency[]> {
    return this.http.get<Currency[]>(
      `${this.configService.apiUrl}/tenant-currencies/available`
    );
  }
}
