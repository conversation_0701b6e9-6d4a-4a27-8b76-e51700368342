package tndung.vnfb.smm.aop;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import tndung.vnfb.smm.dto.RenewalRequest;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.service.AuthenticationFacade;
import tndung.vnfb.smm.service.UserTenantAccessService;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantAccessCheckAspectTest {

    @Mock
    private UserTenantAccessService userTenantAccessService;

    @Mock
    private AuthenticationFacade authenticationFacade;

    @Mock
    private JoinPoint joinPoint;

    @Mock
    private MethodSignature methodSignature;

    @Mock
    private Method method;

    @InjectMocks
    private TenantAccessCheckAspect tenantAccessCheckAspect;

    private GUser testUser;
    private String testTenantId = "test-tenant-123";
    private Long testUserId = 1L;

    @BeforeEach
    void setUp() {
        testUser = new GUser();
        testUser.setId(testUserId);
        testUser.setUserName("testuser");
    }

    @Test
    void checkTenantAccess_Success_WithTenantIdParameter() throws Exception {
        // Arrange
        when(authenticationFacade.getCurrentUser()).thenReturn(Optional.of(testUser));
        when(userTenantAccessService.getAccessibleTenantIds(testUserId))
                .thenReturn(Arrays.asList(testTenantId, "other-tenant"));

        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(method);
        when(joinPoint.getArgs()).thenReturn(new Object[]{testTenantId});

        // Mock method parameters
        Parameter parameter = mock(Parameter.class);
        when(parameter.getName()).thenReturn("tenantId");
        when(method.getParameters()).thenReturn(new Parameter[]{parameter});

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> tenantAccessCheckAspect.checkTenantAccess(joinPoint));

        // Verify interactions
        verify(authenticationFacade).getCurrentUser();
        verify(userTenantAccessService).getAccessibleTenantIds(testUserId);
    }

    @Test
    void checkTenantAccess_Success_WithRequestObject() throws Exception {
        // Arrange
        RenewalRequest request = new RenewalRequest();
        request.setTenantId(testTenantId);

        when(authenticationFacade.getCurrentUser()).thenReturn(Optional.of(testUser));
        when(userTenantAccessService.getAccessibleTenantIds(testUserId))
                .thenReturn(Arrays.asList(testTenantId));

        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(method);
        when(joinPoint.getArgs()).thenReturn(new Object[]{request});

        // Mock method parameters
        Parameter parameter = mock(Parameter.class);
        when(parameter.getName()).thenReturn("request");
        when(method.getParameters()).thenReturn(new Parameter[]{parameter});

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> tenantAccessCheckAspect.checkTenantAccess(joinPoint));
    }

    @Test
    void checkTenantAccess_ThrowsException_UserNotFound() {
        // Arrange
        when(authenticationFacade.getCurrentUser()).thenReturn(Optional.empty());

        // Act & Assert
        InvalidParameterException exception = assertThrows(
                InvalidParameterException.class,
                () -> tenantAccessCheckAspect.checkTenantAccess(joinPoint)
        );

        assertEquals(IdErrorCode.USER_NOT_FOUND, exception.getErrorCode());
    }

    @Test
    void checkTenantAccess_ThrowsException_TenantIdNotFound() throws Exception {
        // Arrange
        when(authenticationFacade.getCurrentUser()).thenReturn(Optional.of(testUser));

        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(method);
        when(joinPoint.getArgs()).thenReturn(new Object[]{"someOtherParam"});

        // Mock method parameters
        Parameter parameter = mock(Parameter.class);
        when(parameter.getName()).thenReturn("otherParam");
        when(method.getParameters()).thenReturn(new Parameter[]{parameter});

        // Act & Assert
        InvalidParameterException exception = assertThrows(
                InvalidParameterException.class,
                () -> tenantAccessCheckAspect.checkTenantAccess(joinPoint)
        );

        assertEquals(IdErrorCode.TENANT_ACCESS_DENIED, exception.getErrorCode());
    }

    @Test
    void checkTenantAccess_ThrowsException_AccessDenied() throws Exception {
        // Arrange
        when(authenticationFacade.getCurrentUser()).thenReturn(Optional.of(testUser));
        when(userTenantAccessService.getAccessibleTenantIds(testUserId))
                .thenReturn(Collections.singletonList("different-tenant"));

        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(method);
        when(joinPoint.getArgs()).thenReturn(new Object[]{testTenantId});

        // Mock method parameters
        Parameter parameter = mock(Parameter.class);
        when(parameter.getName()).thenReturn("tenantId");
        when(method.getParameters()).thenReturn(new Parameter[]{parameter});

        // Act & Assert
        InvalidParameterException exception = assertThrows(
                InvalidParameterException.class,
                () -> tenantAccessCheckAspect.checkTenantAccess(joinPoint)
        );

        assertEquals(IdErrorCode.TENANT_ACCESS_DENIED, exception.getErrorCode());
    }
}
