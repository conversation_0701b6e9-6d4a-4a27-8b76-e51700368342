<!-- src/app/components/date-range-picker/date-range-picker.component.html -->
<div class=" relative" >
    <div
      [ngClass]="'flex items-center justify-between p-2 border border-gray-300 rounded-md cursor-pointer ' + containerClass"
      (click)="toggleCalendar()">
      <div class="flex items-center space-x-2">
        <span class="text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </span>
        <span>{{ startDateStr }} - {{ endDateStr }}</span>
      </div>
      <span class="text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </span>
    </div>

    <!-- Calendar Dropdown -->
    <div *ngIf="showCalendar" class="absolute z-10 mt-1 w-auto min-w-max bg-white border border-gray-200 rounded-md shadow-lg">
      <div class="p-3">
        <!-- Calendar Header -->
        <div class="flex items-center justify-between mb-4">
          <button
            class="p-1 rounded-full hover:bg-gray-100"
            (click)="prevMonth()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <span class="font-medium">{{ months[currentMonth] }} {{ currentYear }}</span>
          <button
            class="p-1 rounded-full hover:bg-gray-100"
            (click)="nextMonth()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- Calendar Days -->
        <div class="grid grid-cols-7 gap-1">
          <!-- Day Headers -->
          <div class="text-center text-gray-500 text-xs py-1">Su</div>
          <div class="text-center text-gray-500 text-xs py-1">Mo</div>
          <div class="text-center text-gray-500 text-xs py-1">Tu</div>
          <div class="text-center text-gray-500 text-xs py-1">We</div>
          <div class="text-center text-gray-500 text-xs py-1">Th</div>
          <div class="text-center text-gray-500 text-xs py-1">Fr</div>
          <div class="text-center text-gray-500 text-xs py-1">Sa</div>

          <!-- Calendar Days -->
          <ng-container *ngFor="let week of calendarDays">
            <ng-container *ngFor="let day of week">
              <div
                class="h-8 w-8 flex items-center justify-center text-sm rounded-full cursor-pointer"
                [ngClass]="{
                  'text-gray-400': !day.isCurrentMonth,
                  'bg-blue-500 text-white': day.isSelected,
                  'bg-blue-100': day.isInRange,
                  'ring-2 ring-blue-500': day.isToday && !day.isSelected
                }"
                (click)="selectDate(day.date)">
                {{ day.date.getDate() }}
              </div>
            </ng-container>
          </ng-container>
        </div>

        <!-- Apply Button -->
        <div class="mt-4 flex justify-end">
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            (click)="applyDateRange()">
            Apply
          </button>
        </div>
      </div>
    </div>
  </div>