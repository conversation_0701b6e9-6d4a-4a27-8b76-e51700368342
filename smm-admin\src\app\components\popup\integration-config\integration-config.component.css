/* Overlay */
.overlay-black {
  @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center;
}

/* Popup Container */
.popup-container {
  @apply bg-white rounded-2xl shadow-xl w-full max-w-md mx-4 overflow-hidden;
  animation: popup-fade-in 0.3s ease-out;
}

@keyframes popup-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Popup Header */
.popup-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.popup-title {
  @apply text-xl font-semibold text-gray-800;
}

.close-button {
  @apply text-gray-500 hover:text-gray-700 transition-colors p-1;
}

/* Popup Content */
.popup-content {
  @apply p-6 space-y-4;
}

/* Form Elements */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input, .form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-hint {
  @apply mt-1 text-xs text-gray-500;
}

.toggle-group {
  @apply flex items-center justify-between;
}

/* Popup Footer */
.popup-footer {
  @apply flex justify-end gap-3 px-6 py-4 border-t border-gray-200 bg-gray-50;
}

.cancel-button {
  @apply px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors;
}

.save-button {
  @apply px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors;
}
