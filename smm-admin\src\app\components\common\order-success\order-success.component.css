

.order-success-container {
  @apply flex justify-center items-center ;
}

.confirmation-card {
  @apply bg-[#d6eff4] rounded-2xl p-6 w-full ;
  font-family: 'Roboto', sans-serif;
}

.close-button {
  @apply absolute top-3 right-3 text-[#30b0c7] hover:text-[#1a8a9e] transition-colors duration-200 w-6 h-6 flex items-center justify-center;
}

.title {
  @apply text-[#30b0c7] text-xl font-semibold mb-4;
}

.details-section {
  @apply space-y-1;
}

.detail-row {
  @apply flex flex-col md:flex-row md:items-start gap-2;
}

.label {
  @apply text-[#30b0c7] font-semibold min-w-[100px];
}

.value {
  @apply text-[#30b0c7] break-all;
}

.link {
  @apply hover:underline cursor-pointer;
}

.thank-you {
  @apply text-[#30b0c7] font-semibold mt-4;
}

/* Responsive Design */
@media (max-width: 768px) {
  /* .confirmation-card {
    @apply mx-4;
  } */

  .detail-row {
    @apply flex-col;
  }
}

