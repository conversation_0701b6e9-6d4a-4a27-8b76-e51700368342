import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

interface Language {
  code: string;
  name: string;
  flag: string;
}

@Component({
  selector: 'app-language-selector',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './language-selector.component.html',
  styleUrl: './language-selector.component.css'
})
export class LanguageSelectorComponent {
  @Input() availableLanguages: string[] = [];
  @Input() selectedLanguages: string[] = [];
  @Output() close = new EventEmitter<void>();
  @Output() languageSelected = new EventEmitter<string>();

  allLanguages: Language[] = [
    { code: 'vi', name: '<PERSON>iế<PERSON> Việ<PERSON>', flag: 'fi fi-vn' },
    { code: 'en', name: 'English', flag: 'fi fi-us' },
    { code: 'cn', name: 'China', flag: 'fi fi-cn' },
    // @smm-dev
  ];

  get unselectedLanguages(): Language[] {
    return this.allLanguages.filter(lang => !this.selectedLanguages.includes(lang.code));
  }

  selectLanguage(languageCode: string): void {
    this.languageSelected.emit(languageCode);
  }

  onClose(): void {
    this.close.emit();
  }
}
