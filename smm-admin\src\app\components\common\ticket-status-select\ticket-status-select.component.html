<div class="flex items-center gap-4">
  <div class="flex-1">
    <app-lite-dropdown
      [options]="statusOptions"
      [selectedOption]="currentStatus"
      [customClassButton]="'h-[46px]'"
      [customClassDropdown]="'border rounded-lg'"
      (selected)="onStatusChange($event)"
    ></app-lite-dropdown>
  </div>
  <button (click)="onUpdateClick()" [disabled]="loading || !currentStatus" class="bg-[#5bc0de] text-white px-4 h-[46px] rounded-lg hover:bg-[#46b8da] flex items-center justify-center min-w-[100px]">
    <span *ngIf="!loading">Cập nhật</span>
    <div *ngIf="loading" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
  </button>
</div>
