# Partial Refund System - Example Scenarios

## 📋 Overview

This document demonstrates how the intelligent partial refund system works with multiple PARTIAL status changes and proper transaction tracking.

## 🎯 Key Features

1. **Intelligent Refund Calculation**: Prevents double refunding by tracking previous partial transactions
2. **Transaction Type 'Partial'**: All partial refunds are logged with type='Partial' for easy tracking
3. **Automatic Balance Management**: Uses BalanceService for consistent transaction logging
4. **Remains-Based Calculation**: Refund amount = `remains * price / 1000`

## 📊 Example Scenarios

### Scenario 1: First Time PARTIAL

**Initial Order:**
- Order ID: 12345
- Total Quantity: 1000
- Price: $10.00 per 1000 units
- Total Charge: $10.00
- Current Status: IN_PROGRESS
- Current Remains: 0

**Action:** Change to PARTIAL with remains = 300

**Calculation:**
```
Total refund for 300 remains = 300 * $10.00 / 1000 = $3.00
Previous partial refunds = $0.00 (no previous partial transactions)
New refund amount = $3.00 - $0.00 = $3.00
```

**Result:**
- Order Status: PARTIAL
- Order Remains: 300
- User Balance: +$3.00
- Transaction Created: type='Partial', change=+$3.00, note="Partial refund for order #12345 (remains: 300): $3.00"

---

### Scenario 2: Second Time PARTIAL (Increase Remains)

**Current Order State:**
- Order ID: 12345
- Status: PARTIAL
- Current Remains: 300
- Previous Partial Refunds: $3.00

**Action:** Change to PARTIAL with remains = 500

**Calculation:**
```
Total refund for 500 remains = 500 * $10.00 / 1000 = $5.00
Previous partial refunds = $3.00 (from previous partial transaction)
New refund amount = $5.00 - $3.00 = $2.00
```

**Result:**
- Order Status: PARTIAL
- Order Remains: 500
- User Balance: +$2.00 (additional)
- Transaction Created: type='Partial', change=+$2.00, note="Partial refund for order #12345 (remains: 500): $2.00"

---

### Scenario 3: Third Time PARTIAL (Decrease Remains)

**Current Order State:**
- Order ID: 12345
- Status: PARTIAL
- Current Remains: 500
- Previous Partial Refunds: $3.00 + $2.00 = $5.00

**Action:** Change to PARTIAL with remains = 200

**Calculation:**
```
Total refund for 200 remains = 200 * $10.00 / 1000 = $2.00
Previous partial refunds = $5.00 (sum of all previous partial transactions)
New refund amount = $2.00 - $5.00 = -$3.00 (negative, so $0.00)
```

**Result:**
- Order Status: PARTIAL
- Order Remains: 200
- User Balance: No change (refund amount is $0.00)
- Transaction Created: None (no refund needed)

**Note:** The system prevents negative refunds. User has already been refunded more than what the current remains justify.

---

### Scenario 4: Complex Multiple Changes

**Order Timeline:**

1. **Initial Order**: 1000 units, $20.00 total, price = $20.00/1000
2. **First PARTIAL**: remains = 400 → Refund = 400 * $20/1000 = $8.00
3. **Second PARTIAL**: remains = 600 → Refund = (600 * $20/1000) - $8.00 = $12.00 - $8.00 = $4.00
4. **Third PARTIAL**: remains = 300 → Refund = (300 * $20/1000) - $12.00 = $6.00 - $12.00 = $0.00
5. **Fourth PARTIAL**: remains = 800 → Refund = (800 * $20/1000) - $12.00 = $16.00 - $12.00 = $4.00

**Final State:**
- Total Refunded: $8.00 + $4.00 + $0.00 + $4.00 = $16.00
- Current Remains: 800
- Expected Refund for 800 remains: $16.00 ✅ (matches total refunded)

## 🔍 Database Tracking

### GTransaction Records

```sql
-- Example transaction records for Order #12345
SELECT id, order_id, type, change, balance, note, created_at 
FROM g_transaction 
WHERE order_id = 12345 AND type = 'Partial'
ORDER BY created_at;

-- Results:
-- id | order_id | type    | change | balance | note                                    | created_at
-- 1  | 12345    | Partial | +3.00  | 103.00  | Partial refund for order #12345...     | 2024-01-01 10:00:00
-- 2  | 12345    | Partial | +2.00  | 105.00  | Partial refund for order #12345...     | 2024-01-01 11:00:00
-- 3  | 12345    | Partial | +4.00  | 109.00  | Partial refund for order #12345...     | 2024-01-01 12:00:00
```

### Calculation Query

```sql
-- Get total partial refunds for an order
SELECT 
    order_id,
    SUM(change) as total_partial_refunds,
    COUNT(*) as partial_refund_count
FROM g_transaction 
WHERE order_id = 12345 AND type = 'Partial'
GROUP BY order_id;
```

## 🛡️ Error Prevention

### Validation Rules

1. **Negative Remains**: Cannot set remains to negative value
2. **Double Refunding**: System automatically prevents over-refunding
3. **Transaction Consistency**: All balance changes are logged
4. **Audit Trail**: Complete history of all partial changes

### Edge Cases Handled

1. **Zero Remains**: No refund processed
2. **Same Remains**: No additional refund needed
3. **Decreased Remains**: May result in $0.00 refund (no negative refunds)
4. **Multiple Rapid Changes**: Each change is calculated independently

## 🚀 API Usage

### Change Order to PARTIAL

```java
// Method 1: Using changePartialWithRefund
OrderRes result = orderService.changePartialWithRefund(orderId, 
    PartialReq.builder().remains(300).build());

// Method 2: Using updateOrderStatus (automatically handles PARTIAL)
OrderRes result = orderService.updateOrderStatus(orderId, OrderStatus.PARTIAL);
```

### Check Refund History

```java
// Get all partial transactions for an order
List<GTransaction> partialRefunds = transactionRepository
    .findAllByOrderAndType(order, TransactionType.Partial);

BigDecimal totalRefunded = partialRefunds.stream()
    .map(GTransaction::getChange)
    .reduce(BigDecimal.ZERO, BigDecimal::add);
```

## ✅ Benefits

1. **Accurate Refunding**: Never over-refund or under-refund users
2. **Complete Audit Trail**: Every partial change is tracked
3. **Automatic Calculation**: No manual calculation errors
4. **Consistent Logic**: Same calculation method across all scenarios
5. **Error Prevention**: Built-in validation and safety checks

This system ensures that partial refunds are handled intelligently and consistently, providing a reliable foundation for order management and financial accuracy.
