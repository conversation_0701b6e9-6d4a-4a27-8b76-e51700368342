.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.form-group {
  @apply mb-6;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)] mb-2;
}

.form-input {
  @apply w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#30B0C7] focus:border-transparent;
}

.input-with-symbol {
  @apply relative;
}

.input-symbol {
  @apply absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none;
}

.with-dollar {
  @apply pr-8;
}

.with-percent {
  @apply pr-8;
}


.save-button {
  @apply w-full bg-[#30B0C7] text-white font-medium py-3 px-4 rounded-lg hover:bg-[#2599a8] active:bg-[#1e7f8a] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed;
}

.loading-spinner {
  @apply inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

.error-message {
  @apply text-red-500 text-sm mb-4 p-3 bg-red-50 border border-red-200 rounded-lg;
}
