<div class="header-container mb-6">
  <div
    class="menu-bar flex items-center justify-between p-4 px-6 bg-white border-b backdrop-filter backdrop-blur-sm bg-opacity-90">
    <!-- Left Section -->
    <div class="left-section flex items-center gap-5">
      <!-- Toggle Button (Mobile only) -->
      <button class="md:hidden text-[#3b82f6] hover:text-[#2563eb] transition-colors duration-200"
        (click)="toggleSidebar()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      <!-- Logo - Keep the default logo for admin pages (hidden on mobile) -->
      <div class="hidden md:flex ml-0 md:ml-4 items-center">
        <div class="logo-container flex items-center">
          <span class="text-xl font-bold text-blue-600">NEWPANEL</span>
        </div>
      </div>
    </div>

    <!-- Desktop Menu (visible only on md and above) -->
    <div class="desktop-menu hidden md:flex items-center justify-center">
      <div class="menu-sections flex gap-6">
        <!-- Main Menu Items -->
        <div *ngFor="let item of getItemsBySection('Main Menu')" class="dropdown-menu">
          <a [routerLink]="item.link" routerLinkActive="active"
            class="nav-item cursor-pointer font-medium py-2 block px-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-md transition-all duration-200">
            <span class="uppercase tracking-wide">{{ item.label }}</span>
          </a>
        </div>

        <!-- More Dropdown -->
        <div class="dropdown-menu relative group" #moreDropdown>
          <button
            class="nav-item cursor-pointer font-medium flex items-center gap-1 py-2 px-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-md transition-all duration-200">
            <span class="uppercase tracking-wide">More</span>
            <fa-icon [icon]="['fas', 'chevron-down']"
              class="text-xs ml-1 transition-transform duration-200 group-hover:rotate-180"></fa-icon>
          </button>
          <!-- Dropdown menu with improved hover behavior -->
          <div class="dropdown-wrapper absolute left-0 top-full w-48 z-50">
            <!-- Invisible bridge to maintain hover state -->
            <div class="h-2 w-full"></div>
            <!-- Dropdown content -->
            <div
              class="dropdown-content bg-white shadow-lg rounded-md py-2 w-full transition-all duration-200 transform origin-top scale-95">
              <a *ngFor="let item of getItemsBySection('More')" [routerLink]="item.link" routerLinkActive="active"
                class="dropdown-item block px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6] transition-colors duration-150">
                <span>{{ item.label }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Section -->
    <div class="right-section flex items-center gap-4">
      <!-- Tenant Switcher -->
      <app-tenant-switcher></app-tenant-switcher>

      <!-- Balance Dropdown -->
      <div class="relative group">
        <button
          class="balance-button flex items-center space-x-2 focus:outline-none bg-white border border-gray-100 px-3 py-1.5 rounded-lg hover:shadow-sm transition-all duration-200">
          <div class="flex items-center">
            <div class="flex items-center mr-1">
              <fa-icon [icon]="['fas', 'wallet']" class="text-green-500 mr-1"></fa-icon>
              <span class="text-sm font-medium text-gray-800">${{ formatBalance(user?.balance) }}</span>
            </div>
            <fa-icon [icon]="['fas', 'chevron-down']" class="text-xs text-gray-500 hidden md:block"></fa-icon>
          </div>
        </button>
        <!-- Balance Dropdown Content -->
        <div class="dropdown-wrapper absolute right-0 top-full w-72 z-50">
          <!-- Invisible bridge to maintain hover state -->
          <div class="h-2 w-full"></div>
          <!-- Dropdown content -->
          <div
            class="dropdown-content bg-white shadow-lg rounded-lg overflow-hidden w-full transition-all duration-200 transform origin-top scale-95">
            <!-- Balance Header -->
            <div class="p-4 bg-white border-b">
              <div class="text-center">
                <div class="text-2xl font-bold text-green-500">${{ formatBalance(user?.balance) }}</div>
                <div class="text-sm text-gray-500">Current Balance</div>
              </div>
            </div>

            <!-- Panel Pricing Info -->
            <div class="p-4 bg-white border-b">
              <div class="text-center mb-3">
                <div class="text-lg font-semibold text-blue-600">Panel Pricing</div>
                <div class="text-2xl font-bold text-green-500">$7.7</div>
                <div class="text-sm text-gray-500">per month</div>
              </div>

              <div class="bg-blue-50 rounded-lg p-3">
                <div class="flex items-center justify-center text-sm text-blue-700">
                  <fa-icon [icon]="['fas', 'info-circle']" class="mr-2"></fa-icon>
                  <span>Affordable monthly subscription</span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="p-4 bg-white flex items-center justify-between">
              <button (click)="goToAddFunds()"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm flex items-center transition-colors duration-200">
                <fa-icon [icon]="['fas', 'plus']" class="mr-2"></fa-icon>
                Add funds
              </button>
              <button (click)="goToHistory()"
                class="text-blue-500 hover:text-blue-600 px-4 py-2 text-sm transition-colors duration-200">
                History
              </button>
            </div>

            <!-- Footer -->
            <div class="p-3 bg-gray-50 border-t text-center">
              <a (click)="goToCommissionInfo()"
                class="text-blue-500 hover:text-blue-600 text-sm flex items-center justify-center cursor-pointer">
                How commission works?
                <fa-icon [icon]="['fas', 'arrow-right']" class="ml-2"></fa-icon>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <app-notification-dropdown></app-notification-dropdown>

      <!-- User dropdown - Visible on all screens -->
      <div class="relative group">

        <div class="profile cursor-pointer focus:outline-none">
          <div class="avatar">
            <img [src]="getAvatarPath()" alt="Profile" class="w-10 h-10 rounded-full border-2 border-[var(--primary)]">
          </div>
          <div class="user-info" class="md:block hidden">
            <span class="font-semibold text-base block">{{ user?.user_name }}</span>
          </div>
        </div>

        <!-- Dropdown menu with improved hover behavior -->
        <div class="dropdown-wrapper absolute right-0 top-full w-48 z-50">
          <!-- Invisible bridge to maintain hover state -->
          <div class="h-2 w-full"></div>
          <!-- Dropdown content -->
          <div
            class="dropdown-content bg-white shadow-lg rounded-md py-2 w-full transition-all duration-200 transform origin-top scale-95">
            <button (click)="goToProfile()"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6]">
              Profile
            </button>
            <button (click)="goToSettings()"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6]">
              Settings
            </button>
            <button (click)="logout()"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6]">
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>