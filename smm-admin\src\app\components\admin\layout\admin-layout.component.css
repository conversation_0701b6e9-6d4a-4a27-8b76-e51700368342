.content-layout {
  @apply flex-1 w-full;
}

@media (min-width: 769px) {
  .main-content {
    @apply flex flex-1;
  }

  /* Adjust content layout for desktop (no sidebar) - full screen */
  .content-layout {
    @apply w-full;
  }

  .layout {
    @apply w-screen h-screen;
  }
}

@media (max-width: 768px) {
  .main-content {
    @apply flex-col;
  }

  .layout-main {
    @apply px-3;
  }
}

app-admin-sidebar {
  @apply w-64;
}

.layout {
  @apply flex flex-col h-full;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9f2ff 100%);
}

@media (min-width: 769px) {
  .layout {
    @apply w-full h-full;
  }
}

.layout-main {
  @apply p-6 rounded-tl-xl;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}
