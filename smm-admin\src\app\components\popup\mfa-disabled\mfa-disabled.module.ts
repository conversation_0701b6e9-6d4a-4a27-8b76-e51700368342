import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MfaDisabledComponent } from './mfa-disabled.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconsModule,
    MfaDisabledComponent
  ],
  exports: [
    MfaDisabledComponent
  ]
})
export class MfaDisabledModule { }
