<div class="overlay-black" (click)="onClose()">
  <div class="modal-container" (click)="$event.stopPropagation()">
    <div class="modal-content">
      <!-- Header -->
      <div class="modal-header">
        <h2 class="modal-title">Create promo code</h2>
        <button class="close-button" (click)="onClose()">
          <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
        </button>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="error-message">
        <fa-icon [icon]="['fas', 'exclamation-circle']" class="mr-2"></fa-icon>
        {{ errorMessage }}
      </div>

      <!-- Form -->
      <form (ngSubmit)="onSubmit()" class="promo-form">
        <!-- Promo code type -->
        <div class="form-group">
          <label class="form-label">Promo code type</label>
          <app-lite-dropdown
            [options]="promoCodeTypes"
            [selectedOption]="selectedType"
            (selected)="onSelectType($event)"
            customClassButton="bg-[#f5f7fc] rounded-lg"
            customClassDropdown="w-full">
          </app-lite-dropdown>
        </div>

        <!-- Promo code -->
        <div class="form-group">
          <label class="form-label">Promo code</label>
          <div class="relative">
            <input
              type="text"
              [(ngModel)]="promoCode"
              name="promoCode"
              class="form-input pr-20"
              placeholder="For example, HELLO500">
            <button
              type="button"
              (click)="generateRandomCode()"
              class="random-button">
              <fa-icon [icon]="['fas', 'random']"></fa-icon>
              <span class="ml-1 text-xs">Random</span>
            </button>
          </div>
        </div>

        <!-- Promo code amount -->
        <div class="form-group">
          <label class="form-label">Promo code amount</label>
          <div class="relative">
            <input
              type="number"
              [(ngModel)]="discountValue"
              name="discountValue"
              class="form-input"
              [placeholder]="selectedType === 'Discount for order' ? 'Enter percentage' : 'Enter amount'"
              min="0"
              [max]="selectedType === 'Discount for order' ? 100 : null"
              [step]="selectedType === 'Discount for order' ? 1 : 0.01">
            <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500">
              {{ selectedType === 'Discount for order' ? '%' : '$' }}
            </span>
          </div>
        </div>

        <!-- Activations count -->
        <div class="form-group">
          <label class="form-label">Activations count</label>
          <input
            type="number"
            [(ngModel)]="activationsCount"
            name="activationsCount"
            class="form-input"
            placeholder="Enter number of activations"
            min="1">
        </div>

        <!-- Submit button -->
        <button
          type="submit"
          class="save-button"
          [disabled]="isSubmitting">
          {{ isSubmitting ? 'Creating...' : 'Create promo code' }}
        </button>
      </form>
    </div>
  </div>
</div>
