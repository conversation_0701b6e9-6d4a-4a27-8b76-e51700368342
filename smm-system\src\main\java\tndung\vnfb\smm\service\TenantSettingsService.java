package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.TenantLanguageSettingsReq;
import tndung.vnfb.smm.dto.response.TenantLanguageSettingsRes;
import tndung.vnfb.smm.dto.response.TenantDefaultLanguageRes;

import java.util.List;

public interface TenantSettingsService {

    /**
     * Get language settings for current tenant
     * @return TenantLanguageSettingsRes
     */
    TenantLanguageSettingsRes getLanguageSettings();

    /**
     * Update language settings for current tenant
     * @param request TenantLanguageSettingsReq
     * @return TenantLanguageSettingsRes
     */
    TenantLanguageSettingsRes updateLanguageSettings(TenantLanguageSettingsReq request);

    /**
     * Get default language for current tenant (public endpoint)
     * @return TenantDefaultLanguageRes
     */
    TenantDefaultLanguageRes getTenantDefaultLanguage();

    /**
     * Get available languages for current tenant (public endpoint for dashboard)
     * @return List of available language codes
     */
    List<String> getTenantAvailableLanguages();
}
