import {  HttpClient, HttpClientModule } from '@angular/common/http';
import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';


@Component({
  selector: 'app-svg-icon',
  standalone: true,
  imports: [HttpClientModule],
  templateUrl: './svg-icon.component.html',
  styleUrl: './svg-icon.component.css'
})
export class SvgIconComponent implements OnChanges{
  @Input() color: string = '#000000';
  @Input() width: string = '24px';
  @Input() height: string = '24px';
  @Input() iconName: string = '';
  
  svgContent: SafeHtml = '';

  constructor(private http: HttpClient, private sanitizer: DomSanitizer) {}

  ngOnChanges(): void {
    this.loadSvg();
  }

  async loadSvg() {
    if (this.iconName) {
      try {
        this.http.get(`assets/svg/${this.iconName}.svg`, { responseType: 'text' }).subscribe((svg) => {
   
        // Thay thế màu trong SVG
        if (!svg.includes('stroke=')) {
          svg = svg.replace(/fill="[^"]*"/g, `fill="${this.color}"`);
        } else {
          svg = svg.replace(/stroke="[^"]*"/g, `stroke="${this.color}"`);
        }

        // Update width and height attributes
        svg = svg.replace(/width="[^"]*"/, `width="${this.width}"`);
        svg = svg.replace(/height="[^"]*"/, `height="${this.height}"`);

        this.svgContent = this.sanitizer.bypassSecurityTrustHtml(svg);
        });
        
      } catch (error) {
        console.error('Error loading SVG:', error);
      }
    }
  }
}
