import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { CommonModule } from '@angular/common';
import { LiteDropdownComponent } from "../../common/lite-dropdown/lite-dropdown.component";
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TicketService } from '../../../core/services/ticket.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-new-ticket',
  standalone: true,
  imports: [FormsModule, CommonModule, LiteDropdownComponent, TranslateModule],
  templateUrl: './new-ticket.component.html',
  styleUrl: './new-ticket.component.css'
})
export class NewTicketComponent implements OnInit {
  selectedForm: string | undefined;
  formOptions = [
    { key: 'Order', translationKey: 'ticket.order' },
    { key: 'Payment', translationKey: 'ticket.payment' },
    { key: 'Request', translationKey: 'ticket.request' },
    { key: 'Point', translationKey: 'ticket.point' },
    { key: 'Child Panel', translationKey: 'child_panel.title' },
    { key: 'Other', translationKey: 'ticket.other' }
  ];
  translatedOptions: string[] = [];

  @Output() close = new EventEmitter<void>();
  @Output() dismiss = new EventEmitter<void>();
  @Output() ticketCreated = new EventEmitter<any>();

  // Options for the different forms
  orderOptions: string[] = [];
  paymentOptions: string[] = [];
  requestOptions: string[] = [];
  pointOptions: string[] = ['Redeem My Points', 'Ask A Question About Points'];

  // Form data
  orderId: string = '';
  selectedOrderOption: string = '';
  selectedPaymentOption: string = '';
  selectedRequestOption: string = '';
  selectedPointOption: string = '';
  message: string = '';
  isSubmitting: boolean = false;
  submitError: string | null = null;

  constructor(
    public translateService: TranslateService,
    private ticketService: TicketService
  ) {}

  onClose() {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent) {
    // Kiểm tra nếu click vào chính overlay, không phải bên trong modal
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onDismiss() {
    this.dismiss.emit();
  }

  ngOnInit() {
    // Translate all options for the dropdown
    this.translatedOptions = this.formOptions.map(option =>
      this.translateService.instant(option.translationKey)
    );

    // Initialize options for the different forms
    this.orderOptions = [
      this.translateService.instant('ticket.refill'),
      this.translateService.instant('ticket.cancel'),
      this.translateService.instant('ticket.speed_up'),
      this.translateService.instant('ticket.other')
    ];

    this.paymentOptions = [
      'Paypal', 'Skrill', 'Payoneer', 'Perfect Money', 'Western Union', 'BTC', 'Crypto Currency',
      this.translateService.instant('ticket.other')
    ];

    this.requestOptions = [
      'A Feature', 'A Service',
      this.translateService.instant('ticket.other')
    ];
  }

  selectOption(value: string) {
    console.log(value);
    // Find the corresponding key for the selected translated value
    const selectedOption = this.formOptions.find(option =>
      this.translateService.instant(option.translationKey) === value
    );

    if (selectedOption) {
      this.selectedForm = selectedOption.key;
      console.log('Selected form key:', this.selectedForm);
    }
  }

  selectOrderOption(value: string) {
    this.selectedOrderOption = value;
  }

  selectPaymentOption(value: string) {
    this.selectedPaymentOption = value;
  }

  selectRequestOption(value: string) {
    this.selectedRequestOption = value;
  }

  selectPointOption(value: string) {
    this.selectedPointOption = value;
  }

  onSubmit() {
    if (!this.selectedForm) {
      this.submitError = 'Please select a ticket type';
      return;
    }

    if (!this.message) {
      this.submitError = 'Please enter a message';
      return;
    }

    this.isSubmitting = true;
    this.submitError = null;

    // Format the description based on the selected form type
    let subject = this.selectedForm;
    let description = '';
    let placeholder = '';
    let data = '';

    switch (this.selectedForm) {
      case 'Order':
        placeholder = 'Order ID';
        data = this.orderId || '';
        if (this.selectedOrderOption) {
          data += '\n' + this.selectedOrderOption;
        }
        break;
      case 'Payment':
        if (this.selectedPaymentOption) {
          placeholder = 'Payment';
          data = this.selectedPaymentOption;
        }
        break;
      case 'Request':
        if (this.selectedRequestOption) {
          placeholder = 'Request';
          data = this.selectedRequestOption;
        }
        break;
      case 'Point':
        if (this.selectedPointOption) {
          placeholder = 'Point';
          data = this.selectedPointOption;
        }
        break;
      // Child Panel and Other don't have additional fields
    }

    // Format the description according to the specified format
    // description = subject + '\n' + placeholder + ':' + data + '\n' + message
    if (placeholder && data) {
      description = placeholder + ': ' + data + '\n' + this.message;
    }
    else if (data) {
      description = data + '\n' + this.message;
    }
    else {
      description =  this.message;
    }

    this.ticketService.createTicket(subject, description)
      .pipe(finalize(() => this.isSubmitting = false))
      .subscribe({
        next: (response) => {
          console.log('Ticket created successfully:', response);
          // Emit the ticketCreated event with the new ticket data
          this.ticketCreated.emit(response);
          this.onClose();
        },
        error: (error) => {
          console.error('Error creating ticket:', error);
          this.submitError = 'Failed to create ticket. Please try again.';
        }
      });
  }
}
