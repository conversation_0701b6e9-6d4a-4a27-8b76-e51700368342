import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserService } from './user.service';
import { CurrencyRes, UserRes } from '../../model/response/user-res.model';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CurrencyService {
  private _currentCurrency$ = new BehaviorSubject<CurrencyRes | undefined>(undefined);

  constructor(private userService: UserService) {
    // Subscribe to user changes to get the preferred currency
    this.userService.user$.subscribe(user => {
      if (user && user.preferred_currency) {
        this._currentCurrency$.next(user.preferred_currency);
      }
    });
  }

  /**
   * Get the current currency as an observable
   */
  get currency$(): Observable<CurrencyRes | undefined> {
    return this._currentCurrency$.asObservable();
  }

  /**
   * Get the current currency value
   */
  get currencyValue(): CurrencyRes | undefined {
    return this._currentCurrency$.getValue();
  }

  /**
   * Convert a price from base currency to the user's preferred currency
   * @param price The price in base currency
   * @returns The converted price
   */
  convertPrice(price: number): number {
    const currency = this.currencyValue;
    if (!currency || !currency.exchange_rate) {
      return price; // Return original price if no currency or exchange rate
    }

    return price * currency.exchange_rate;
  }

  /**
   * Format a price with the user's preferred currency symbol
   * @param price The price to format
   * @returns The formatted price with currency symbol
   */
  formatPrice(price: number): string {
    const currency = this.currencyValue;
    const convertedPrice = this.convertPrice(price);

    // Format the number with thousand separators
    const formattedPrice = convertedPrice.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 3,
      useGrouping: true
    }).replace(/,/g, '.');

    // Add the currency symbol if available
    if (currency && currency.symbol) {
      return `${formattedPrice} ${currency.symbol}`;
    }

    // Default to $ if no currency symbol is available
    return `${formattedPrice} $`;
  }

  /**
   * Format balance with up to 6 decimal places, removing trailing zeros
   * @param balance The balance to format
   * @returns The formatted balance string
   */
  formatBalance(balance: number | undefined): string {
    if (balance === undefined || balance === null) return '0.00';
    // Use toFixed(6) then parseFloat to remove trailing zeros
    return parseFloat(balance.toFixed(6)).toString();
  }

  /**
   * Format balance with currency symbol
   * @param balance The balance to format
   * @returns The formatted balance with currency symbol
   */
  formatBalanceWithCurrency(balance: number | undefined): string {
    const formattedBalance = this.formatBalance(balance);
    const currency = this.currencyValue;

    // Add the currency symbol if available
    if (currency && currency.symbol) {
      return `${formattedBalance} ${currency.symbol}`;
    }

    // Default to $ if no currency symbol is available
    return `$${formattedBalance}`;
  }
}
