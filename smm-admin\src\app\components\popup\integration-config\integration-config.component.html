<div class="overlay-black" >
  <div class="popup-container">
    <!-- Header -->
    <div class="popup-header">
      <h2 class="popup-title">{{ getIntegrationTitle() }}</h2>
      <button class="close-button" (click)="onClose()">
        <fa-icon icon="xmark"></fa-icon>
      </button>
    </div>

    <!-- Content -->
    <div class="popup-content">
      <!-- Username/Value Field -->
      <div class="form-group">
        <label for="username" class="form-label">{{ getValueFieldLabel() }}</label>
        <input
          type="text"
          id="username"
          class="form-input"
          [(ngModel)]="formData.username"
          [placeholder]="getValueFieldPlaceholder()">
      </div>

      <!-- Position Field -->
      <div class="form-group">
        <label for="position" class="form-label">Position</label>
        <select
          id="position"
          class="form-select"
          [(ngModel)]="formData.position">
          <option *ngFor="let pos of positionOptions" [value]="pos">
            {{ pos.charAt(0).toUpperCase() + pos.slice(1) }}
          </option>
        </select>
      </div>

      <!-- No additional fields -->
    </div>

    <!-- Footer -->
    <div class="popup-footer">
      <button class="cancel-button" (click)="onClose()">Cancel</button>
      <button class="save-button" (click)="onSave()">
        {{ integration?.enabled ? 'Update' : 'Connect' }}
      </button>
    </div>
  </div>
</div>
