import { Injectable } from '@angular/core';
import { AdminServiceService } from './admin-service.service';
import { ToastService } from './toast.service';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PlatformService {
  constructor(
    private adminService: AdminServiceService,
    private toastService: ToastService
  ) {}

  /**
   * Loads platforms with services
   * @param callback Callback function to execute after completion
   * @param errorCallback Optional callback function to execute on error
   */
  loadPlatformsWithServices(
    callback?: (platforms: SuperPlatformRes[]) => void,
    errorCallback?: (error: any) => void
  ): void {
    this.adminService.getPlatformsWithServices().subscribe({
      next: (platforms) => {
        // Filter out hidden platforms
        const visiblePlatforms = platforms.filter(platform => !platform.hide);
        if (callback) callback(visiblePlatforms);
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.toastService.showError(error?.message || 'Failed to load platforms and services');
        if (errorCallback) errorCallback(error);
      }
    });
  }

  /**
   * Checks if a category platform is selected
   * @param categoryId The category ID
   * @param platformId The platform ID
   * @param categoryPlatformSelections The category platform selections map
   * @returns True if the platform is selected for the category, false otherwise
   */
  isCategoryPlatformSelected(
    categoryId: number,
    platformId: number,
    categoryPlatformSelections: Map<number, Set<number>>
  ): boolean {
    const selectedPlatforms = categoryPlatformSelections.get(categoryId);
    return selectedPlatforms ? selectedPlatforms.has(platformId) : false;
  }

  /**
   * Selects a platform for a category
   * @param category The category
   * @param platform The platform
   * @param categoryPlatformSelections The category platform selections map
   */
  selectCategoryPlatform(
    category: any,
    platform: SuperPlatformRes,
    categoryPlatformSelections: Map<number, Set<number>>
  ): void {
    // Clear any existing selections for this category
    let selectedPlatforms = categoryPlatformSelections.get(category.id);
    if (!selectedPlatforms) {
      selectedPlatforms = new Set<number>();
      categoryPlatformSelections.set(category.id, selectedPlatforms);
    } else {
      selectedPlatforms.clear();
    }

    // Select only the clicked platform
    selectedPlatforms.add(platform.id);

    // Update the category's platform info
    category.platformName = platform.name;
    category.platformIcon = platform.icon;

    // Call API to change the category's platform
    this.adminService.changeCategoryPlatform(category.id, platform.id).subscribe({
      next: () => {
        console.log(`Category ${category.id} (${category.name}) platform changed to ${platform.id} (${platform.name})`);
      },
      error: (error) => {
        console.error(`Error changing category ${category.id} platform:`, error);
        // Revert UI changes on error
        selectedPlatforms.clear();
        // Try to find the original platform
        const originalPlatform = category.platformName ?
          this.findPlatformByName(category.platformName) : null;
        if (originalPlatform) {
          selectedPlatforms.add(originalPlatform.id);
        }
      }
    });
  }

  /**
   * Finds a platform by name
   * @param platformName The platform name
   * @returns The platform, or null if not found
   */
  private findPlatformByName(platformName: string): SuperPlatformRes | null {
    // We need to get platforms from the admin service
    let foundPlatform: SuperPlatformRes | null = null;

    // This is a synchronous method, so we can't use the async API call directly
    // Instead, we'll rely on the most recent data from the admin service
    this.adminService.platforms$.subscribe(platforms => {
      foundPlatform = platforms.find(p => p.name === platformName) || null;
    }).unsubscribe();

    return foundPlatform;
  }

  /**
   * Finds a platform ID by name
   * @param platformName The platform name
   * @param allPlatforms All platforms
   * @returns The platform ID, or 0 if not found
   */
  findPlatformIdByName(platformName: string | undefined, allPlatforms: SuperPlatformRes[]): number {
    if (!platformName) return 0;

    const platform = allPlatforms.find(p => p.name === platformName);
    return platform ? platform.id : 0;
  }

  /**
   * Reorders platform positions
   * @param id1 The first platform ID
   * @param id2 The second platform ID
   * @returns An observable of the reorder operation
   */
  swapPlatformPositions(id1: number, id2: number): Observable<void> {
    return this.adminService.swapPlatformPositions(id1, id2);
  }

  /**
   * Deletes a platform
   * @param id The platform ID
   * @returns An observable of the delete operation
   */
  deletePlatform(id: number): Observable<void> {
    return this.adminService.deletePlatform(id);
  }
}
