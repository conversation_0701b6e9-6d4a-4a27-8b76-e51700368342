.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.popup-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.close-button:hover {
  color: #374151;
}

.popup-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

/* Selected Languages Tags */
.selected-languages {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.language-tag {
  display: flex;
  align-items: center;
  background: #f1f5f9;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px 12px;
  gap: 10px;
  transition: all 0.2s;
  position: relative;
}

.language-tag:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.language-tag.is-default {
  background: #eff6ff;
  border-color: var(--primary, #3b82f6);
}

.language-tag.is-default:hover {
  background: #dbeafe;
}

.language-tag .language-flag {
  width: 20px;
  height: 15px;
  border-radius: 2px;
  overflow: hidden;
}

.language-tag .language-flag span {
  width: 100%;
  height: 100%;
  display: block;
}

.language-tag .language-name {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
}

.language-tag.is-default .language-name {
  color: var(--primary, #3b82f6);
  font-weight: 600;
}

/* Default Selector */
.default-selector {
  display: flex;
  align-items: center;
  margin-right: 4px;
}

.default-radio {
  display: none;
}

.default-label {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-indicator {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: white;
}

.language-tag:hover .radio-indicator {
  border-color: #9ca3af;
}

.language-tag.is-default .radio-indicator {
  border-color: var(--primary, #3b82f6);
  background: var(--primary, #3b82f6);
}

.radio-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.2s;
}

.language-tag.is-default .radio-dot {
  background: white;
}

.remove-btn {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 0.75rem;
}

.remove-btn:hover:not(:disabled) {
  background: #ef4444;
  color: white;
  transform: scale(1.1);
}

.remove-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Info Section */
.default-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.info-icon {
  color: var(--primary, #3b82f6);
  font-size: 0.875rem;
}

.info-text {
  color: #64748b;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Empty State */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 24px;
  color: #9ca3af;
  font-size: 0.875rem;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
}



.language-flag {
  width: 32px;
  height: 24px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-flag span {
  width: 100%;
  height: 100%;
  display: block;
}

.language-info {
  flex: 1;
}

.language-name {
  font-weight: 500;
  color: #111827;
  font-size: 0.95rem;
}

.language-code {
  color: #6b7280;
  font-size: 0.8rem;
  margin-top: 2px;
}

.language-radio {
  margin-left: 12px;
}

.language-radio input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary, #3b82f6);
}

.language-checkbox {
  margin-left: 12px;
}

.language-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary, #3b82f6);
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f9fafb;
}

.btn-primary {
  background-color: var(--primary, #3b82f6);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-outline {
  background-color: white;
  color: var(--primary, #3b82f6);
  border-color: var(--primary, #3b82f6);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary, #3b82f6);
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
  gap: 6px;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
