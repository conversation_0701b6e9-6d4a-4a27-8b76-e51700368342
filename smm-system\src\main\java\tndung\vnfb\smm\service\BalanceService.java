package tndung.vnfb.smm.service;

import tndung.vnfb.smm.constant.enums.TransactionSource;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GUser;

import java.math.BigDecimal;

/**
 * Service for managing user balance operations.
 * Ensures all balance changes are properly logged in GTransaction.
 */
public interface BalanceService {

    /**
     * Add funds to user balance with transaction logging
     * @param user The user to add funds to
     * @param amount Amount to add (positive value)
     * @param source Transaction source
     * @param note Optional note for the transaction
     * @return Updated user with new balance
     */
    GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note);

    /**
     * Deduct funds from user balance with transaction logging
     * @param user The user to deduct funds from
     * @param amount Amount to deduct (positive value)
     * @param source Transaction source
     * @param note Optional note for the transaction
     * @return Updated user with new balance
     * @throws tndung.vnfb.smm.exception.InvalidParameterException if insufficient balance
     */
    GUser deductBalance(GUser user, BigDecimal amount, TransactionType type, TransactionSource source, String note);

    /**
     * Add funds to user balance with order reference
     * @param user The user to add funds to
     * @param amount Amount to add (positive value)
     * @param source Transaction source
     * @param note Optional note for the transaction
     * @param order Related order (can be null)
     * @return Updated user with new balance
     */
    GUser addBalance(GUser user, BigDecimal amount, TransactionSource source, String note, GOrder order);

    /**
     * Deduct funds from user balance with order reference
     * @param user The user to deduct funds from
     * @param amount Amount to deduct (positive value)
     * @param source Transaction source
     * @param note Optional note for the transaction
     * @param order Related order (can be null)
     * @return Updated user with new balance
     * @throws tndung.vnfb.smm.exception.InvalidParameterException if insufficient balance
     */
    GUser deductBalance(GUser user, BigDecimal amount, TransactionSource source, String note, GOrder order);

    /**
     * Process refund for an order
     * @param order The order to refund
     * @return Updated user with refunded balance
     */
    GUser processRefund(GOrder order);

    /**
     * Check if user has sufficient balance
     * @param user The user to check
     * @param amount Amount to check
     * @return true if user has sufficient balance
     */
    boolean hasSufficientBalance(GUser user, BigDecimal amount);

    /**
     * Transfer balance between users (for future use)
     * @param fromUser User to transfer from
     * @param toUser User to transfer to
     * @param amount Amount to transfer
     * @param note Transaction note
     * @return Array containing [fromUser, toUser] with updated balances
     */
    GUser[] transferBalance(GUser fromUser, GUser toUser, BigDecimal amount, String note);

    /**
     * Calculate the correct cancel refund amount by accounting for previous partial refunds
     * @param order The order to calculate cancel refund for
     * @return The amount that should be refunded for cancellation
     */
    BigDecimal calculateCancelRefundAmount(GOrder order);
}
