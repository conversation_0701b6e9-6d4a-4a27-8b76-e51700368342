import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { NotificationPopupContentComponent } from '../notification-popup-content/notification-popup-content.component';
import { CommonModule } from '@angular/common';
import { SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-notification-popup',
  standalone: true,
  imports: [TranslateModule, IconsModule, NotificationPopupContentComponent, CommonModule],
  templateUrl: './notification-popup.component.html',
  styleUrl: './notification-popup.component.css'
})
export class NotificationPopupComponent {
  @Input() title: string = 'Notification';
  @Input() content: SafeHtml = '';
  @Input() notificationId: number | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() dismiss = new EventEmitter<void>();
  @Input() autoDismiss: boolean = false;
  @Input() dismissHours: number = 24;
  onClose() {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent) {
    // Kiểm tra nếu click vào chính overlay, không phải bên trong modal
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onDismiss() {
    this.dismiss.emit();
  }
}
