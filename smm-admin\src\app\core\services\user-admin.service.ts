import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize } from 'rxjs';
import { UserSearchReq } from '../../model/request/user-search-req.model';
import { GUserSuperRes } from '../../model/response/g-user-super-res.model';
import { PageResponse } from '../../model/response/page-response.model';
import { GUserPasswordRes } from '../../model/response/g-user-password-res.model';
import { ConfigService } from './config.service';
import { LoadingService } from './loading.service';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root'
})
export class UserAdminService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _users$ = new BehaviorSubject<GUserSuperRes[]>([]);
  private _pagination$ = new BehaviorSubject<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private loadingService: LoadingService,
    private toastService: ToastService
  ) {}

  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  get users$(): Observable<GUserSuperRes[]> {
    return this._users$.asObservable();
  }

  get pagination$(): Observable<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }> {
    return this._pagination$.asObservable();
  }

  /**
   * Search users with filters (admin only)
   * @param req Search request with filters
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Observable of paginated user results
   */
  searchUsers(req: UserSearchReq, page: number = 0, size: number = 10): Observable<PageResponse<GUserSuperRes>> {
    this._loading$.next(true);
    this.loadingService.show('Loading users...');

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (req.keyword) {
      params = params.set('keyword', req.keyword);
    }

    if (req.status) {
      params = params.set('status', req.status);
    }

    return this.http.get<PageResponse<GUserSuperRes>>(`${this.configService.apiUrl}/users/search`, { params })
      .pipe(
        finalize(() => {
          this._loading$.next(false);
          this.loadingService.hide();
        })
      );
  }

  /**
   * Load users with filters and update the BehaviorSubjects
   * @param req Search request with filters
   * @param page Page number (0-based)
   * @param size Page size
   */
  loadUsers(req: UserSearchReq = {}, page: number = 0, size: number = 10): void {
    this.searchUsers(req, page, size).subscribe({
      next: (response) => {
        this._users$.next(response.content);
        this._pagination$.next({
          pageNumber: response.pageable.page_number,
          pageSize: response.pageable.page_size,
          totalElements: response.total_elements,
          totalPages: response.total_pages
        });
      },
      error: (error) => {
        this.toastService.showError(error?.message || 'Failed to load users');
        this._users$.next([]);
      }
    });
  }

  /**
   * Activate a user account
   * @param userId User ID to activate
   * @returns Observable of the response
   */
  activateUser(userId: number): Observable<any> {
    this._loading$.next(true);
    this.loadingService.show('Activating user...');
    return this.http.put<any>(
      `${this.configService.apiUrl}/users/${userId}/active`,
      {}
    ).pipe(
      finalize(() => {
        this._loading$.next(false);
        this.loadingService.hide();
      })
    );
  }

  /**
   * Deactivate a user account
   * @param userId User ID to deactivate
   * @param reason Reason for deactivation (optional)
   * @returns Observable of the response
   */
  deactivateUser(userId: number, reason?: string): Observable<any> {
    this._loading$.next(true);
    this.loadingService.show('Deactivating user...');

    // If reason is provided, we could send it in the request body
    const body = reason ? { reason } : {};

    return this.http.put<any>(
      `${this.configService.apiUrl}/users/${userId}/deactivate`,
      body
    ).pipe(
      finalize(() => {
        this._loading$.next(false);
        this.loadingService.hide();
      })
    );
  }

  /**
   * Reset a user's password
   * @param userId User ID to reset password for
   * @returns Observable of the response with new password
   */
  resetPassword(userId: number): Observable<GUserPasswordRes> {
    this._loading$.next(true);
    this.loadingService.show('Resetting password...');

    return this.http.put<GUserPasswordRes>(
      `${this.configService.apiUrl}/users/${userId}/reset-password`,
      {}
    ).pipe(
      finalize(() => {
        this._loading$.next(false);
        this.loadingService.hide();
      })
    );
  }

  /**
   * Delete custom discount prices for a user
   * @param userId User ID to delete custom prices for
   * @returns Observable of the response
   */
  deleteCustomDiscountByUser(userId: number): Observable<any> {
    this._loading$.next(true);
    this.loadingService.show('Deleting custom prices...');

    return this.http.delete<any>(
      `${this.configService.apiUrl}/users/${userId}/services/custom-discount`
    ).pipe(
      finalize(() => {
        this._loading$.next(false);
        this.loadingService.hide();
      })
    );
  }

  /**
   * Edit a user (admin only)
   * @param userId User ID to edit
   * @param userReq User information to update
   * @returns Observable with updated user data
   */
  editUser(userId: number, userReq: any): Observable<GUserSuperRes> {
    this._loading$.next(true);
    this.loadingService.show('Updating user...');
    return this.http
      .put<GUserSuperRes>(
        `${this.configService.apiUrl}/users/${userId}/edit`,
        userReq
      )
      .pipe(
        finalize(() => {
          this._loading$.next(false);
          this.loadingService.hide();
        })
      );
  }
}
