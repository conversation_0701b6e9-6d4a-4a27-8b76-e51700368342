import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { AdminOrderService } from '../../../core/services/admin-order.service';
import { ToastService } from '../../../core/services/toast.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-set-partial',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './set-partial.component.html',
  styleUrl: './set-partial.component.css'
})
export class SetPartialComponent {
  @Input() orderId: number = 0;
  @Input() quantity: number = 0;
  @Output() close = new EventEmitter<void>();
  @Output() partialUpdated = new EventEmitter<number>();

  remains: number = 0;
  isLoading: boolean = false;

  constructor(
    private adminOrderService: AdminOrderService,
    private toastService: ToastService
  ) {}

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  savePartial(): void {
    // Ensure remains is a valid number and orderId is provided
    if (this.remains !== null && this.remains !== undefined && this.orderId) {
      // Validate that remains is not greater than quantity
      if (this.remains > this.quantity) {
        this.toastService.showError('Remains cannot be greater than quantity');
        return;
      }

      if (this.remains < 0) {
        this.toastService.showError('Remains cannot be negative');
        return;
      }

      this.isLoading = true;

      // Call the API to update the order status to PARTIAL with remains
      this.adminOrderService.updateOrderStatusWithRemains(this.orderId, 'PARTIAL', this.remains)
        .pipe(
          finalize(() => this.isLoading = false)
        )
        .subscribe({
          next: (updatedOrder) => {
            this.toastService.showSuccess('Order marked as partial successfully');
            this.partialUpdated.emit(this.remains);
            this.onClose();
          },
          error: (error) => {
            console.error('Error updating order to partial:', error);
            this.toastService.showError(error?.message || 'Failed to mark order as partial');
          }
        });
    } else if (!this.orderId) {
      this.toastService.showError('Order ID is required');
    }
  }
}
