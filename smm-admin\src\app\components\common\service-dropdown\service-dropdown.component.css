:host {
  display: block;
  position: relative;
}

/* Dropdown menu container */
.service-dropdown-menu-container {
  position: fixed;
  max-height: 400px;
  overflow-y: auto;
  width: auto;
  min-width: 200px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 99999; /* Very high z-index to ensure it's above everything */
  transform: translateZ(0); /* Force hardware acceleration for smoother rendering */
}

/* Style for the body when dropdown is open */
body.service-dropdown-open {
  /* This prevents the body from scrolling when the dropdown is open */
  /* overflow: hidden; */
}
