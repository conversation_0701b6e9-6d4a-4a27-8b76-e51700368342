/* Desktop Layout */
.settings-layout {
  @apply w-full flex;
  padding-left: 10rem;
  min-height: calc(100vh - 80px);
  position: relative;
}

.content-layout {
  @apply flex-1;
  padding-bottom: 2rem;
  margin-left: 2rem;
  margin-top: 1rem;
  position: relative;
}

.sidebar-container {
  @apply flex-shrink-0;
  position: sticky;
  top: 1rem;
  height: 100%;
  align-self: flex-start;
}

app-settings-sidebar {
  width: 18rem;
}

/* Mobile Layout */
@media (max-width: 768px) {
  .settings-layout {
    padding-left: 0;
  }

  .content-layout {
    @apply w-full;
    margin-left: 0;
    padding: 1rem;
  }

  /* Toggle Button */
  .settings-toggle-btn {
    transition: all 0.3s ease;
  }

  .settings-toggle-btn:hover {
    transform: rotate(30deg);
  }

  /* Mobile Sidebar Container */
  .mobile-sidebar-container {
    visibility: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    transition: visibility 0.3s ease;
  }

  .mobile-sidebar-container.open {
    visibility: visible;
  }

  /* Overlay */
  .sidebar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .mobile-sidebar-container.open .sidebar-overlay {
    opacity: 1;
  }

  /* Sidebar */
  .mobile-sidebar {
    position: absolute;
    top: 0;
    right: 0;
    width: 85%;
    max-width: 300px;
    height: 100%;
    background-color: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    z-index: 101; /* Ensure it's above the main admin sidebar */
  }

  .mobile-sidebar-container.open .mobile-sidebar {
    transform: translateX(0);
  }

  /* Header */
  .mobile-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
  }

  .mobile-sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
  }

  .close-button {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transition: background-color 0.2s;
  }

  .close-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  /* Content */
  .mobile-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
  }

  .mobile-sidebar-content app-settings-sidebar {
    width: 100%;
  }
}
