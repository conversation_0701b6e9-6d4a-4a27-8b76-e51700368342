<div class="relative inline-flex items-center justify-center w-6 h-6 cursor-pointer hover:opacity-80"
     [ngClass]="{'cursor-wait': loading}"
     [class.loading]="loading"
     (click)="toggleCheckbox()">
  <ng-container *ngIf="!loading">
    <app-svg-icon [iconName]="checked ? 'heart-filled' : 'heart'"
                 [color]="checked ? '#FF7883' : '#000'"
                 [width]="'20px'"
                 [height]="'20px'"
                 class="transition-all duration-300"></app-svg-icon>
  </ng-container>
  <div *ngIf="loading" class="loading-spinner"></div>
</div>