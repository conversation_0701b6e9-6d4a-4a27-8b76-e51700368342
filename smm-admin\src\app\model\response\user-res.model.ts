import {Tokens} from '../tokens.model';

export interface CurrencyRes {
  code: string;
  symbol: string;
  exchange_rate: number;
}

export interface UserRes {
  id: number;
  user_name: string;
  name: string;
  email: string;
  phone: string;
  balance: number;
  tokens: Tokens;
  avatar: string;
  preferred_currency?: CurrencyRes;
  mfa_enabled?: boolean;
  api_key?: string;
  created_at?: string;
  last_login_at?: string;
  total_order: number;
}
