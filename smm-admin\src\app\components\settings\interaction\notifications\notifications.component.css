.notifications-container {
  @apply w-full max-w-5xl mx-auto;
}

.header-section {
  @apply mb-8;
}

.add-button {
  @apply shadow-sm;
}

.section-container {
  @apply bg-white rounded-lg shadow-sm  md:p-6;
}

.section-header {
  @apply border-b border-gray-200 pb-4 mb-6;
}

.notification-list {
  @apply space-y-4;
}

.empty-state {
  @apply py-8 text-center text-gray-500 italic;
}

.notification-item {
  @apply flex justify-between items-start p-4 bg-[#f5f7fc] rounded-lg border border-gray-200 hover:border-[#30B0C7] transition-colors;
  width: 100%;
}

.notification-content {
  @apply flex items-start gap-4 flex-1;
}

.notification-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0;
}

.notification-details {
  @apply flex-1;
}

.notification-title {
  @apply text-lg font-medium text-gray-800 mb-1;
}

.notification-text {
  @apply text-sm text-gray-600 mb-2;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.notification-text.collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 3em;
}

.notification-text img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 8px 0;
}

/* Handle other embedded content */
.notification-text iframe,
.notification-text video,
.notification-text embed {
  max-width: 100%;
  max-height: 300px;
  margin: 8px 0;
}

/* Style for tables in notification content */
.notification-text table {
  max-width: 100%;
  border-collapse: collapse;
  margin: 8px 0;
  overflow-x: auto;
  display: block;
}

.notification-text table td,
.notification-text table th {
  border: 1px solid #ddd;
  padding: 8px;
}

.content-toggle {
  @apply text-xs text-[#30B0C7] cursor-pointer mt-1 inline-block;
  font-weight: 500;
}

.notification-meta {
  @apply flex flex-wrap gap-4 text-xs text-gray-500;
}

.meta-item {
  @apply flex items-center gap-1;
}

.meta-icon {
  @apply text-gray-400;
}

.notification-actions {
  @apply flex items-center gap-3;
}

.action-button {
  @apply p-2 rounded-full text-gray-500 hover:bg-gray-200 transition-colors;
}

.action-button.edit {
  @apply hover:text-blue-600;
}

.action-button.delete {
  @apply hover:text-red-600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notification-item {
    @apply flex-col items-start;
  }

  .notification-content {
    @apply w-full;
  }

  .notification-details {
    @apply w-full;
  }

  .notification-text {
    max-width: 100%;
  }

  .notification-actions {
    @apply w-full justify-end mt-4 pt-4 border-t border-gray-200;
  }
}
