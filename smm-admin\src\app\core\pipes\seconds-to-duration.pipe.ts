import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
  name: 'secondsToDuration',
  standalone: true
})
export class SecondsToDurationPipe implements PipeTransform {
  constructor(private translate: TranslateService) {}

  transform(seconds: number | null | undefined): string {
    if (seconds === null || seconds === undefined || isNaN(seconds) || seconds < 0) {
      return 'N/A';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    let result = '';

    if (hours > 0) {
      result += `${hours} ${this.translate.instant('time.hours')} `;
    }
    
    if (minutes > 0 || (hours === 0 && remainingSeconds === 0)) {
      result += `${minutes} ${this.translate.instant('time.minutes')} `;
    }
    
    if (remainingSeconds > 0 && hours === 0) {
      result += `${remainingSeconds} ${this.translate.instant('time.seconds')}`;
    }

    return result.trim();
  }
}
