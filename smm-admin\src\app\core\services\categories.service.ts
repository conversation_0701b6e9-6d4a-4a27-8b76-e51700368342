import { HttpClient } from '@angular/common/http';
import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ConfigService } from './config.service';
import { Status } from '../../constant/status';

import { BehaviorSubject, Observable, Subject, of, switchMap, tap, shareReplay } from 'rxjs';
import { finalize, map } from 'rxjs/operators';
import { SuperPlatformRes } from '../../model/response/super-platform.model';


@Injectable({ providedIn: 'root' })
export class CategoriesService {
  get loadingAdd$(): BehaviorSubject<boolean> {
    return this._loadingAdd$;
  }
  get get$(): Subject<void> {
    return this._get$;
  }

  get platformsSubject$(): BehaviorSubject<SuperPlatformRes[]> {
    return this._platformsSubject$;
  }
  private _loadingAdd$ = new BehaviorSubject<boolean>(false);
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _get$ = new Subject<void>();
  private _platformsSubject$ = new BehaviorSubject<SuperPlatformRes[]>([]);

  // Cache for platforms data
  private platformsCache$: Observable<SuperPlatformRes[]> | null = null;


  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object,
    private configService: ConfigService
  ) {
    // Only set up subscription in browser environment
    if (isPlatformBrowser(this.platformId)) {
      this._get$
        .pipe(
          tap(() => this._loading$.next(true)),
          switchMap(() => this.getPlatforms(true)) // Force refresh when explicitly requested
        )
        .subscribe((searchResult: SuperPlatformRes[]) => {
          this._platformsSubject$.next(searchResult);
        });
    }
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  /**
   * Get platforms data with caching to prevent duplicate API calls
   * @param forceRefresh Whether to force a refresh of the data from the API
   * @returns Observable of platforms data
   */
  public getPlatforms(forceRefresh: boolean = false): Observable<SuperPlatformRes[]> {
    console.log('CategoriesService - Getting platforms, forceRefresh:', forceRefresh);

    // If we're on the server, return an empty array to avoid localStorage issues
    if (!isPlatformBrowser(this.platformId)) {
      console.log('CategoriesService - Server-side rendering, returning empty array');
      return of([]);
    }

    // If we have cached data and don't need to refresh, return the cached data
    if (this.platformsCache$ && !forceRefresh) {
      console.log('CategoriesService - Returning cached platforms data');
      return this.platformsCache$;
    }

    // Otherwise, make the API call and cache the result
    console.log('CategoriesService - Fetching platforms from API');
    console.log('CategoriesService - API URL:', `${this.configService.apiUrl}/platforms`);

    this._loading$.next(true);

    this.platformsCache$ = this.http
      .get<SuperPlatformRes[]>(`${this.configService.apiUrl}/platforms`)
      .pipe(
        map(platforms => {
          console.log('CategoriesService - Platforms received:', platforms);
          // Sort platforms by sort field
          platforms = platforms.sort((a, b) => a.sort - b.sort);

          platforms.forEach(p => {
            // Sort categories by sort field
            p.categories = [...p.categories].sort((a, b) => a.sort - b.sort);
            // p.categories.forEach(c => {
            //   c.hide = c.status !== Status.ACTIVATED;
            // });
            // p.hide = p.categories.every(c => c.hide)
          });
          this.platformsSubject$.next(platforms);
          return platforms;
        }),
        finalize(() => this._loading$.next(false)),
        // Use shareReplay to cache the result and share it with multiple subscribers
        shareReplay(1)
      );

    return this.platformsCache$;
  }

  /**
   * Clear the platforms cache to force a fresh API call on the next getPlatforms() call
   */
  public clearPlatformsCache(): void {
    console.log('CategoriesService - Clearing platforms cache');
    this.platformsCache$ = null;
  }

  // active(id :number): Observable<SuperGeneralSvRes> {
  //   return this.http
  //     .put<SuperGeneralSvRes>(`${this.configService.apiUrl}/categories/${id}/active`, null);
  // }

  // deactivate(id :number): Observable<SuperGeneralSvRes> {
  //   return this.http
  //     .put<SuperGeneralSvRes>(`${this.configService.apiUrl}/categories/${id}/deactivate`, null);
  // }
}
