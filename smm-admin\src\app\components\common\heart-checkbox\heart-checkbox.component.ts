import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SvgIconComponent } from "../svg-icon/svg-icon.component";
@Component({
  selector: 'app-heart-checkbox',
  standalone: true,
  imports: [ CommonModule,  SvgIconComponent],
  templateUrl: './heart-checkbox.component.html',
  styleUrl: './heart-checkbox.component.css'
})
export class HeartCheckboxComponent {
  @Input() checked = false;
  @Input() serviceId: number | null = null;
  @Input() loading = false;
  @Output() toggleFavorite = new EventEmitter<number | null>();

  toggleCheckbox() {
    if (!this.loading) {
      this.loading = true;
      this.toggleFavorite.emit(this.serviceId);
    }
  }

  // Method to be called from parent component when loading is complete
  setLoading(isLoading: boolean) {
    this.loading = isLoading;
  }
}
