import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { NewServiceComponent } from '../new-service/new-service.component';

import { IconName, IconPrefix } from '@fortawesome/fontawesome-svg-core';

interface ResourceItem {
  title: string;
  description: string;
  iconArray: [IconPrefix, IconName]; // [prefix, iconName]
  iconClass: string;
  rightIcon?: string; // Making this optional since we're not using it anymore
}


@Component({
  selector: 'app-resources',
  standalone: true,
  imports: [CommonModule, IconsModule, NewServiceComponent],
  templateUrl: './resources.component.html',
  styleUrl: './resources.component.css'
})
export class ResourcesComponent {
  @Output() close = new EventEmitter<void>();
  @Output() openNewService = new EventEmitter<string>();

  showNewServiceModal = false;

  resources: ResourceItem[] = [
    {
      title: 'Provider',
      description: 'Add working services from provider panels.',
      iconArray: ['fas', 'folder'],
      iconClass: 'service-icon text-blue-500'
    },
    {
      title: 'Manual',
      description: 'Make any service you like, but you have to complete it with admin api or manually.',
      iconArray: ['fas', 'file'],
      iconClass: 'service-icon text-yellow-500'
    },
    {
      title: 'BotsApi own service',
      description: 'Make own service which you have to complete with soft using bots.socpanel.com',
      iconArray: ['fas', 'tag'],
      iconClass: 'service-icon text-orange-500'
    }
  ];

  onClose() {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent) {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onServiceClick(service: ResourceItem) {
    if (service.title === 'Provider') {
      // Close this component and emit event to open new-service component in Provider mode
      this.openNewService.emit('Provider');
      this.onClose();
    } else if (service.title === 'Manual') {
      // Close this component and emit event to open new-service component in Manual mode
      this.openNewService.emit('Manual');
      this.onClose();
    } else if (service.title === 'BotsApi own service') {
      // Close this component and emit event to open new-service component in BotsApi mode
      this.openNewService.emit('BotsApi');
      this.onClose();
    }
  }
}
