import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { UserService } from '../../core/services/user.service';
import { UserRes } from '../../model/response/user-res.model';
import { ToastService } from '../../core/services/toast.service';
import { LiteDropdownComponent } from '../common/lite-dropdown/lite-dropdown.component';
import { LanguageService } from '../../core/services/language.service';
import { LoginHistoryItem } from '../../model/response/login-history.model';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
// ToggleSwitchComponent removed as we're using buttons instead
import { ActivatedRoute } from '@angular/router';
import { EditUserReq } from '../../model/request/edit-user-req.model';
import { MfaSettingComponent } from '../popup/mfa-setting/mfa-setting.component';
import { MfaDisabledComponent } from '../popup/mfa-disabled/mfa-disabled.component';
import { IconsModule } from '../../icons/icons.module';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent,
    MfaSettingComponent,
    MfaDisabledComponent
],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css', './online-dot.css']
})
export class ProfileComponent implements OnInit, OnDestroy {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  user: UserRes | undefined;
  isLoading = false;
  isPasswordLoading = false;
  isVerifying = false;
  loginHistory: LoginHistoryItem[] = [];
  isLoadingHistory = false;
  private subscriptions: Subscription[] = [];
  passwordError: string | null = null;
  profileError: string | null = null;

  // Tab navigation
  activeTab: 'account' | 'security' | 'settings' | 'history' = 'account';

  // Password visibility toggles
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;

  // Pagination for login history
  currentPage = 1;
  totalPages = 1;
  itemsPerPage = 10;

  // Security options
  is2FAEnabled = false;

  // Settings options
  languageOptions = ['English', 'Tiếng Việt'];
  selectedLanguage = 'English';

  timezoneOptions = ['(UTC +7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam', '(UTC +8:00) Beijing, Hong Kong, Singapore'];
  selectedTimezone = '(UTC +7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam';
  formattedBalance: string = '';
  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private toast: ToastService,
    private translate: TranslateService,
    private languageService: LanguageService,
    private route: ActivatedRoute
  ) {
    // Initialize profile form
    this.profileForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]]
    });

    // Initialize password form
    this.passwordForm = this.fb.group({
      oldPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.minLength(8)]],
      confirmPassword: ['']
    }, {
      validators: this.passwordMatchValidator
    });
  }

  ngOnInit(): void {
    // Check for tab parameter in URL query params
    this.route.queryParams.subscribe(params => {
      if (params['tab']) {
        // Check if the tab parameter is valid
        const tabParam = params['tab'];
        if (tabParam === 'account' || tabParam === 'security' ||
            tabParam === 'settings' || tabParam === 'history') {
          this.activeTab = tabParam as 'account' | 'security' | 'settings' | 'history';
        }
      }
    });

    // Subscribe to user data from the UserService - use take(1) to only get the current value
    const userSub = this.userService.user$.subscribe(user => {
      this.user = user;

      if (user) {
        // Populate form with user data
        this.profileForm.patchValue({
          email: user.email || '',
          phone: user.phone || ''
        });
        this.formattedBalance = user.balance?.toFixed(6).replace(/\.?0+$/, '') || '0';

        // Load login history only once
        this.loadLoginHistory();

        // Load security settings
        this.loadSecuritySettings();
      } else {
        // If user is not loaded yet, trigger a fetch
        this.userService.get$.next();
      }
    });
    this.subscriptions.push(userSub);

    // Initialize language based on current setting
    const currentLang = this.translate.currentLang;
    this.selectedLanguage = currentLang === 'en' ? 'English' : 'Tiếng Việt';
  }

  // Load security settings from user data
  loadSecuritySettings(): void {
    if (this.user) {
      // Check if 2FA is enabled (assuming the user object has this information)
      this.is2FAEnabled = this.user.mfa_enabled || false;
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Load login history
  loadLoginHistory(): void {
    console.log('Loading login history...');
    this.isLoadingHistory = true;

    // Set a default empty array to prevent undefined errors
    this.loginHistory = [];

    const historySub = this.userService.getLoginHistory().subscribe({
      next: (response) => {
        console.log('Login history API response:', response);

        if (response && response.content) {
          this.loginHistory = [...response.content]; // Create a new array to trigger change detection
          console.log('Login history items:', this.loginHistory.length);

          // Add sample items for testing if the array is empty
          // if (this.loginHistory.length === 0 && response.content.length === 0) {
          //   console.log('Adding sample login history items for testing');
          //   // Sample data for testing
          //   this.loginHistory = [
          //     {
          //       id: 1,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/*********',
          //       created_at: new Date().toISOString()
          //     },
          //     {
          //       id: 2,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/605.1.15',
          //       created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
          //     },
          //     {
          //       id: 3,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) Mobile/15E148',
          //       created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
          //     }
          //   ];
          // }

          // Calculate pagination
          this.totalPages = Math.ceil(this.loginHistory.length / this.itemsPerPage);
          if (this.totalPages === 0) this.totalPages = 1;
          this.currentPage = 1;
        } else {
          // If response doesn't have the expected structure, keep the empty array
          console.warn('Login history response did not have expected structure:', response);
        }

        // Log the final state for debugging
        console.log('Final login history state:', this.loginHistory);
        this.isLoadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading login history:', error);
        this.loginHistory = []; // Ensure loginHistory is initialized even on error
        this.isLoadingHistory = false;
        this.toast.showError('Failed to load login history');
      }
    });

    this.subscriptions.push(historySub);
  }

  // Pagination methods
  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  get paginatedLoginHistory(): LoginHistoryItem[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.loginHistory.slice(startIndex, endIndex);
  }

  // Format date for display
  formatDate(dateString: string | null | undefined): string {
    if (!dateString) {
      return 'N/A';
    }
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  /**
   * Get the avatar image path based on user ID or a default one
   * @returns Path to the avatar image
   */
  getAvatarPath(): string {
    if (!this.user || !this.user.avatar) {
      // Default avatar if no user or no avatar
      return 'assets/images/profile-1.png';
    }

    // Return the avatar path based on user.avatar
    return `assets/images/${this.user.avatar}.png`;
  }

  // Properties for MFA popups
  showMfaSettings = false;
  showMfaDisabled = false;

  // Toggle 2FA status
  toggle2FA(): void {
    console.log('toggle2FA called, is2FAEnabled:', this.is2FAEnabled);

    if (this.is2FAEnabled) {
      // If 2FA is enabled, show the disable popup
      console.log('Showing MFA disable popup');
      this.showMfaDisabled = true;
    } else {
      // If 2FA is disabled, show the enable popup
      this.showEnableMfa();
    }
  }

  // Show enable MFA popup
  showEnableMfa(): void {
    console.log('showEnableMfa called');
    this.showMfaSettings = true;
  }

  // Show disable MFA popup
  showDisableMfa(): void {
    console.log('showDisableMfa called');
    this.showMfaDisabled = true;
  }

  // Close MFA settings popup
  closeMfaSettings(): void {
    console.log('Closing MFA settings popup');
    this.showMfaSettings = false;
  }

  // Close MFA disabled popup
  closeMfaDisabled(): void {
    console.log('Closing MFA disabled popup');
    this.showMfaDisabled = false;
  }

  // Handle MFA enabled event
  onMfaEnabled(): void {
    this.is2FAEnabled = true;
    // Update user data
    this.userService.get$.next();
  }

  // Handle MFA disabled event
  onMfaDisabled(): void {
    this.is2FAEnabled = false;
    // Update user data
    this.userService.get$.next();
  }

  // API Key related methods removed as requested

  // Settings methods
  onLanguageChange(language: string): void {
    this.selectedLanguage = language;
    const langCode = language === 'English' ? 'en' : 'vi';
    this.languageService.changeLanguage(langCode);
    this.toast.showSuccess(`Language changed to ${language}`);
  }

  onTimezoneChange(timezone: string): void {
    this.selectedTimezone = timezone;
    this.toast.showSuccess('Timezone updated');
  }

  // Custom validator to check if passwords match
  passwordMatchValidator(group: FormGroup) {
    const newPassword = group.get('newPassword')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;

    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
      group.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else if (group.get('confirmPassword')?.hasError('passwordMismatch')) {
      // Clear the error if passwords now match
      group.get('confirmPassword')?.setErrors(null);
    }

    return null;
  }

  // Verify email
  verifyEmail(): void {
    this.isVerifying = true;
    // Implement email verification logic here
    setTimeout(() => {
      this.isVerifying = false;
      this.toast.showSuccess('Verification email sent');
    }, 1000);
  }

  // Update profile
  onSubmit(): void {
    // Reset error message
    this.profileError = null;

    if (this.profileForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.profileForm.controls).forEach(key => {
        const control = this.profileForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isLoading = true;

    // Update user info (email and phone)
    const userInfo: EditUserReq = {
      email: this.profileForm.value.email || '',
      phone: this.profileForm.value.phone || ''
    };

    this.userService.updateInfo(userInfo).subscribe({
      next: (updatedUser) => {
        this.isLoading = false;
        // Update only specific fields in the user object
        if (updatedUser && this.user) {
          // Only update the email and phone fields
          this.user = {
            ...this.user,
            email: updatedUser.email,
            phone: updatedUser.phone
          };
        }
        this.toast.showSuccess(this.translate.instant('profile.profile_updated'));
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Profile update error:', err);

        // Set error message based on the response
        if (err && err.message) {
          this.profileError = err.message;
        } else {
          this.profileError = 'Failed to update profile. Please try again.';
        }

        this.toast.showError(this.profileError || 'Failed to update profile');
      }
    });
  }

  // Change password
  onChangePassword(): void {
    // Reset error message
    this.passwordError = null;

    if (this.passwordForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.passwordForm.controls).forEach(key => {
        const control = this.passwordForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isPasswordLoading = true;

    const passwordData = {
      oldPassword: this.passwordForm.value.oldPassword,
      newPassword: this.passwordForm.value.newPassword
    };

    this.userService.changePass({
      old_password: passwordData.oldPassword,
      new_password: passwordData.newPassword
    }).subscribe({
      next: () => {
        this.isPasswordLoading = false;
        this.toast.showSuccess(this.translate.instant('profile.password_updated'));
        // Reset form
        this.passwordForm.reset();
      },
      error: (err) => {
        this.isPasswordLoading = false;
        console.error('Password update error:', err);

        // Set error message based on the response
        if (err && err.message) {
          this.passwordError = err.message;
        } else {
          this.passwordError = 'Failed to update password. Please try again.';
        }

        this.toast.showError(this.passwordError || 'Failed to update password');
      }
    });
  }
}
