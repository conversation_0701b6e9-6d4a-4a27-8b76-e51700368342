package tndung.vnfb.smm.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tndung.vnfb.smm.dto.UserNotificationReq;
import tndung.vnfb.smm.dto.UserNotificationRes;
import tndung.vnfb.smm.entity.GUser;

import java.util.List;

public interface UserNotificationService {

    // Create notifications
    UserNotificationRes createNotification(UserNotificationReq req);

    // Get notifications
    Page<UserNotificationRes> getNotifications(Pageable pageable);
    List<UserNotificationRes> getUnreadNotifications();
    long getUnreadCount();

    // Mark as read
    void markAsRead(Long notificationId);
    void markAllAsRead();

    // System notifications for user actions
    void createOrderNotification(GUser user, String orderDetails);
    void createBalanceNotification(GUser user, String balanceDetails);
    void createSystemNotification(GUser user, String message);
}
