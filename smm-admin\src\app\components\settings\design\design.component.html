<div class="design-settings">
  <div class="settings-header">
    <h1 class="settings-title">Design & Content</h1>
    <p class="settings-description">Customize the appearance of your site</p>
  </div>

  <!-- Logo & Favicon Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'image']" class="section-icon"></fa-icon>
        Logo & Favicon
      </h2>
    </div>

    <div class="card-content">
      <!-- Logo Upload -->
      <div class="upload-item">
        <div class="upload-item-header">
          <label class="upload-label">Logo</label>
          <div class="upload-format">.png or .jpg 320×128</div>
        </div>
        <div class="upload-container">
          <div class="preview-box light-bg">
            <span *ngIf="!logoPreviewUrl" class="preview-placeholder">NEWPANEL</span>
            <img *ngIf="logoPreviewUrl" [src]="logoPreviewUrl" alt="Logo preview" class="preview-image" />
          </div>
          <div class="upload-actions">
            <button class="upload-button">
              <fa-icon [icon]="['fas', 'upload']"></fa-icon>
              <span>Upload</span>
              <input type="file" (change)="onLogoFileSelected($event)" accept="image/png,image/jpeg" class="hidden-input" />
            </button>
            <div *ngIf="logoUploading" class="upload-progress">
              <div class="progress-bar" [style.width.%]="uploadProgress"></div>
            </div>
            <div *ngIf="logoUploadSuccess" class="upload-success">
              <fa-icon [icon]="['fas', 'check-circle']"></fa-icon>
              <span>Upload successful</span>
            </div>
            <div *ngIf="logoUploadError" class="upload-error">
              <fa-icon [icon]="['fas', 'exclamation-circle']"></fa-icon>
              <span>{{ logoUploadError }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Favicon Upload -->
      <div class="upload-item">
        <div class="upload-item-header">
          <label class="upload-label">Favicon</label>
          <div class="upload-format">ico 16×16</div>
        </div>
        <div class="upload-container">
          <div class="favicon-container">
            <div class="favicon-preview">
              <div class="favicon-box">
                <span *ngIf="!faviconPreviewUrl" class="favicon-placeholder">ico</span>
                <img *ngIf="faviconPreviewUrl" [src]="faviconPreviewUrl" alt="Favicon preview" class="favicon-image" />
              </div>
              <div class="favicon-url-container">
                <input type="text" [value]="currentDomain" class="favicon-url" readonly />
                <div class="favicon-actions">
                  <button class="action-button" title="Copy URL" (click)="copyFaviconUrl()">
                    <fa-icon [icon]="['fas', 'copy']"></fa-icon>
                  </button>
                </div>
              </div>
            </div>
            <div class="upload-actions">
              <button class="upload-button">
                <fa-icon [icon]="['fas', 'upload']"></fa-icon>
                <span>Upload</span>
                <input type="file" (change)="onFaviconFileSelected($event)" accept="image/x-icon" class="hidden-input" />
              </button>
              <div *ngIf="faviconUploading" class="upload-progress">
                <div class="progress-bar" [style.width.%]="uploadProgress"></div>
              </div>
              <div *ngIf="faviconUploadSuccess" class="upload-success">
                <fa-icon [icon]="['fas', 'check-circle']"></fa-icon>
                <span>Upload successful</span>
              </div>
              <div *ngIf="faviconUploadError" class="upload-error">
                <fa-icon [icon]="['fas', 'exclamation-circle']"></fa-icon>
                <span>{{ faviconUploadError }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Colors Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'palette']" class="section-icon"></fa-icon>
        Colors
      </h2>
    </div>

    <div class="card-content">
      <!-- Text on Buttons -->
      <!-- <div class="color-item">
        <label class="color-label">Text on buttons</label>
        <div class="color-options">
          <div *ngFor="let color of textButtonColors"
               class="color-option"
               [style.background-color]="color.value"
               [class.selected]="color.selected"
               (click)="selectTextButtonColor(color)">
            <fa-icon *ngIf="color.selected" [icon]="['fas', 'check']" class="check-icon"></fa-icon>
          </div>
        </div>
      </div> -->

      <!-- Primary Color -->
      <div class="color-item">
        <div class="color-header">
          <label class="color-label">Primary color</label>
          <button class="custom-color-btn" (click)="toggleCustomColorInput()">
            <fa-icon [icon]="['fas', showCustomColorInput ? 'times' : 'plus']"></fa-icon>
            {{ showCustomColorInput ? 'Cancel' : 'Custom' }}
          </button>
        </div>

        <!-- Predefined Colors -->
        <div class="color-options" *ngIf="!showCustomColorInput">
          <div *ngFor="let color of primaryColors"
               class="color-option"
               [style.background-color]="color.value"
               [class.selected]="color.selected"
               (click)="selectPrimaryColor(color)">
            <fa-icon *ngIf="color.selected" [icon]="['fas', 'check']" class="check-icon"></fa-icon>
          </div>
        </div>

        <!-- Custom Color Input -->
        <div class="custom-color-input" *ngIf="showCustomColorInput">
          <input type="text"
                 [(ngModel)]="customPrimaryColor"
                 placeholder="var(--primary)"
                 class="hex-input"
                 pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$">
          <button class="apply-color-btn" (click)="applyCustomColor()">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
            Apply
          </button>
        </div>

        <!-- Color Preview -->
        <div class="color-preview">
          <div class="preview-swatch" [style.background-color]="getSelectedPrimaryColor()"></div>
          <span class="preview-value">{{ getSelectedPrimaryColor() }}</span>
        </div>
      </div>

      <!-- Preview Section (moved below color section) -->
      <div class="preview-container mt-6">
        <h3 class="text-lg font-medium mb-4">Preview</h3>
        <div class="preview-item">
          <span class="preview-label">Active Link</span>
          <a href="javascript:void(0)" class="active-link-preview"
             [ngStyle]="{'color': getSelectedPrimaryColor()}">
            Link text
          </a>
        </div>
        <div class="preview-item">
          <span class="preview-label">Button</span>
          <button class="button-preview"
                  [ngStyle]="{
                    'background-color': getSelectedPrimaryColor(),
                    'color': getSelectedTextButtonColor()
                  }">
            <fa-icon [icon]="['fas', 'shopping-cart']"></fa-icon>
            Button text
          </button>
        </div>
        <div class="preview-item">
          <span class="preview-label">Icon</span>
          <div class="icon-preview"
               [ngStyle]="{
                 'background-color': getSelectedPrimaryColor(),
                 'color': getSelectedTextButtonColor()
               }">
            <fa-icon [icon]="['fas', 'shopping-cart']"></fa-icon>
          </div>
        </div>
        <div class="preview-item">
          <span class="preview-label">Primary Button</span>
          <button class="primary-button-preview"
                  [ngStyle]="{
                    'background-color': getSelectedPrimaryColor(),
                    'color': '#FFFFFF'
                  }">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
            Primary Button
          </button>
        </div>
        <div class="preview-item">
          <span class="preview-label">Primary Elements</span>
          <div class="primary-elements-preview">
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor()}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.8}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.6}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.4}"></div>
            <div class="element" [ngStyle]="{'background-color': getSelectedPrimaryColor(), 'opacity': 0.2}"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Header Style Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'window-maximize']" class="section-icon"></fa-icon>
        Header Style
      </h2>
    </div>

    <div class="card-content">
      <div class="header-style-options">
        <div *ngFor="let option of headerStyleOptions"
             class="header-style-option"
             [class.selected]="option.selected"
             (click)="selectHeaderStyle(option)">
          <div class="header-style-preview" [ngClass]="option.id" [class.active-preview]="option.id === getPreviewHeaderStyle()">
            <!-- Standard style preview -->
            <div *ngIf="option.id === 'standard'" class="preview-header default-header shadow-sm rounded">
              <div class="preview-left">
                <span class="preview-nav"></span>
                <span class="preview-nav"></span>
              </div>
              <div class="preview-right">
                <span class="preview-balance green"></span>
                <span class="preview-icon"></span>
                <span class="preview-icon"></span>
              </div>
            </div>

            <!-- Compact style preview -->
            <div *ngIf="option.id === 'compact'" class="preview-header compact-header">
              <div class="preview-left">

                <span class="preview-nav small uppercase"></span>
                <span class="preview-nav small uppercase"></span>
              </div>
              <div class="preview-right">
                <span class="preview-balance small bordered"></span>
                <span class="preview-icon small"></span>
                <span class="preview-icon small"></span>
              </div>
            </div>

            <!-- Modern style preview -->
            <div *ngIf="option.id === 'modern'" class="preview-header-container">
              <div class="preview-header modern-header shadow-sm rounded">
                <div class="preview-left">

                  <span class="preview-button"></span>
                  <span class="preview-nav small"></span>
                </div>
                <div class="preview-right">
                  <span class="preview-balance blue"></span>
                  <span class="preview-icon"></span>
                  <span class="preview-icon"></span>
                </div>
              </div>
            </div>

            <!-- Minimal style preview -->
            <div *ngIf="option.id === 'minimal'" class="preview-header minimal-header">
              <div class="preview-left">
                <span class="preview-nav small underlined"></span>
                <span class="preview-nav small"></span>
                <span class="preview-nav small"></span>
              </div>
              <div class="preview-right">
                <span class="preview-balance small bordered-left"></span>
                <span class="preview-icon small"></span>
                <span class="preview-icon small"></span>
              </div>
            </div>
          </div>
          <div class="header-style-label">{{ option.label }}</div>
          <div class="header-style-check" *ngIf="option.selected">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Sidebar Style Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'columns']" class="section-icon"></fa-icon>
        Sidebar Style
      </h2>
    </div>

    <div class="card-content">
      <div class="header-style-options">
        <div *ngFor="let option of sidebarStyleOptions"
             class="header-style-option"
             [class.selected]="option.selected"
             (click)="selectSidebarStyle(option)">
          <div class="header-style-preview" [ngClass]="option.id" [class.active-preview]="option.id === getPreviewSidebarStyle()">
            <!-- Standard sidebar style preview -->
            <div *ngIf="option.id === 'standard'" class="preview-sidebar default-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title"></div>
                <div class="preview-sidebar-item"></div>
                <div class="preview-sidebar-item active"></div>
                <div class="preview-sidebar-item"></div>
              </div>
            </div>

            <!-- Compact sidebar style preview -->
            <div *ngIf="option.id === 'compact'" class="preview-sidebar compact-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title small"></div>
                <div class="preview-sidebar-item small"></div>
                <div class="preview-sidebar-item small active"></div>
                <div class="preview-sidebar-item small"></div>
              </div>
            </div>

            <!-- Modern sidebar style preview -->
            <div *ngIf="option.id === 'modern'" class="preview-sidebar modern-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title"></div>
                <div class="preview-sidebar-item"></div>
                <div class="preview-sidebar-item active-indicator"></div>
                <div class="preview-sidebar-item"></div>
              </div>
            </div>

            <!-- Minimal sidebar style preview -->
            <div *ngIf="option.id === 'minimal'" class="preview-sidebar minimal-sidebar">
              <div class="preview-sidebar-section">
                <div class="preview-sidebar-title small"></div>
                <div class="preview-sidebar-item small"></div>
                <div class="preview-sidebar-item small active"></div>
                <div class="preview-sidebar-item small"></div>
              </div>
            </div>

            <!-- Card sidebar style preview -->
            <div *ngIf="option.id === 'card'" class="preview-sidebar card-sidebar">
              <div class="preview-sidebar-grid">
                <div class="preview-sidebar-card"></div>
                <div class="preview-sidebar-card active"></div>
                <div class="preview-sidebar-card"></div>
                <div class="preview-sidebar-card"></div>
              </div>
            </div>
          </div>
          <div class="header-style-label">{{ option.label }}</div>
          <div class="header-style-check" *ngIf="option.selected">
            <fa-icon [icon]="['fas', 'check']"></fa-icon>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Landing Section -->
  <section class="settings-card">
    <div class="card-header">
      <h2 class="card-title">
        <fa-icon [icon]="['fas', 'home']" class="section-icon"></fa-icon>
        Landing Page
      </h2>
    </div>

    <div class="card-content">
      <div class="landing-settings">
        <div class="landing-options">
          <div class="landing-type-selection">
            <label class="landing-label">Template</label>
            <div class="landing-types">
              <div *ngFor="let type of landingTypes"
                   class="landing-type"
                   [class.selected]="type.selected"
                   (click)="selectLandingType(type)">
                <span>{{ type.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="landing-preview-wrapper">
          <label class="landing-label">Preview</label>
          <div class="landing-preview">
            <div class="landing-preview-container">
              <div class="landing-header">
                <div class="landing-logo">NEWPANEL</div>
                <div class="landing-actions">
                  <span>New order</span>
                  <span>Services</span>
                </div>
              </div>
              <div class="landing-content">
                <h3 class="landing-title">The most comfortable</h3>
              </div>
            </div>
            <div class="landing-scroll-indicator"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Save Button -->
  <div class="save-section">
    <button class="save-button" (click)="saveSettings()">
      <fa-icon [icon]="['fas', 'save']"></fa-icon>
      Save changes
    </button>
  </div>
</div>
