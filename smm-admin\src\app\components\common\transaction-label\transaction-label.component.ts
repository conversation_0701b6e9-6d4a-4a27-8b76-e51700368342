import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyTransactionRes } from '../../../model/response/my-transaction.model';
import { TagLabelComponent } from '../tag-label/tag-label.component';
import { TransactionType } from '../../../model/response/transaction.model';
import { CurrencyService } from '../../../core/services/currency.service';
import { CurrencyConvertPipe } from '../../../core/pipes/currency-convert.pipe';

@Component({
  selector: 'app-transaction-label',
  standalone: true,
  imports: [CommonModule, TagLabelComponent, CurrencyConvertPipe],
  templateUrl: './transaction-label.component.html',
  styleUrl: './transaction-label.component.css'
})
export class TransactionLabelComponent {
  @Input() transaction: MyTransactionRes = {} as MyTransactionRes;

  constructor(private currencyService: CurrencyService) {}

  // Format service price with negative sign and currency symbol
  formatServicePrice(price: number | undefined): string {
    if (!price) return '';
    // Get the converted price with currency symbol
    const formattedPrice = this.currencyService.formatPrice(price);
    // Add a negative sign
    return `-${formattedPrice}`;
  }

  // Format number with thousand separators and apply exchange rate
  formatNumber(value: number): string {
    return this.currencyService.formatPrice(value);
  }

  // Get concise description based on transaction type
  getTransactionDescription(): string {
    if (!this.transaction || !this.transaction.type) {
      return '';
    }

    switch (this.transaction.type) {
      case TransactionType.DEPOSIT:
        return 'Nạp tiền';
      case TransactionType.WITHDRAW:
        return 'Rút tiền';
      case TransactionType.REFUND:
        return 'Hoàn tiền';
      case TransactionType.PAYMENT:
        return 'Thanh toán';
      case TransactionType.SPENT:
        return 'Chi tiêu';
      case TransactionType.BONUS:
        return 'Thưởng';
      default:
        return this.transaction.type.toString();
    }
  }

  // Get CSS class based on transaction type
  getTransactionTypeClass(): string {
    if (!this.transaction || !this.transaction.type) {
      return '';
    }

    switch (this.transaction.type) {
      case TransactionType.DEPOSIT:
      case TransactionType.REFUND:
      case TransactionType.BONUS:
        return 'text-green-500';
      case TransactionType.WITHDRAW:
      case TransactionType.PAYMENT:
      case TransactionType.SPENT:
        return 'text-red-500';
      default:
        return '';
    }
  }
}
