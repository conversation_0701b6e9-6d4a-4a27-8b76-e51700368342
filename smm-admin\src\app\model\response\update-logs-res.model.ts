export interface UpdateLogItem {
  id: number;
  service: string;
  status: string;
  price_change_from: number | null;
  price_change_to: number | null;
  created_at: string;
}

export interface Pageable {
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  offset: number;
  page_size: number;
  page_number: number;
  unpaged: boolean;
  paged: boolean;
}

export interface UpdateLogsPageRes {
  content: UpdateLogItem[];
  pageable: Pageable;
  last: boolean;
  total_elements: number;
  total_pages: number;
  size: number;
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  first: boolean;
  number_of_elements: number;
  empty: boolean;
}
