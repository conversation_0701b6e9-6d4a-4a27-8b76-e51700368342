package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.TenantCurrencyReq;
import tndung.vnfb.smm.dto.response.TenantCurrencyRes;
import tndung.vnfb.smm.entity.Currency;

import java.util.List;

public interface TenantCurrencyService {
    
    TenantCurrencyRes getTenantCurrencies();
    
    TenantCurrencyRes updateTenantCurrencies(TenantCurrencyReq req);
    
    List<Currency> getAvailableCurrenciesForCurrentTenant();
}
