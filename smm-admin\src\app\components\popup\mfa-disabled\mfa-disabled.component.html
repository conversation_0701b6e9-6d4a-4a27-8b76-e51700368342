<div class="overlay-black" >
  <div class="modal-container" (click)="onPopupClick($event)">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'auth.disable_two_factor_auth' | translate }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <p class="confirmation-text">{{ 'auth.disable_two_factor_auth_confirmation' | translate }}</p>
        
        <div class="warning-box">
          <div class="warning-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="warning-content">
            {{ 'auth.disable_two_factor_auth_warning' | translate }}
          </div>
        </div>

        <form [formGroup]="disableForm" (ngSubmit)="onSubmit()" class="disable-form">
          <div class="form-group">
            <label for="password" class="form-label">{{ 'auth.password' | translate }}</label>
            <div class="password-input-container">
              <input 
                [type]="showPassword ? 'text' : 'password'" 
                id="password" 
                formControlName="password" 
                class="form-input" 
                placeholder="{{ 'auth.enter_password' | translate }}"
                [ngClass]="{'input-error': disableForm.get('password')?.invalid && disableForm.get('password')?.touched}"
              >
              <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
                <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
            <div *ngIf="disableForm.get('password')?.invalid && disableForm.get('password')?.touched" class="error-message">
              <div *ngIf="disableForm.get('password')?.errors?.['required']">{{ 'auth.password_required' | translate }}</div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-button" (click)="onClose()">
              {{ 'auth.cancel' | translate }}
            </button>
            <button type="submit" class="disable-button" [disabled]="disableForm.invalid || isLoading">
              <span *ngIf="!isLoading">{{ 'auth.disable' | translate }}</span>
              <span *ngIf="isLoading" class="loading-spinner-small"></span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
