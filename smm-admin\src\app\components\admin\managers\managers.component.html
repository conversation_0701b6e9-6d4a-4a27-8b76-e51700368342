<div class="admin-container">
  <!-- Header -->
  <div class="admin-header">
    <h1 class="admin-title">Manager Management</h1>
    <button
      (click)="openAddManagerPopup()"
      class="btn btn-primary">
      <fa-icon [icon]="['fas', 'plus']"></fa-icon>
      Add Manager
    </button>
  </div>

  <!-- Search -->
  <div class="search-filter-container">
    <div class="flex flex-col md:flex-row gap-4 mb-6">
      <!-- Search -->
      <div class="flex-1">
        <div class="search-input-wrapper">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            placeholder="Search managers..."
            class="search-input"
          >
          <button class="search-button">
            <fa-icon [icon]="['fas', 'search']"></fa-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <app-loading *ngIf="loading"></app-loading>

  <!-- Managers Table (Desktop) -->
  <div *ngIf="!loading" class="hidden md:block bg-white rounded-lg shadow-sm overflow-hidden">
    <table class="w-full">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accessible Panels</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let manager of filteredManagers" class="hover:bg-gray-50">
          <!-- Manager Info -->
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full flex items-center justify-center"
                   [ngClass]="getRoleColor(manager.role)">
                <fa-icon [icon]="['fas', getRoleIcon(manager.role)]"></fa-icon>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">{{ manager.login }}</div>
                <div class="text-sm text-gray-500">ID: {{ manager.id }}</div>
              </div>
            </div>
          </td>

          <!-- Role -->
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="getRoleColor(manager.role)">
              {{ manager.role }}
            </span>
          </td>

          <!-- Accessible Panels -->
          <td class="px-6 py-4">
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let panel of manager.accessiblePanels"
                    class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                {{ panel }}
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Empty State -->
    <div *ngIf="filteredManagers.length === 0" class="text-center py-12">
      <fa-icon [icon]="['fas', 'user-cog']" class="text-gray-400 text-4xl mb-4"></fa-icon>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No managers found</h3>
      <p class="text-gray-500 mb-4">Get started by adding your first manager.</p>
     
    </div>
  </div>

  <!-- Managers Cards (Mobile) -->
  <div *ngIf="!loading" class="md:hidden space-y-4">
    <div *ngFor="let manager of filteredManagers"
         class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <!-- Card Header -->
      <div class="flex justify-between items-center p-4 border-b border-gray-100">
        <div class="flex items-center">
          <div class="w-10 h-10 rounded-full flex items-center justify-center"
               [ngClass]="getRoleColor(manager.role)">
            <fa-icon [icon]="['fas', getRoleIcon(manager.role)]"></fa-icon>
          </div>
          <div class="ml-3">
            <div class="font-medium text-gray-900">{{ manager.login }}</div>
            <div class="text-sm text-gray-500">ID: {{ manager.id }}</div>
          </div>
        </div>
      </div>

      <!-- Card Content -->
      <div class="p-4 space-y-3">
        <!-- Role -->
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-500">Role:</span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                [ngClass]="getRoleColor(manager.role)">
            {{ manager.role }}
          </span>
        </div>

        <!-- Accessible Panels -->
        <div>
          <span class="text-sm text-gray-500 block mb-2">Accessible Panels:</span>
          <div class="flex flex-wrap gap-1">
            <span *ngFor="let panel of manager.accessiblePanels"
                  class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
              {{ panel }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Empty State -->
    <div *ngIf="filteredManagers.length === 0" class="text-center py-12">
      <fa-icon [icon]="['fas', 'users-cog']" class="text-gray-400 text-4xl mb-4"></fa-icon>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No managers found</h3>
      <p class="text-gray-500 mb-4">Get started by adding your first manager.</p>
      <button
        (click)="openAddManagerPopup()"
        class="btn btn-primary">
        <fa-icon [icon]="['fas', 'plus']"></fa-icon>
        Add Manager
      </button>
    </div>
  </div>
</div>

<!-- Add Manager Popup -->
<app-add-manager
  *ngIf="showAddManagerPopup"
  (close)="closeAddManagerPopup()"
  (managerAdded)="onManagerAdded($event)">
</app-add-manager>
