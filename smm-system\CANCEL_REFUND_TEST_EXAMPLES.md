# Cancel Refund Test Examples - Comprehensive Scenarios

## 🧪 Test Case 1: Cancel Order Without Previous Partials

### Setup
```java
// Create order: 100 items, $5 per 1000, total charge = $0.50
GOrder order = new GOrder();
order.setId(12345L);
order.setQuantity(100);
order.setPrice(new BigDecimal("5.00"));
order.setCharge(new BigDecimal("0.50")); // 100 * 5.00 / 1000
order.setStatus(OrderStatus.IN_PROGRESS);
order.setRemains(0);

GUser user = new GUser();
user.setBalance(new BigDecimal("10.00"));
order.setUser(user);
```

### Action: Cancel Order
```java
// Update to CANCELED status
order.setStatus(OrderStatus.CANCELED);
balanceService.processRefund(order);
```

### Expected Result
```
Cancel refund calculation:
- Original charge: $0.50
- Total partial refunds: $0.00 (no previous partials)
- Cancel refund amount: $0.50 - $0.00 = $0.50

User balance: $10.00 + $0.50 = $10.50
Transaction created: type='Refund', change=+$0.50
```

## 🧪 Test Case 2: Cancel Order With Previous Partials

### Setup
```java
// Same order as above, but with previous partial transactions
GOrder order = createOrder(12345L, 100, "5.00", "0.50");

// Simulate previous partial transactions
// 1. First partial: remains=30, refunded $0.15 (30 * 5.00 / 1000)
// 2. Second partial: remains=20, deducted $0.05 ((20-30) * 5.00 / 1000)
// Net partial refunds: $0.15 - $0.05 = $0.10

List<GTransaction> partialTransactions = Arrays.asList(
    createPartialTransaction(order, new BigDecimal("0.15"), "remains: 30"),
    createPartialTransaction(order, new BigDecimal("-0.05"), "remains: 20")
);
```

### Action: Cancel Order
```java
order.setStatus(OrderStatus.CANCELED);
balanceService.processRefund(order);
```

### Expected Result
```
Cancel refund calculation:
- Original charge: $0.50
- Total partial refunds: $0.15 + (-$0.05) = $0.10
- Cancel refund amount: $0.50 - $0.10 = $0.40

User balance: $10.00 + $0.40 = $10.40
Transaction created: type='Refund', change=+$0.40

Total refunded to user: $0.10 (partials) + $0.40 (cancel) = $0.50 ✅
```

## 🧪 Test Case 3: Cancel Order Where All Amount Already Refunded

### Setup
```java
// Order with partials that equal the full charge
GOrder order = createOrder(12345L, 100, "5.00", "0.50");

// Simulate partial transactions that total the full charge
// 1. First partial: remains=50, refunded $0.25 (50 * 5.00 / 1000)
// 2. Second partial: remains=100, refunded $0.25 ((100-50) * 5.00 / 1000)
// Net partial refunds: $0.25 + $0.25 = $0.50 (full amount)

List<GTransaction> partialTransactions = Arrays.asList(
    createPartialTransaction(order, new BigDecimal("0.25"), "remains: 50"),
    createPartialTransaction(order, new BigDecimal("0.25"), "remains: 100")
);
```

### Action: Cancel Order
```java
order.setStatus(OrderStatus.CANCELED);
balanceService.processRefund(order);
```

### Expected Result
```
Cancel refund calculation:
- Original charge: $0.50
- Total partial refunds: $0.25 + $0.25 = $0.50
- Cancel refund amount: $0.50 - $0.50 = $0.00

User balance: $10.00 + $0.00 = $10.00 (no change)
No transaction created (amount = $0.00)

Total refunded to user: $0.50 (already refunded through partials) ✅
```

## 🧪 Test Case 4: Complex Scenario With Multiple Partial Adjustments

### Setup
```java
// Order: 200 items, $8 per 1000, total charge = $1.60
GOrder order = createOrder(12345L, 200, "8.00", "1.60");

// Complex partial history:
// 1. First partial: remains=50, refunded $0.40 (50 * 8.00 / 1000)
// 2. Second partial: remains=30, deducted $0.16 ((30-50) * 8.00 / 1000)
// 3. Third partial: remains=80, refunded $0.40 ((80-30) * 8.00 / 1000)
// 4. Fourth partial: remains=60, deducted $0.16 ((60-80) * 8.00 / 1000)
// Net partial refunds: $0.40 - $0.16 + $0.40 - $0.16 = $0.48

List<GTransaction> partialTransactions = Arrays.asList(
    createPartialTransaction(order, new BigDecimal("0.40"), "remains: 50"),
    createPartialTransaction(order, new BigDecimal("-0.16"), "remains: 30"),
    createPartialTransaction(order, new BigDecimal("0.40"), "remains: 80"),
    createPartialTransaction(order, new BigDecimal("-0.16"), "remains: 60")
);
```

### Action: Cancel Order
```java
order.setStatus(OrderStatus.CANCELED);
balanceService.processRefund(order);
```

### Expected Result
```
Cancel refund calculation:
- Original charge: $1.60
- Total partial refunds: $0.40 - $0.16 + $0.40 - $0.16 = $0.48
- Cancel refund amount: $1.60 - $0.48 = $1.12

User balance: $10.00 + $1.12 = $11.12
Transaction created: type='Refund', change=+$1.12

Total refunded to user: $0.48 (partials) + $1.12 (cancel) = $1.60 ✅
```

## 🧪 Test Case 5: Failed Order With Same Logic

### Setup
```java
// Same as Test Case 2, but order fails instead of being canceled
GOrder order = createOrderWithPartials(12345L, 100, "5.00", "0.50");
// Previous partials: net $0.10 refunded
```

### Action: Fail Order
```java
order.setStatus(OrderStatus.FAILED);
balanceService.processRefund(order);
```

### Expected Result
```
Failed refund calculation (uses same logic as cancel):
- Original charge: $0.50
- Total partial refunds: $0.10
- Failed refund amount: $0.50 - $0.10 = $0.40

User balance: $10.00 + $0.40 = $10.40
Transaction created: type='Refund', change=+$0.40, note="Failed order refund..."
```

## ✅ Verification Points

For each test case, verify:

1. **No Over-Refunding:** Total refunds never exceed original charge
2. **Accurate Calculations:** Cancel refund = Original charge - Partial refunds
3. **Proper Logging:** All transactions logged with correct amounts and notes
4. **Edge Case Handling:** Zero refunds when fully refunded through partials
5. **Duplicate Prevention:** No duplicate refund transactions

## 🔧 Helper Methods for Testing

```java
private GOrder createOrder(Long id, int quantity, String price, String charge) {
    GOrder order = new GOrder();
    order.setId(id);
    order.setQuantity(quantity);
    order.setPrice(new BigDecimal(price));
    order.setCharge(new BigDecimal(charge));
    return order;
}

private GTransaction createPartialTransaction(GOrder order, BigDecimal change, String note) {
    GTransaction transaction = new GTransaction();
    transaction.setOrder(order);
    transaction.setType(TransactionType.Partial);
    transaction.setChange(change);
    transaction.setNote("Partial refund for order #" + order.getId() + " (" + note + ")");
    return transaction;
}
```

These test cases ensure the cancel refund logic works correctly in all scenarios and prevents financial discrepancies.
