import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpRequest,
  HttpClient,
  HttpHeaders
} from '@angular/common/http';
import { PLATFORM_ID, inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

import { BehaviorSubject, Observable, of, switchMap, throwError } from 'rxjs';
import { catchError, filter, take, finalize } from 'rxjs/operators';
import { AuthUtilsService } from '../services/auth-utils.service';
import { UserRes } from '../../model/response/user-res.model';
import { Router } from '@angular/router';
import { ConfigService } from '../services/config.service';

// Shared state for the interceptor function
let isRefreshing = false;
const refreshTokenSubject = new BehaviorSubject<any>(null);

export function jwtInterceptorFn(request: HttpRequest<unknown>, next: HttpHandlerFn): Observable<any> {
  const platformId = inject(PLATFORM_ID);
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);
  const http = inject(HttpClient);
  const configService = inject(ConfigService);
  const authReq = request;

  // Skip token handling on server-side
  if (!isPlatformBrowser(platformId)) {
    return next(request);
  }
  // add auth header with jwt if user is logged in and request is to the api url

  const user = authUtils.getUserFromStorage();
  const isApiUrl = request.url.startsWith(configService.apiUrl);

  // Check if the request is for login or registration
  const isLoginRequest = request.url.includes('/access/login');
  const isRegisterRequest = request.url.includes('/users/register');

  // Check if the request is for platforms


  // For debugging
  console.log('JWT Interceptor - Request URL:', request.url);
  console.log('JWT Interceptor - Is API URL:', isApiUrl);
  console.log('JWT Interceptor - Is Authenticated:', user && authUtils.isAuthenticated());
  console.log('JWT Interceptor - Is Login Request:', isLoginRequest);
  console.log('JWT Interceptor - Is Register Request:', isRegisterRequest);

  let modifiedRequest = request;
  // Add token to all requests except login and registration
  if (user && authUtils.isAuthenticated() && isApiUrl && !isLoginRequest && !isRegisterRequest) {
    console.log('JWT Interceptor - Adding token header');
    modifiedRequest = authUtils.addTokenHeader(request, user);
  }

  // Special handling for platforms request - always add token if available
  if ( user && user.tokens && user.tokens.access_token) {
    console.log('JWT Interceptor - Special handling for platforms request');
    modifiedRequest = authUtils.addTokenHeader(request, user);
  }

  // Always add error handling for 401 errors to handle token expiration
  return next(modifiedRequest).pipe(
    catchError(error => {
      if (error instanceof HttpErrorResponse && !authReq.url.includes('access/login') && error.status === 401) {
        console.log('JWT Interceptor - 401 error detected, attempting to refresh token');
        return handle401Error(authReq, next, http, authUtils, router);
      }
      return throwError(() => error);
    })
  );
}

function handle401Error(
  request: HttpRequest<any>,
  next: HttpHandlerFn,
  http: HttpClient,
  authUtils: AuthUtilsService,
  router: Router
) {
  const configService = inject(ConfigService);
  // Skip refresh token handling on server-side
  const platformId = inject(PLATFORM_ID);
  if (!isPlatformBrowser(platformId)) {
    return next(request);
  }

  // Check if we're already refreshing to avoid multiple refresh requests
  if (!isRefreshing) {
    isRefreshing = true;
    refreshTokenSubject.next(null);
    console.log('JWT Interceptor - Starting token refresh process');

    const user = authUtils.getUserFromStorage();
    const refreshToken = user?.tokens?.refresh_token;

    if (refreshToken) {
      console.log('JWT Interceptor - Refresh token found, attempting to refresh');
      // Use HttpClient directly to refresh the token
      const headers = new HttpHeaders({'x-refresh-token': refreshToken});

      return http.post<UserRes>(
        `${configService.apiUrl}/access/refresh`, null, {headers}
      ).pipe(
        switchMap((user: UserRes) => {
          console.log('JWT Interceptor - Token refresh successful');
          isRefreshing = false;

          // Update user in localStorage
          if (isPlatformBrowser(platformId)) {
            localStorage.setItem('user', JSON.stringify(user));
          }

          // Notify other subscribers that the token has been refreshed
          refreshTokenSubject.next(user);

          // Retry the original request with the new token
          return next(authUtils.addTokenHeader(request, user));
        }),
        catchError((err) => {
          console.error('JWT Interceptor - Token refresh failed:', err);
          isRefreshing = false;

          // Clear authentication data and redirect to login
          if (isPlatformBrowser(platformId)) {
            localStorage.removeItem('user');

            // Only redirect if not already on auth page
            if (!router.url.includes('/auth/')) {
              // Store the current URL for redirect after login
              localStorage.setItem('redirectAfterLogin', router.url);
              console.log('JWT Interceptor - Redirecting to login after refresh failure');
              // Redirect to login page
              router.navigate(['/auth/login']);
            }
          }

          return throwError(() => err);
        })
      );
    } else {
      console.log('JWT Interceptor - No refresh token available');
      isRefreshing = false;

      // Clear user data and redirect to login if not already on auth page
      if (isPlatformBrowser(platformId)) {
        localStorage.removeItem('user');

        if (!router.url.includes('/auth/')) {
          // Store the current URL for redirect after login
          localStorage.setItem('redirectAfterLogin', router.url);
          console.log('JWT Interceptor - Redirecting to login (no refresh token)');
          router.navigate(['/auth/login']);
        }
      }

      return throwError(() => new Error('No refresh token available'));
    }
  }

  // If we're already refreshing, wait for the new token
  console.log('JWT Interceptor - Already refreshing, waiting for new token');
  return refreshTokenSubject.pipe(
    filter(token => token !== null),
    take(1),
    switchMap((token) => {
      console.log('JWT Interceptor - New token received, retrying request');
      return next(authUtils.addTokenHeader(request, token));
    })
  );
}


