<div class="overlay-black" >
  <div class="modal-container bg-white rounded-2xl shadow-lg w-[450px] max-w-full">
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-800">Editing {{ username }}</h2>
      <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Form -->
    <div class="p-6">
      <form (ngSubmit)="saveUser()">
        <!-- Login/Username -->
        <div class="mb-4">
          <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Login</label>
          <input
            type="text"
            id="username"
            name="username"
            [(ngModel)]="username"
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
          >
        </div>

        <!-- Email -->
        <div class="mb-4">
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="email"
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
          >
        </div>

        <!-- Phone -->
        <div class="mb-6">
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input
            type="text"
            id="phone"
            name="phone"
            [(ngModel)]="phone"
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
          >
        </div>

        <!-- Error message -->
        <div *ngIf="errorMessage" class="mb-4 text-red-500 text-sm">
          {{ errorMessage }}
        </div>

        <!-- Save Button -->
        <button
          type="submit"
          [disabled]="loading"
          class="w-full bg-[var(--primary)] hover:bg-[var(--primary-hover)] text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!loading">Save</span>
          <span *ngIf="loading">Saving...</span>
        </button>
      </form>
    </div>
  </div>
</div>
