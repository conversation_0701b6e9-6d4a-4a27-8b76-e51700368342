/* Overlay */
.currency-settings-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50;
  padding: 20px;
  overflow-y: auto;
}

/* Popup Container */
.currency-settings-popup {
  @apply bg-white rounded-xl shadow-2xl w-full max-w-md mx-4;
  margin: 20px 0;
}

/* Header */
.popup-header {
  @apply flex items-center justify-between p-6 border-b border-gray-100;
}

.header-content {
  @apply flex items-center space-x-4;
}

.header-icon {
  @apply w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600;
}

.header-text {
  @apply flex-1;
}

.popup-title {
  @apply text-xl font-semibold text-gray-800 mb-1;
}

.popup-subtitle {
  @apply text-sm text-gray-500;
}

.close-button {
  @apply w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors;
}

/* Main Currency Section */
.main-currency-section {
  @apply p-6 border-b border-gray-100;
}

.main-currency-item {
  @apply flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg;
}

.currency-info {
  @apply flex items-center space-x-3;
}

.currency-code {
  @apply text-lg font-bold text-gray-800;
}

.currency-name {
  @apply text-sm text-gray-600;
}

.main-currency-badge {
  @apply flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium;
}

/* Secondary Currencies */
.secondary-currencies {
  @apply mt-6;
}

.secondary-currencies-header {
  @apply flex items-center space-x-2 mb-4;
}

.secondary-title {
  @apply text-sm font-medium text-gray-700;
}

.secondary-count {
  @apply text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full;
}

.secondary-currencies-list {
  @apply space-y-2;
}

.secondary-currency-item {
  @apply flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg;
}

.secondary-currency-item .currency-info {
  @apply flex items-center space-x-3;
}

.secondary-currency-item .currency-code {
  @apply text-sm font-semibold text-gray-800;
}

.secondary-currency-item .currency-name {
  @apply text-xs text-gray-600;
}

.secondary-currency-item .currency-actions {
  @apply flex items-center space-x-2;
}

.secondary-currency-item .currency-symbol {
  @apply text-xs text-gray-500 font-mono bg-gray-200 px-2 py-1 rounded;
}

.remove-currency-btn {
  @apply w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors;
}

/* No Secondary Currencies */
.no-secondary-currencies {
  @apply mt-6;
}

.no-currencies-content {
  @apply flex flex-col items-center justify-center py-8 text-center;
}

.no-currencies-icon {
  @apply text-2xl text-gray-300 mb-3;
}

.no-currencies-text {
  @apply text-sm font-medium text-gray-500 mb-1;
}

.no-currencies-hint {
  @apply text-xs text-gray-400;
}

/* List Currencies Section */
.list-currencies-section {
  @apply p-6 border-b border-gray-100;
}

.list-currencies-button {
  @apply w-full flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 hover:bg-blue-100 transition-colors;
}

.arrow-icon {
  @apply transition-transform duration-200;
}

/* Search Input Wrapper (shared) */
.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* Toggle Switch (shared) */
.currency-toggle {
  @apply flex items-center;
}

.toggle-switch {
  @apply relative w-12 h-6 bg-gray-300 rounded-full transition-colors duration-200 cursor-pointer;
}

.toggle-switch.active {
  @apply bg-blue-500;
}

.toggle-circle {
  @apply absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform duration-200;
}

.toggle-switch.active .toggle-circle {
  @apply transform translate-x-6;
}

/* Loading */
.loading-container {
  @apply flex items-center justify-center space-x-3 py-8 text-gray-500;
}

.spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

.spinner-small {
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Footer */
.popup-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-100 bg-gray-50;
}

.cancel-button {
  @apply px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors;
}

.save-button {
  @apply flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Currency List Modal */
.currency-list-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50;
  padding: 20px;
  overflow-y: auto;
}

.currency-list-modal {
  @apply bg-white rounded-xl shadow-2xl w-full max-w-4xl mx-4 flex flex-col;
  margin: 20px 0;
}

/* Modal Header */
.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-100 bg-gray-50;
}

.modal-title {
  @apply text-xl font-semibold text-gray-800 mb-1;
}

.modal-subtitle {
  @apply text-sm text-gray-500;
}

/* Search Section */
.search-section {
  @apply p-6 border-b border-gray-100;
}

/* Modal Content */
.modal-content {
  @apply flex-1 p-6;
}

.currency-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.currency-card {
  @apply p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors;
}

.currency-card.selected {
  @apply border-blue-500 bg-blue-50;
}

.currency-card.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.currency-info {
  @apply space-y-2;
}

.currency-header {
  @apply flex items-center justify-between;
}

.currency-code {
  @apply text-lg font-bold text-gray-800;
}

.currency-name {
  @apply text-sm text-gray-600;
}

.currency-symbol {
  @apply text-xs text-gray-500 font-mono;
}

/* No Results */
.no-results {
  @apply flex flex-col items-center justify-center py-12 text-gray-500;
}

.no-results-icon {
  @apply text-4xl mb-4;
}

/* Modal Footer */
.modal-footer {
  @apply flex items-center justify-between p-6 border-t border-gray-100 bg-gray-50;
}

.selected-count {
  @apply text-sm text-gray-600 font-medium;
}

.footer-buttons {
  @apply flex items-center space-x-3;
}

.apply-button {
  @apply flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors;
}
