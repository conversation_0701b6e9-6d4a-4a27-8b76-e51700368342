import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';

import { NotificationsComponent } from './notifications/notifications.component';
import { NewNotificationComponent } from './new-notification/new-notification.component';
import { ToggleSwitchComponent } from '../../common/toggle-switch/toggle-switch.component';
import { interactionRoutes } from './interaction.routes';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(interactionRoutes),
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconsModule,
    NotificationsComponent,
    NewNotificationComponent,
    ToggleSwitchComponent
  ]
})
export class InteractionModule { }
