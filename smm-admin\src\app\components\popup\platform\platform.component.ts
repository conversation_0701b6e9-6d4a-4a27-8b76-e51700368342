import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
interface SocialMediaItem {
  icon: string;
  name: string;
  value: string;
}

@Component({
  selector: 'app-platform',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './platform.component.html',
  styleUrl: './platform.component.css'
})
export class PlatformComponent {

  @Output() close = new EventEmitter<void>();
  @Output() dismiss = new EventEmitter<void>();

  onClose() {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent) {
    // Ki<PERSON>m tra nếu click vào chính overlay, không phải bên trong modal
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onDismiss() {
    this.dismiss.emit();
  }

  socialMediaItems: SocialMediaItem[] = [
    { icon: 'https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/social-i.png', name: 'Facebook', value: '' },
    { icon: 'https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/social-i-2.png', name: 'Youtube', value: '' },
    { icon: 'https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/social-i-3.png', name: 'TW', value: '' },
    { icon: 'https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/social-i-4.png', name: '123', value: '' },
    { icon: 'https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/social-i-5.png', name: '213', value: '' }
  ];

  onEdit(index: number): void {
    // Handle edit action
    console.log('Edit item at index:', index);
  }

  onDelete(index: number): void {
    // Handle delete action
    console.log('Delete item at index:', index);
  }
}
