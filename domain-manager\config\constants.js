/**
 * Configuration constants for the domain manager service
 */

module.exports = {
  // Server configuration
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // External service URLs
  DB_API: 'http://smm-system:8095/api/v1/domains',
  SMM_DASHBOARD_URL: 'http://smm-dashboard:4002',
  SMM_ADMIN_URL: 'http://smm-admin:4001',
  SMM_SYSTEM_URL: 'http://smm-system:8095',
  
  // File paths
  SSL_PATH: '/etc/nginx/ssl/certificates',
  NGINX_CONF_DIR: '/etc/nginx/conf.d',
  LETSENCRYPT_PATH: '/etc/letsencrypt/live',
  CERTBOT_WEBROOT: '/var/www/certbot',
  NGINX_SIGNALS_DIR: '/etc/nginx/signals',
  RENEWAL_REQUESTS_DIR: '/etc/letsencrypt/renewal-requests',
  
  // DNS configuration
  SERVER_IP: process.env.SERVER_IP || '**************',
  BASE_DOMAIN: 'autovnfb.com',
  
  // SSL configuration
  ADMIN_EMAIL: '<EMAIL>',
  
  // Timeouts and intervals
  NGINX_RELOAD_WAIT: 2000,
  SSL_GENERATION_WAIT: 5000,
  SSL_CHECK_INTERVAL: 5000,
  SSL_MAX_ATTEMPTS: 60,
  
  // Cache settings
  CDN_CACHE_MAX_AGE: 2592000, // 30 days
  
  // Security headers
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block'
  },
  
  // CORS settings
  CORS_HEADERS: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept'
  }
};
