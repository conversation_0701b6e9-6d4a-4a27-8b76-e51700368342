import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AppAssetsService } from '../../../core/services/app-assets.service';
import { Subscription } from 'rxjs';

interface SideNavItem {
  icon: IconName;
  label: string;
  link: string;
  isActive: boolean;
}

interface SideNavSection {
  title: string;
  items: SideNavItem[];
}

@Component({
  selector: 'app-admin-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, IconsModule, TranslateModule],
  templateUrl: './admin-sidebar.component.html',
  styleUrl: './admin-sidebar.component.css'
})
export class AdminSidebarComponent implements OnInit, OnDestroy {
  @Input() isOpen: boolean = true;
  //logoUrl: string = 'assets/images/logo.png'; // Keep the default logo for admin pages
  private subscription = new Subscription();

  adminNavItems: SideNavSection[] = [
    {
      title: 'Main Menu',
      items: [
        {
          icon: 'user' as IconName,
          label: 'Users',
          link: '/panel/users',
          isActive: false
        },
        {
          icon: 'shopping-cart' as IconName,
          label: 'Orders',
          link: '/panel/orders',
          isActive: false
        },
        {
          icon: 'tag' as IconName,
          label: 'Services',
          link: '/panel/services',
          isActive: false
        },
        {
          icon: 'headset' as IconName,
          label: 'Support',
          link: '/panel/tickets',
          isActive: false
        },
        {
          icon: 'chart-line' as IconName,
          label: 'Statistics',
          link: '/panel/statistics',
          isActive: false
        }
      ]
    },
    {
      title: 'More',
      items: [
        {
          icon: 'chart-bar' as IconName,
          label: 'Dashboard',
          link: '/panel/dashboard',
          isActive: false
        },
        {
          icon: 'cog' as IconName,
          label: 'Settings',
          link: '/panel/settings',
          isActive: false
        },

      ]
    }
  ];

  constructor(
    private router: Router
  ) {}

  ngOnInit() {
    this.updateActiveState();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  updateActiveState() {
    const currentUrl = this.router.url;

    this.adminNavItems.forEach(section => {
      section.items.forEach(item => {
        // Check if the current URL starts with the item's link
        // This handles both exact matches and child routes (like /admin/tickets/123)
        if (item.link === '/panel/tickets') {
          item.isActive = currentUrl.startsWith(item.link);
        } else {
          item.isActive = currentUrl === item.link;
        }
      });
    });
  }
}
