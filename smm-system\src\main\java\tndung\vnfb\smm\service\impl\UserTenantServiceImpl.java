package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.entity.UserTenant;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.UserTenantRepository;
import tndung.vnfb.smm.service.UserTenantService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UserTenantServiceImpl implements UserTenantService {
    private final UserTenantRepository userTenantRepository;
    @Override
    public UserTenant save(Long userid, String tenantId) {
        final UserTenant userTenant = new UserTenant();
        userTenant.setTenantId(tenantId);
        userTenant.setUserId(userid);
        return userTenantRepository.save(userTenant);
    }

    @Override
    public boolean hasAccessToTenant(Long userId, String tenantId) {
        return userTenantRepository.existsByUserIdAndTenantId(userId, tenantId);
    }

    @Override
    public UserTenant findByUserId(Long userId) {
        List<UserTenant> userTenants = userTenantRepository.findByUserId(userId);
        if (userTenants.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }
        return userTenants.get(0);

    }
}
