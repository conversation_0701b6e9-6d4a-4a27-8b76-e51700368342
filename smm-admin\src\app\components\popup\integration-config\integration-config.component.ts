import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { IntegrationConfig } from '../../../core/services/integrations.service';

@Component({
  selector: 'app-integration-config',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './integration-config.component.html',
  styleUrl: './integration-config.component.css'
})
export class IntegrationConfigComponent {
  @Input() integration: IntegrationConfig | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<IntegrationConfig>();

  // Form model
  formData: IntegrationConfig = {
    type: '',
    username: '',
    position: 'left',
    enabled: true
  };

  // Position options
  positionOptions: string[] = ['left', 'right'];

  ngOnChanges(): void {
    if (this.integration) {
      this.formData = { ...this.integration };
      // Set default position if not provided
      if (!this.formData.position) {
        this.formData.position = 'left';
      }
    }
  }

  onClose(): void {
    this.close.emit();
  }

  onSave(): void {
    // Always set enabled to true when saving
    this.formData.enabled = true;
    this.save.emit(this.formData);
  }

  onOverlayClick(event: MouseEvent): void {
    // Prevent closing when clicking inside the modal content
    if ((event.target as HTMLElement).classList.contains('overlay-black')) {
      this.onClose();
    }
  }

  getIntegrationTitle(): string {
    if (!this.integration) return 'Integration';

    const type = this.integration.type;
    return type.charAt(0).toUpperCase() + type.slice(1);
  }

  /**
   * Get field label based on integration type
   */
  getValueFieldLabel(): string {
    if (!this.integration) return 'Username';

    switch (this.integration.type.toLowerCase()) {
      case 'telegram':
        return 'Telegram Username';
      case 'whatsapp':
        return 'WhatsApp Number';
      default:
        return 'Value';
    }
  }

  /**
   * Get field placeholder based on integration type
   */
  getValueFieldPlaceholder(): string {
    if (!this.integration) return 'Enter value';

    switch (this.integration.type.toLowerCase()) {
      case 'telegram':
        return '@username';
      case 'whatsapp':
        return '+84123456789';
      default:
        return 'Enter value';
    }
  }
}
