<div class="overlay-black" >
  <div class="modal-container bg-white rounded-2xl shadow-lg w-[400px] max-w-full">
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
      <div>
        <h2 class="text-xl font-semibold text-gray-800">{{ isBanning ? 'Ban account' : 'Unban account' }}</h2>
        <p *ngIf="isBulkAction" class="text-sm text-gray-500 mt-1">{{ selectedCount }} users selected</p>
      </div>
      <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Form -->
    <div class="p-6">
      <form (ngSubmit)="submitAction()">
        <!-- Login/Username -->
        <div class="mb-4" *ngIf="!isBulkAction">
          <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Login</label>
          <input
            type="text"
            id="username"
            name="username"
            [value]="userName"
            disabled
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)] opacity-70"
          >
        </div>

        <!-- Multiple users message -->
        <div class="mb-4" *ngIf="isBulkAction">
          <div class="p-3 bg-blue-50 text-blue-700 rounded-lg">
            <p>{{ isBanning ? 'This action will ban all selected active users.' : 'This action will unban all selected banned users.' }}</p>
          </div>
        </div>

        <!-- Reason (only for banning) -->
        <div *ngIf="isBanning" class="mb-6">
          <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">Reason</label>
          <input
            type="text"
            id="reason"
            name="reason"
            [(ngModel)]="reason"
            placeholder="Ban reason"
            class="w-full px-4 py-3 bg-[var(--background)] border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
          >
        </div>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
          {{ errorMessage }}
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-4">
          <!-- Ban/Unban Button -->
          <button
            type="submit"
            [disabled]="isLoading"
            class="flex-1 py-3 px-4 rounded-lg transition-colors duration-200 font-medium flex items-center justify-center"
            [ngClass]="isBanning ? 'bg-[var(--error)] hover:opacity-90 text-white' : 'bg-[var(--success)] hover:opacity-90 text-white'"
          >
            <div *ngIf="isLoading" class="mr-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            {{ isBanning ? 'Ban' : 'Unban' }}
          </button>

          <!-- Cancel Button -->
          <button
            type="button"
            [disabled]="isLoading"
            (click)="closeModal()"
            class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 px-4 rounded-lg transition-colors duration-200 font-medium"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
