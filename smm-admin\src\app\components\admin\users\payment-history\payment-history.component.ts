import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { TransactionRes, TransactionPageRes, TransactionType } from '../../../../model/response/transaction.model';
import { TransactionSearchReq } from '../../../../model/response/my-transaction.model';
import { UserBalanceService } from '../../../../core/services/user-balance.service';
import { ToastService } from '../../../../core/services/toast.service';
import { Subscription } from 'rxjs';
import { IconName } from '@fortawesome/fontawesome-svg-core';

@Component({
  selector: 'app-payment-history',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule
  ],
  templateUrl: './payment-history.component.html',
  styleUrls: ['./payment-history.component.css']
})
export class PaymentHistoryComponent implements OnInit, OnDestroy {
  @Input() userId!: number;
  @Input() userName!: string;
  @Output() close = new EventEmitter<void>();

  transactions: TransactionRes[] = [];
  isLoading = false;

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };

  // Filter
  searchFilter: TransactionSearchReq = {};

  private subscriptions: Subscription[] = [];

  constructor(
    private userBalanceService: UserBalanceService,
    private toast: ToastService
  ) {}

  ngOnInit(): void {
    this.loadTransactions();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadTransactions(page: number = 0): void {
    if (!this.userId) return;

    this.isLoading = true;

    const subscription = this.userBalanceService.getUserTransactions(
      this.userId,
      page,
      this.pagination.pageSize
    ).subscribe({
      next: (response: TransactionPageRes) => {
        this.transactions = response.content;
        this.pagination = {
          pageNumber: response.number,
          pageSize: response.size,
          totalElements: response.total_elements,
          totalPages: response.total_pages
        };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading payment history:', error);
        this.toast.showError(error?.message || 'Failed to load payment history');
        this.isLoading = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  getTransactionTypeClass(type: string): string {
    switch (type) {
      case TransactionType.DEPOSIT:
      case TransactionType.BONUS:
        return 'text-green-600 bg-green-100';
      case TransactionType.WITHDRAW:
      case TransactionType.PAYMENT:
      case TransactionType.SPENT:
        return 'text-red-600 bg-red-100';
      case TransactionType.REFUND:
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  getFormattedAmount(transaction: TransactionRes): string {
    if (transaction.change > 0) {
      return `+$${transaction.change.toFixed(6).replace(/\.?0+$/, '')}`;
    } else {
      return `-$${Math.abs(transaction.change).toFixed(6).replace(/\.?0+$/, '')}`;
    }
  }

  getTransactionIcon(type: string): IconName {
    switch (type) {
      case TransactionType.DEPOSIT:
        return 'plus-circle';
      case TransactionType.WITHDRAW:
        return 'minus-circle';
      case TransactionType.BONUS:
        return 'gift';
      case TransactionType.PAYMENT:
      case TransactionType.SPENT:
        return 'shopping-cart';
      case TransactionType.REFUND:
        return 'undo';
      default:
        return 'exchange-alt';
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  formatBalance(balance: number): string {
    return balance.toFixed(6).replace(/\.?0+$/, '');
  }

  // Expose Math to template
  Math = Math;

  nextPage(): void {
    if (this.pagination.pageNumber < this.pagination.totalPages - 1) {
      this.loadTransactions(this.pagination.pageNumber + 1);
    }
  }

  prevPage(): void {
    if (this.pagination.pageNumber > 0) {
      this.loadTransactions(this.pagination.pageNumber - 1);
    }
  }

  closeModal(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }
}
