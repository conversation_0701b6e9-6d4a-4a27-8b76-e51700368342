// Example of how to use the MFA Setting component in a profile component

// 1. Import the MFA Setting component in your component
import { MfaSettingComponent } from '../popup/mfa-setting/mfa-setting.component';

// 2. Add it to your component imports
@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    MfaSettingComponent
    // ... other imports
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
  // Add a property to control the visibility of the MFA Setting popup
  showMfaSettings = false;

  // ... other component code

  // Method to open the MFA Setting popup
  openMfaSettings(): void {
    this.showMfaSettings = true;
  }

  // Method to close the MFA Setting popup
  closeMfaSettings(): void {
    this.showMfaSettings = false;
  }
}

// 3. Add the MFA Setting component to your template
<!-- In your profile.component.html -->
<div class="security-section">
  <h3>{{ 'profile.security' | translate }}</h3>
  
  <div class="security-option">
    <div class="option-info">
      <h4>{{ 'profile.two_factor_auth' | translate }}</h4>
      <p>{{ 'profile.two_factor_auth_description' | translate }}</p>
    </div>
    <div class="option-action">
      <button 
        *ngIf="!is2FAEnabled" 
        class="enable-button" 
        (click)="openMfaSettings()">
        {{ 'profile.enable' | translate }}
      </button>
      <button 
        *ngIf="is2FAEnabled" 
        class="disable-button" 
        (click)="disable2FA()">
        {{ 'profile.disable' | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Add the MFA Setting component at the end of your template -->
<app-mfa-setting 
  *ngIf="showMfaSettings" 
  (close)="closeMfaSettings()">
</app-mfa-setting>
