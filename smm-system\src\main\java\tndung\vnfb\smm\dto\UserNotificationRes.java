package tndung.vnfb.smm.dto;

import lombok.Data;
import tndung.vnfb.smm.entity.UserNotification;

import java.time.OffsetDateTime;

@Data
public class UserNotificationRes {
    private Long id;
    private String title;
    private String content;
    private UserNotification.NotificationType type;
    private UserNotification.NotificationCategory category;
    private Boolean isRead;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
}
