<div class="relative">
  <!-- Notification Bell Icon -->
  <button
    (click)="toggleDropdown()"
    class="relative p-2 text-gray-600 hover:text-gray-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg">

    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
    </svg>

    <!-- Enhanced Unread Count Badge -->
    <span
      *ngIf="unreadCount > 0"
      class="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg animate-pulse-custom">
      {{ unreadCount > 99 ? '99+' : unreadCount }}
    </span>
  </button>

  <!-- Dropdown Menu -->
  <div
    *ngIf="isOpen"
    class="notification-dropdown absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 flex flex-col"
    style="max-height: 500px;">

    <!-- Enhanced Header -->
    <div class="flex-shrink-0 px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-2">
          <h3 class="text-lg font-semibold text-gray-900">Thông báo</h3>
          <span *ngIf="unreadCount > 0" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
            {{ unreadCount }}
          </span>
        </div>
        <div class="flex items-center gap-2">
          <button
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()"
            class="text-sm text-blue-600 hover:text-blue-800 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors">
            <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Đánh dấu tất cả
          </button>
          <button
            (click)="toggleShowAll()"
            class="text-xs text-gray-500 hover:text-gray-700 font-medium px-2 py-1 rounded hover:bg-gray-100 transition-colors">
            {{ showAllNotifications ? 'Chỉ chưa đọc' : 'Tất cả' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="px-4 py-8 text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="text-gray-500 mt-2">Đang tải...</p>
    </div>

    <!-- Notifications List -->
    <div *ngIf="!loading" class="flex-1 overflow-y-auto" style="max-height: 350px;">
      <div *ngIf="getDisplayedNotifications().length === 0" class="px-4 py-12 text-center">
        <div class="bg-gray-50 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
        </div>
        <p class="text-gray-500 font-medium">
          {{ showAllNotifications ? 'Không có thông báo nào' : 'Không có thông báo mới' }}
        </p>
        <p class="text-gray-400 text-sm mt-1">
          {{ showAllNotifications ? 'Tất cả thông báo sẽ xuất hiện ở đây' : 'Thông báo mới sẽ xuất hiện ở đây' }}
        </p>
      </div>

      <div
        *ngFor="let notification of getDisplayedNotifications(); trackBy: trackByNotificationId"
        (click)="markAsRead(notification)"
        class="notification-item cursor-pointer border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors duration-200"
        [class.unread]="!notification.is_read"
        [class.read]="notification.is_read">

        <div class="px-4 py-3">
          <!-- Title -->
          <h4 class="notification-title text-sm font-medium mb-2 leading-5 text-gray-900"
              [class.font-semibold]="!notification.is_read"
              [class.!text-gray-700]="notification.is_read">
            {{ notification.title }}
          </h4>

          <!-- Content -->
          <p class="notification-content text-sm mb-3 leading-relaxed text-gray-600"
             [class.!text-gray-500]="notification.is_read">
            {{ notification.content }}
          </p>

          <!-- Footer with time and status -->
          <div class="flex items-center justify-between">
            <span class="notification-time text-xs font-medium text-blue-600"
                  [class.!text-gray-400]="notification.is_read">
              {{ formatDate(notification.created_at) }}
            </span>

            <!-- Status indicator -->
            <div class="flex items-center gap-1 flex-shrink-0">
              <!-- Unread dot -->
              <span *ngIf="!notification.is_read"
                    class="w-2 h-2 bg-blue-600 rounded-full"></span>
              <!-- Read checkmark -->
              <svg *ngIf="notification.is_read"
                   class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Footer -->
    <div class="flex-shrink-0 px-4 py-3 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
      <div class="flex items-center justify-between">
        <div class="text-xs text-gray-500">
          {{ getDisplayedNotifications().length }} thông báo
        </div>
        <button class="text-sm text-blue-600 hover:text-blue-800 font-medium px-3 py-1 rounded hover:bg-blue-50 transition-colors flex items-center gap-1">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
          Xem tất cả
        </button>
      </div>
    </div>
  </div>
</div>
