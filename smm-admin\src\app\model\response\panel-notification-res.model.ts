export interface PanelNotificationRes {
  id: number;
  tenantId: string;
  userId?: number;
  title: string;
  content: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  category: 'SYSTEM' | 'RENEWAL' | 'SETUP';
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

export interface PanelNotificationReq {
  title: string;
  content: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  category: 'SYSTEM' | 'RENEWAL' | 'SETUP';
  userId?: number;
}
