/**
 * Model representing a notification from the API
 */
export interface NotificationRes {
  id: number;
  title: string;
  content: string;
  type: 'Popup' | 'Fixed';
  only_for_customers: boolean;
  offer_ending_type: 'Never' | 'After_Some_Time' | 'Period';
  offer_end_date: string | null;
  show: boolean;
  auto_dismiss?: boolean;
  dismiss_hours?: number;
}

/**
 * Request model for creating or updating a notification
 */
export interface NotificationReq {
  title: string;
  content: string;
  type: 'Popup' | 'Fixed';
  only_for_customers: boolean;
  offer_ending_type: 'Never' | 'After_Some_Time' | 'Period';
  offer_end_date?: string;
  show: boolean;
  auto_dismiss?: boolean;
  dismiss_hours?: number;
}
