package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.dto.request.TenantCurrencyReq;
import tndung.vnfb.smm.dto.response.TenantCurrencyRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.service.CurrencyService;
import tndung.vnfb.smm.service.TenantCurrencyService;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.config.TenantContext;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TenantCurrencyServiceImpl implements TenantCurrencyService {

    private final TenantService tenantService;
    private final CurrencyService currencyService;

    @Override
    public TenantCurrencyRes getTenantCurrencies() {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        TenantCurrencyRes response = new TenantCurrencyRes();

        // Get available currencies for this tenant
        List<String> availableCurrencies = Arrays.asList(
            tenant.getAvailableCurrencies().split(",")
        );
        response.setAvailableCurrencies(availableCurrencies);

        // Get all currencies from system
        response.setAllCurrencies(currencyService.getAllCurrencies());

        return response;
    }

    @Override
    @Transactional
    public TenantCurrencyRes updateTenantCurrencies(TenantCurrencyReq req) {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        // Validate that USD is always included
        if (!req.getAvailableCurrencies().contains("USD")) {
            req.getAvailableCurrencies().add(0, "USD");
        }

        // Validate that all currencies exist
        for (String currencyCode : req.getAvailableCurrencies()) {
            currencyService.getCurrencyByCode(currencyCode);
        }

        // Update tenant currencies
        String currenciesString = String.join(",", req.getAvailableCurrencies());
        tenant.setAvailableCurrencies(currenciesString);
        tenantService.save(tenant);

        return getTenantCurrencies();
    }

    @Override
    public List<Currency> getAvailableCurrenciesForCurrentTenant() {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new RuntimeException("Tenant not found"));

        List<String> availableCurrencyCodes = Arrays.asList(
            tenant.getAvailableCurrencies().split(",")
        );

        return availableCurrencyCodes.stream()
                .map(currencyService::getCurrencyByCode)
                .collect(Collectors.toList());
    }
}
