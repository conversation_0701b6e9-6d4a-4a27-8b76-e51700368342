# HTTP server block for ${tenant.domain} - handles Let's Encrypt challenges
server {
    listen 80;
    listen [::]:80;
    server_name autovnfb.com;

    # Let's Encrypt challenge response - MUST BE BEFORE THE REDIRECT
    location ~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other HTTP requests to HTTPS
    location / {
      return 301 https://$host$request_uri;
    }
}

# HTTPS server block for ${tenant.domain}
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name autovnfb.com www.autovnfb.com;

    ssl_certificate /etc/nginx/ssl/certificates/autovnfb.com.crt;
    ssl_certificate_key /etc/nginx/ssl/certificates/autovnfb.com.key;

    # Set tenant-specific environment variables
    set $tenant_id "0e22c37d-bfb5-4276-bd30-355fcdb39c9e";
    set $tenant_domain "autovnfb.com";
    set $tenant_api_url "https://autovnfb.com/api/v1";
    set $tenant_site_url "https://autovnfb.com";

    # Frontend routing
    location / {
        proxy_pass http://smm-site:4001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Tenant-ID "0e22c37d-bfb5-4276-bd30-355fcdb39c9e";
        proxy_set_header X-Tenant-Domain "autovnfb.com";
        proxy_set_header X-Tenant-API-URL "https://autovnfb.com/api/v1";

        # Pass tenant-specific configuration to the frontend
        sub_filter '</head>' '<script>
            window.TENANT_CONFIG = {
                tenantId: "0e22c37d-bfb5-4276-bd30-355fcdb39c9e",
                domain: "autovnfb.com",
                apiUrl: "https://autovnfb.com/api/v1"
            };
        </script></head>';
        sub_filter_once on;
    }


    # Backend API routing
    location /api/ {
        proxy_pass http://smm-system:8095;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID "0e22c37d-bfb5-4276-bd30-355fcdb39c9e";
        proxy_set_header X-Tenant-Domain "autovnfb.com";
    }
    #cdn
    location /cdn/ {
        alias /etc/nginx/cdn/;

        # Cache settings
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header X-Content-Type-Options "nosniff";

        # CORS settings
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";

        # Enable directory listing for debugging (remove in production)
        autoindex on;

        # Limit access to image files only
        location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
            try_files $uri =404;
        }
    }
}