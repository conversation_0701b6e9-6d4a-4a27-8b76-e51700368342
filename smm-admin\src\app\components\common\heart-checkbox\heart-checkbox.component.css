/* All styles converted to Tailwind classes in the HTML template */

/* Loading spinner animation - can't be done with pure Tailwind */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-spinner {
  width: 1.25rem; /* w-5 */
  height: 1.25rem; /* h-5 */
  border-radius: 9999px; /* rounded-full */
  border: 2px solid rgba(255, 120, 131, 0.3);
  border-top-color: #FF7883;
  animation: spin 0.8s linear infinite;
}

/* Cursor style for loading state */
.heart-checkbox.loading {
  cursor: wait;
}
