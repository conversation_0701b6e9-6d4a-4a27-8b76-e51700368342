.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.promo-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)];
}

.form-input {
  @apply w-full px-4 py-3 bg-[#f5f7fc] border border-transparent rounded-lg focus:border-[#30B0C7] focus:ring-1 focus:ring-[#30B0C7] outline-none text-[var(--gray-800)];
}

/* Random button styling */
.random-button {
  @apply absolute right-0 top-0 h-full px-3 bg-gray-100 border-l border-gray-200 rounded-r-lg text-gray-600 hover:text-gray-800 hover:bg-gray-200 transition-colors flex items-center;
}

.input-error {
  @apply border-red-500 focus:border-red-500 focus:ring-red-500;
}

.validation-error {
  @apply text-red-500 text-xs mt-1;
}

.error-message {
  @apply bg-red-100 text-red-700 p-3 rounded-lg mb-4 text-sm;
}

.save-button {
  @apply w-full bg-[#30B0C7] text-white font-medium py-3 px-4 rounded-lg hover:bg-[#2599a8] active:bg-[#1e7f8a] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed;
}
