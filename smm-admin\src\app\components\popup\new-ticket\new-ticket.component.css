.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.btn-submit {
  @apply bg-[var(--primary)] text-white py-3 px-4 rounded-xl font-medium;
  transition: all 0.2s;
}

.btn-submit:hover {
  opacity: 0.9;
}

.btn-submit:active {
  transform: scale(0.98);
}

.btn-submit:disabled {
  @apply bg-gray-400 cursor-not-allowed;
  opacity: 0.7;
}
