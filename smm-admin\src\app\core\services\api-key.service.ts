import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { ConfigService } from './config.service';
import { finalize, tap } from 'rxjs/operators';
import { ApiKeyUserRes } from '../../model/response/api-key-user-res.model';


@Injectable({
  providedIn: 'root'
})
export class ApiKeyService {
  private _loading$ = new BehaviorSubject<boolean>(false);

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  /**
   * Get the user's API key
   * @returns Observable with API key response
   */
  getApiKey(): Observable<ApiKeyUserRes> {
    this._loading$.next(true);
    return this.http.get<ApiKeyUserRes>(`${this.configService.apiUrl}/users/me/api-key`)
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Generate a new API key for the user
   * @returns Observable with new API key response
   */
  generateApiKey(): Observable<ApiKeyUserRes> {
    this._loading$.next(true);
    return this.http.put<ApiKeyUserRes>(`${this.configService.apiUrl}/users/me/api-key`, {})
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }
}
