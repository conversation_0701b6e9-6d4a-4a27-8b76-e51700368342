export enum TransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAW = 'WITHDRAW',
  REFUND = 'REFUND',
  PAYMENT = 'PAYMENT',
  SPENT = 'Spent',
  BONUS = 'Bonus'
}

export enum TransactionSource {
  MANUAL = 'MANUAL',
  SYSTEM = 'SYSTEM',
  BONUS = 'BONUS',
  REMOVE = 'REMOVE',
  PAYMENT = 'PAYMENT',
  ADD_ORDER = 'ADD_ORDER'
}

export interface TransactionRes {
  id: number;
  action?: string;
  user?: number;
  note: string;
  order_id?: number;
  change: number;
  balance: number;
  type: TransactionType | string;
  source: TransactionSource | string;
  created_at: string;
}

export interface TransactionPageRes {
  content: TransactionRes[];
  pageable: {
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    offset: number;
    page_size: number;
    page_number: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  total_pages: number;
  total_elements: number;
  size: number;
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  first: boolean;
  number_of_elements: number;
  empty: boolean;
}
