import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class SidebarService {
  private sidebarOpenSubject: BehaviorSubject<boolean>;
  public sidebarOpen$: Observable<boolean>;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    // Mặc định đóng trên cả server và client
    this.sidebarOpenSubject = new BehaviorSubject<boolean>(false);
    this.sidebarOpen$ = this.sidebarOpenSubject.asObservable();

    // Chỉ xử lý trên browser
    if (this.isBrowser()) {
      // Kiểm tra kích thước ban đầu
      this.handleResize();
      // Thêm event listener cho resize
      window.addEventListener('resize', () => {
        this.handleResize();
      });
    }
  }

  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  private handleResize(): void {
    const isDesktop = window.innerWidth >= 768; // md breakpoint
    // Chỉ tự động mở trên desktop, không đóng tự động nếu đã mở thủ công
    if (isDesktop && !this.sidebarOpenSubject.value) {
      this.sidebarOpenSubject.next(true);
    }
  }

  toggleSidebar(): void {
    if (this.isBrowser()) {
      const currentState = this.sidebarOpenSubject.value;
      this.sidebarOpenSubject.next(!currentState);
    }
  }

  setSidebarState(state: boolean): void {
    this.sidebarOpenSubject.next(state);
  }

  getSidebarState(): boolean {
    return this.sidebarOpenSubject.value;
  }


  
}