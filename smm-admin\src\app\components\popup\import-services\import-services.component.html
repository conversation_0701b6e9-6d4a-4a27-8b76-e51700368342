<!-- Step 1: Select services -->
<div *ngIf="currentStep === 1" class="overlay-black p-2" (click)="onOverlayClick($event)">
  <div class="modal-container">
    <div class="modal-content">
      <!-- Header with close button -->
      <div class="modal-header">
        <div class="flex items-center">
          <fa-icon [icon]="['fas', 'file-import']" class="text-[#0095f6] text-xl mr-3"></fa-icon>
          <h2 class="modal-title">{{ 'Select provider and services' }}</h2>
        </div>
        <button class="close-button" (click)="closePopup()">
          <fa-icon [icon]="['fas', 'xmark']" class="text-lg"></fa-icon>
        </button>
      </div>

      <!-- Provider selection -->
      <div class="form-section">
        <label class="form-label">{{ 'Provider' }}</label>
        <app-lite-dropdown
          [options]="providerNames"
          (selected)="onProviderChange($event)"
          [customClassDropdown]="'bg-white border border-gray-300 rounded-lg'"
          [customClassButton]="'text-gray-700'">
        </app-lite-dropdown>
      </div>

      <!-- Search bar -->
      <div class="form-section">
        <div class="search-container h-13">
          <input
            type="text"
            class="search-input "
            placeholder="{{ 'Search by service ID or name' }}"
            [(ngModel)]="searchTerm"
            (input)="onSearch()"
            [disabled]="isLoading">
          <fa-icon [icon]="['fas', 'search']" class="search-icon"></fa-icon>
        </div>
      </div>

      <!-- Services list -->
      <div class="services-container">
        <div *ngIf="isLoading" class="loading-indicator">
          <fa-icon [icon]="['fas', 'spinner']" [spin]="true" class="text-[#0095f6] text-2xl"></fa-icon>
          <p>{{ 'Loading...' }}</p>
        </div>

        <div *ngIf="!isLoading && filteredServices.length === 0" class="empty-state">
          <fa-icon [icon]="['fas', 'info-circle']" class="text-gray-400 text-2xl mb-2"></fa-icon>
          <p>{{ 'No services found' }}</p>
        </div>

        <div *ngIf="!isLoading && filteredServices.length > 0">
          <!-- Select all row -->
          <div class="select-all-row">
            <div class="flex items-center justify-between px-3 py-2 border-b border-gray-200">
              <div class="flex items-center">
                <input
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 mr-2"
                  [checked]="isAllSelected()"
                  (change)="toggleSelectAll()">
                <span class="text-sm font-medium text-gray-700">Select all</span>
              </div>
              <span class="text-sm font-medium text-gray-500">{{ selectedServices.size }} services</span>
            </div>
          </div>

          <div class="service-list">
            <div *ngFor="let service of filteredServices"
                class="service-item cursor-pointer hover:bg-gray-50"
                (click)="toggleServiceSelection(service.service)">

                <input
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 mr-2"
                  [checked]="isServiceSelected(service.service)"
                  (click)="$event.stopPropagation();"
                  (change)="toggleServiceSelection(service.service)">

              <div class="service-info">
                <div class="service-id">ID {{ service.service }}</div>
                <div class="service-name">{{ service.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action button -->
      <div class="action-container">
        <button
          class="import-button"
          [disabled]="!selectAtLeastOneService() || isLoading"
          [class.disabled]="!selectAtLeastOneService() || isLoading"
          (click)="importSelectedServices()">
          {{ 'Continue' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Step 2: Configure services -->
<app-import-service-step2
  *ngIf="currentStep === 2"
  [selectedServices]="selectedServicesData"
  [selectedProvider]="selectedProvider"
  (close)="closePopup()"
  (back)="goBackToStep1()"
  (servicesConfigured)="finalizeImport($event)">
</app-import-service-step2>
