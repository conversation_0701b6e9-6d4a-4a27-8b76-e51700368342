import { Component, Output, EventEmitter, Input, OnInit, OnDestroy, HostListener, ElementRef, OnChanges, SimpleChanges, Renderer2, AfterViewInit } from '@angular/core';
import { IconBaseModel } from '../../../model/base-model';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { SocialIconComponent } from '../social-icon/social-icon.component';
import { DropdownService } from '../../../core/services/dropdown.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-icon-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule, SocialIconComponent, TranslateModule],
  templateUrl: './icon-dropdown.component.html',
  styleUrl: './icon-dropdown.component.css'
})
export class IconDropdownComponent implements OnInit, OnDestroy, OnChanges, AfterViewInit {

  @Input() options: IconBaseModel[] = []; // Danh sách option
  @Input() placeholder: string = 'Chọn một tùy chọn';
  @Input() iconSize: number = 20;
  @Input() selectedOption: IconBaseModel | undefined;

  @Input() customClassButton: string = '';
  @Input() customClassDropdown : string = '';


  @Output() selected = new EventEmitter<IconBaseModel>(); // Emit giá trị đã chọn
  isOpen = false;
  private dropdownMenuElement: HTMLElement | null = null;

  // Flag to track if we just opened the dropdown
  private justOpened = false;

  // Unique ID for this dropdown instance
  private dropdownId: string = `icon-dropdown-${Math.random().toString(36).substring(2, 9)}`;

  // Subscription to the closeAllDropdowns observable
  private closeAllSubscription: Subscription;

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private dropdownService: DropdownService
  ) {
    // Subscribe to the closeAllDropdowns observable
    this.closeAllSubscription = this.dropdownService.closeAllDropdowns.subscribe(() => {
      if (this.isOpen) {
        this.isOpen = false;
        this.removeDropdownFromDOM();
        document.body.classList.remove('icon-dropdown-open');
      }
    });
  }

  ngOnDestroy() {
    // Clean up any dropdown menu that might be in the DOM
    this.removeDropdownFromDOM();

    // Remove the class from the body if it was added
    document.body.classList.remove('icon-dropdown-open');

    // Unsubscribe from the closeAllDropdowns observable
    if (this.closeAllSubscription) {
      this.closeAllSubscription.unsubscribe();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // If selectedOption changes from parent component, update it
    if (changes['selectedOption'] && changes['selectedOption'].currentValue) {
      this.selectedOption = changes['selectedOption'].currentValue;
    }
  }

  ngOnInit(): void {
    // If selectedOption is provided, use it; otherwise, use the first option
    if (!this.selectedOption && this.options.length > 0) {
      this.selectedOption = this.options[0];
      this.selected.emit(this.options[0]);
    }
  }

  ngAfterViewInit(): void {
    // Add a small delay to ensure the component is fully rendered
    setTimeout(() => {
      this.updateDropdownPosition();
    }, 0);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // If we just opened the dropdown, ignore this click event
    if (this.justOpened) {
      this.justOpened = false;
      return;
    }

    // Check if the click is outside the dropdown
    const target = event.target as HTMLElement;
    const isInsideDropdown = this.elementRef.nativeElement.contains(target);
    const isInsideDropdownMenu = this.dropdownMenuElement && this.dropdownMenuElement.contains(target);

    if (!isInsideDropdown && !isInsideDropdownMenu) {
      this.isOpen = false;
      this.removeDropdownFromDOM();

      // Remove the class from the body
      document.body.classList.remove('icon-dropdown-open');
    }
  }

  @HostListener('window:resize')
  onWindowResize() {
    if (this.isOpen) {
      this.updateDropdownPosition();
    }
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    if (this.isOpen) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        this.updateDropdownPosition();
      });
    }
  }

  toggleDropdown(event?: MouseEvent) {
    if (event) {
      // Stop the event from propagating to prevent document click handler from firing
      event.stopPropagation();
      event.preventDefault();
    }

    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      // Notify the dropdown service that this dropdown is now open
      this.dropdownService.openDropdown(this.dropdownId, this);

      // Set the flag to indicate we just opened the dropdown
      this.justOpened = true;

      // Add a small delay to ensure the dropdown is rendered before positioning
      setTimeout(() => {
        this.updateDropdownPosition();

        // Add a class to the body to prevent scrolling while dropdown is open
        document.body.classList.add('icon-dropdown-open');

        // Force a reflow to ensure the dropdown is properly positioned
        const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.dropdown-menu-container');
        if (dropdownMenuElement) {
          void dropdownMenuElement.offsetHeight;
        }
      }, 0);
    } else {
      this.removeDropdownFromDOM();

      // Remove the class from the body
      document.body.classList.remove('icon-dropdown-open');

      // Notify the dropdown service that this dropdown is now closed
      this.dropdownService.closeDropdown();
    }
  }

  selectOption(option: IconBaseModel, event?: MouseEvent) {
    if (event) {
      // Stop the event from propagating
      event.stopPropagation();
    }

    this.selectedOption = option;
    this.selected.emit(option);
    this.isOpen = false;
    this.removeDropdownFromDOM();

    // Remove the class from the body
    document.body.classList.remove('icon-dropdown-open');

    // Notify the dropdown service that this dropdown is now closed
    this.dropdownService.closeDropdown();
  }

  private updateDropdownPosition() {
    if (!this.isOpen) return;

    // Get the button element
    const buttonElement = this.elementRef.nativeElement.querySelector('button');
    if (!buttonElement) return;

    // Get the dropdown menu element
    const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.dropdown-menu-container');
    if (!dropdownMenuElement) return;

    // Store reference to the dropdown menu for click detection
    this.dropdownMenuElement = dropdownMenuElement;

    // Get the button's position
    const buttonRect = buttonElement.getBoundingClientRect();

    // Calculate the position for the dropdown
    const top = buttonRect.bottom;
    const left = buttonRect.left;
    const width = buttonRect.width;

    // Set the position and width of the dropdown
    // Use fixed positioning relative to the viewport
    this.renderer.setStyle(dropdownMenuElement, 'position', 'fixed');
    this.renderer.setStyle(dropdownMenuElement, 'top', `${top}px`);
    this.renderer.setStyle(dropdownMenuElement, 'left', `${left}px`);
    this.renderer.setStyle(dropdownMenuElement, 'width', `${width}px`);
    this.renderer.setStyle(dropdownMenuElement, 'z-index', '99999'); // Very high z-index to ensure it's above everything

    // Check if the dropdown would go off the bottom of the screen
    const dropdownHeight = dropdownMenuElement.offsetHeight;
    const viewportHeight = window.innerHeight;

    if (top + dropdownHeight > viewportHeight) {
      // Position the dropdown above the button instead
      const newTop = buttonRect.top - dropdownHeight;
      if (newTop >= 0) {
        this.renderer.setStyle(dropdownMenuElement, 'top', `${newTop}px`);
      } else {
        // If there's not enough space above either, limit the height and add scrolling
        this.renderer.setStyle(dropdownMenuElement, 'top', '10px');
        this.renderer.setStyle(dropdownMenuElement, 'max-height', `${viewportHeight - 20}px`);
        this.renderer.setStyle(dropdownMenuElement, 'overflow-y', 'auto');
      }
    }
  }

  private removeDropdownFromDOM() {
    // Clear the reference to the dropdown menu
    this.dropdownMenuElement = null;
  }

  /**
   * Public method to programmatically select an option
   * This is used by parent components to update the selected option
   * @param option The option to select
   */
  public updateSelectedOption(option: IconBaseModel): void {
    if (option && option.id) {
      console.log('Icon dropdown programmatically selecting option:', option.label);
      this.selectedOption = option;
      // Don't emit the selected event to avoid circular updates
    }
  }
}
