package tndung.vnfb.smm.dto;

import lombok.Data;
import tndung.vnfb.smm.entity.PanelNotification;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;

@Data
public class PanelNotificationRes {
    private Long id;
  //  private String tenantId;
  //  private Long userId;
    private String title;
    private String content;
    private PanelNotification.NotificationType type;
    private PanelNotification.NotificationCategory category;
    private Boolean isRead;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
}
