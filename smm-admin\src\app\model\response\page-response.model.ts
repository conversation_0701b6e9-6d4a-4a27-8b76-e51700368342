export interface PageResponse<T> {
  content: T[];
  pageable: {
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    offset: number;
    page_number: number;
    page_size: number;
    unpaged: boolean;
    paged: boolean;
  };
  last: boolean;
  total_elements: number;
  total_pages: number;
  size: number;
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  first: boolean;
  number_of_elements: number;
  empty: boolean;
}
