.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal, 1000);
}

.modal-container {
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.password-field {
  position: relative;
}

.copy-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: color 0.2s;
}

.copy-button:hover {
  color: #3b82f6;
}

button {
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.98);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-container {
    width: 95%;
    max-width: 95%;
    border-radius: 8px;
  }
}
