.header-container {
    @apply flex items-center gap-2 mb-3;
}

.lite-description {
    @apply overflow-hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    width: 100%;
    padding-left: 2px; /* Add a small padding to prevent text from touching the icon */
}

.copy-icon {
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s;
  font-size: 14px;
}
.copy-icon:hover {
  color: #3b82f6;
}

.no-service-message {
    @apply text-sm font-medium text-gray-500 py-2;
    border-left: 3px solid #f3f4f6;
    background-color: #fafafa;
    border-radius: 4px;
}

/* Discount styles */
.discount-badge {
    font-size: 0.75rem;
    padding: 0.1rem 0.3rem;
    border-radius: 0.25rem;
    background-color: #ef4444;
    color: white;
    font-weight: 500;
}

/* Description style for full view */
.description {
    @apply overflow-hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

