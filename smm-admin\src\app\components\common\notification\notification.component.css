.notification-container {
  @apply w-[364px] shadow-[0_0_1px_#e9eaec] absolute hidden z-[1000] rounded-[20px] m-0 p-0;
  top: calc(100% + 10px);
  right: 0;
  transform: translateX(0);
}

.user-menu-container {
  @apply relative inline-block;
  margin-left: auto;
}

.notification-container.show {
  @apply block;
}

.triangle-pointer {
  @apply absolute w-0 h-0;
  top: -8px;
  right: 12px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

/* Media query for desktop */
@media (min-width: 769px) {
  .notification-container {
    right: 0;
    transform: translateX(0);
  }
}

/* Media query for mobile */
@media (max-width: 768px) {
  .notification-container {
    @apply w-[90vw] max-w-[364px];
    right: 0;
    left: auto;
    transform: translateX(0);
  }
}

  .notification-box {
    @apply p-6 bg-white rounded-[20px] border border-[#e9eaec] flex flex-col gap-2.5;
    max-height: 80vh;
    overflow-y: auto;
  }

  .notification-header {
    @apply py-2 px-4 rounded-[10px];
  }

  .notification-item {
    @apply py-2 px-4 rounded-[10px] hover:bg-gray-50 cursor-pointer transition-all duration-200;
  }

  .notification-content {
    @apply flex flex-row;
  }

  .header-text {
    @apply  font-bold text-2xl leading-[22px] text-[#262b38];
  }

  .message-text {
    @apply font-medium text-sm leading-[22px] text-[#262b38] whitespace-pre-line;
  }


  /* Standard Header Style */
  .notification {
    @apply w-8 h-8 rounded-full bg-[var(--primary-light)] flex items-center justify-center;
  }

  /* Compact Header Style */
  .notification-compact {
    @apply w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors duration-200;
  }

  /* Modern Header Style */
  .notification-modern {
    @apply w-8 h-8 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center hover:bg-blue-100 transition-colors duration-200;
  }

  /* Minimal Header Style */
  .notification-minimal {
    @apply w-6 h-6 rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors duration-200;
  }

  /* Overlay for closing the menu when clicking outside */
  .overlay {
    @apply fixed inset-0 z-[999];
  }