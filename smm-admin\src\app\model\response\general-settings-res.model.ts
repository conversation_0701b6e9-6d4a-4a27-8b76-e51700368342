/**
 * Response model for general settings
 */
export interface GeneralSettingsRes {
  id: string;
  
  // Site information
  site_name: string;
  site_description: string;
  contact_email: string;
  support_email: string;
  
  // Registration settings
  allow_registration: boolean;
  require_email_verification: boolean;
  
  // Security settings
  enable_captcha: boolean;
  enable_2fa: boolean;
  
  // Order settings
  min_order_amount: number;
  max_order_amount: number;
  
  // User settings
  default_user_balance: number;
  
  // Maintenance settings
  maintenance_mode: boolean;
  maintenance_message: string;
  
  // System information
  created_at: string;
  updated_at: string;
}
