<div class="overlay-black">

    <div class="bg-white w-full max-w-md rounded-xl shadow-lg p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-2xl font-semibold">{{ 'ticket.new_ticket' | translate }}</h2>
          <button (click)="onClose()" class="text-gray-500 text-4xl hover:text-gray-700">&times;</button>
        </div>

        <hr class="mb-6">

        <div class="flex flex-col gap-6">
          <!-- Error message -->
          <div *ngIf="submitError" class="bg-red-100 text-red-700 p-3 rounded-lg text-sm">
            {{ submitError }}
          </div>

          <!-- Ticket Type Dropdown -->
          <app-lite-dropdown
            [options]="translatedOptions"
            (selected)="selectOption($event)"
            [customClassDropdown]="'border rounded-xl'"
          ></app-lite-dropdown>

          <!-- Order Form -->
          <div class="flex flex-col gap-6" *ngIf="selectedForm === 'Order'">
            <input
              [(ngModel)]="orderId"
              placeholder="{{ 'ticket.orders_id' | translate }}"
              class="w-full bg-white rounded-xl py-3 px-4 text-sm border"
            />
            <app-lite-dropdown
              [options]="orderOptions"
              (selected)="selectOrderOption($event)"
              [customClassDropdown]="'border rounded-xl'"
            ></app-lite-dropdown>
          </div>

          <!-- Payment Form -->
          <div *ngIf="selectedForm === 'Payment'">
            <app-lite-dropdown
              [customClassDropdown]="'border rounded-xl'"
              [options]="paymentOptions"
              (selected)="selectPaymentOption($event)"
            ></app-lite-dropdown>
          </div>

          <!-- Request Form -->
          <div *ngIf="selectedForm === 'Request'">
            <app-lite-dropdown
              [customClassDropdown]="'border rounded-xl'"
              [options]="requestOptions"
              (selected)="selectRequestOption($event)"
            ></app-lite-dropdown>
          </div>

          <!-- Point Form -->
          <div *ngIf="selectedForm === 'Point'">
            <app-lite-dropdown
              [customClassDropdown]="'border rounded-xl'"
              [options]="pointOptions"
              (selected)="selectPointOption($event)"
            ></app-lite-dropdown>
          </div>

          <!-- Forms without dropdowns -->
          <div *ngIf="selectedForm === 'Child Panel' || selectedForm === 'Other'"></div>

          <!-- Message -->
          <textarea
            [(ngModel)]="message"
            class="w-full p-4 border rounded-xl mb-4"
            rows="6"
            placeholder="{{ 'ticket.message' | translate }}"
          ></textarea>

          <!-- Submit Button -->
          <button
            [disabled]="isSubmitting"
            (click)="onSubmit()"
            class="btn-submit w-full flex items-center justify-center"
          >
            <span *ngIf="isSubmitting" class="mr-2">
              <!-- Loading spinner -->
              <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ 'ticket.submit_now' | translate }}
          </button>
        </div>
      </div>
</div>