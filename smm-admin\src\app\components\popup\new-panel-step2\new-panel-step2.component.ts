import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-new-panel-step2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './new-panel-step2.component.html',
  styleUrl: './new-panel-step2.component.css'
})
export class NewPanelStep2Component {
  @Input() domain: string = '';
  @Output() close = new EventEmitter<void>();
  @Output() back = new EventEmitter<void>();
  @Output() next = new EventEmitter<void>();

  dnsServers = [
    'ns1.autovnfb.com',
    'ns2.autovnfb.com'
  ];

  onClose(): void {
    this.close.emit();
  }

  onBack(): void {
    this.back.emit();
  }

  onNext(): void {
    this.next.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
      console.log('Text copied to clipboard');
    }).catch(err => {
      console.error('Could not copy text: ', err);
    });
  }
}
