import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, finalize } from 'rxjs';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class FavoritesService {
  private _favorites$ = new BehaviorSubject<number[]>([]);
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _loadingServiceIds = new Set<number>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.loadFavorites();
  }

  get favorites$(): Observable<number[]> {
    return this._favorites$.asObservable();
  }

  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  isServiceLoading(serviceId: number): boolean {
    return this._loadingServiceIds.has(serviceId);
  }

  loadFavorites(): void {
    this._loading$.next(true);
    this.http.get<number[]>(`${this.configService.apiUrl}/services/favorites`)
      .pipe(
        tap(favoriteIds => {
          // Convert number IDs to strings

          this._favorites$.next(favoriteIds);
          this._loading$.next(false);
        })
      )
      .subscribe({
        error: (error) => {
          console.error('Error loading favorites:', error);
          this._loading$.next(false);
        }
      });
  }

  addFavorite(serviceId: number): Observable<any> {
    this._loading$.next(true);
    this._loadingServiceIds.add(serviceId);

    return this.http.put(`${this.configService.apiUrl}/services/favorites/${serviceId}`, {})
      .pipe(
        tap(() => {
          const currentFavorites = this._favorites$.value;
          if (!currentFavorites.includes(serviceId)) {
            this._favorites$.next([...currentFavorites, serviceId]);
          }
        }),
        finalize(() => {
          this._loadingServiceIds.delete(serviceId);
          this._loading$.next(false);
        })
      );
  }

  removeFavorite(serviceId: number): Observable<any> {
    this._loading$.next(true);
    this._loadingServiceIds.add(serviceId);

    return this.http.delete(`${this.configService.apiUrl}/services/favorites/${serviceId}`)
      .pipe(
        tap(() => {
          const currentFavorites = this._favorites$.value;
          this._favorites$.next(currentFavorites.filter(id => id !== serviceId));
        }),
        finalize(() => {
          this._loadingServiceIds.delete(serviceId);
          this._loading$.next(false);
        })
      );
  }

  isFavorite(serviceId: number): boolean {
    return this._favorites$.value.includes(serviceId);
  }

  toggleFavorite(serviceId: number): Observable<any> {
    if (this.isFavorite(serviceId)) {
      return this.removeFavorite(serviceId);
    } else {
      return this.addFavorite(serviceId);
    }
  }
}
