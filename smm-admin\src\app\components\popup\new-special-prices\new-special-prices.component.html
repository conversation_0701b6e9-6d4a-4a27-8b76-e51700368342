<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ editMode ? 'Edit special price' : 'New special price' }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Selected Service Info (if available) -->
      <!-- <div *ngIf="selectedService" class="selected-service-info mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div class="font-medium text-gray-700">Selected Service:</div>
        <div class="flex justify-between items-center mt-1">
          <div class="text-sm">
            <div class="font-medium">{{ selectedService.name }}</div>
            <div class="text-gray-500">ID: {{ selectedService.id }}</div>
          </div>
          <div class="text-right">
            <div class="font-medium">{{ selectedService.price }} VNĐ</div>
          </div>
        </div>
      </div> -->

      <!-- User ID Input (shown only in create mode and not from user view) -->
      <div class="form-group" *ngIf="!editMode && !fromUserView">
        <label for="userId" class="form-label">User ID</label>
        <input
          type="text"
          id="userId"
          [(ngModel)]="userId"
          class="form-input"
          placeholder="Enter user ID"
        >
      </div>

      <!-- Service Dropdown (shown in edit mode or when opened from user view) -->
      <div class="form-group" *ngIf="editMode || fromUserView">
        <label for="serviceId" class="form-label">Service</label>
        <app-lite-dropdown
          [options]="serviceOptions"
          [selectedOption]="selectedOption"
          (selected)="onServiceSelected($event)"
          [customClassDropdown]="'bg-white border border-gray-300 rounded-lg'"
          [customClassButton]="'py-3'"
        ></app-lite-dropdown>
        <div *ngIf="currentServicePrice !== null" class="mt-2 text-sm font-medium text-gray-700">
          Current price: {{ currentServicePrice }} $
        </div>
      </div>

      <!-- Discount Type Dropdown -->
      <div class="form-group">
        <label for="discountType" class="form-label">Discount type</label>
        <app-lite-dropdown
          [options]="discountTypes"
          [selectedOption]="discountType"
          (selected)="toggleDiscountType($event)"
          [customClassDropdown]="'bg-white border border-gray-300 rounded-lg'"
          [customClassButton]="'py-3'"
        ></app-lite-dropdown>
      </div>

      <!-- New Price Input -->
      <div class="form-group">
        <label for="newPrice" class="form-label">
          {{ discountType === 'Fix price' ? 'Fixed price' : 'Percentage discount (0-100)' }}
        </label>
        <div class="input-with-symbol">
          <input
            type="number"
            id="newPrice"
            [(ngModel)]="newPrice"
            class="form-input"
            [class.with-dollar]="discountType === 'Fix price'"
            [class.with-percent]="discountType === 'Percentage discount'"
            [placeholder]="discountType === 'Fix price' ? 'Enter fixed price' : 'Enter percentage (0-100)'"
            min="0"
            [max]="discountType === 'Percentage discount' ? 100 : null"
            step="0.01"
          >
          <span class="input-symbol" *ngIf="discountType === 'Fix price'">$</span>
          <span class="input-symbol" *ngIf="discountType === 'Percentage discount'">%</span>
        </div>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <!-- Save Button -->
      <button
        (click)="saveSpecialPrice()"
        class="save-button"
        [disabled]="isSubmitting || (!editMode && !fromUserView && !userId) || newPrice === null || (fromUserView && !selectedServiceId)"
      >
        <span *ngIf="!isSubmitting">{{ editMode ? 'Update' : 'Save' }}</span>
        <span *ngIf="isSubmitting" class="loading-spinner"></span>
      </button>
    </div>
  </div>
</div>
