import { Injectable } from '@angular/core';
import { ExtendedCategoryRes } from '../../model/extended/extended-category.model';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';

@Injectable({
  providedIn: 'root'
})
export class SelectionService {
  // Selected services tracking
  private selectedServices: Set<number> = new Set<number>();

  constructor() {}

  /**
   * Checks if a service is selected
   * @param service The service to check
   * @returns True if the service is selected, false otherwise
   */
  isServiceSelected(service: SuperGeneralSvRes): boolean {
    return this.selectedServices.has(service.id);
  }

  /**
   * Toggles selection of a service
   * @param service The service to toggle
   * @param isChecked Whether the service should be selected
   */
  toggleServiceSelection(service: SuperGeneralSvRes, isChecked: boolean): void {
    if (isChecked) {
      this.selectedServices.add(service.id);
    } else {
      this.selectedServices.delete(service.id);
    }
  }

  /**
   * Checks if all services in a category are selected
   * @param category The category to check
   * @returns True if all services in the category are selected, false otherwise
   */
  isCategoryFullySelected(category: ExtendedCategoryRes): boolean {
    if (!category || !category.services || category.services.length === 0) {
      return false;
    }

    return category.services.every(service => this.selectedServices.has(service.id));
  }

  /**
   * Toggles selection of all services in a category
   * @param category The category to toggle
   * @param isChecked Whether the services should be selected
   */
  toggleCategorySelection(category: ExtendedCategoryRes, isChecked: boolean): void {
    if (isChecked) {
      // Select all services in the category
      category.services.forEach(service => {
        this.selectedServices.add(service.id);
      });
    } else {
      // Deselect all services in the category
      category.services.forEach(service => {
        this.selectedServices.delete(service.id);
      });
    }
  }

  /**
   * Checks if all services across all categories are selected
   * @param displayCategories The display categories array
   * @param currentCategory The current category
   * @returns True if all services are selected, false otherwise
   */
  areAllServicesSelected(
    displayCategories: ExtendedCategoryRes[],
    currentCategory: ExtendedCategoryRes | null
  ): boolean {
    let allServices: SuperGeneralSvRes[] = [];

    // Collect all services from displayCategories
    if (displayCategories.length > 0) {
      displayCategories.forEach(category => {
        allServices = [...allServices, ...category.services];
      });
    }
    // Or from currentCategory if it's the only one displayed
    else if (currentCategory) {
      allServices = currentCategory.services;
    }

    // If there are no services, return false
    if (allServices.length === 0) {
      return false;
    }

    // Check if all services are selected
    return allServices.every(service => this.selectedServices.has(service.id));
  }

  /**
   * Gets the count of selected services
   * @returns The count of selected services
   */
  getSelectedServicesCount(): number {
    return this.selectedServices.size;
  }

  /**
   * Checks if any services are selected
   * @returns True if any services are selected, false otherwise
   */
  hasSelectedServices(): boolean {
    return this.selectedServices.size > 0;
  }

  /**
   * Toggles selection of all services across all categories
   * @param displayCategories The display categories array
   * @param currentCategory The current category
   * @param isChecked Whether the services should be selected
   */
  toggleAllServices(
    displayCategories: ExtendedCategoryRes[],
    currentCategory: ExtendedCategoryRes | null,
    isChecked: boolean
  ): void {
    let allServices: SuperGeneralSvRes[] = [];

    // Collect all services from displayCategories
    if (displayCategories.length > 0) {
      displayCategories.forEach(category => {
        allServices = [...allServices, ...category.services];
      });
    }
    // Or from currentCategory if it's the only one displayed
    else if (currentCategory) {
      allServices = currentCategory.services;
    }

    if (isChecked) {
      // Select all services
      allServices.forEach(service => {
        this.selectedServices.add(service.id);
      });
    } else {
      // Deselect all services
      this.selectedServices.clear();
    }
  }

  /**
   * Gets the selected services
   * @returns The selected services
   */
  getSelectedServices(): Set<number> {
    return this.selectedServices;
  }

  /**
   * Clears all selected services
   */
  clearSelectedServices(): void {
    this.selectedServices.clear();
  }
}
