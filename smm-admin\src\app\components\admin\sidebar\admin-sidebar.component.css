:host {
  @apply block h-full;
}

/* Menu section styling */
.menu-section {
  @apply mb-6;
}

.section-title {
  @apply text-gray-500 text-sm font-medium uppercase mb-4 px-3 tracking-wider;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  border-left: 3px solid #3b82f6;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0 4px 4px 0;
}

.menu-list {
  @apply list-none p-0 m-0 flex flex-col gap-2;
}

.menu-item {
  @apply flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-600;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  transition: width 0.3s ease;
  z-index: -1;
}

.menu-item:hover::before {
  width: 100%;
}

.menu-item.active {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 text-[var(--primary)] shadow-sm;
  transform: translateX(5px);
}

.menu-label {
  @apply text-sm font-medium transition-all duration-200;
}

.menu-label.active {
  @apply text-[var(--primary)] font-semibold;
}

.menu-item:hover {
  @apply text-[var(--primary)];
}

/* Logo styling */
.logo-container {
  @apply flex items-center p-4 mb-4;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6eeff 100%);
  border-radius: 0 0 1rem 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.logo {
  @apply h-16 w-auto object-contain;
}

/* Responsive styles */
@media (max-width: 768px) {
  .sidebar {
    @apply fixed top-0 left-0 z-50 w-72 h-full bg-white shadow-lg transform -translate-x-full transition-all duration-300;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  }

  .sidebar.open {
    @apply translate-x-0;
  }

  .sidebar > .flex-container {
    @apply p-0 h-full;
  }

  .logo {
    @apply h-7;
  }

  .menu-sidebar {
    @apply flex-1 overflow-y-auto px-4 py-2;
  }
}

@media (min-width: 769px) {
  /* Hide sidebar on desktop */
  .sidebar {
    @apply hidden;
  }

  /* Keep this for reference but it won't be used on desktop */
  .menu-sidebar {
    @apply flex flex-col w-64 min-w-[240px] rounded-2xl p-6 h-full bg-white shadow-sm duration-300;
  }
}
