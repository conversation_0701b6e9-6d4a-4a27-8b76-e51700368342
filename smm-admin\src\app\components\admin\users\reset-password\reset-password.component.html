<div class="overlay-black" >
  <div class="modal-container rounded-lg shadow-lg w-[380px] max-w-full">
    <!-- Header -->
    <div class="flex justify-between items-center p-5">
      <h2 class="text-xl font-medium text-gray-800">Change user password</h2>
      <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700">
        <fa-icon [icon]="['fas', 'xmark']"></fa-icon>
      </button>
    </div>

    <!-- Content -->
    <div class="px-5 pb-5">
      <!-- Loading state -->
      <div *ngIf="isLoading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="mb-4 p-4 bg-red-100 text-red-700 rounded-lg">
        {{ errorMessage }}
        <div class="mt-2">
          <button
            (click)="resetUserPassword()"
            class="text-sm text-blue-600 hover:underline"
          >
            Try again
          </button>
        </div>
      </div>

      <!-- Success state -->
      <div *ngIf="!isLoading && !errorMessage" class="space-y-5">
        <div class="mb-4">
          <div class="text-base font-normal text-gray-700 mb-2">Login</div>
          <div class="password-field">
            <input
              type="text"
              readonly
              [value]="userName"
              class="w-full px-4 py-3 bg-gray-100 border border-gray-300 rounded-lg text-gray-800 focus:outline-none"
            >
          </div>
        </div>

        <div>
          <div class="text-base font-normal text-gray-700 mb-2">Password</div>
          <div class="password-field">
            <input
              type="text"
              readonly
              [value]="newPassword"
              class="w-full px-4 py-3 bg-gray-100 border border-gray-300 rounded-lg text-gray-800 focus:outline-none"
            >
            <button (click)="copyToClipboard()" class="copy-button" title="Copy to clipboard">
              <fa-icon [icon]="['fas', 'copy']" class="text-gray-500"></fa-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
