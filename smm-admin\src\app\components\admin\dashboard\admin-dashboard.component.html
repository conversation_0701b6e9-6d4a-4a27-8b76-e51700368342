<div class="layout-container py-6 px-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Dashboard</h1>
    <div *ngIf="period" class="text-sm text-gray-600">
      Period: {{ period }}
    </div>
  </div>



  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div *ngFor="let card of statCards" class="bg-white rounded-lg p-6 border-sm border hover:shadow-md transition-shadow duration-300">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-700">{{ card.title }}</h2>
        <div class="h-12 w-12 flex items-center justify-center rounded-full" [ngClass]="{
          'bg-blue-100 text-blue-600': card.title === 'Total Orders',
          'bg-green-100 text-green-600': card.title === 'Total Revenue',
          'bg-purple-100 text-purple-600': card.title === 'Active Users',
          'bg-yellow-100 text-yellow-600': card.title === 'Pending Tickets'
        }">
          <fa-icon [icon]="['fas', card.icon]" class="text-xl"></fa-icon>
        </div>
      </div>
      <div class="flex items-end justify-between">
        <div>
          <p class="text-2xl font-bold text-gray-800">{{ card.value }}</p>
          <p class="flex items-center mt-2">
            <fa-icon
              [icon]="['fas', card.isIncrease ? 'arrow-up' : 'arrow-down']"
              [ngClass]="{'text-green-500': card.isIncrease, 'text-red-500': !card.isIncrease}"
              class="mr-1">
            </fa-icon>
            <span [ngClass]="{'text-green-500': card.isIncrease, 'text-red-500': !card.isIncrease}">
              {{ card.change }} since previous period
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Orders -->
  <!-- <div class="bg-white rounded-lg border-sm border p-6 mb-8 hover:shadow-md transition-shadow duration-300">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-bold text-gray-800">Recent Orders</h2>
      <a routerLink="/admin/orders" class="text-[#3b82f6] hover:underline flex items-center">
        <span>View All</span>
        <fa-icon [icon]="['fas', 'arrow-right']" class="ml-1 text-sm"></fa-icon>
      </a>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              ID
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Service
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Price
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr class="hover:bg-[#f0f7ff] transition-colors duration-150">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              1001
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              Vantung17308
            </td>
            <td class="px-6 py-4 text-sm text-gray-700">
              <div class="max-w-xs truncate">Instagram Followers</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              $20.00
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                Completed
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              2023-09-15
            </td>
          </tr>
          <tr class="hover:bg-[#f0f7ff] transition-colors duration-150">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              1002
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              User123
            </td>
            <td class="px-6 py-4 text-sm text-gray-700">
              <div class="max-w-xs truncate">YouTube Views</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              $45.00
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                In Progress
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              2023-09-14
            </td>
          </tr>
          <tr class="hover:bg-[#f0f7ff] transition-colors duration-150">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              1003
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              Customer456
            </td>
            <td class="px-6 py-4 text-sm text-gray-700">
              <div class="max-w-xs truncate">TikTok Likes</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              $30.00
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                Pending
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              2023-09-13
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div> -->

  <!-- Quick Stats -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Top Services -->
    <div class="bg-white rounded-lg border-sm border p-6 hover:shadow-md transition-shadow duration-300">
      <h2 class="text-xl font-bold text-gray-800 mb-6">Top Services</h2>

      <!-- No data message -->
      <div *ngIf="topServices.length === 0" class="text-center py-4 text-gray-500">
        No top services data available
      </div>

      <!-- Top services list -->
      <div class="space-y-4" *ngIf="topServices.length > 0">
        <div *ngFor="let service of topServices; let i = index"
             class="flex items-center justify-between p-2 hover:bg-[#f0f7ff] rounded-lg transition-colors duration-150">
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full mr-3"
                 [ngClass]="{
                   'bg-[#3b82f6]': i === 0,
                   'bg-blue-500': i === 1,
                   'bg-green-500': i === 2,
                   'bg-yellow-500': i === 3,
                   'bg-purple-500': i >= 4
                 }"></div>
            <span class="text-gray-700">{{ service.name }}</span>
          </div>
          <span class="text-gray-900 font-medium">{{ service.percentage.toFixed(1) }}%</span>
        </div>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white rounded-lg border-sm border p-6 hover:shadow-md transition-shadow duration-300">
      <h2 class="text-xl font-bold text-gray-800 mb-6">Recent Activities</h2>

      <!-- No data message -->
      <div *ngIf="latestActivities.length === 0" class="text-center py-4 text-gray-500">
        No recent activities available
      </div>

      <!-- Activities list with scrolling -->
      <div class="space-y-4 max-h-[350px] overflow-y-auto pr-2" *ngIf="latestActivities.length > 0">
        <div *ngFor="let activity of latestActivities"
             class="flex p-2 hover:bg-[#f0f7ff] rounded-lg transition-colors duration-150">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-10 w-10 rounded-full text-white"
                 [ngClass]="getActivityColor(activity.entity_name)">
              <fa-icon [icon]="['fas', getActivityIcon(activity.entity_name)]"></fa-icon>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-gray-900">{{ getActivityTitle(activity) }}</h3>
            <p class="text-sm text-gray-600">{{ getActivityDescription(activity) }}</p>
            <p class="text-xs text-gray-500 mt-1">{{ activity.username }} • {{ getTimeAgo(getActivityTimestamp(activity)) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
