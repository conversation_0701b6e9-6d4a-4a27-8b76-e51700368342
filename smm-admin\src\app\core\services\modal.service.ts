import { Injectable, ComponentRef, ApplicationRef, createComponent, EnvironmentInjector, Type } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modalComponentRef: ComponentRef<any> | null = null;

  constructor(
    private appRef: ApplicationRef,
    private injector: EnvironmentInjector
  ) {}

  /**
   * Open a modal component
   * @param component The component to open as a modal
   * @param props Properties to pass to the component
   * @returns The component reference
   */
  open<T extends object>(component: Type<T>, props: Partial<T> = {}): ComponentRef<T> {
    // Close any existing modal
    this.close();

    // Create a div that will host our modal
    const modalHost = document.createElement('div');
    modalHost.className = 'modal-host';
    document.body.appendChild(modalHost);

    // Create the component
    const componentRef = createComponent(component, {
      environmentInjector: this.injector,
      hostElement: modalHost
    });

    // Set properties on the component instance
    Object.assign(componentRef.instance, props);

    // Attach to the application
    this.appRef.attachView(componentRef.hostView);

    // Store the component reference
    this.modalComponentRef = componentRef;

    return componentRef;
  }

  /**
   * Close the currently open modal
   */
  close(): void {
    if (this.modalComponentRef) {
      // Detach the view
      this.appRef.detachView(this.modalComponentRef.hostView);

      // Destroy the component
      this.modalComponentRef.destroy();

      // Remove the host element
      const modalHost = document.querySelector('.modal-host');
      if (modalHost) {
        document.body.removeChild(modalHost);
      }

      this.modalComponentRef = null;
    }
  }
}
