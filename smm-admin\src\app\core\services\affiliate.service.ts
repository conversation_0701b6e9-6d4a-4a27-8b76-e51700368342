import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize, tap } from 'rxjs';
import { ReferralRes, ReferralStatsRes } from '../../model/response/referral-res.model';
import { PageResponse } from '../../model/response/page-response.model';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class AffiliateService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _referrals$ = new BehaviorSubject<ReferralRes[]>([]);
  private _stats$ = new BehaviorSubject<ReferralStatsRes | null>(null);
  private _affiliateLink$ = new BehaviorSubject<string>('');
  private _pagination$ = new BehaviorSubject<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  get referrals$(): Observable<ReferralRes[]> {
    return this._referrals$.asObservable();
  }

  get stats$(): Observable<ReferralStatsRes | null> {
    return this._stats$.asObservable();
  }

  get affiliateLink$(): Observable<string> {
    return this._affiliateLink$.asObservable();
  }

  get pagination$(): Observable<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }> {
    return this._pagination$.asObservable();
  }

  /**
   * Get current user's referrals
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Observable of paginated referral results
   */
  getMyReferrals(page: number = 0, size: number = 10): Observable<PageResponse<ReferralRes>> {
    this._loading$.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<PageResponse<ReferralRes>>(`${this.configService.apiUrl}/api/v1/users/me/referrals`, { params })
      .pipe(
        tap(response => {
          this._referrals$.next(response.content);
          this._pagination$.next({
            pageNumber: response.pageable.page_number,
            pageSize: response.pageable.page_size,
            totalElements: response.total_elements,
            totalPages: response.total_pages
          });
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Get affiliate statistics for current user
   * @returns Observable of affiliate statistics
   */
  getAffiliateStats(): Observable<ReferralStatsRes> {
    this._loading$.next(true);

    return this.http.get<ReferralStatsRes>(`${this.configService.apiUrl}/api/v1/users/me/referral-stats`)
      .pipe(
        tap(stats => {
          this._stats$.next(stats);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Get user's affiliate link
   * @returns Observable of affiliate link
   */
  getAffiliateLink(): Observable<{ referral_link: string }> {
    this._loading$.next(true);

    return this.http.get<{ referral_link: string }>(`${this.configService.apiUrl}/api/v1/users/me/referral-link`)
      .pipe(
        tap(response => {
          this._affiliateLink$.next(response.referral_link);
        }),
        finalize(() => this._loading$.next(false))
      );
  }
}
