import { Component, EventEmitter, Output, HostListener, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastService } from '../../../core/services/toast.service';
import { NotifyType } from '../../../constant/notify-type';
import { IconsModule } from '../../../icons/icons.module';
import { MfaService, MfaDisableRequest } from '../../../core/services/mfa.service';

@Component({
  selector: 'app-mfa-disabled',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconsModule
  ],
  templateUrl: './mfa-disabled.component.html',
  styleUrl: './mfa-disabled.component.css'
})
export class MfaDisabledComponent {
  @Output() close = new EventEmitter<void>();
  @Output() disabled = new EventEmitter<void>();

  disableForm: FormGroup;
  isLoading = false;
  showPassword = false;

  constructor(
    private fb: FormBuilder,
    private elementRef: ElementRef,
    private mfaService: MfaService,
    private toastService: ToastService
  ) {
    this.disableForm = this.fb.group({
      password: ['', [Validators.required]]
    });
  }

  /**
   * Disable MFA
   */
  onSubmit(): void {
    if (this.disableForm.invalid) {
      // Mark all form controls as touched to show validation errors
      Object.keys(this.disableForm.controls).forEach(key => {
        const control = this.disableForm.get(key);
        if (control) {
          control.markAsTouched();
        }
      });
      return;
    }

    this.isLoading = true;

    const disableData: MfaDisableRequest = {
      password: this.disableForm.value.password
    };

    this.mfaService.disableMfa(disableData)
      .subscribe({
        next: () => {
          this.toastService.showToast('Two-factor authentication disabled successfully!', NotifyType.SUCCESS);
          this.isLoading = false;
          this.disabled.emit();
          this.onClose();
        },
        error: (error: any) => {
          console.error('Error disabling MFA:', error);
          this.toastService.showToast('Failed to disable MFA. Please check your password and try again.', NotifyType.ERROR);
          this.isLoading = false;
        }
      });
  }

  /**
   * Close the popup when clicking outside
   */
  // @HostListener('document:click', ['$event'])
  // onOverlayClick(event: MouseEvent): void {
  //   console.log('MFA Disabled - onOverlayClick called');
  //   const clickedInside = this.elementRef.nativeElement.contains(event.target);
  //   console.log('Clicked inside:', clickedInside);
  //   if (!clickedInside) {
  //     this.onClose();
  //   }
  // }

  /**
   * Prevent event propagation when clicking inside the popup
   */
  onPopupClick(event: MouseEvent): void {
    event.stopPropagation();
  }

  /**
   * Close the popup
   */
  onClose(): void {
    console.log('MFA Disabled - onClose called');
    this.close.emit();
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
