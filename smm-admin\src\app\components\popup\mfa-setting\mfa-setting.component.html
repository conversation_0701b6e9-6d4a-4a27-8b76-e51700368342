<div class="overlay-black" >
  <div class="modal-container" (click)="onPopupClick($event)">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'auth.google_authentication_setting' | translate }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <h3 class="section-title">{{ 'auth.get_google_authenticator' | translate }}</h3>

        <div class="app-download-section">
          <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank" class="app-download-button android">
            <i class="fab fa-android"></i>
            <span>{{ 'auth.get_on_android' | translate }}</span>
          </a>
          <a href="https://apps.apple.com/us/app/google-authenticator/id388497605" target="_blank" class="app-download-button ios">
            <i class="fab fa-apple"></i>
            <span>{{ 'auth.get_on_ios' | translate }}</span>
          </a>
        </div>

        <p class="instruction-text">{{ 'auth.scan_qr_code_instruction' | translate }}</p>

        <div class="qr-code-container">
          <div *ngIf="isLoading" class="loading-spinner">
            <div class="spinner"></div>
          </div>
          <img *ngIf="!isLoading && qrCodeImage" [src]="qrCodeImage" alt="QR Code" class="qr-code-image">
        </div>

        <div class="manual-entry-section">
          <div class="alert-box">
            <div class="alert-icon">!</div>
            <div class="alert-content">
              <p>{{ 'auth.manual_entry_instruction' | translate }}</p>
              <p class="secret-key">{{ secretKey }}</p>
            </div>
          </div>
        </div>

        <form [formGroup]="mfaForm" (ngSubmit)="onSubmit()" class="mfa-form">
          <div class="form-group">
            <label for="code" class="form-label">{{ 'auth.enter_authentication_code' | translate }}</label>
            <input
              type="text"
              id="code"
              formControlName="code"
              class="form-input"
              placeholder="000000"
              maxlength="6"
              [ngClass]="{'input-error': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}"
            >
            <div *ngIf="mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched" class="error-message">
              <div *ngIf="mfaForm.get('code')?.errors?.['required']">{{ 'auth.code_required' | translate }}</div>
              <div *ngIf="mfaForm.get('code')?.errors?.['pattern'] || mfaForm.get('code')?.errors?.['minlength'] || mfaForm.get('code')?.errors?.['maxlength']">
                {{ 'auth.code_must_be_6_digits' | translate }}
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">{{ 'auth.password' | translate }}</label>
            <div class="password-input-container">
              <input
                [type]="showPassword ? 'text' : 'password'"
                id="password"
                formControlName="password"
                class="form-input"
                placeholder="{{ 'auth.enter_password' | translate }}"
                [ngClass]="{'input-error': mfaForm.get('password')?.invalid && mfaForm.get('password')?.touched}"
              >
              <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
                <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
            <div *ngIf="mfaForm.get('password')?.invalid && mfaForm.get('password')?.touched" class="error-message">
              <div *ngIf="mfaForm.get('password')?.errors?.['required']">{{ 'auth.password_required' | translate }}</div>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="submit-button" [disabled]="mfaForm.invalid || isLoading">
              <span *ngIf="!isLoading">{{ 'auth.enable' | translate }}</span>
              <span *ngIf="isLoading" class="loading-spinner-small"></span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
