import { Component, EventEmitter, Input, Output, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { LiteDropdownComponent } from '../lite-dropdown/lite-dropdown.component';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-ticket-status-select',
  standalone: true,
  imports: [CommonModule, FormsModule, LiteDropdownComponent, IconsModule],
  templateUrl: './ticket-status-select.component.html',
  styleUrl: './ticket-status-select.component.css'
})
export class TicketStatusSelectComponent implements OnInit, OnChanges {
  @Input() status: string = '';
  @Input() loading: boolean = false;
  @Output() statusChange = new EventEmitter<string>();
  @Output() updateStatus = new EventEmitter<void>();

  statusOptions = ['Pending', 'Accept', 'Closed', 'Solved'];
  currentStatus: string | null = null;

  ngOnInit(): void {
    // Initialize currentStatus with the input status if it's valid
    if (this.status && this.statusOptions.includes(this.status)) {
      this.currentStatus = this.status;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update currentStatus when the input status changes
    if (changes['status'] && changes['status'].currentValue) {
      const newStatus = changes['status'].currentValue;
      if (this.statusOptions.includes(newStatus)) {
        this.currentStatus = newStatus;
      }
    }
  }

  onStatusChange(selectedStatus: string): void {
    this.currentStatus = selectedStatus;
    this.statusChange.emit(this.currentStatus);
  }

  onUpdateClick(): void {
    this.updateStatus.emit();
  }
}
