import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize } from 'rxjs';
import { ConfigService } from './config.service';
import { VoucherRes } from '../../model/response/voucher-res.model';
import { VoucherReq } from '../../model/request/voucher-req.model';
import { VoucherLiteRes } from '../../model/response/voucher-lite-res.model';

@Injectable({
  providedIn: 'root'
})
export class VoucherService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _vouchers$ = new BehaviorSubject<VoucherRes[]>([]);

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get vouchers$(): BehaviorSubject<VoucherRes[]> {
    return this._vouchers$;
  }

  /**
   * Get all vouchers
   */
  getAllVouchers(): Observable<VoucherRes[]> {
    this._loading$.next(true);
    return this.http
      .get<VoucherRes[]>(`${this.configService.apiUrl}/vouchers`)
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Create a new voucher
   * @param voucher The voucher data to create
   */
  createVoucher(voucher: VoucherReq): Observable<VoucherRes> {
    this._loading$.next(true);
    return this.http
      .post<VoucherRes>(`${this.configService.apiUrl}/vouchers`, voucher)
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Delete a voucher
   * @param id The voucher ID to delete
   */
  deleteVoucher(id: number): Observable<any> {
    this._loading$.next(true);
    return this.http
      .delete<any>(`${this.configService.apiUrl}/vouchers/${id}`)
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Generate a random voucher code
   * Format: XXXX-XXXX-XXXX (where X is alphanumeric)
   */
  generateRandomCode(): string {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';

    // Generate first part
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    result += '-';

    // Generate second part
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    result += '-';

    // Generate third part
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  /**
   * Verify a voucher code for an order
   * @param code The voucher code to verify
   * @returns Observable with voucher validation response
   */
  verifyVoucherCode(code: string): Observable<VoucherLiteRes> {
    this._loading$.next(true);
    return this.http
      .get<VoucherLiteRes>(`${this.configService.apiUrl}/vouchers/${code}/order/validate`)
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }
}
