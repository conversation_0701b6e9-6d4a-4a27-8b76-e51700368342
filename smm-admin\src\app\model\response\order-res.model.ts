import { GeneralSvRes } from "./general-sv.model"
import { OrderStatus } from "../../constant/order-status"
import { OrderTag } from "../../constant/order-tag"
import { ProviderRes } from "./provider-res.model"
import { UserRes } from "./user-res.model"

export interface OrderRes {
  id: number
  type: string
  category_id: number
  service: GeneralSvRes
  api_provider: ProviderRes,
  user: UserRes,
  api_service_id: string
  api_order_id: string
  link: string
  quantity: number
  charge: number
  actual_charge: number
  start_count: number
  remains: number
  status: OrderStatus
  tag?: OrderTag
  note?: string
  created_at: string
  updated_at: string
  checked: boolean
  loading: boolean
}
