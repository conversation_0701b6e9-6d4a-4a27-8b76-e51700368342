import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
@Component({
  selector: 'app-success-order',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './success-order.component.html',
  styleUrl: './success-order.component.css'
})
export class SuccessOrderComponent {
  @Input() orderId: string = '1059205560';
  @Input() service: string = '4000h xem Youtube | Yêu cầu video 7-30 phút | Lượt xem thật | 12k lượt xem = 1000 giờ xem - $0.981';
  @Input() link: string = 'https://www.youtube.com/watch?v=RlqyufEXJhQ';
  @Input() quantity: number = 1000;
  @Input() fee: number = 0.981;
  @Input() balance: number = 0.999;

  onConfirm() {
    // Handle confirmation logic
    console.log('Order confirmed');
  }
  
  onOverlayClick(event: MouseEvent) {
    // <PERSON><PERSON><PERSON> tra nếu click vào ch<PERSON>h overlay, không phải bên trong modal
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }
  @Output() close = new EventEmitter<void>();

  closeModal() {
    this.close.emit();
  }
}
