<div class="page-container">
  <div class="content-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="flex items-center gap-3">
        <button class="back-button" (click)="goBack()">
          <fa-icon [icon]="['fas', 'arrow-left']"></fa-icon>
        </button>
        <h1 class="page-title">{{ pageTitle | translate }}</h1>
      </div>
    </div>

    <!-- Form Container -->
    <div class="form-container">
      <!-- Form -->
      <form [formGroup]="notificationForm" (ngSubmit)="onSubmit()" class="notification-form">


        <!-- Title - Hidden for Fixed type -->
        <div class="form-group" *ngIf="notificationForm.get('type')?.value !== 'Fixed'">
          <label class="form-label required">{{ 'Title' | translate }}</label>
          <input
            type="text"
            class="form-input"
            formControlName="title"
            [class.input-error]="notificationForm.get('title')?.invalid && notificationForm.get('title')?.touched"
            placeholder="{{ 'Enter notification title' | translate }}">
          <div *ngIf="notificationForm.get('title')?.invalid && notificationForm.get('title')?.touched" class="validation-error">
            {{ 'Title is required' | translate }}
          </div>
        </div>

        <!-- Content -->
        <div class="form-group">
          <div class="content-header">
            <label class="form-label">{{ 'Content' | translate }}</label>
            <div class="html-toggle-container">
              <label class="html-toggle-label">HTML</label>
              <div class="toggle-switch small">
                <input type="checkbox" id="htmlMode" [checked]="htmlMode" (change)="toggleHtmlMode()">
                <label for="htmlMode" class="toggle-slider"></label>
              </div>
            </div>
          </div>
          <div class="quill-container" *ngIf="!htmlMode"
            [class.quill-error]="notificationForm.get('content')?.invalid && notificationForm.get('content')?.touched">
            <!-- Only render Quill editor in browser environment -->
            <quill-editor *ngIf="isBrowser"
              class="quill-editor-full-width"
              formControlName="content"
              [modules]="quillModules"
              [placeholder]="'Enter notification content' | translate"
              [styles]="{height: '200px', width: '100%', display: 'block'}"
              style="width: 100%; display: block;"
              [class.input-error]="notificationForm.get('content')?.invalid && notificationForm.get('content')?.touched">
            </quill-editor>
            <!-- Fallback for server-side rendering -->
            <textarea *ngIf="!isBrowser"
              class="form-input content-textarea"
              formControlName="content"
              placeholder="{{ 'Enter notification content' | translate }}">
            </textarea>
          </div>
          <!-- HTML Mode Textarea -->
          <div class="html-container" *ngIf="htmlMode">
            <textarea
              class="form-input content-textarea html-textarea"
              formControlName="content"
              placeholder="{{ 'Enter HTML content' | translate }}"
              [class.input-error]="notificationForm.get('content')?.invalid && notificationForm.get('content')?.touched">
            </textarea>
          </div>
          <div *ngIf="notificationForm.get('content')?.invalid && notificationForm.get('content')?.touched" class="validation-error">
            {{ 'Content is required' | translate }}
          </div>
        </div>

        <!-- Auto dismiss after hours - Only for Popup type -->
        <div class="form-group" *ngIf="showFullForm && notificationForm.get('type')?.value === 'Popup'">
          <div class="auto-dismiss-container">
            <div class="toggle-container">
              <label class="toggle-label">{{ 'Auto dismiss notification' | translate }}</label>
              <div class="toggle-switch">
                <input type="checkbox" formControlName="autoDismiss" id="autoDismiss">
                <label for="autoDismiss" class="toggle-slider"></label>
              </div>
            </div>
            <p class="help-text">{{ 'When enabled, notification will be automatically dismissed after specified hours' | translate }}</p>

            <!-- Hours input field - shown when toggle is enabled -->
            <div class="hours-input-container" *ngIf="notificationForm.get('autoDismiss')?.value">
              <label class="hours-label">{{ 'Turn off after' | translate }}</label>
              <div class="hours-input-wrapper">
                <input
                  type="number"
                  class="hours-input"
                  formControlName="dismissHours"
                  min="1"
                  max="168"
                  placeholder="24">
                <span class="hours-unit">{{ 'hours' | translate }}</span>
              </div>
            </div>
          </div>
        </div>



        <!-- Expiry Options - Only shown in full form -->
        <div class="form-group" *ngIf="showFullForm">
          <label class="form-label">{{ 'Notification Expiry' | translate }}</label>
          <div class="expiry-options">
            <label class="radio-option" [class.active]="notificationForm.get('expiryOption')?.value === 'never'">
              <input type="radio" id="never" value="never" formControlName="expiryOption">
              <span class="radio-checkmark"></span>
              <div class="flex flex-col">
                <span class="radio-label">{{ 'Never' | translate }}</span>
                <span class="radio-description">{{ 'Notification will not expire automatically' | translate }}</span>
              </div>
              <fa-icon class="radio-icon" [icon]="['fas', 'check']"></fa-icon>
            </label>
            <label class="radio-option" [class.active]="notificationForm.get('expiryOption')?.value === 'period'">
              <input type="radio" id="period" value="period" formControlName="expiryOption">
              <span class="radio-checkmark"></span>
              <div class="flex flex-col">
                <span class="radio-label">{{ 'After some time' | translate }}</span>
                <span class="radio-description">{{ 'Notification will expire after a specified time period' | translate }}</span>
              </div>
              <fa-icon class="radio-icon" [icon]="['fas', 'check']"></fa-icon>
            </label>
            <label class="radio-option" [class.active]="notificationForm.get('expiryOption')?.value === 'date'">
              <input type="radio" id="date" value="date" formControlName="expiryOption">
              <span class="radio-checkmark"></span>
              <div class="flex flex-col">
                <span class="radio-label">{{ 'Specific date' | translate }}</span>
                <span class="radio-description">{{ 'Notification will expire on a specific date and time' | translate }}</span>
              </div>
              <fa-icon class="radio-icon" [icon]="['fas', 'check']"></fa-icon>
            </label>
          </div>

          <!-- Time Period input (shown when "After some time" is selected) -->
          <div *ngIf="notificationForm.get('expiryOption')?.value === 'period'" class="time-period-input">
            <div class="light-theme-container">
              <input
                type="number"
                class="form-input light-theme-input"
                formControlName="timePeriod"
                min="1"
                (change)="onTimePeriodChange()">
              <span class="time-period-label">{{ 'hours' | translate }}</span>
            </div>
            <div class="calculated-date" *ngIf="calculatedEndDate">
              <fa-icon [icon]="['fas', 'calendar-check']"></fa-icon>
              {{ 'Expires on' | translate }}: {{ calculatedEndDate | date:'yyyy-MM-dd HH:mm' }}
            </div>
          </div>

          <!-- Date/Time inputs (shown conditionally) -->
          <div *ngIf="notificationForm.get('expiryOption')?.value === 'date'" class="date-time-inputs">
            <div class="date-time-container">
              <span class="date-time-label">{{ 'Date' | translate }}</span>
              <input
                type="date"
                class="date-input"
                formControlName="expiryDate">
            </div>
            <div class="date-time-container">
              <span class="date-time-label">{{ 'Time' | translate }}</span>
              <input
                type="time"
                class="time-input"
                formControlName="expiryTime">
            </div>
          </div>
        </div>

        <!-- Preview Button - Only in browser and for content/preview -->
        <div *ngIf="isBrowser && (notificationForm.get('type')?.value === 'Fixed' || showFullForm)" class="preview-toggle">
          <button type="button" class="preview-button" (click)="togglePreview()">
            <fa-icon [icon]="['fas', 'eye']"></fa-icon>
            {{ (showPreview ? 'Hide Preview' : 'Show Preview') | translate }}
          </button>
        </div>

        <!-- Preview Section - Only in browser and for content/preview -->
        <div *ngIf="isBrowser && showPreview && (notificationForm.get('type')?.value === 'Fixed' || showFullForm)" class="preview-section">
          <h3 class="preview-title">{{ 'Preview' | translate }}</h3>

            <!-- Show popup content for Popup type -->
            <app-notification-popup-content
              *ngIf="notificationForm.get('type')?.value === 'Popup'"
              [content]="getSanitizedContent()"
              [title]="notificationForm.get('title')?.value || 'Notification Title'"
              [autoDismiss]="notificationForm.get('autoDismiss')?.value"
              [dismissHours]="notificationForm.get('dismissHours')?.value">
            </app-notification-popup-content>

            <!-- Show fixed notification for Fixed type -->
            <app-notification-home
              *ngIf="notificationForm.get('type')?.value === 'Fixed'"
              [content]="getSanitizedContent()">
            </app-notification-home>

        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          class="save-button"
          [disabled]="isSubmitting">
          <span *ngIf="!isSubmitting">{{ submitButtonText | translate }}</span>
          <span *ngIf="isSubmitting">
            <fa-icon [icon]="['fas', 'spinner']" [spin]="true"></fa-icon>
            {{ (isEditMode ? 'Updating...' : 'Creating...') | translate }}
          </span>
        </button>
      </form>
    </div>
  </div>
</div>
