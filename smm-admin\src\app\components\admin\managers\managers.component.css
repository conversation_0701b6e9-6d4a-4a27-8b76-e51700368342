/* Admin container */
.admin-container {
  @apply w-full bg-white rounded-lg md:p-6 p-2;
}

/* Admin header */
.admin-header {
  @apply flex justify-between items-center mb-6;
}

.admin-title {
  @apply text-xl font-bold uppercase text-gray-800;
}

/* Search and filter container */
.search-filter-container {
  @apply mb-6;
}

/* Search input */
.search-input-wrapper {
  @apply relative flex items-center w-full;
}

.search-input {
  @apply w-full h-[52px] px-4 py-2 bg-[#f5f7fc] rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.search-button {
  @apply absolute right-2 bg-[var(--primary)] text-white p-2 rounded-md hover:bg-[var(--primary-hover)] transition-colors;
}

/* Table styling */
table {
  @apply w-full text-sm text-left;
}

thead {
  @apply bg-gray-50;
}

th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

tbody {
  @apply bg-white divide-y divide-gray-200;
}

tr {
  @apply hover:bg-gray-50 transition-colors;
}

td {
  @apply px-6 py-4 whitespace-nowrap text-sm;
}

/* Status badges */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

/* Role badges */
.role-badge {
  @apply px-2.5 py-0.5 text-xs font-medium rounded-full;
}

/* Panel tags */
.panel-tag {
  @apply inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700;
}

/* Card styling for mobile */
.manager-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:border-[var(--primary)] transition-colors duration-150;
}

.card-header {
  @apply flex justify-between items-center p-4 border-b border-gray-100;
}

.card-content {
  @apply p-4 space-y-3;
}

/* Button styling */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2;
}

.btn-primary {
  @apply bg-[var(--primary)] text-white hover:bg-[var(--primary-hover)] hover:shadow-lg hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-white text-[var(--primary)] border border-[var(--primary)] hover:bg-gray-50;
}

/* Empty state styling */
.empty-state {
  @apply text-center py-12;
}

.empty-state-icon {
  @apply text-gray-400 text-4xl mb-4;
}

.empty-state-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.empty-state-description {
  @apply text-gray-500 mb-4;
}

/* Loading state */
.loading-container {
  @apply flex items-center justify-center py-12;
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-container {
    @apply p-4;
  }
  
  .admin-header {
    @apply flex-col gap-4 items-start;
  }
  
  .search-filter-container {
    @apply space-y-4;
  }
}

/* Animation for cards */
.manager-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.manager-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Focus states */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:focus {
  @apply outline-none ring-2 ring-[var(--primary)] border-[var(--primary)];
}

/* Custom scrollbar for mobile cards */
@media (max-width: 768px) {
  .space-y-4 {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }
  
  .space-y-4::-webkit-scrollbar {
    width: 4px;
  }
  
  .space-y-4::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }
  
  .space-y-4::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 2px;
  }
}
