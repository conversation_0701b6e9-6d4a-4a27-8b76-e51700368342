<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'Order editing' }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Link Input -->
      <div class="form-group">
        <label for="link" class="form-label">{{ 'New link' }}</label>
        <input
          type="text"
          id="link"
          [(ngModel)]="link"
          class="form-input"
          placeholder="https://example.com/order"
        >
      </div>

      <!-- Save Button -->
      <button
        (click)="saveLink()"
        class="save-button"
        [disabled]="!link || !link.trim()"
      >
        {{ 'Save' }}
      </button>
    </div>
  </div>
</div>
