<div class="overlay-black" (click)="onOverlayClick($event)">
  <div class="modal-container">
    <div class="modal-content">
      <!-- Back Button -->
      <button type="button" class="back-button" (click)="onBack()">
        Back
      </button>

      <!-- Icon -->
      <div class="icon-container">
        <div class="icon-circle">
          <fa-icon [icon]="['fas', 'puzzle-piece']" class="puzzle-icon"></fa-icon>
        </div>
      </div>

      <!-- Header -->
      <div class="header-section">
        <h1 class="title">Tie domain</h1>
        <p class="description">
          For the domain to work properly<br>
          you need to add Socpanel DNS servers.
        </p>
      </div>

      <!-- Steps -->
      <div class="steps-container">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-text">Go to the domain settings in domain service cabinet</div>
        </div>
        
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-text">Find your DNS servers settings</div>
        </div>
        
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-text">Enter both DNS servers listed below</div>
        </div>

        <!-- DNS Servers -->
        <div class="dns-servers">
          <div class="dns-server" *ngFor="let server of dnsServers">
            <span>{{ server }}</span>
            <button class="copy-button" (click)="copyToClipboard(server)">
              <fa-icon [icon]="['fas', 'copy']"></fa-icon>
            </button>
          </div>
        </div>

        <div class="step">
          <div class="step-number">4</div>
          <div class="step-text">You entered DNS into the domain?</div>
        </div>
      </div>

      <!-- Action Button -->
      <button type="button" class="next-button" (click)="onNext()">
        Yes
      </button>
    </div>
  </div>
</div>
