import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-toggle-switch',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './toggle-switch.component.html',
  styleUrl: './toggle-switch.component.css'
})
export class ToggleSwitchComponent {
  @Input() isChecked: boolean = false;
  @Input() label: string = '';
  @Output() toggled = new EventEmitter<boolean>();
  @Input() circleColor: string = '#B6BCCC';
  @Input() toggledBgColor: string = 'var(--primary)';
  @Input() disabled: boolean = false;

  onToggle() {
    if (this.disabled) return;

    this.isChecked = !this.isChecked;
    this.toggled.emit(this.isChecked);
  }
}
