export interface LogoFileReq {
  url: string;
  file_type: string;
}

export interface ColorSchemeReq {
  primary: string;
  text_on_buttons: string;
}

export interface LandingSettingsReq {
  type: string;
}

export interface HeaderSettingsReq {
  type: 'standard' | 'compact' | 'modern' | 'minimal';
}

export interface SidebarSettingsReq {
  type: 'standard' | 'compact' | 'modern' | 'minimal' | 'card';
}

export interface DesignSettingsReq {
  id?: string;

  // Logo & Favicon
  logo?: LogoFileReq;
  favicon?: LogoFileReq;

  // Color scheme
  color_scheme?: ColorSchemeReq;

  // Layout settings
  landing_settings?: LandingSettingsReq;
  header_settings?: HeaderSettingsReq;
  sidebar_settings?: SidebarSettingsReq;
}
