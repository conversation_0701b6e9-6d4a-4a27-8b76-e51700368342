package tndung.vnfb.smm.anotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that require tenant configuration validation.
 * When applied to a method, the TenantCheckAspect will verify that a tenant
 * is properly configured in the TenantContext before method execution.
 * 
 * If no tenant is configured, an InvalidParameterException with 
 * PANEL_NOT_CONFIGURED error code will be thrown.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantCheck {
}
