<div class="overlay-black" (click)="onOverlayClick($event)">
  <div class="modal-container bg-white text-gray-800 rounded-2xl shadow-lg w-[800px] max-w-full max-h-[90vh] overflow-hidden">
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
      <div>
        <h2 class="text-xl font-semibold">Payment History</h2>
        <p class="text-sm text-gray-500 mt-1">{{ userName }}</p>
      </div>
      <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
        <fa-icon [icon]="['fas', 'times']" class="text-lg" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Content -->
    <div class="flex flex-col h-[600px]">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <fa-icon [icon]="['fas', 'spinner']" class="text-3xl text-blue-500 animate-spin mb-4"></fa-icon>
          <p class="text-gray-600">Loading payment history...</p>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && transactions.length === 0" class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <fa-icon [icon]="['fas', 'search']" class="text-6xl text-gray-300 mb-4"></fa-icon>
          <h3 class="text-lg font-medium text-gray-800 mb-2">No payment history found</h3>
          <p class="text-gray-600">This user has no transaction records yet.</p>
        </div>
      </div>

      <!-- Transactions Table -->
      <div *ngIf="!isLoading && transactions.length > 0" class="flex-1 overflow-hidden flex flex-col">
        <div class="flex-1 overflow-y-auto">
          <table class="w-full">
            <thead class="bg-gray-50 sticky top-0">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Balance
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Note
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let transaction of transactions" class="hover:bg-gray-50 transition-colors">
                <!-- Type -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3"
                         [ngClass]="getTransactionTypeClass(transaction.type)">
                      <fa-icon [icon]="['fas', getTransactionIcon(transaction.type)]" class="text-sm"></fa-icon>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ transaction.type }}</div>
                      <div class="text-sm text-gray-500">{{ transaction.source }}</div>
                    </div>
                  </div>
                </td>

                <!-- Amount -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="text-sm font-medium"
                        [ngClass]="transaction.change > 0 ? 'text-green-600' : 'text-red-600'">
                    {{ getFormattedAmount(transaction) }}
                  </span>
                </td>

                <!-- Balance -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="text-sm text-gray-900">
                    ${{ formatBalance(transaction.balance) }}
                  </span>
                </td>

                <!-- Note -->
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900 max-w-xs truncate" [title]="transaction.note">
                    {{ transaction.note || '-' }}
                  </div>
                  <div *ngIf="transaction.order_id" class="text-xs text-gray-500 mt-1">
                    Order #{{ transaction.order_id }}
                  </div>
                </td>

                <!-- Date -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(transaction.created_at) }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="border-t border-gray-200 bg-white px-6 py-3">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Showing {{ (pagination.pageNumber * pagination.pageSize) + 1 }} to
              {{ Math.min((pagination.pageNumber + 1) * pagination.pageSize, pagination.totalElements) }}
              of {{ pagination.totalElements }} results
            </div>
            <div class="flex items-center space-x-2">
              <button
                (click)="prevPage()"
                [disabled]="pagination.pageNumber === 0"
                class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                Previous
              </button>
              <span class="text-sm text-gray-700">
                Page {{ pagination.pageNumber + 1 }} of {{ pagination.totalPages }}
              </span>
              <button
                (click)="nextPage()"
                [disabled]="pagination.pageNumber >= pagination.totalPages - 1"
                class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
