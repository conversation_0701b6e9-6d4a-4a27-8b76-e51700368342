<div class="landing-page bg-white text-gray-800">
  <!-- Header -->
  <header class="landing-header bg-white shadow-sm">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center">
      <div class="logo-container flex items-center">
        <span class="text-xl font-bold text-blue-600">NEWPANEL</span>
      </div>
      <nav class="hidden md:flex space-x-6">
        <a href="#services" class="text-gray-700 hover:text-blue-600 transition-colors">{{ 'landing.header.services' | translate }}</a>
        <a href="#features" class="text-gray-700 hover:text-blue-600 transition-colors">{{ 'landing.header.features' | translate }}</a>
        <a href="#pricing" class="text-gray-700 hover:text-blue-600 transition-colors">{{ 'landing.header.pricing' | translate }}</a>
      </nav>
      <div class="flex items-center space-x-3">
        <div class="relative mr-2">
          <button (click)="toggleLanguageDropdown()" class="flex items-center px-2 py-1 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
            <span [class]="getFlagClass(currentLanguage)" class="w-4 h-3 mr-1 rounded"></span>
            <span class="mr-1">{{ currentLanguage.toUpperCase() }}</span>
            <fa-icon [icon]="['fas', 'chevron-down']" class="text-xs"></fa-icon>
          </button>
          <div *ngIf="showLanguageDropdown" class="absolute right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-md z-50">
            <button
              *ngFor="let lang of languages"
              (click)="changeLanguage(lang.code)"
              class="flex items-center w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors"
              [class.font-bold]="currentLanguage === lang.code">
              <span [class]="lang.flag" class="w-4 h-3 mr-2 rounded"></span>
              {{ lang.name }}
            </button>
          </div>
        </div>
        <button (click)="navigateToLogin()" class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
          {{ 'landing.header.login' | translate }}
        </button>
        <button (click)="navigateToRegister()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          {{ 'landing.header.signup' | translate }}
        </button>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero-section bg-gradient-to-r from-blue-50 to-indigo-50 py-16 md:py-24">
    <div class="container mx-auto px-4 flex flex-col md:flex-row items-center">
      <div class="md:w-1/2 mb-10 md:mb-0">
        <h1 class="text-4xl md:text-5xl font-bold mb-4 text-gray-900">
          {{ 'landing.hero.title' | translate }}
        </h1>
        <p class="text-xl text-gray-700 mb-8">
          {{ 'landing.hero.subtitle' | translate }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4">
          <button (click)="navigateToRegister()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium">
            {{ 'landing.hero.signup_button' | translate }}
          </button>
          <button (click)="navigateToDashboard()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors text-lg font-medium">
            {{ 'landing.hero.dashboard_button' | translate }}
          </button>
        </div>
      </div>
      <div class="md:w-1/2 flex justify-center">
        <div class="relative w-full max-w-md">
          <div class="bg-white rounded-xl shadow-xl p-6 relative z-10">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                <fa-icon [icon]="['fas', 'users']"></fa-icon>
              </div>
              <div>
                <p class="text-sm text-gray-500">{{ 'landing.stats.happy_customers' | translate }}</p>
              </div>
            </div>
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mr-3">
                <fa-icon [icon]="['fas', 'chart-line']"></fa-icon>
              </div>
              <div>
                <p class="text-sm text-gray-500">{{ 'landing.stats.completed_orders' | translate }}</p>
              </div>
            </div>
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
                <fa-icon [icon]="['fas', 'star']"></fa-icon>
              </div>
              <div>
                <p class="text-sm text-gray-500">{{ 'landing.stats.quality_services' | translate }}</p>
              </div>
            </div>
          </div>
          <div class="absolute -bottom-4 -right-4 w-full h-full bg-blue-100 rounded-xl z-0"></div>
        </div>
      </div>
    </div>
  </section>

  <!-- Social Media Platforms -->
  <section id="services" class="py-16 bg-white">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-3xl md:text-4xl font-bold mb-3 text-gray-900">{{ 'landing.services.title' | translate }}</h2>
      <p class="text-xl text-gray-600 mb-12">{{ 'landing.services.subtitle' | translate }}</p>

      <div class="relative">
        <div class="social-icons-container flex flex-wrap justify-center gap-6 mb-10">
          <div *ngFor="let platform of socialPlatforms" class="social-icon-wrapper">
            <div class="w-14 h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center shadow-md transform hover:scale-110 transition-transform"
                [ngClass]="{
                  'bg-blue-500': platform.icon === 'facebook',
                  'bg-gradient-to-r from-purple-500 to-pink-500': platform.icon === 'instagram',
                  'bg-blue-400': platform.icon === 'twitter',
                  'bg-red-600': platform.icon === 'youtube',
                  'bg-blue-600': platform.icon === 'telegram-plane'
                }">
              <app-social-icon [icon]="platform.icon" zClass="text-2xl text-white"></app-social-icon>
            </div>
          </div>
        </div>
      </div>

      <button (click)="navigateToServices()" class="mt-8 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium">
        {{ 'landing.services.view_all' | translate }}
      </button>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-3xl md:text-4xl font-bold mb-3 text-gray-900">{{ 'landing.features.title' | translate }}</h2>
      <p class="text-xl text-gray-600 mb-12">{{ 'landing.features.subtitle' | translate }}</p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div class="bg-white p-8 rounded-xl shadow-md text-left">
          <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-4">
            <fa-icon [icon]="['fas', 'chart-line']"></fa-icon>
          </div>
          <h3 class="text-xl font-bold mb-2">{{ 'landing.features.stats' | translate }}</h3>
          <p class="text-gray-600">{{ 'landing.features.stats_desc' | translate }}</p>
        </div>

        <div class="bg-white p-8 rounded-xl shadow-md text-left">
          <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-4">
            <fa-icon [icon]="['fas', 'shield']"></fa-icon>
          </div>
          <h3 class="text-xl font-bold mb-2">{{ 'landing.features.payments' | translate }}</h3>
          <p class="text-gray-600">{{ 'landing.features.payments_desc' | translate }}</p>
        </div>

        <div class="bg-white p-8 rounded-xl shadow-md text-left">
          <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-4">
            <fa-icon [icon]="['fas', 'code']"></fa-icon>
          </div>
          <h3 class="text-xl font-bold mb-2">{{ 'landing.features.api' | translate }}</h3>
          <p class="text-gray-600">{{ 'landing.features.api_desc' | translate }}</p>
        </div>

        <div class="bg-white p-8 rounded-xl shadow-md text-left">
          <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center text-red-600 mb-4">
            <fa-icon [icon]="['fas', 'phone']"></fa-icon>
          </div>
          <h3 class="text-xl font-bold mb-2">{{ 'landing.features.support' | translate }}</h3>
          <p class="text-gray-600">{{ 'landing.features.support_desc' | translate }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Service Features -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
        <div class="order-2 md:order-1">
          <div class="bg-gray-900 text-white p-6 rounded-lg shadow-xl">
            <div class="flex justify-between items-center mb-6">
              <div class="text-lg font-bold">{{ 'landing.services.title' | translate }}</div>
              <div class="flex space-x-3 text-sm">
                <span>{{ 'landing.hero.dashboard_button' | translate }}</span>
                <span>{{ 'landing.header.services' | translate }}</span>
              </div>
            </div>

            <div class="space-y-4">
              <div class="flex items-center justify-between bg-gray-800 p-3 rounded">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mr-3">
                    <fa-icon [icon]="['fab', 'facebook']" class="text-white"></fa-icon>
                  </div>
                  <span>Facebook {{ 'landing.services.followers' | translate }}</span>
                </div>
                <span>{{ 'landing.quality.price_prefix' | translate }}5.99</span>
              </div>

              <div class="flex items-center justify-between bg-gray-800 p-3 rounded">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded flex items-center justify-center mr-3">
                    <fa-icon [icon]="['fab', 'instagram']" class="text-white"></fa-icon>
                  </div>
                  <span>Instagram {{ 'landing.services.likes' | translate }}</span>
                </div>
                <span>{{ 'landing.quality.price_prefix' | translate }}3.99</span>
              </div>

              <div class="flex items-center justify-between bg-gray-800 p-3 rounded">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-red-600 rounded flex items-center justify-center mr-3">
                    <fa-icon [icon]="['fab', 'youtube']" class="text-white"></fa-icon>
                  </div>
                  <span>YouTube {{ 'landing.services.views' | translate }}</span>
                </div>
                <span>{{ 'landing.quality.price_prefix' | translate }}7.99</span>
              </div>
            </div>
          </div>
        </div>

        <div class="order-1 md:order-2 flex flex-col justify-center">
          <h2 class="text-3xl font-bold mb-6 text-gray-900">{{ 'landing.quality.title' | translate }}</h2>
          <div class="space-y-6">
            <div *ngFor="let feature of serviceFeatures.slice(0, 2)" class="flex">
              <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4 flex-shrink-0">
                <fa-icon [icon]="['fas', feature.icon]"></fa-icon>
              </div>
              <div>
                <h3 class="text-xl font-bold mb-1">
                  {{ feature.title === 'Amazing quality' ? ('landing.quality.amazing_quality' | translate) :
                     feature.title === 'Affordable prices' ? ('landing.quality.affordable_prices' | translate) :
                     feature.title }}
                </h3>
                <p class="text-gray-600">
                  {{ feature.title === 'Amazing quality' ? ('landing.quality.amazing_quality_desc' | translate) :
                     feature.title === 'Affordable prices' ? ('landing.quality.affordable_prices_desc' | translate) :
                     feature.description }}
                </p>
              </div>
            </div>
          </div>

          <button (click)="navigateToRegister()" class="mt-8 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium self-start">
            {{ 'landing.quality.get_started' | translate }}
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-6 md:mb-0">
          <span class="text-xl font-bold">NEWPANEL</span>
          <p class="mt-2 text-gray-400">{{ 'landing.footer.slogan' | translate }}</p>
        </div>

        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'facebook']"></fa-icon>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'twitter']"></fa-icon>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'instagram']"></fa-icon>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <fa-icon [icon]="['fab', 'telegram-plane']"></fa-icon>
          </a>
        </div>
      </div>

      <hr class="border-gray-800 my-8">

      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 mb-4 md:mb-0">{{ 'landing.footer.copyright' | translate }}</p>
        <div class="flex space-x-6">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">{{ 'landing.footer.terms' | translate }}</a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">{{ 'landing.footer.privacy' | translate }}</a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">{{ 'landing.footer.contact' | translate }}</a>
        </div>
      </div>
    </div>
  </footer>
</div>
