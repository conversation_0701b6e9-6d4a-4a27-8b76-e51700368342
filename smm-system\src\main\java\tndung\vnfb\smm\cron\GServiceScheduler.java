package tndung.vnfb.smm.cron;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.repository.tenant.ApiProviderRepository;
import tndung.vnfb.smm.repository.tenant.GSvRepository;
import tndung.vnfb.smm.rest.SMMConsumer;
import tndung.vnfb.smm.service.CurrencySynchronizeService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!local") // Disable in local environment
public class GServiceScheduler {
    private final ApiProviderRepository apiProviderRepository;
    private final GSvRepository gSvRepository;
    private final SMMConsumer smmConsumer;

    // Batch size for processing services
    private static final int BATCH_SIZE = 50;

    // In-memory cache for provider services (only for current job run)
    private final Map<Long, List<SMMServiceRes>> providerServicesCache = new HashMap<>();



    /**
     * Chạy job đồng bộ thông tin dịch vụ từ provider mỗi 3 phút một lần
     * Mỗi lần chạy sẽ lấy khoảng 50 dịch vụ để đồng bộ
     */
    @Scheduled(fixedRate = 3 * 60 * 1000) // 3 minutes in milliseconds
    public void scheduleServiceSync() {
        TenantContext.setCurrentTenant(Common.FULL_ACCESS_TENANT);
        log.info("Running scheduled service synchronization");

        // Clear the in-memory cache before each run
        providerServicesCache.clear();

        // Get all providers
        List<ApiProvider> providers = apiProviderRepository.getAll();
        if (providers.isEmpty()) {
            log.info("No providers found for service synchronization");
            return;
        }

        // Get services that have at least one sync flag enabled (filtered at database level)
        Pageable pageable = PageRequest.of(0, BATCH_SIZE);
        List<GService> services = gSvRepository.findServicesWithSyncFlagsEnabled(pageable);

        if (services.isEmpty()) {
            log.info("No services found with sync flags enabled");
            return;
        }

        log.info("Found {} services to synchronize", services.size());

        // Group services by provider to minimize API calls
        Map<ApiProvider, List<GService>> servicesByProvider = services.stream()
                .collect(java.util.stream.Collectors.groupingBy(GService::getApiProvider));

        // Process each provider
        servicesByProvider.forEach(this::syncServicesForProvider);

        // Log cache statistics
        log.info("Provider services cache used for {} providers in this run", providerServicesCache.size());
        TenantContext.clear();
    }

    /**
     * Synchronize services for a specific provider
     *
     * @param provider The API provider
     * @param services List of services to synchronize
     */
    private void syncServicesForProvider(ApiProvider provider, List<GService> services) {
        try {
            // Get provider services from cache or API
            List<SMMServiceRes> providerServices = getProviderServices(provider);

            if (providerServices == null || providerServices.isEmpty()) {
                log.warn("No services returned from provider: {}", provider.getName());
                return;
            }

            // Create a map for faster lookup
            Map<Integer, SMMServiceRes> serviceMap = new HashMap<>();
            for (SMMServiceRes smmService : providerServices) {
                serviceMap.put(smmService.getService(), smmService);
            }

            // Process each service
            for (GService service : services) {
                syncService(service, serviceMap);
            }

            log.info("Successfully synchronized {} services for provider: {}",
                    services.size(), provider.getName());

        } catch (Exception e) {
            log.error("Error synchronizing services for provider {}: {}",
                    provider.getName(), e.getMessage(), e);
        }
    }

    /**
     * Get provider services from in-memory cache or API
     * Cache only lasts for the duration of the current job run
     *
     * @param provider The API provider
     * @return List of provider services
     */
    private List<SMMServiceRes> getProviderServices(ApiProvider provider) {
        // Try to get from cache first
        List<SMMServiceRes> cachedServices = providerServicesCache.get(provider.getId());

        if (cachedServices != null && !cachedServices.isEmpty()) {
            log.debug("Using cached services for provider: {}", provider.getName());
            return cachedServices;
        }

        // If not in cache, call the API
        try {
            log.info("Fetching services from provider: {}", provider.getName());
            List<SMMServiceRes> services = smmConsumer.getServices(provider);

            // Cache the result in memory for this job run
            if (services != null && !services.isEmpty()) {
                providerServicesCache.put(provider.getId(), services);
                log.debug("Cached {} services for provider: {}", services.size(), provider.getName());
            }

            return services;
        } catch (Exception e) {
            log.error("Error fetching services from provider {}: {}",
                    provider.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Synchronize a single service with provider data
     *
     * @param service The service to synchronize
     * @param serviceMap Map of provider services for lookup
     */
    private void syncService(GService service, Map<Integer, SMMServiceRes> serviceMap) {
        if (service.getApiServiceId() == null) {
            log.warn("Service {} has no API service ID", service.getId());
            return;
        }

        try {
            Integer apiServiceId = Integer.parseInt(service.getApiServiceId());
            SMMServiceRes providerService = serviceMap.get(apiServiceId);

            if (providerService == null) {
                log.warn("Provider service not found for API service ID: {}", apiServiceId);

                // If syncStatus is enabled and service is not found in provider, deactivate it
                if (Boolean.TRUE.equals(service.getSyncStatus())) {
                    if (service.getStatus() != CommonStatus.DEACTIVATED) {
                        service.setStatus(CommonStatus.DEACTIVATED);
                        gSvRepository.save(service);
                        log.info("Service {} deactivated - not found in provider {} service list",
                                service.getId(), service.getApiProvider().getName());
                    }
                }
                return;
            }

            boolean updated = false;

            // Sync min/max if enabled
            if (Boolean.TRUE.equals(service.getSyncMinMax())) {
                if (!providerService.getMin().equals(service.getMin()) ||
                    !providerService.getMax().equals(service.getMax())) {

                    service.setMin(providerService.getMin());
                    service.setMax(providerService.getMax());
                    updated = true;
                    log.debug("Updated min/max for service {}: min={}, max={}",
                            service.getId(), providerService.getMin(), providerService.getMax());
                }
            }

            // Sync refill if enabled
            if (Boolean.TRUE.equals(service.getSyncRefill())) {
                if (!providerService.getRefill().equals(service.getRefill())) {
                    service.setRefill(providerService.getRefill());
                    updated = true;
                    log.debug("Updated refill for service {}: refill={}",
                            service.getId(), providerService.getRefill());
                }
            }

            // Sync cancel if enabled
            if (Boolean.TRUE.equals(service.getSyncCancel())) {
                if (providerService.getCancel() != null &&
                    !providerService.getCancel().equals(service.getCancelButton())) {

                    service.setCancelButton(providerService.getCancel());
                    updated = true;
                    log.debug("Updated cancel for service {}: cancel={}",
                            service.getId(), providerService.getCancel());
                }
            }

            // Save the service if any updates were made
            if (updated) {
                gSvRepository.save(service);
                log.info("Synchronized service: {}", service.getId());
            }

        } catch (NumberFormatException e) {
            log.error("Invalid API service ID for service {}: {}",
                    service.getId(), service.getApiServiceId());
        } catch (Exception e) {
            log.error("Error synchronizing service {}: {}",
                    service.getId(), e.getMessage(), e);
        }
    }
}
