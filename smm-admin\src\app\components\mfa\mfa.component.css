/* Fade-in animation for the form */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Shake animation for error states */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  20%, 60% {
    transform: translateX(-5px);
  }
  40%, 80% {
    transform: translateX(5px);
  }
}
.animate-shake {
  animation: shake 0.3s ease-in-out;
}

/* Style for the MFA digit inputs */
.mfa-digit-input {
  width: 48px;
  height: 56px;
  font-size: 24px;
  font-weight: 500;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: white;
  transition: all 0.2s ease;
  -moz-appearance: textfield; /* Remove spinner for Firefox */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* Remove spinner for Chrome, Safari, Edge, Opera */
.mfa-digit-input::-webkit-outer-spin-button,
.mfa-digit-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Focus state */
.mfa-digit-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb, 59, 130, 246), 0.3);
  outline: none;
  animation: pulse 0.5s ease-out;
}

/* Animation when input changes */
.mfa-digit-input:not(:placeholder-shown) {
  animation: bounce 0.3s ease-out;
}

/* Style for the MFA digit inputs when in error state */
.mfa-digit-input.border-red-500 {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Pulse animation for focus */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb, 59, 130, 246), 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--primary-rgb, 59, 130, 246), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb, 59, 130, 246), 0);
  }
}

/* Bounce animation for input */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Background blob animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
