import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private loadingMessageSubject = new BehaviorSubject<string>('');
  
  loading$: Observable<boolean> = this.loadingSubject.asObservable();
  loadingMessage$: Observable<string> = this.loadingMessageSubject.asObservable();

  /**
   * Show the loading indicator
   * @param message Optional message to display with the loading indicator
   */
  show(message: string = ''): void {
    this.loadingMessageSubject.next(message);
    this.loadingSubject.next(true);
  }

  /**
   * Hide the loading indicator
   */
  hide(): void {
    this.loadingSubject.next(false);
    this.loadingMessageSubject.next('');
  }

  /**
   * Update the loading message
   * @param message The new message to display
   */
  updateMessage(message: string): void {
    this.loadingMessageSubject.next(message);
  }

  /**
   * Get the current loading state
   * @returns The current loading state
   */
  isLoading(): boolean {
    return this.loadingSubject.value;
  }
}
