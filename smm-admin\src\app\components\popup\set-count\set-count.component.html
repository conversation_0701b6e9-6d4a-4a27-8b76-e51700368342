<div class="overlay-black" 


>
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'Set start count' }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Start Count Input -->
      <div class="form-group">
        <label for="startCount" class="form-label">{{ 'Start' }}</label>
        <input
          type="number"
          id="startCount"
          [(ngModel)]="startCount"
          class="form-input"
          placeholder="0"
          min="0"
        >
      </div>

      <!-- Save Button -->
      <button
        (click)="saveCount()"
        class="save-button"
        [disabled]="isLoading"
      >
        <span *ngIf="!isLoading">{{ 'Save' }}</span>
        <span *ngIf="isLoading">
          <svg class="spinner" viewBox="0 0 50 50">
            <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
          </svg>
          {{ 'Saving...' }}
        </span>
      </button>
    </div>
  </div>
</div>
