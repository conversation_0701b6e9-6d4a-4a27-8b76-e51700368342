package tndung.vnfb.smm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSubscriptionDto {
    private String id;
    private String domain;
    private String status;
    private ZonedDateTime subscriptionStartDate;
    private ZonedDateTime subscriptionEndDate;
    private Integer daysUntilExpiration;
    private String expirationAlert;
    private Boolean autoRenewal;
    private String subscriptionPlan;
    private String contactEmail;
}

