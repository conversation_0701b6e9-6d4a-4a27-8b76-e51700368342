import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ConfigService } from './config.service';
import { ToastService } from './toast.service';

import { BehaviorSubject, Observable, Subject, switchMap, tap, throwError } from 'rxjs';
import { catchError, finalize, first, map } from 'rxjs/operators';
import { MyTransactionSummaryRes } from '../../model/response/my-transaction-summary.model';

import { UserRes } from '../../model/response/user-res.model';
import { UserSearchReq } from '../../model/request/user-search-req.model';
import { GUserSuperRes } from '../../model/response/g-user-super-res.model';
import { PageResponse } from '../../model/response/page-response.model';
import { LoginHistoryRes, LoginHistoryItem } from '../../model/response/login-history.model';
import { CurrencyReq } from '../../model/request/currency-req.model';
import { EditUserReq } from '../../model/request/edit-user-req.model';

@Injectable({providedIn: 'root'})
export class UserService {
  get summarySubject$(): BehaviorSubject<MyTransactionSummaryRes | undefined> {
    return this._summarySubject$;
  }
  get getSummary$(): Subject<void> {
    return this._getSummary$;
  }
  get loadingSummary$(): BehaviorSubject<boolean> {
    return this._loadingSummary$;
  }
  get loadingUpdate$(): BehaviorSubject<boolean> {
    return this._loadingUpdate$;
  }
  get get$(): Subject<void> {
    return this._get$;
  }




  public readonly _user$: Observable<UserRes | undefined>;
  private _loadingChangePass$ = new BehaviorSubject<boolean>(false);
  private _loadingUpdate$ = new BehaviorSubject<boolean>(false);
  private _loadingSummary$ = new BehaviorSubject<boolean>(false);
  private _userSubject$ : BehaviorSubject<UserRes | undefined>;


  private _summarySubject$!:  BehaviorSubject<MyTransactionSummaryRes | undefined>;
  private _get$ = new Subject<void>();
  private _getSummary$ = new Subject<void>();
  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object,
    private configService: ConfigService,
    private toastService: ToastService
  ) {
    // Initialize with undefined or get from localStorage if in browser
    let storedUser: UserRes | undefined = undefined;

    if (isPlatformBrowser(this.platformId)) {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          storedUser = JSON.parse(userStr);
        } catch (e) {
          console.error('Error parsing user from localStorage', e);
        }
      }
    }

    this._userSubject$ = new BehaviorSubject<UserRes | undefined>(storedUser);
    this._user$ = this._userSubject$.asObservable();

    this._summarySubject$ = new BehaviorSubject<MyTransactionSummaryRes | undefined>(undefined);
    this._getSummary$
      .pipe(switchMap(() => this.getSummary()))
      .subscribe(res => this._summarySubject$.next(res));

    this._get$
      .pipe(switchMap(() => this.getMe()))
      .subscribe(res => this._userSubject$.next(res));
  }


  userValue(){
    return this._userSubject$.value;
  }

  /**
   * Update user information (email and phone)
   * @param userReq User information to update
   * @returns Observable with updated user data
   */
  updateInfo(userReq: EditUserReq): Observable<UserRes> {
    this.loadingUpdate$.next(true);
    return this.http
      .put<UserRes>(
        `${this.configService.apiUrl}/users/me`,
        userReq
      )
      .pipe(
        first(),
        tap(updatedUser => {
          // Update only specific fields in the user subject
          if (updatedUser) {
            const currentUser = this._userSubject$.getValue();
            if (currentUser) {
              // Create a new user object with only updated fields
              const mergedUser = {
                ...currentUser,
                email: updatedUser.email,
                phone: updatedUser.phone
              };

              this._userSubject$.next(mergedUser);

              // Update localStorage if in browser environment
              // if (isPlatformBrowser(this.platformId)) {
              //   localStorage.setItem('user', JSON.stringify(mergedUser));
              // }
            } else {
              // If no current user, just use the updated user
              this._userSubject$.next(updatedUser);

              // Update localStorage if in browser environment
              // if (isPlatformBrowser(this.platformId)) {
              //   localStorage.setItem('user', JSON.stringify(updatedUser));
              // }
            }
          }
        }),
        catchError(error => {
          console.error('Error updating user info:', error);
          this.toastService.showError(error?.message || 'Failed to update user information');
          return throwError(() => error);
        }),
        finalize(() => this.loadingUpdate$.next(false))
      );
  }

  changePass(password: { old_password: string, new_password: string }): Observable<UserRes> {
    this.loadingChangePass$.next(true);
    return this.http
      .put<UserRes>(
        `${this.configService.apiUrl}/users/me/change-password`,
        password
      )
      .pipe(
        first(),
        catchError(error => {
          console.error('Error changing password:', error);
          this.toastService.showError(error?.message || 'Failed to change password');
          return throwError(() => error);
        }),
        finalize(() => this.loadingChangePass$.next(false))
      );
  }

  getMe(): Observable<UserRes> {
    return this.http
      .get<UserRes>(
        `${this.configService.apiUrl}/users/me`)
      .pipe(
        first(),
        tap(user => this._userSubject$.next(user)),
        catchError(error => {
          console.error('Error getting user info:', error);
          this.toastService.showError(error?.message || 'Failed to load user information');
          return throwError(() => error);
        })
      );
  }

  /**
   * Set the user's preferred currency
   * @param currencyReq The currency request containing the currency code
   * @returns An observable of the API response with updated UserRes
   */
  setCurrency(currencyReq: CurrencyReq): Observable<UserRes> {
    return this.http
      .patch<UserRes>(
        `${this.configService.apiUrl}/users/me/currency`,
        currencyReq
      )
      .pipe(
        first(),
        tap(updatedUser => {
          // Update only the currency field in the user subject
          if (updatedUser) {
            const currentUser = this._userSubject$.getValue();
            if (currentUser) {
              // Create a new user object with only the updated currency field
              const mergedUser = {
                ...currentUser,
                preferred_currency: updatedUser.preferred_currency
              };

              this._userSubject$.next(mergedUser);

              // Update localStorage if in browser environment
              // if (isPlatformBrowser(this.platformId)) {
              //   localStorage.setItem('user', JSON.stringify(mergedUser));
              // }
            } else {
              // If no current user, just use the updated user
              this._userSubject$.next(updatedUser);

              // Update localStorage if in browser environment
              // if (isPlatformBrowser(this.platformId)) {
              //   localStorage.setItem('user', JSON.stringify(updatedUser));
              // }
            }
          } else {
            // If no user is returned, refresh user data to get updated currency
            this.getMe().subscribe();
          }
        }),
        catchError(error => {
          console.error('Error setting currency:', error);
          this.toastService.showError(error?.message || 'Failed to update currency');
          return throwError(() => error);
        })
      );
  }

  getSummary(): Observable<MyTransactionSummaryRes> {
    this._loadingSummary$.next(true);
    return this.http
      .get<MyTransactionSummaryRes>(
        `${this.configService.apiUrl}/users/me/transactions/summary`
      )
      .pipe(
        catchError(error => {
          console.error('Error getting transaction summary:', error);
          this.toastService.showError(error?.message || 'Failed to load transaction summary');
          return throwError(() => error);
        }),
        finalize(() => this._loadingSummary$.next(false))
      );
  }


  get loadingChangePass$(): BehaviorSubject<boolean> {
    return this._loadingChangePass$;
  }

  get user$(): Observable<UserRes | undefined> {
    return this._user$;
  }

  /**
   * Search users with filters (admin only)
   * @param req Search request with filters
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Observable of paginated user results
   */
  searchUsers(req: UserSearchReq, page: number = 0, size: number = 10): Observable<PageResponse<GUserSuperRes>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (req.keyword) {
      params = params.set('keyword', req.keyword);
    }

    if (req.status) {
      params = params.set('status', req.status);
    }

    return this.http.get<PageResponse<GUserSuperRes>>(`${this.configService.apiUrl}/users/search`, { params })
      .pipe(
        catchError(error => {
          console.error('Error searching users:', error);
          this.toastService.showError(error?.message || 'Failed to search users');
          return throwError(() => error);
        })
      );
  }

  /**
   * Get login history for the current user
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Observable of login history
   */
  getLoginHistory(page: number = 0, size: number = 10): Observable<LoginHistoryRes> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<LoginHistoryRes | LoginHistoryItem[]>(`${this.configService.apiUrl}/users/me/login-history`, { params })
      .pipe(
        map(response => {
          console.log('Raw API response:', response);

          // If the response is an array, convert it to the expected format
          if (Array.isArray(response)) {
            console.log('Converting array response to LoginHistoryRes format');
            return {
              content: response,
              total_elements: response.length
            };
          }

          // Otherwise, return the response as is
          return response as LoginHistoryRes;
        })
      );
  }
}
