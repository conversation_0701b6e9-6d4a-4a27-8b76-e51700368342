import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { IconName, SizeProp } from '@fortawesome/fontawesome-svg-core';
import { brandColors } from '../../../icons/icons.font-awesome-brands';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-social-icon',
  standalone: true,
  imports: [IconsModule, CommonModule],
  templateUrl: './social-icon.component.html',
  styleUrl: './social-icon.component.css'
})
export class SocialIconComponent implements OnChanges {
  ngOnChanges(changes: SimpleChanges): void {
    this.  iconName = this.icon as IconName;
  }
  ngOnInit(): void {
   
  }
  brandColors = brandColors;
  @Input() icon: string = 'facebook';
  @Input() zClass: string = '';

  iconName: IconName = 'facebook';
}
