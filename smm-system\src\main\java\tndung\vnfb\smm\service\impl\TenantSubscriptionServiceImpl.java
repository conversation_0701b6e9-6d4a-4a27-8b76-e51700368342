package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.anotation.TenantAccessCheck;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.constant.enums.TransactionSource;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.RenewalRequest;
import tndung.vnfb.smm.dto.RenewalResponse;
import tndung.vnfb.smm.dto.TenantSubscriptionDto;
import tndung.vnfb.smm.entity.GTransaction;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.entity.UserTenantAccess;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.repository.nontenant.UserTenantAccessRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class TenantSubscriptionServiceImpl implements TenantSubscriptionService{

    private final TenantRepository tenantRepository;
    private final TenantService tenantService;
    private final PanelNotificationService notificationService;
    private final UserTenantAccessRepository userTenantAccessRepository;
    private final GUserRepository gUserRepository;
    private final TransactionRepository transactionRepository;
    @Transactional
    @Override
    @TenantAccessCheck
    public RenewalResponse renewSubscription(RenewalRequest request) {
        try {


            Optional<Tenant> tenantOpt = tenantService.findByTenantId(request.getTenantId());

            if (tenantOpt.isEmpty()) {
                return new RenewalResponse(false, "Tenant not found", null, request.getTenantId());
            }

            Tenant tenant = tenantOpt.get();
            ZonedDateTime currentEndDate = tenant.getSubscriptionEndDate();
            ZonedDateTime newEndDate;

            // Calculate new end date
            if (currentEndDate != null && currentEndDate.isAfter(ZonedDateTime.now())) {
                newEndDate = currentEndDate.plusDays(request.getExtensionDays());
            } else {
                newEndDate = ZonedDateTime.now().plusDays(request.getExtensionDays());
            }

            // Update tenant
            tenant.setSubscriptionEndDate(newEndDate);
            tenant.setStatus(TenantStatus.Active);
            tenant.setLastRenewalDate(ZonedDateTime.now());
            tenant.setRenewalNotificationSent(false);


            tenantService.save(tenant);

            log.info("Renewed subscription for tenant: {} until {}",
                    request.getTenantId(), newEndDate);

            return new RenewalResponse(true, "Subscription renewed successfully",
                    newEndDate, request.getTenantId());

        } catch (Exception e) {
            log.error("Error renewing subscription for tenant: {}", request.getTenantId(), e);
            return new RenewalResponse(false, "Error renewing subscription: " + e.getMessage(),
                    null, request.getTenantId());
        }
    }

    @Transactional
    @Override
    public int updateExpiredTenants() {
        try {
            int expiredCount = tenantRepository.updateExpiredTenants();

            // Also handle suspended tenants
            LocalDateTime gracePeriodEnd = LocalDateTime.now().minusDays(7); // Default grace period
            int suspendedCount = tenantRepository.updateSuspendedTenants(gracePeriodEnd);

            log.info("Updated {} expired tenants and {} suspended tenants",
                    expiredCount, suspendedCount);

            return expiredCount + suspendedCount;
        } catch (Exception e) {
            log.error("Error updating expired tenants", e);
            return 0;
        }
    }
    @Override
    public List<TenantSubscriptionDto> getTenantsExpiringSoon(int daysAhead) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureDate = now.plusDays(daysAhead);

        List<Tenant> tenants = tenantRepository.findTenantsExpiringSoon(
                TenantStatus.Active, now, futureDate);

        return tenants.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
//    @Override
//    public List<TenantSubscriptionDto> getAllTenantSubscriptions() {
//        return tenantRepository.findAll().stream()
//                .filter(t -> !t.getIsDeleted())
//                .map(this::convertToDto)
//                .collect(Collectors.toList());
//    }

    @Override
    @TenantAccessCheck
    public TenantSubscriptionDto getTenantSubscription(String tenantId) {
        return tenantService.findByTenantId(tenantId)
                .map(this::convertToDto).orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));
    }

    @Transactional
    @Override
    public void sendRenewalNotifications() {
        List<Tenant> expiringSoon = tenantRepository.findTenantsExpiringSoon(
                TenantStatus.Active,
                LocalDateTime.now(),
                LocalDateTime.now().plusDays(7)
        );

        for (Tenant tenant : expiringSoon) {
            try {
                // Send renewal notification using PanelNotificationService
                notificationService.createRenewalReminderNotification(tenant);
                tenant.setRenewalNotificationSent(true);
                tenantService.save(tenant);

                log.info("Sent renewal notification to tenant: {}", tenant.getId());
            } catch (Exception e) {
                log.error("Error sending renewal notification to tenant: {}", tenant.getId(), e);
            }
        }
    }

    private TenantSubscriptionDto convertToDto(Tenant tenant) {
        TenantSubscriptionDto dto = new TenantSubscriptionDto();
        dto.setId(tenant.getId());
        dto.setDomain(tenant.getDomain());
        dto.setStatus(tenant.getStatus().name());
        dto.setSubscriptionStartDate(tenant.getSubscriptionStartDate());
        dto.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        dto.setDaysUntilExpiration(tenant.getDaysUntilExpiration());
        dto.setAutoRenewal(tenant.getAutoRenewal());
        dto.setContactEmail(tenant.getContactEmail());

        // Set expiration alert
        if (tenant.getDaysUntilExpiration() != null) {
            if (tenant.getDaysUntilExpiration() < 0) {
                dto.setExpirationAlert("Đã hết hạn");
            } else if (tenant.getDaysUntilExpiration() <= 7) {
                dto.setExpirationAlert("Sắp hết hạn");
            } else if (tenant.getDaysUntilExpiration() <= 30) {
                dto.setExpirationAlert("Bình thường");
            } else {
                dto.setExpirationAlert("Còn lâu");
            }
        }

        return dto;
    }

    @Transactional
    @Override
    @TenantAccessCheck
    public void updateAutoRenewal(String tenantId, Boolean autoRenewal) {

            Optional<Tenant> tenantOpt = tenantService.findByTenantId(tenantId);

            if (tenantOpt.isEmpty()) {
                throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
            }

            Tenant tenant = tenantOpt.get();
            tenant.setAutoRenewal(autoRenewal);
        tenantService.save(tenant);

            log.info("Updated auto-renewal for tenant: {} to {}", tenantId, autoRenewal);

    }

    @Transactional
    @Override
    public int processAutoRenewals() {
        try {
            // Find expired tenants with auto-renewal enabled
            List<Tenant> expiredTenantsWithAutoRenewal = tenantRepository.findExpiredTenantsWithAutoRenewal();

            int renewedCount = 0;
            BigDecimal renewalCost = new BigDecimal("7.7");

            for (Tenant tenant : expiredTenantsWithAutoRenewal) {
                try {
                    // Find the first admin/panel user for this tenant
                    List<UserTenantAccess> userAccesses = userTenantAccessRepository.findByTenantId(tenant.getId());

                    if (userAccesses.isEmpty()) {
                        log.warn("No users found for tenant: {}, skipping auto-renewal", tenant.getId());
                        continue;
                    }

                    // Get the first user (assuming they are the owner/admin)
                    Long userId = userAccesses.get(0).getUserId();
                    Optional<GUser> userOpt = gUserRepository.findById(userId);

                    if (userOpt.isEmpty()) {
                        log.warn("User {} not found for tenant: {}, skipping auto-renewal", userId, tenant.getId());
                        continue;
                    }

                    GUser user = userOpt.get();

                    // Check if user has sufficient balance
                    if (user.getBalance().compareTo(renewalCost) < 0) {
                        log.warn("Insufficient balance for user {} (tenant: {}), skipping auto-renewal. Required: {}, Available: {}",
                                userId, tenant.getId(), renewalCost, user.getBalance());
                        continue;
                    }

                    // Deduct renewal cost from user balance
                    user.setBalance(user.getBalance().subtract(renewalCost));
                    gUserRepository.save(user);

                    // Create transaction record
                    GTransaction transaction = new GTransaction();
                    transaction.setUserId(userId);
                    transaction.setChange(renewalCost.negate()); // Negative amount for deduction
                    transaction.setBalance(user.getBalance());
                    transaction.setType(TransactionType.Subscription);
                    transaction.setSource(TransactionSource.AUTO_RENEWAL);
                    transaction.setNote("Auto-renewal for tenant: " + tenant.getDomain());
                    transactionRepository.save(transaction);

                    // Renew the tenant subscription
                    ZonedDateTime newEndDate = ZonedDateTime.now().plusDays(30); // 30 days renewal
                    tenant.setSubscriptionEndDate(newEndDate);
                    tenant.setStatus(TenantStatus.Active);
                    tenant.setLastRenewalDate(ZonedDateTime.now());
                    tenant.setRenewalNotificationSent(false);
                    tenantService.save(tenant);

                    renewedCount++;
                    log.info("Successfully auto-renewed tenant: {} for user: {}, cost: {}",
                            tenant.getId(), userId, renewalCost);

                } catch (Exception e) {
                    log.error("Error processing auto-renewal for tenant: {}", tenant.getId(), e);
                    // Continue with next tenant instead of failing the entire batch
                }
            }

            return renewedCount;

        } catch (Exception e) {
            log.error("Error in processAutoRenewals", e);
            return 0;
        }
    }
}
