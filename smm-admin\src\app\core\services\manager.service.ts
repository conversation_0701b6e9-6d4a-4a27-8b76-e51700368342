import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AddManagerReq } from '../../model/request/add-manager-req.model';
import { ManagerRes } from '../../model/response/manager-res.model';
import { ApiResponse } from '../../model/response/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class ManagerService {
  private readonly API_URL = `${environment.apiUrl}/admin/managers`;

  constructor(private http: HttpClient) {}

  getManagers(): Observable<ApiResponse<ManagerRes[]>> {
    return this.http.get<ApiResponse<ManagerRes[]>>(this.API_URL);
  }

  addManager(request: AddManagerReq): Observable<ApiResponse<ManagerRes>> {
    return this.http.post<ApiResponse<ManagerRes>>(this.API_URL, request);
  }

  updateManager(id: number, request: Partial<AddManagerReq>): Observable<ApiResponse<ManagerRes>> {
    return this.http.put<ApiResponse<ManagerRes>>(`${this.API_URL}/${id}`, request);
  }

  deleteManager(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.API_URL}/${id}`);
  }

  toggleManagerStatus(id: number): Observable<ApiResponse<ManagerRes>> {
    return this.http.patch<ApiResponse<ManagerRes>>(`${this.API_URL}/${id}/toggle-status`, {});
  }
}
