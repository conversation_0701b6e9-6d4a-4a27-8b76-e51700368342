
.modal-container {
  @apply flex flex-col items-center p-8 bg-white rounded-2xl w-[436px];
  font-family: 'Roboto', sans-serif;
}
  
  .illustration-container {
    @apply w-[265px] h-[140px] mb-8;
  }
  
  .ornament-wrapper {
    @apply relative w-full h-full;
  }
  
  .check-circle {
    @apply absolute top-[20px] left-[82px] w-[100px] h-[100px] bg-[#133ebf] rounded-full flex items-center justify-center shadow-lg;
  }
  
  .check-icon {
    @apply w-[60px] h-[60px];
  }
  
  .success-box {
    @apply w-full p-4 bg-[#e4fbe1] rounded-2xl mb-6 shadow-md;
  }
  
  .success-header {
    @apply flex flex-row items-center gap-2 mb-4;
  }
  
  .success-text {
    @apply text-[#289d35] text-lg font-semibold;
  }
  
  .details-container {
    @apply flex flex-col gap-3;
  }
  
  .detail-item {
    @apply text-[#262b38] text-base font-medium;
  }
  
  .link {
    @apply text-blue-600 underline;
  }
  
  .confirm-button {
    @apply w-full py-5 px-6 bg-[#133ebf] text-white rounded-lg text-base font-semibold;
    transition: background-color 0.2s ease;
  }
  
  .confirm-button:hover {
    @apply bg-[#0f2f8f];
  }
  
  .confirm-button:active {
    @apply bg-[#0b246f];
  }