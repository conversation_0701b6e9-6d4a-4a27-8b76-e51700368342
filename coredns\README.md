# CoreDNS Setup for Domain Nameservers

This document explains how to set up and manage CoreDNS for providing nameserver services to domains in your SMM system.

## Overview

CoreDNS is used to provide DNS nameserver functionality, allowing clients to point their domains to your nameservers (`ns1.autovnfb.com` and `ns2.autovnfb.com`). This setup enables you to manage DNS records for client domains directly from your system.

## Configuration Files

- **Corefile**: The main CoreDNS configuration file
- **zones/**: Directory containing zone files for each domain
- **Dockerfile**: Used to build the CoreDNS Docker image

## Management Scripts

The following scripts are provided to help manage domains in CoreDNS:

### Linux/Bash Scripts

- **add-domain.sh**: Add a new domain to CoreDNS
  ```bash
  ./add-domain.sh example.com **************
  ```

- **remove-domain.sh**: Remove a domain from CoreDNS
  ```bash
  ./remove-domain.sh example.com
  ```

- **reload-config.sh**: Reload CoreDNS configuration without restarting
  ```bash
  ./reload-config.sh
  ```

### Windows PowerShell Scripts

- **add-domain.ps1**: Add a new domain to CoreDNS
  ```powershell
  .\add-domain.ps1 -Domain example.com -IpAddress **************
  ```

- **remove-domain.ps1**: Remove a domain from CoreDNS
  ```powershell
  .\remove-domain.ps1 -Domain example.com
  ```

- **reload-config.ps1**: Reload CoreDNS configuration without restarting
  ```powershell
  .\reload-config.ps1
  ```

## Initial Setup

1. Create the CoreDNS directory structure:
   ```bash
   mkdir -p coredns/zones
   ```

2. Create the Corefile, zone files, and management scripts as provided.

3. Add the CoreDNS service to your docker-compose.gitlab.yml file:
   ```yaml
   coredns:
     build:
       context: ./coredns
       dockerfile: Dockerfile
     container_name: coredns
     restart: always
     ports:
       - "53:53/udp"
       - "53:53/tcp"
       - "9153:9153"
     volumes:
       - ./coredns/Corefile:/etc/coredns/Corefile
       - ./coredns/zones:/etc/coredns/zones
     networks:
       - app-network
     cap_add:
       - NET_BIND_SERVICE
   ```

4. Build and start the CoreDNS service:
   ```bash
   docker-compose -f docker-compose.gitlab.yml up -d coredns
   ```

## Domain Management

### Adding a New Domain

1. When a user creates a child panel with their domain, add the domain to CoreDNS:
   ```bash
   cd coredns
   ./add-domain.sh user-domain.com **************
   ./reload-config.sh
   ```

2. Instruct the user to set their domain's nameservers to:
   - ns1.autovnfb.com
   - ns2.autovnfb.com

### Removing a Domain

When a user's service is terminated or their domain needs to be removed:

```bash
cd coredns
./remove-domain.sh user-domain.com
./reload-config.sh
```

## DNS Record Types

The default zone file configuration includes:

- **SOA Record**: Start of Authority record with administrative information
- **NS Records**: Nameserver records pointing to ns1.autovnfb.com and ns2.autovnfb.com
- **A Records**: Address records mapping the domain and www subdomain to your server IP
- **Wildcard Record**: Catches all subdomains and points them to your server IP

## Troubleshooting

### Checking DNS Resolution

Test if a domain is resolving correctly:

```bash
dig @localhost example.com
```

Or from outside the server:

```bash
dig @your-server-ip example.com
```

### Checking CoreDNS Logs

View CoreDNS logs:

```bash
docker logs coredns
```

### Common Issues

1. **Port 53 already in use**: Make sure no other DNS service (like systemd-resolved) is using port 53.
   ```bash
   sudo lsof -i :53
   ```

2. **Domain not resolving**: Check if the zone file exists and is correctly formatted.

3. **CoreDNS not starting**: Check Docker logs for error messages.

## Integration with Domain Manager

To integrate CoreDNS with your domain-manager service, you can modify the domain-manager to call the add-domain and remove-domain scripts when domains are added or removed through its API.

## DNS Propagation

Remind users that DNS changes can take 24-48 hours to propagate globally, although many DNS providers update much faster.
