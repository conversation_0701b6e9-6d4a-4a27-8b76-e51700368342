.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.platform-management-container {
  @apply flex flex-col bg-white rounded-2xl p-6 w-full max-w-md mx-auto shadow-lg;
  position: relative;
}

.header-section {
  @apply flex flex-col items-center mb-6 text-center;
  position: relative;
}

.close-btn {
  @apply absolute top-0 right-0 p-2 text-gray-500 hover:text-gray-700 transition-colors;
}

.icon-container {
  @apply mb-4;
}

.folder-icon {
  @apply w-16 h-16 text-gray-600;
}

.text-container {
  @apply flex flex-col gap-2;
}

.title {
  @apply text-xl font-semibold text-gray-800;
}

.description {
  @apply text-sm text-gray-600;
}

.platform-list {
  @apply bg-[#f5f7fc] rounded-lg mb-6 overflow-hidden;
  width: 100%;
}

.platform-item {
  @apply flex items-center justify-between p-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-50 cursor-pointer;
}

.item-content {
  @apply flex items-center gap-3;
}

.drag-handle {
  @apply text-gray-400 cursor-grab;
}

.handle-icon {
  @apply w-5 h-5;
}

.platform-icon {
  @apply flex items-center justify-center;
}

.platform-name {
  @apply text-sm font-medium text-gray-800;
}

.item-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply p-2 rounded-md hover:bg-gray-200 transition-colors;
}

.edit-btn {
  @apply text-blue-500;
}

.delete-btn {
  @apply text-red-500;
}

.action-icon {
  @apply w-4 h-4;
}

.add-platform-btn {
  @apply flex items-center justify-center gap-2 bg-[var(--primary)] text-white font-medium py-3 px-4 rounded-lg hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)] transition-colors duration-300;
}

.plus-icon {
  @apply w-5 h-5;
}

/* Loading state */
.loading-spinner {
  @apply flex justify-center items-center py-8;
}

.spinner {
  @apply w-8 h-8 border-4 border-[var(--primary)] border-t-transparent rounded-full animate-spin;
}

/* Empty state */
.empty-state {
  @apply flex flex-col items-center justify-center py-8 text-gray-500;
}

/* Drag and Drop Styles */
.cdk-drag-preview {
  @apply shadow-lg bg-white rounded-lg z-10;
  width: var(--item-width) !important;
  max-width: var(--item-width) !important;
  box-sizing: border-box;
}

.cdk-drag-placeholder {
  @apply opacity-0;
}

.cdk-drag-animating {
  @apply transition-transform duration-300;
}

.platform-list.cdk-drop-list-dragging .platform-item:not(.cdk-drag-placeholder) {
  @apply transition-transform duration-300;
}

.platform-item-preview {
  @apply flex items-center justify-between p-3 bg-white rounded-lg shadow-lg;
  width: var(--item-width);
  box-sizing: border-box;
}

.item-actions-preview {
  @apply flex items-center gap-2;
  width: 72px; /* Width of the action buttons area */
}

/* Custom cursor for draggable items */
.platform-item {
  @apply cursor-pointer;
}

.platform-item:hover {
  @apply bg-gray-50;
}

.platform-item:active {
  @apply cursor-grabbing;
}

/* Delete Confirmation Modal */
.delete-confirmation-overlay {
  @apply fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[calc(var(--z-modal)+1)];
}

.delete-confirmation-modal {
  @apply bg-white rounded-lg p-6 max-w-md w-full shadow-xl;
}

.confirmation-title {
  @apply text-xl font-semibold text-gray-800 mb-4;
}

.confirmation-message {
  @apply text-gray-600 mb-6;
}

.confirmation-actions {
  @apply flex justify-end gap-4;
}

.cancel-btn {
  @apply px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors;
}

.confirm-btn {
  @apply px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center justify-center min-w-[80px];
}

.confirm-btn:disabled, .cancel-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-spinner {
  @apply w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}
