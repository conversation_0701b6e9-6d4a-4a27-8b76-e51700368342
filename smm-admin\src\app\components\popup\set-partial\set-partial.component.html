<div class="overlay-black" (click)="onOverlayClick($event)">
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'Set partial remains' }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Order Info -->
      <div class="form-group">
        <label class="form-label">{{ 'Order quantity' }}</label>
        <div class="info-text">{{ quantity }}</div>
      </div>

      <!-- Remains Input -->
      <div class="form-group">
        <label for="remains" class="form-label">{{ 'Remains' }}</label>
        <input
          type="number"
          id="remains"
          [(ngModel)]="remains"
          class="form-input"
          placeholder="0"
          min="0"
          [max]="quantity"
        >
        <div class="help-text">Enter the number of items that were completed</div>
      </div>

      <!-- Action Buttons -->
      <div class="button-group">
        <button
          type="button"
          class="cancel-button"
          (click)="onClose()"
          [disabled]="isLoading">
          {{ 'Cancel' }}
        </button>
        <button
          type="button"
          class="save-button"
          (click)="savePartial()"
          [disabled]="isLoading">
          <svg *ngIf="isLoading" class="spinner" viewBox="0 0 50 50">
            <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
          </svg>
          {{ isLoading ? 'Updating...' : 'Update Status' }}
        </button>
      </div>
    </div>
  </div>
</div>
