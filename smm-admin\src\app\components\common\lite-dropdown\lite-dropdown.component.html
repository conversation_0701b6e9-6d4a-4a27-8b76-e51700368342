<div class="relative inline-block text-left w-full" [ngClass]="customClassDropdown">
    <!-- Button -->
    <button
      type="button"
      class="w-full dropdown-container p-4" [ngClass]="customClassButton"
      (click)="toggleDropdown($event)">

      <div class="flex items-center">
        <!-- Display selected option or placeholder -->
        <span>{{ selectedOption !== null ? (selectedOption || (options.length > 0 ? options[0] : placeholder)) : placeholder }}</span>
      </div>

      <!-- Dropdown Arrow -->
      <fa-icon class="absolute right-5 top-1/2 transform -translate-y-1/2 w-5 h-5"
      [icon]='["fas", "angle-down"]'></fa-icon>
    </button>

    <!-- Dropdown list - Using fixed positioning to render outside of parent container -->
    <div *ngIf="isOpen" class="lite-dropdown-menu-container" (click)="$event.stopPropagation()">
      <ul>
        <li *ngFor="let option of options"
            (click)="selectOption(option, $event)"
            class="px-4 py-4 flex items-center cursor-pointer hover:bg-gray-100">

          <!-- Icon bên trái -->
          <span>{{ option }}</span>

        </li>
      </ul>
    </div>

    <!-- This is a hidden element that will be used to create a portal for the dropdown -->
    <div class="dropdown-portal-anchor" style="display: none;"></div>
  </div>
