import { Compo<PERSON>, On<PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, ElementRef, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { PanelNotificationService } from '../../../core/services/panel-notification.service';
import { PanelNotificationRes } from '../../../model/response/panel-notification-res.model';
import { IconsModule } from '../../../icons/icons.module';


@Component({
  selector: 'app-notification-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './notification-dropdown.component.html',
  styleUrl: './notification-dropdown.component.css'
})
export class NotificationDropdownComponent implements OnInit, OnDestroy {
  isOpen = false;
  unreadCount = 0;
  notifications: PanelNotificationRes[] = [];
  loading = false;
  showAllNotifications = true; // Default to show all notifications

  private subscriptions: Subscription[] = [];

  constructor(
    private panelNotificationService: PanelNotificationService,
    private elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    // Subscribe to unread count
    const unreadCountSub = this.panelNotificationService.unreadCount$.subscribe(count => {
      this.unreadCount = count;
    });
    this.subscriptions.push(unreadCountSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  toggleDropdown(): void {
    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      this.loadNotifications();
    }
  }

  toggleShowAll(): void {
    this.showAllNotifications = !this.showAllNotifications;
    this.loadNotifications();
  }

  getDisplayedNotifications(): PanelNotificationRes[] {
    if (this.showAllNotifications) {
      return this.notifications;
    }
    return this.notifications.filter(n => !n.is_read);
  }

  private loadNotifications(): void {
    this.loading = true;

    if (this.showAllNotifications) {
      // Load all notifications with pagination
      this.panelNotificationService.getNotifications(0, 20).subscribe({
        next: (response) => {

          this.notifications = response.content;

          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading all notifications:', error);
          this.loading = false;
        }
      });
    } else {
      // Load only unread notifications
      this.panelNotificationService.getUnreadNotifications().subscribe({
        next: (response) => {

            this.notifications = response;

          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading unread notifications:', error);
          this.loading = false;
        }
      });
    }
  }

  markAsRead(notification: PanelNotificationRes): void {
    if (!notification.is_read) {
      this.panelNotificationService.markAsRead(notification.id).subscribe({
        next: () => {
          notification.is_read = true;
          this.panelNotificationService.updateUnreadCount();
        },
        error: (error) => {
          console.error('Error marking notification as read:', error);
        }
      });
    }
  }

  markAllAsRead(): void {
    this.panelNotificationService.markAllAsRead().subscribe({
      next: () => {
        this.notifications.forEach(n => n.is_read = true);
        this.panelNotificationService.updateUnreadCount();
      },
      error: (error) => {
        console.error('Error marking all notifications as read:', error);
      }
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} giờ trước`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} ngày trước`;

    return date.toLocaleDateString('vi-VN');
  }

  trackByNotificationId(_index: number, notification: PanelNotificationRes): number {
    return notification.id;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
  }
}
