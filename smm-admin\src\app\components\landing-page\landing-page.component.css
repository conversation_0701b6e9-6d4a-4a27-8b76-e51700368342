/* Landing Page Styles */

/* Header styles */
.landing-header {
  position: sticky;
  top: 0;
  z-index: 50;
}

/* Hero section animations */
.hero-section {
  position: relative;
  overflow: hidden;
}

/* Social icons container */
.social-icons-container {
  position: relative;
  z-index: 10;
}

.social-icon-wrapper {
  transition: transform 0.3s ease;
}

.social-icon-wrapper:hover {
  transform: translateY(-5px);
}

/* Features section */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

/* Button hover effects */
button {
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
}

/* Footer links */
footer a {
  transition: color 0.3s ease;
}
