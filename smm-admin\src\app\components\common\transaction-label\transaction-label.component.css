.link-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.description-row {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tag {
  background-color: var(--primary, #0ea5e9);
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: bold;
}

.link a {
  color: #3b82f6;
  text-decoration: none;
}

.link a:hover {
  text-decoration: underline;
}
