/* Main Container */
.general-settings {
  @apply  md:p-6 max-w-6xl mx-auto;
}

/* Header Styles */
.settings-header {
  @apply mb-6;
}

.settings-title {
  @apply text-2xl font-bold text-gray-800;
}

.settings-description {
  @apply text-gray-500 mt-1;
}

/* Section Styles */
.settings-section {
  @apply bg-white rounded-lg shadow-sm mb-6 p-5 overflow-hidden;
}

.section-title {
  @apply text-lg font-medium text-gray-800 mb-4 pb-2 border-b border-gray-100;
}

/* Features List */
.features-list {
  @apply space-y-4;
}

.feature-item {
  @apply flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0 cursor-pointer hover:bg-gray-50 rounded-lg px-3;
}

.feature-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center text-white;
}

.services-icon {
  @apply bg-emerald-500;
}

.language-icon {
  @apply bg-blue-500;
}

.discount-icon {
  @apply bg-orange-500;
}

.affiliate-icon {
  @apply bg-amber-500;
}

.top-users-icon {
  @apply bg-pink-500;
}

.feature-content {
  @apply flex-1 ml-3;
}

.feature-name {
  @apply text-sm font-medium text-gray-800;
}

.feature-status {
  @apply text-xs text-gray-500;
}

.feature-status.active {
  @apply text-green-500;
}

.feature-action {
  @apply text-gray-400;
}

/* Currency Settings */
.currency-setting {
  @apply flex items-center justify-between py-3 cursor-pointer hover:bg-gray-50 rounded-lg px-3;
}

.setting-label {
  @apply text-sm font-medium text-gray-800;
}

.setting-value {
  @apply flex items-center text-sm text-gray-600;
}

.currency-code {
  @apply font-medium text-gray-800 ml-2;
}

.currency-arrow {
  @apply ml-2 text-gray-400;
}

/* Form Styles */
.form-group {
  @apply mb-6 last:mb-0;
}

.form-row {
  @apply flex flex-col md:flex-row gap-6;
}

.form-col {
  @apply flex-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-4 py-2 border border-gray-200 rounded-lg text-sm focus:border-[var(--primary)] focus:ring-1 focus:ring-[var(--primary)] outline-none;
}

.form-textarea {
  @apply w-full px-4 py-2 border border-gray-200 rounded-lg text-sm focus:border-[var(--primary)] focus:ring-1 focus:ring-[var(--primary)] outline-none min-h-[100px] resize-none;
}

.form-error {
  @apply text-red-500 text-xs mt-1;
}



/* Responsive Adjustments */
@media (max-width: 768px) {
  .settings-section {
    @apply p-4;
  }

  .feature-item {
    @apply px-2;
  }

  .feature-icon {
    @apply w-10 h-10;
  }
}
