<!-- Global loading component -->
<app-global-loading></app-global-loading>

<!-- Loading overlay while translations are loading -->
<!-- <app-loading
  *ngIf="!isTranslationsLoaded"
  [fullScreen]="true"
  [message]="'Loading translations...'"
  size="lg">
</app-loading> -->

<!-- Only show content when translations are loaded -->
<!-- <div [style.visibility]="isTranslationsLoaded ? 'visible' : 'hidden'"> -->
  <router-outlet></router-outlet>
<!-- </div> -->