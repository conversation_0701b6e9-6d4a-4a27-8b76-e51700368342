# Fixed Partial Refund Logic - Correct Business Logic Implementation

## 🎯 Problem Fixed

**Your Requirements:**
1. **First partial remains = 30**: <PERSON><PERSON><PERSON> tiền cho 30 items (30 * price / 1000)
2. **Second partial remains = 20**: <PERSON>r<PERSON> lại tiền chênh lệch (30-20) * price / 1000
3. **If remains increase 20 → 30**: <PERSON><PERSON><PERSON> thêm tiền chênh lệch (30-20) * price / 1000

**New Logic Solution:**
- First partial: Refund for current remains
- Subsequent partials: Calculate difference and refund/deduct accordingly

## 📊 Detailed Example Walkthrough

### Initial Order Setup
```
Order ID: 12345
Quantity: 50 items
Price: $5.00 per 1000 items
Total Charge: $0.25 (50 * $5.00 / 1000)
Status: IN_PROGRESS
Remains: 0
```

### Step 1: First Partial (remains = 30)

**Calculation (First Partial Logic):**
```
No previous partials → Refund for current remains
Refund amount = 30 * $5.00 / 1000 = $0.15
```

**Result:**
- Order Status: PARTIAL
- Order Remains: 30
- User Balance: +$0.15
- Transaction: type='Partial', change=+$0.15, note="Partial refund for order #12345 (remains: 30): $0.15"

### Step 2: Second Partial (remains = 20)

**Calculation (Subsequent Partial Logic):**
```
Previous remains = 30 (from transaction note)
Current remains = 20
Difference = 20 - 30 = -10 (remains decreased)
Deduct amount = 10 * $5.00 / 1000 = $0.05 (negative adjustment)
```

**Result:**
- Order Status: PARTIAL
- Order Remains: 20
- User Balance: -$0.05 (deducted)
- Transaction: type='Partial', change=-$0.05, note="Partial deduction for order #12345 (remains: 20): $0.05"

### Step 3: Third Partial (remains = 25)

**Calculation (Subsequent Partial Logic):**
```
Previous remains = 20 (from transaction note)
Current remains = 25
Difference = 25 - 20 = +5 (remains increased)
Refund amount = 5 * $5.00 / 1000 = $0.025 (positive adjustment)
```

**Result:**
- Order Status: PARTIAL
- Order Remains: 25
- User Balance: +$0.025 (refunded)
- Transaction: type='Partial', change=+$0.025, note="Partial refund for order #12345 (remains: 25): $0.025"

## 🔧 Technical Implementation

### Key Changes Made:

1. **Completely rewrote `calculatePartialAdjustmentAmount()` method:**
   - First partial: Refund for current remains (remains * price / 1000)
   - Subsequent partials: Calculate difference (current - previous) and return positive/negative amount
   - Positive = refund more, Negative = deduct back

2. **Updated `processPartialRefund()` method:**
   - Handles both positive (refund) and negative (deduct) adjustments
   - Uses `addBalanceWithCustomType()` for refunds
   - Uses `deductBalanceWithCustomType()` for deductions
   - Includes balance validation for deductions

3. **Added `deductBalanceWithCustomType()` helper method:**
   - Safely deducts balance with custom transaction type
   - Creates negative transaction records for proper audit trail

### Business Logic Flow:
```java
if (previousPartialTransactions.isEmpty()) {
    // First partial: refund for current remains
    return remains * price / 1000;
} else {
    // Subsequent: calculate difference
    int difference = currentRemains - previousRemains;
    return difference * price / 1000; // positive or negative
}
```

### Transaction Note Formats:
```
// For refunds (positive amounts)
"Partial refund for order #[ORDER_ID] (remains: [CURRENT_REMAINS]): $[AMOUNT]"

// For deductions (negative amounts)
"Partial deduction for order #[ORDER_ID] (remains: [CURRENT_REMAINS]): $[AMOUNT]"
```

## 🧪 Testing Your Exact Scenarios

### Scenario 1: quantity=50, first partial remains=30, second remains=20
```java
GOrder order = createTestOrder(50, new BigDecimal("5.00")); // 50 items, $5 per 1000

// First partial: remains = 30
order.setRemains(30);
order.setStatus(OrderStatus.PARTIAL);
balanceService.processRefund(order); // Refunds 30 * $5 / 1000 = $0.15

// Second partial: remains = 20
order.setRemains(20);
balanceService.processRefund(order); // Deducts (20-30) * $5 / 1000 = -$0.05
```

### Scenario 2: quantity=50, first partial remains=20, second remains=30
```java
GOrder order = createTestOrder(50, new BigDecimal("5.00")); // 50 items, $5 per 1000

// First partial: remains = 20
order.setRemains(20);
order.setStatus(OrderStatus.PARTIAL);
balanceService.processRefund(order); // Refunds 20 * $5 / 1000 = $0.10

// Second partial: remains = 30
order.setRemains(30);
balanceService.processRefund(order); // Refunds (30-20) * $5 / 1000 = +$0.05
```

## ✅ Benefits of the Correct Implementation

1. **Exact Business Logic:** Matches your requirements perfectly
2. **Bidirectional Adjustments:** Handles both refunds and deductions
3. **Balance Validation:** Prevents insufficient balance errors
4. **Complete Audit Trail:** All adjustments logged with proper transaction types
5. **Error Prevention:** Validates balance before deductions

## 🛡️ Edge Cases Handled

1. **First Partial:** Always refunds for current remains
2. **Insufficient Balance:** Throws exception when user can't afford deduction
3. **Zero Difference:** No adjustment when remains don't change
4. **Note Parsing Failure:** Gracefully handles with zero adjustment
5. **Multiple Adjustments:** Correctly tracks through all partial changes

## 🎯 Summary

The logic now works exactly as you specified:
- **Lần đầu partial remains = 30**: Hoàn tiền 30 * price / 1000 ✅
- **Lần thứ 2 remains = 20**: Trừ lại (30-20) * price / 1000 ✅
- **Nếu remains tăng 20 → 30**: Cộng thêm (30-20) * price / 1000 ✅
