# User Notification System Implementation

## Tổng quan
Đã tạo thành công hệ thống thông báo cho `@smm-dashboard/` tương tự như `@smm-admin/`, tối ưu code bằng cách tái sử dụng logic từ `panel_notification`.

## Cấu trúc Backend (@smm-system)

### 1. Entity
- **UserNotification.java**: Entity chính extend AbstractTenantEntity
  - Tự động xử lý tenant_id thông qua AbstractTenantEntity
  - Các enum: NotificationType (INFO, SUCCESS, WARNING, ERROR)
  - Các enum: NotificationCategory (SYSTEM, RENEWAL, SETUP, ORDER, BALANCE)

### 2. Repository
- **UserNotificationRepository.java**: Extend TenantAwareRepository
  - Tự động filter theo tenant
  - Các query methods cho unread notifications, count, etc.

### 3. Service Layer
- **UserNotificationService.java**: Interface định nghĩa các methods
- **UserNotificationServiceImpl.java**: Implementation tái sử dụng logic từ PanelNotificationService
  - Sử dụng Redis để cache read status
  - Tự động lấy current user từ AuthenticationFacade
  - Các helper methods để tạo notifications cho order, balance, system

### 4. Controller
- **UserNotificationController.java**: REST API endpoints
  - GET `/user-notifications` - Lấy danh sách notifications (có phân trang)
  - GET `/user-notifications/unread` - Lấy notifications chưa đọc
  - GET `/user-notifications/unread/count` - Đếm số notifications chưa đọc
  - POST `/user-notifications/{id}/read` - Đánh dấu đã đọc
  - POST `/user-notifications/read-all` - Đánh dấu tất cả đã đọc

### 5. DTOs và Mappers
- **UserNotificationReq.java**: Request DTO
- **UserNotificationRes.java**: Response DTO  
- **UserNotificationMapper.java**: MapStruct mapper

## Cấu trúc Frontend (@smm-dashboard)

### 1. Models
- **user-notification-res.model.ts**: TypeScript interfaces cho request/response

### 2. Services
- **user-notification.service.ts**: Service tương tự PanelNotificationService
  - Auto-polling unread count mỗi 30 giây
  - Observable patterns cho reactive updates
  - HTTP client methods cho tất cả API calls

### 3. Components
- **UserNotificationDropdownComponent**: Component dropdown thông báo
  - Hiển thị bell icon với badge số lượng chưa đọc
  - Dropdown menu với danh sách notifications
  - Filter giữa "Tất cả" và "Chưa đọc"
  - Mark as read functionality
  - Responsive design

### 4. Integration
- **HeaderComponent**: Đã được cập nhật để sử dụng UserNotificationDropdownComponent
- Thay thế `<app-notification>` bằng `<app-user-notification-dropdown>`

## Database Schema

### Bảng user_notification
```sql
CREATE TABLE user_notification (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    user_id BIGINT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'INFO',
    category VARCHAR(20) NOT NULL DEFAULT 'SYSTEM',
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Indexes
- `idx_user_notification_tenant_id`
- `idx_user_notification_user_id`
- `idx_user_notification_is_read`
- `idx_user_notification_created_at`
- `idx_user_notification_tenant_user`
- `idx_user_notification_tenant_unread`

## Tối ưu hóa Code

### 1. Tái sử dụng Logic
- UserNotificationService tái sử dụng patterns từ PanelNotificationService
- Cùng cấu trúc Redis caching
- Cùng authentication patterns với AuthenticationFacade

### 2. Consistency
- Cùng naming conventions
- Cùng error handling patterns
- Cùng response structures

### 3. Performance
- Database indexes được tối ưu
- Redis caching cho read status
- Pagination support
- Auto-polling với reasonable intervals

## Cách sử dụng

### Tạo notification từ code
```java
@Autowired
private UserNotificationService userNotificationService;

// Tạo notification cho order
userNotificationService.createOrderNotification(user, "Đơn hàng #123 đã được tạo thành công");

// Tạo notification cho balance
userNotificationService.createBalanceNotification(user, "Số dư đã được cộng thêm $10.00");

// Tạo system notification
userNotificationService.createSystemNotification(user, "Hệ thống sẽ bảo trì vào 2AM");
```

### Frontend Usage
Component tự động được tích hợp vào header, không cần thêm code.

## Testing
- Unit tests cho Controller đã được tạo
- Integration tests có thể được thêm cho Service layer

## Migration
- File migration SQL đã được tạo: `V1__Create_user_notification_table.sql`
- Chạy migration để tạo bảng và indexes

## Next Steps
1. Chạy migration để tạo database table
2. Test API endpoints
3. Kiểm tra frontend integration
4. Thêm notifications vào các business logic khác (order creation, balance changes, etc.)
