<div class="link-content">
  <div class="text-gray-500 font-medium text-[14px] flex items-center gap-2">
    ID: {{ transaction.order?.id }} | {{ transaction.created_at | date:'yyyy-MM-dd' }} | {{ transaction.created_at | date:'HH:mm:ss' }}
  </div>
  <div class="description-row text-[13px] font-semibold" *ngIf="transaction.order?.service">
    <div class="tag">{{ transaction.order?.service?.id }}</div>
    <span class="">{{ transaction.order?.service?.name }} - <span class="text-red-500">-{{ transaction.order?.service?.price | currencyConvert }}</span></span>
  </div>
  <div class="link text-[13px] font-semibold" *ngIf="transaction.order?.link">
    <a *ngIf="transaction.order?.link" [href]="transaction.order?.link" target="_blank">{{ transaction.order?.link }}</a>
  </div>
</div>
