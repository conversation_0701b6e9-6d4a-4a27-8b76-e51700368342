.panel-layout-container {
  @apply max-w-full mx-auto;
}

/* Table styling */
table {
  @apply w-full text-sm text-left text-gray-900;
}

thead {
  @apply text-xs text-gray-500;
}

th, td {
  @apply px-6 py-3;
}

tbody tr {
  @apply border-b border-gray-200;
}

/* Status badge styling */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

/* Mobile card view styling */
.mobile-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.mobile-card-header {
  @apply flex justify-between items-center p-4 border-b border-gray-100;
}

.mobile-card-body {
  @apply p-4 space-y-3;
}

.mobile-card-footer {
  @apply px-4 py-3 bg-gray-50 flex justify-end;
}

.mobile-card-item {
  @apply flex items-center;
}

.mobile-card-icon {
  @apply text-gray-400 w-5;
}

.mobile-card-text {
  @apply ml-2 text-sm text-gray-700;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layout-container {
    @apply px-4;
  }
}


