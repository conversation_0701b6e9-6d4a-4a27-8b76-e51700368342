#!/bin/sh
# Certbot Request Watcher Script
# This script monitors the /etc/letsencrypt/renewal-requests directory for request files
# and generates SSL certificates using certbot

REQUESTS_DIR="/etc/letsencrypt/renewal-requests"
PROCESSED_DIR="${REQUESTS_DIR}/processed"
LOG_FILE="/var/log/letsencrypt/request-watcher.log"

# Create directories if they don't exist
mkdir -p "${REQUESTS_DIR}"
mkdir -p "${PROCESSED_DIR}"
mkdir -p "$(dirname ${LOG_FILE})"
touch "${LOG_FILE}"

log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "${LOG_FILE}"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

process_request_file() {
  local request_file="$1"
  local filename=$(basename "${request_file}")

  log "Processing request file: ${filename}"

  # Extract domain from filename (remove .json extension)
  # Make sure to remove ALL 'success-' or 'failure-' prefixes that might be in the filename
  local domain=$(echo "${filename}" | sed 's/\.json$//' | sed 's/^success-//g' | sed 's/^failure-//g')

  # Check if the request file is valid JSON
  if ! jq . "${request_file}" > /dev/null 2>&1; then
    log "Error: Invalid JSON in request file ${filename}"
    mv "${request_file}" "${PROCESSED_DIR}/${filename}.invalid.$(date '+%s')"
    return 1
  fi

  # Extract email from JSON if available
  local email="<EMAIL>"
  if command -v jq > /dev/null 2>&1; then
    local json_email=$(jq -r '.email // empty' "${request_file}")
    if [ -n "${json_email}" ]; then
      email="${json_email}"
    fi
  fi

  # Validate domain name length according to DNS standards (RFC 1035)
  # Each label (part between dots) must be 63 characters or less
  local is_valid=true
  local max_label_length=63
  local parts=$(echo "${domain}" | tr '.' ' ')

  for part in $parts; do
    if [ ${#part} -gt $max_label_length ]; then
      log "Error: Domain label '${part}' exceeds maximum length of ${max_label_length} characters"
      log "Full domain: ${domain}"
      is_valid=false
      break
    fi
  done

  # Also check total domain length (max 255 characters)
  if [ ${#domain} -gt 255 ]; then
    log "Error: Total domain name length (${#domain}) exceeds maximum of 255 characters"
    log "Domain: ${domain}"
    is_valid=false
  fi

  if [ "$is_valid" = false ]; then
    log "Skipping invalid domain: ${domain}"
    mv "${request_file}" "${PROCESSED_DIR}/${filename}.invalid.$(date '+%s')"
    # Create a failure marker file that domain-manager can detect
    echo "{\"success\":false,\"domain\":\"${domain}\",\"timestamp\":\"$(date -Iseconds)\",\"error\":\"Invalid domain name (exceeds length limits)\"}" > "/etc/letsencrypt/renewal-requests/failure-${domain}.json"
    return 1
  fi

  log "Generating certificate for domain: ${domain} with email: ${email}"

  # Check if certificate already exists
  if [ -d "/etc/letsencrypt/live/${domain}" ]; then
    log "Certificate already exists for ${domain}, attempting renewal"
    certbot renew --cert-name "${domain}" >> "${LOG_FILE}" 2>&1
    result=$?
  else
    log "Generating new certificate for ${domain}"

    # Check if webroot directory exists and is accessible
    if [ ! -d "/var/www/certbot" ]; then
      log "Error: Webroot directory /var/www/certbot does not exist"
      mkdir -p /var/www/certbot
      log "Created webroot directory /var/www/certbot"
    fi

    # Check permissions on webroot directory
    ls -la /var/www/certbot >> "${LOG_FILE}" 2>&1
    log "Webroot directory permissions listed above"

    # Create a test file to verify webroot is working
    echo "Certbot test file" > "/var/www/certbot/test.txt"
    log "Created test file at /var/www/certbot/test.txt"

    # Run certbot with dry-run first to check configuration
    log "Running certbot with --dry-run to test configuration"
    certbot certonly --webroot -w /var/www/certbot \
      --agree-tos --non-interactive \
      --email "${email}" \
      -d "${domain}" --dry-run -v >> "${LOG_FILE}" 2>&1

    # Run certbot to generate the certificate with verbose output
    log "Running certbot to generate certificate"
    certbot certonly --webroot -w /var/www/certbot \
      --agree-tos --non-interactive \
      --email "${email}" \
      --preferred-challenges http \
      -d "${domain}" -v >> "${LOG_FILE}" 2>&1
    result=$?

    # Log the result with more details
    if [ $result -ne 0 ]; then
      log "Certbot failed with exit code $result. Check logs for details."
      # Try to get more detailed error information
      tail -n 50 /var/log/letsencrypt/letsencrypt.log >> "${LOG_FILE}" 2>&1
    fi
  fi

  if [ $result -eq 0 ]; then
    log "Certificate operation successful for ${domain}"

    # Create a success marker file that domain-manager can detect
    # Make sure we're using a clean domain name (no prefixes)
    local clean_domain=$(echo "${domain}" | sed 's/^success-//g' | sed 's/^failure-//g')
    echo "{\"success\":true,\"domain\":\"${clean_domain}\",\"timestamp\":\"$(date -Iseconds)\"}" > "/etc/letsencrypt/renewal-requests/success-${clean_domain}.json"

    # Move the processed file to the processed directory
    mv "${request_file}" "${PROCESSED_DIR}/${filename}.success.$(date '+%s')"
    log "Request file processed successfully and moved to processed directory"
  else
    log "Failed to generate/renew certificate for ${domain} (exit code: $result)"

    # Create a failure marker file that domain-manager can detect
    # Make sure we're using a clean domain name (no prefixes)
    local clean_domain=$(echo "${domain}" | sed 's/^success-//g' | sed 's/^failure-//g')
    echo "{\"success\":false,\"domain\":\"${clean_domain}\",\"timestamp\":\"$(date -Iseconds)\",\"error\":\"Certbot failed with exit code $result\"}" > "/etc/letsencrypt/renewal-requests/failure-${clean_domain}.json"

    # Move the processed file to the processed directory
    mv "${request_file}" "${PROCESSED_DIR}/${filename}.failed.$(date '+%s')"
    log "Request file processed with errors and moved to processed directory"
  fi
}

cleanup_old_files() {
  # Remove processed files older than 7 days
  find "${PROCESSED_DIR}" -type f -mtime +7 -delete

  # Log the cleanup
  log "Cleaned up old processed request files"
}

log "Starting Certbot request watcher..."

# Main loop
while true; do
  # Check for request files
  for request_file in "${REQUESTS_DIR}"/*.json; do
    if [ -f "${request_file}" ]; then
      process_request_file "${request_file}"
    fi
  done

  # Cleanup old files once a day (every 86400 seconds)
  if [ $(date '+%H:%M') = "00:00" ]; then
    cleanup_old_files
  fi

  # Sleep for a short time before checking again
  sleep 5
done
