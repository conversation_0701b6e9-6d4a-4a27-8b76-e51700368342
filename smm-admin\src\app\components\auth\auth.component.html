
<!-- Hero Section with Login Form -->
<section class="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0">
    <div class="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
    <div class="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
  </div>

  <div class="relative container mx-auto px-4 py-16">
    <div class="grid lg:grid-cols-2 gap-12 items-center min-h-screen">
      <!-- Mobile-First: Login Form (appears first on mobile) -->
      <div class="flex justify-center lg:justify-end order-1 lg:order-2">
        <div class="w-full max-w-md">
          <div class="bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl p-8 border border-white/20">
            <div class="text-center mb-8">
              <h2 class="text-2xl font-bold text-gray-900 mb-2">
                {{ 'auth.welcome_back' | translate }}
              </h2>
              <p class="text-gray-600">
                {{ 'auth.sign_in_to_continue' | translate }}
              </p>
            </div>

            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6">
              <!-- Email Field -->
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ 'auth.username' | translate }}
                </label>
                <input
                  id="email"
                  type="email"
                  formControlName="email"
                  placeholder="{{ 'auth.enter_username' | translate }}"
                  class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 backdrop-blur-sm"
                  [ngClass]="{'border-red-500 focus:ring-red-500': email.invalid && (email.dirty || email.touched)}">
                <div *ngIf="email.invalid && (email.dirty || email.touched)" class="text-red-500 text-sm mt-1">
                  <div *ngIf="email.errors?.['required']">{{ 'auth.username_required' | translate }}</div>
                  <div *ngIf="email.errors?.['email']">{{ 'auth.username_invalid' | translate }}</div>
                </div>
              </div>

              <!-- Password Field -->
              <div>
                <div class="flex justify-between items-center mb-2">
                  <label for="password" class="block text-sm font-medium text-gray-700">
                    {{ 'auth.password' | translate }}
                  </label>
                  <a href="#" class="text-blue-600 text-sm hover:text-blue-500 transition-colors">
                    {{ 'auth.forgot_password' | translate }}
                  </a>
                </div>
                <input
                  id="password"
                  type="password"
                  formControlName="password"
                  placeholder="{{ 'auth.enter_password' | translate }}"
                  class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 backdrop-blur-sm"
                  [ngClass]="{'border-red-500 focus:ring-red-500': password.invalid && (password.dirty || password.touched)}">
                <div *ngIf="password.invalid && (password.dirty || password.touched)" class="text-red-500 text-sm mt-1">
                  <div *ngIf="password.errors?.['required']">{{ 'auth.password_required' | translate }}</div>
                </div>
              </div>

              <!-- Remember Me Checkbox -->
              <div class="flex items-center">
                <input
                  id="remember-me"
                  type="checkbox"
                  formControlName="rememberMe"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                  {{ 'auth.remember_me' | translate }}
                </label>
              </div>

              <!-- Error Message -->
              <div *ngIf="loginError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm animate-shake">
                {{ loginError }}
              </div>

              <!-- Login Button -->
              <button
                type="submit"
                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium text-lg shadow-lg"
                [disabled]="loginForm.invalid || isLoading"
                [ngClass]="{'opacity-50 cursor-not-allowed': loginForm.invalid || isLoading}">
                <span *ngIf="!isLoading">{{ 'auth.login' | translate }}</span>
                <span *ngIf="isLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ 'auth.logging_in' | translate }}
                </span>
              </button>

              <!-- Register Link -->
              <div class="text-center">
                <p class="text-sm text-gray-600">
                  {{ 'auth.no_account' | translate }}
                  <a routerLink="/auth/register" class="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                    {{ 'auth.register_now' | translate }}
                  </a>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Content Section (appears second on mobile) -->
      <div class="space-y-8 order-2 lg:order-1">
        <div class="space-y-6">
          <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
            Create your own
            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              SMM panel
            </span>
          </h1>
          <p class="text-xl text-gray-600 leading-relaxed">
            {{ 'landing.hero.subtitle' | translate }}
          </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-2 gap-6">
          <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover-lift">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Ultra fast</h3>
            <p class="text-sm text-gray-600">Almost speed of light loading and order sync</p>
          </div>

          <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover-lift">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Cool functions</h3>
            <p class="text-sm text-gray-600">Updates every month with new features</p>
          </div>

          <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover-lift">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Hot design</h3>
            <p class="text-sm text-gray-600">Far ahead of others with exceptional quality</p>
          </div>

          <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover-lift">
            <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Ecosystem</h3>
            <p class="text-sm text-gray-600">Market transparency with top providers</p>
          </div>
        </div>

        <!-- Stats -->
        <div class="flex space-x-8">
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900">105K+</div>
            <div class="text-sm text-gray-600">Panels created</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900">84M+</div>
            <div class="text-sm text-gray-600">Orders completed</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900">24/7</div>
            <div class="text-sm text-gray-600">Support</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Launch SMM Panel Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4">
    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <!-- Left Side - Content -->
      <div class="space-y-8">
        <div class="space-y-6">
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
            Launch SMM panel
            <span class="text-blue-600">in 5 minutes</span>
          </h2>
          <p class="text-xl text-gray-600">
            No code solution with everything you need to start your SMM business
          </p>
        </div>

        <!-- Features List -->
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Payment systems for every taste</span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Localization in 20+ languages</span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Child Panel & Drip Feed features</span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Ultimate solutions out of the box</span>
          </div>
        </div>

        <button routerLink="/auth/register" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
          Start now
        </button>
      </div>

      <!-- Right Side - Dashboard Preview -->
      <div class="relative">
        <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-3xl p-8 shadow-2xl">
          <!-- Dashboard Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="text-white font-semibold">YOURPANEL</div>
            <div class="flex items-center space-x-2">
              <div class="bg-green-500 text-white px-3 py-1 rounded-full text-sm">Pro</div>
              <div class="text-green-400 text-sm">Discount: 12%</div>
            </div>
          </div>

          <!-- Balance Section -->
          <div class="bg-gray-800 rounded-2xl p-6 mb-6">
            <div class="text-white text-3xl font-bold mb-2">500 $</div>
            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">Add funds</button>
          </div>

          <!-- Navigation -->
          <div class="grid grid-cols-4 gap-2 mb-6">
            <button class="bg-gray-700 text-white py-2 px-3 rounded-lg text-sm">New order</button>
            <button class="bg-gray-700 text-white py-2 px-3 rounded-lg text-sm">My orders</button>
            <button class="bg-gray-700 text-white py-2 px-3 rounded-lg text-sm">Services</button>
            <button class="bg-gray-700 text-white py-2 px-3 rounded-lg text-sm">API</button>
          </div>

          <!-- Services List -->
          <div class="space-y-3">
            <div class="bg-gray-700 rounded-lg p-4 flex justify-between items-center">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 6.728-.896 6.728-.302 1.507-1.123 1.777-1.123 1.777s-.821.27-1.777-.302L9.64 13.8l-1.777-2.302 2.302-1.777 3.555 2.632 1.777-2.302s.572-.572 1.777.302c.906.657.896 1.507.896 1.507z"/>
                  </svg>
                </div>
                <span class="text-white text-sm">Telegram Subscribers</span>
              </div>
              <div class="text-right">
                <div class="text-white font-semibold">0.3 $</div>
                <div class="text-gray-400 text-xs">0.6 per 1000</div>
              </div>
            </div>

            <div class="bg-gray-700 rounded-lg p-4 flex justify-between items-center">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <span class="text-white text-sm">Premium Votes Asian</span>
              </div>
              <div class="text-right">
                <div class="text-white font-semibold">1 $</div>
                <div class="text-gray-400 text-xs">1.3 per 1000</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Payment Methods Section -->
<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4 text-center">
    <h2 class="text-4xl font-bold text-gray-900 mb-4">Payment systems for every taste</h2>
    <p class="text-xl text-gray-600 mb-12">200+ payment methods supported</p>

    <!-- Payment Icons -->
    <div class="flex flex-wrap justify-center items-center gap-8 mb-12">
      <div class="bg-white rounded-2xl p-6 shadow-lg">
        <div class="text-2xl font-bold text-gray-900">USDT</div>
      </div>
      <div class="bg-white rounded-2xl p-6 shadow-lg">
        <div class="text-lg font-semibold text-gray-900">Perfect Money</div>
      </div>
      <div class="bg-white rounded-2xl p-6 shadow-lg">
        <div class="text-lg font-semibold text-gray-900">Payeer</div>
      </div>
      <div class="bg-white rounded-2xl p-6 shadow-lg">
        <div class="text-lg font-semibold text-gray-900">Binance Pay</div>
      </div>
    </div>

    <div class="text-gray-600 mb-8">and much more...</div>
  </div>
</section>

<!-- Design Templates Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 text-center">
    <h2 class="text-4xl font-bold text-gray-900 mb-4">Choose from ready designs</h2>
    <p class="text-xl text-gray-600 mb-12">Multiple layout styles with 8+ color themes and infinite customization</p>

    <!-- Header Styles -->
    <div class="mb-16">
      <h3 class="text-2xl font-bold text-gray-900 mb-8">Header Styles</h3>
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Standard Header -->
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-4 mb-4">
            <div class="flex justify-between items-center mb-2">
              <div class="flex space-x-2">
                <div class="w-8 h-2 bg-blue-500 rounded"></div>
                <div class="w-6 h-2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex space-x-1">
                <div class="w-4 h-2 bg-green-500 rounded"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
              </div>
            </div>
            <div class="h-12 bg-gray-100 rounded"></div>
          </div>
          <h3 class="font-semibold text-gray-900">Standard</h3>
          <p class="text-sm text-gray-600">Classic header layout</p>
        </div>

        <!-- Compact Header -->
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-4 mb-4">
            <div class="flex justify-between items-center mb-2">
              <div class="flex space-x-1">
                <div class="w-6 h-1.5 bg-green-500 rounded text-xs"></div>
                <div class="w-5 h-1.5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex space-x-1">
                <div class="w-3 h-1.5 bg-green-500 rounded border"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
              </div>
            </div>
            <div class="h-12 bg-gray-100 rounded"></div>
          </div>
          <h3 class="font-semibold text-gray-900">Compact</h3>
          <p class="text-sm text-gray-600">Space-saving design</p>
        </div>

        <!-- Modern Header -->
        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-4 mb-4">
            <div class="flex justify-between items-center mb-2">
              <div class="flex space-x-2">
                <div class="w-4 h-4 bg-purple-500 rounded"></div>
                <div class="w-6 h-2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex space-x-1">
                <div class="w-4 h-2 bg-blue-500 rounded"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
              </div>
            </div>
            <div class="h-12 bg-gray-100 rounded shadow-sm"></div>
          </div>
          <h3 class="font-semibold text-gray-900">Modern</h3>
          <p class="text-sm text-gray-600">Contemporary style</p>
        </div>

        <!-- Minimal Header -->
        <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-4 mb-4">
            <div class="flex justify-between items-center mb-2">
              <div class="flex space-x-2">
                <div class="w-6 h-1 bg-gray-600 rounded border-b-2 border-gray-600"></div>
                <div class="w-4 h-1 bg-gray-300 rounded"></div>
              </div>
              <div class="flex space-x-1">
                <div class="w-4 h-1 bg-gray-500 rounded border-l-2 border-gray-500"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
              </div>
            </div>
            <div class="h-12 bg-gray-100 rounded"></div>
          </div>
          <h3 class="font-semibold text-gray-900">Minimal</h3>
          <p class="text-sm text-gray-600">Clean & simple</p>
        </div>
      </div>
    </div>

    <!-- Sidebar Styles -->
    <div class="mb-16">
      <h3 class="text-2xl font-bold text-gray-900 mb-8">Sidebar Layouts</h3>
      <div class="grid md:grid-cols-3 lg:grid-cols-5 gap-6">
        <!-- Standard Sidebar -->
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-4 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-3 mb-3">
            <div class="w-full h-1 bg-blue-500 rounded mb-2"></div>
            <div class="space-y-1">
              <div class="w-full h-1.5 bg-gray-300 rounded"></div>
              <div class="w-full h-1.5 bg-blue-500 rounded"></div>
              <div class="w-full h-1.5 bg-gray-300 rounded"></div>
            </div>
          </div>
          <h4 class="font-semibold text-gray-900 text-sm">Standard</h4>
        </div>

        <!-- Compact Sidebar -->
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-4 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-3 mb-3">
            <div class="w-full h-0.5 bg-green-500 rounded mb-2"></div>
            <div class="space-y-0.5">
              <div class="w-full h-1 bg-gray-300 rounded"></div>
              <div class="w-full h-1 bg-green-500 rounded"></div>
              <div class="w-full h-1 bg-gray-300 rounded"></div>
            </div>
          </div>
          <h4 class="font-semibold text-gray-900 text-sm">Compact</h4>
        </div>

        <!-- Modern Sidebar -->
        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-4 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-3 mb-3">
            <div class="w-full h-1 bg-purple-500 rounded mb-2"></div>
            <div class="space-y-1">
              <div class="w-full h-1.5 bg-gray-300 rounded"></div>
              <div class="w-full h-1.5 bg-purple-500 rounded border-l-2 border-purple-700"></div>
              <div class="w-full h-1.5 bg-gray-300 rounded"></div>
            </div>
          </div>
          <h4 class="font-semibold text-gray-900 text-sm">Modern</h4>
        </div>

        <!-- Minimal Sidebar -->
        <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-4 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-3 mb-3">
            <div class="w-full h-0.5 bg-gray-600 rounded mb-2"></div>
            <div class="space-y-0.5">
              <div class="w-full h-1 bg-gray-300 rounded"></div>
              <div class="w-full h-1 bg-gray-600 rounded"></div>
              <div class="w-full h-1 bg-gray-300 rounded"></div>
            </div>
          </div>
          <h4 class="font-semibold text-gray-900 text-sm">Minimal</h4>
        </div>

        <!-- Card Sidebar -->
        <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-4 shadow-lg hover-lift design-card">
          <div class="bg-white rounded-xl p-3 mb-3">
            <div class="grid grid-cols-2 gap-1">
              <div class="h-3 bg-gray-300 rounded"></div>
              <div class="h-3 bg-orange-500 rounded"></div>
              <div class="h-3 bg-gray-300 rounded"></div>
              <div class="h-3 bg-gray-300 rounded"></div>
            </div>
          </div>
          <h4 class="font-semibold text-gray-900 text-sm">Card</h4>
        </div>
      </div>
    </div>

    <!-- Color Themes -->
    <div class="mb-12">
      <h3 class="text-2xl font-bold text-gray-900 mb-8">Color Themes</h3>
      <div class="flex flex-wrap justify-center gap-4">
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#30B0C7] rounded-full"></div>
          <span class="text-sm font-medium">Ocean Blue</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#10B981] rounded-full"></div>
          <span class="text-sm font-medium">Emerald</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#3B82F6] rounded-full"></div>
          <span class="text-sm font-medium">Sky Blue</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#8B5CF6] rounded-full"></div>
          <span class="text-sm font-medium">Purple</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#EC4899] rounded-full"></div>
          <span class="text-sm font-medium">Pink</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#F59E0B] rounded-full"></div>
          <span class="text-sm font-medium">Orange</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#EF4444] rounded-full"></div>
          <span class="text-sm font-medium">Red</span>
        </div>
        <div class="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg">
          <div class="w-6 h-6 bg-[#6B7280] rounded-full"></div>
          <span class="text-sm font-medium">Gray</span>
        </div>
      </div>
    </div>

    <div class="text-center">
      <p class="text-lg text-gray-600 mb-6">Mix and match any header style, sidebar layout, and color theme</p>
      <button routerLink="/auth/register" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
        Create Your Custom Panel
      </button>
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4 text-center">
    <h2 class="text-4xl font-bold text-gray-900 mb-4">Don't waste your time</h2>
    <h3 class="text-3xl font-bold text-blue-600 mb-8">Just make money</h3>
    <p class="text-xl text-gray-600 mb-12">Professional SMM panel solution at an affordable price</p>

    <!-- Pricing Cards -->
    <div class="grid lg:grid-cols-2 gap-12 max-w-5xl mx-auto mb-16">
      <!-- Monthly Plan -->
      <div class="bg-white rounded-3xl p-8 shadow-xl border-2 border-gray-200 hover-lift">
        <div class="text-center mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Monthly Plan</h3>
          <div class="text-5xl font-bold text-blue-600 mb-2">$7.7</div>
          <p class="text-gray-600">per month</p>
        </div>

        <div class="space-y-4 mb-8">
          <div class="flex items-center space-x-3">
            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Complete SMM panel setup</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">SSL certificate & hosting included</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Multiple payment gateways</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">24/7 technical support</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Custom branding & design</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">API integration & documentation</span>
          </div>
        </div>

        <div class="text-center">
          <div class="text-sm text-gray-500 mb-4">Everything you need to start your SMM business</div>
          <button routerLink="/auth/register" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg">
            Start Your Panel
          </button>
        </div>
      </div>

      <!-- Value Proposition -->
      <div class="bg-gradient-to-br from-blue-600 to-purple-600 rounded-3xl p-8 shadow-xl text-white hover-lift">
        <div class="text-center mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Why Choose Us?</h3>
          <p class="text-blue-100">The most cost-effective SMM panel solution</p>
        </div>

        <div class="space-y-6 mb-8">
          <div class="bg-white/10 rounded-xl p-4">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold">Traditional Development</span>
              <span class="text-red-300">$2,000+</span>
            </div>
            <div class="text-sm text-blue-100">Custom development from scratch</div>
          </div>

          <div class="bg-white/10 rounded-xl p-4">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold">Hosting & Maintenance</span>
              <span class="text-red-300">$50+/month</span>
            </div>
            <div class="text-sm text-blue-100">Server costs and technical maintenance</div>
          </div>

          <div class="bg-white/20 rounded-xl p-4 border-2 border-white/30">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold">Our Solution</span>
              <span class="text-green-300 text-xl font-bold">$7.7/month</span>
            </div>
            <div class="text-sm text-blue-100">Everything included - Ready to use!</div>
          </div>
        </div>

        <div class="text-center">
          <div class="text-lg font-semibold mb-2">Save over $2,000 in development costs</div>
          <div class="text-sm text-blue-100 mb-4">Start earning immediately with our proven solution</div>
          <button routerLink="/auth/register" class="w-full bg-white text-blue-600 py-3 px-6 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-200 shadow-lg">
            Get Started Now
          </button>
        </div>
      </div>
    </div>

    <!-- Additional Benefits -->
    <div class="grid md:grid-cols-3 gap-8 mb-12">
      <div class="text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
          <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        <h4 class="font-semibold text-gray-900 mb-2">No Setup Fees</h4>
        <p class="text-gray-600 text-sm">Start immediately without any upfront costs</p>
      </div>

      <div class="text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h4 class="font-semibold text-gray-900 mb-2">Launch in 5 Minutes</h4>
        <p class="text-gray-600 text-sm">Quick setup and instant deployment</p>
      </div>

      <div class="text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
          <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h4 class="font-semibold text-gray-900 mb-2">Full Support</h4>
        <p class="text-gray-600 text-sm">24/7 technical assistance and guidance</p>
      </div>
    </div>

    <!-- CTA Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <button routerLink="/auth/register" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
        Start Your SMM Panel
      </button>
      <button class="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-200">
        View Demo
      </button>
    </div>
  </div>
</section>

<!-- Trusted Users Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 text-center">
    <h2 class="text-4xl font-bold text-gray-900 mb-4">We are trusted by</h2>
    <h3 class="text-3xl font-bold text-blue-600 mb-12">thousands of users every day</h3>

    <!-- Large Stats Display -->
    <div class="mb-16">
      <div class="text-8xl lg:text-9xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
        105K+M
      </div>
      <div class="text-xl text-gray-600">Happy customers worldwide</div>
    </div>

    <!-- Final CTA -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <button routerLink="/auth/register" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
        Get started
      </button>
      <button class="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-200">
        Join our community in Telegram
      </button>
    </div>
  </div>
</section>