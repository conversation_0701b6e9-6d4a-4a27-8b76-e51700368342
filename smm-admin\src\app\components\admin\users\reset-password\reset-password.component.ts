import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { UserAdminService } from '../../../../core/services/user-admin.service';
import { GUserPasswordRes } from '../../../../model/response/g-user-password-res.model';
import { ToastService } from '../../../../core/services/toast.service';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [CommonModule, FormsModule, IconsModule, TranslateModule],
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.css'
})
export class ResetPasswordComponent implements OnInit {
  @Input() userId: number = 0;
  @Input() userName: string = '';
  @Output() close = new EventEmitter<void>();

  newPassword: string = '';
  isLoading: boolean = false;
  isPasswordReset: boolean = false;
  errorMessage: string = '';

  constructor(private userAdminService: UserAdminService, private toastService: ToastService) {}

  ngOnInit(): void {
    this.resetUserPassword();
  }

  resetUserPassword(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.userAdminService.resetPassword(this.userId).subscribe({
      next: (response: GUserPasswordRes) => {
        this.newPassword = response.new_password;
        this.isPasswordReset = true;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error resetting password:', error);
        this.errorMessage = error?.message || 'Failed to reset password. Please try again.';
        this.toastService.showError(this.errorMessage);
        this.isLoading = false;
      }
    });
  }

  closeModal(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close the modal if the user clicks on the overlay (outside the modal content)
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }

  copyToClipboard(): void {
    navigator.clipboard.writeText(this.newPassword).then(() => {
      // Could show a toast notification here
      console.log('Password copied to clipboard');
      this.toastService.showSuccess('Password copied to clipboard');
    });
  }
}
