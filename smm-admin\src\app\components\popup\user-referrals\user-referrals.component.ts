import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { ReferralService } from '../../../core/services/referral.service';
import { ReferralRes, ReferralStatsRes } from '../../../model/response/referral-res.model';
import { GUserSuperRes } from '../../../model/response/g-user-super-res.model';

@Component({
  selector: 'app-user-referrals',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './user-referrals.component.html',
  styleUrls: ['./user-referrals.component.css']
})
export class UserReferralsComponent implements OnInit {
  @Input() userId: number = 0;
  @Input() userName: string = '';
  @Input() customReferralRate: number = 0;
  @Output() close = new EventEmitter<void>();

  referrals: ReferralRes[] = [];
  stats: ReferralStatsRes | null = null;
  isLoading = false;
  customRate: number = 0;
  isEditingRate = false;

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };

  constructor(private referralService: ReferralService) {}

  ngOnInit(): void {
    // Initialize customRate from the input property
    this.customRate = this.customReferralRate;

    // Create a minimal stats object with the custom rate
    this.stats = {
      total_referrals: 0,
      total_earnings: 0,
      custom_referral_rate: this.customReferralRate,
      default_referral_rate: 0
    };

    // Subscribe to loading state
    this.referralService.loading$.subscribe(loading => {
      this.isLoading = loading;
    });

    // Subscribe to referrals data
    this.referralService.referrals$.subscribe(referrals => {
      this.referrals = referrals;
    });

    // Subscribe to pagination data
    this.referralService.pagination$.subscribe(pagination => {
      this.pagination = pagination;
    });

    // Load referrals data
    if (this.userId) {
      this.loadReferrals(0);
    }
  }



  loadReferrals(page: number = 0): void {
    if (!this.userId) return;

    this.referralService.getUserReferrals(this.userId, page, this.pagination.pageSize).subscribe({
      error: (error: any) => {
        console.error('Error loading referrals:', error);
      }
    });
  }

  validateCustomRate(): void {
    // Ensure the rate is within valid range
    if (this.customRate < 0) {
      this.customRate = 0;
    } else if (this.customRate > 100) {
      this.customRate = 100;
    }

    // Ensure it's an integer
    this.customRate = Math.round(this.customRate);
  }

  saveCustomRate(): void {
    if (!this.userId) return;

    // Validate before saving
    this.validateCustomRate();

    this.referralService.updateCustomReferralRate(this.userId, this.customRate).subscribe({
      next: () => {
        // Update the stats object with the new custom rate
        if (this.stats) {
          this.stats.custom_referral_rate = this.customRate;
        }
        this.isEditingRate = false;
      },
      error: (error: any) => {
        console.error('Error updating custom referral rate:', error);
      }
    });
  }

  cancelEdit(): void {
    if (this.stats) {
      this.customRate = this.stats.custom_referral_rate;
    }
    this.isEditingRate = false;
  }

  onCloseClick(): void {
    this.close.emit();
  }
}
