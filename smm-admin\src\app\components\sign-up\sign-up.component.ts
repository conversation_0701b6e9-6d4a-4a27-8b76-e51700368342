import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import { ToastService } from '../../core/services/toast.service';
import { UserReq } from '../../model/request/user-req.model';
import { catchError, finalize } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { NotifyType } from '../../constant/notify-type';

@Component({
  selector: 'app-sign-up',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, RouterModule],
  templateUrl: './sign-up.component.html',
  styleUrl: './sign-up.component.css'
})
export class SignUpComponent {
  registrationForm: FormGroup;
  isLoading: boolean = false;
  registrationError: string = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private toastService: ToastService
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      username: ['', Validators.required],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', Validators.required],
      termsAgreed: [false, Validators.requiredTrue]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(formGroup: FormGroup) {
    const password = formGroup.get('password')?.value;
    const confirmPassword = formGroup.get('confirmPassword')?.value;

    if (password !== confirmPassword) {
      formGroup.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    return null;
  }

  onSubmit() {
    if (this.registrationForm.valid) {
      this.isLoading = true;
      this.registrationError = '';

      // Map form values to UserReq model
      const userReq: UserReq = {
        user_name: this.registrationForm.get('username')?.value,
        email: this.registrationForm.get('email')?.value,
        password: this.registrationForm.get('password')?.value,
        name: '', // Not collected in the form anymore
        phone: '' // Not collected in the form, but required by the API model
      };

      console.log('Registering user:', userReq);

      this.authService.register(userReq)
        .pipe(
          catchError(err => {
            console.error('Registration error:', err);
            // Set error message for display in the template
            this.registrationError = err.message || 'Registration failed. Please try again.';
            // Show toast notification
            this.toastService.showToast(this.registrationError, NotifyType.ERROR);
            return throwError(() => err);
          }),
          finalize(() => {
            this.isLoading = false;
          })
        )
        .subscribe({
          next: (user) => {
            console.log('Registration successful:', user);
            this.toastService.showToast('Registration successful!', NotifyType.SUCCESS);
            // Navigate to dashboard or home page
            this.router.navigate(['/panel/dashboard']);
          },
          error: (error) => {
            console.error('Registration subscription error:', error);
            // Error is already handled in the catchError operator
          }
        });
    } else {
      // Mark all form controls as touched to show validation errors
      Object.keys(this.registrationForm.controls).forEach(key => {
        const control = this.registrationForm.get(key);
        control?.markAsTouched();
      });
    }
  }

  login() {
    this.router.navigate(['/auth/login']);
  }
}
