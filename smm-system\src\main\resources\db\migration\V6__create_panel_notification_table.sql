-- Create panel_notification table for tenant-specific notifications
CREATE TABLE IF NOT EXISTS panel_notification (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    user_id BIGINT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'INFO', -- INFO, SUCCESS, WARNING, ERROR
    category VARCHAR(50) NOT NULL DEFAULT 'SYSTEM', -- SYSTEM, RENEWAL, SETUP
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_panel_notification_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenant(tenant_id) ON DELETE CASCADE,
    CONSTRAINT fk_panel_notification_user 
        FOREIGN KEY (user_id) REFERENCES g_user(id) ON DELETE SET NULL
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_panel_notification_tenant_id ON panel_notification(tenant_id);
CREATE INDEX idx_panel_notification_user_id ON panel_notification(user_id);
CREATE INDEX idx_panel_notification_is_read ON panel_notification(is_read);
CREATE INDEX idx_panel_notification_created_at ON panel_notification(created_at);
CREATE INDEX idx_panel_notification_category ON panel_notification(category);
