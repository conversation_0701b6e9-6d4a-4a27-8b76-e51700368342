import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../../core/services/loading.service';
import { LoadingComponent } from '../loading/loading.component';

@Component({
  selector: 'app-global-loading',
  standalone: true,
  imports: [CommonModule, LoadingComponent],
  template: `
    <app-loading 
      *ngIf="isLoading" 
      [fullScreen]="true" 
      [message]="loadingMessage"
      size="lg">
    </app-loading>
  `,
  styles: []
})
export class GlobalLoadingComponent implements OnInit, OnDestroy {
  isLoading = false;
  loadingMessage = '';
  private subscription = new Subscription();

  constructor(private loadingService: LoadingService) {}

  ngOnInit(): void {
    // Subscribe to loading state changes
    this.subscription.add(
      this.loadingService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to loading message changes
    this.subscription.add(
      this.loadingService.loadingMessage$.subscribe(message => {
        this.loadingMessage = message;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
