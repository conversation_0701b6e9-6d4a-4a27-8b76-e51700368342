import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { UserRes } from '../../model/response/user-res.model';
import { JwtHelperService } from '@auth0/angular-jwt';
import { isPlatformBrowser } from '@angular/common';
import { Role } from '../../constant/role';

@Injectable({
  providedIn: 'root'
})
export class AuthUtilsService {
  constructor(
    private jwtHelper: JwtHelperService,
  ) {}

  /**
   * L<PERSON>y thông tin người dùng từ localStorage
   */
  getUserFromStorage(): UserRes | null {

    const userStr = localStorage.getItem('user');
   // console.log('AuthUtilsService - User from localStorage:', userStr ? 'Found' : 'Not found');

    if (!userStr) return null;

    try {
      const user = JSON.parse(userStr) as UserRes;
      //console.log('AuthUtilsService - User parsed successfully, has token:', !!user?.tokens?.access_token);
      return user;
    } catch (e) {
      console.error('Error parsing user from localStorage', e);
      return null;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> tra xem người dùng có được xác thực không
   */
  isAuthenticated(): boolean {


    const user = this.getUserFromStorage();
    const isAuth = !!(user && user.tokens && user.tokens.access_token);

   // console.log('AuthUtilsService - isAuthenticated:', isAuth, 'URL:', window.location.pathname);

    return isAuth;
  }

  /**
   * Kiểm tra xem token có hết hạn không
   * Adds a buffer time to token expiration to refresh before it actually expires
   */
  isTokenExpired(): boolean {
    const user = this.getUserFromStorage();
    if (!user || !user.tokens || !user.tokens.access_token) {
      console.log('AuthUtilsService - isTokenExpired: No valid user or token, returning true');
      return true;
    }

    try {
      // Add a buffer time (5 minutes) to refresh token before it actually expires
      const tokenExpirationDate = this.jwtHelper.getTokenExpirationDate(user.tokens.access_token);
      const currentTime = new Date();
      const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

      if (!tokenExpirationDate) {
        console.log('AuthUtilsService - isTokenExpired: Could not determine token expiration date, returning true');
        return true;
      }

      // Check if token will expire within the buffer time
      const timeUntilExpiration = tokenExpirationDate.getTime() - currentTime.getTime();
      const isExpiringSoon = timeUntilExpiration < bufferTime;

      // Standard check for already expired tokens
      const isExpired = this.jwtHelper.isTokenExpired(user.tokens.access_token);

      console.log('AuthUtilsService - Token expiration:', tokenExpirationDate);
      console.log('AuthUtilsService - Time until expiration (ms):', timeUntilExpiration);
      console.log('AuthUtilsService - Is expired:', isExpired);
      console.log('AuthUtilsService - Is expiring soon:', isExpiringSoon);

      // Return true if token is expired or will expire soon
      return isExpired || isExpiringSoon;
    } catch (error) {
      console.error('AuthUtilsService - Error checking token expiration:', error);
      return true; // Assume expired if there's an error
    }
  }

  /**
   * Kiểm tra xem người dùng có quyền admin không
   */
  isAdmin(): boolean {
    const user = this.getUserFromStorage();
    if (!user || !user.tokens || !user.tokens.access_token) {
      console.log('AuthUtilsService - isAdmin: No valid user or token, returning false');
      return false;
    }

    try {
      const tokenPayload = this.jwtHelper.decodeToken(user.tokens.access_token);
      const roles = tokenPayload.roles;

      if (!roles) {
        console.log('AuthUtilsService - isAdmin: No roles in token payload, returning false');
        return false;
      }

      // Check if roles is an array and contains PANEL
      if (Array.isArray(roles) && roles.includes(Role.PANEL)) {
        console.log('AuthUtilsService - isAdmin: User has PANEL role, returning true');
        return true;
      }

      console.log('AuthUtilsService - isAdmin: User does not have ADMIN role, returning false');
      return false;
    } catch (error) {
      console.error('AuthUtilsService - Error checking admin role:', error);
      return false; // Assume not admin if there's an error
    }
  }

  /**
   * Thêm token vào header của request
   */
  addTokenHeader(request: any, user: UserRes) {
    // console.log('AuthUtilsService - Adding token header for URL:', request.url);
    // console.log('AuthUtilsService - Client ID:', user.tokens.client_id);
    // console.log('AuthUtilsService - Access Token:', user.tokens.access_token ? 'Present' : 'Missing');

    const modifiedRequest = request.clone({
      setHeaders: {
        'x-client-id': `${user.tokens.client_id}`,
        Authorization: `Bearer ${user.tokens.access_token}`,
      }
    });

    console.log('AuthUtilsService - Headers added successfully');
    return modifiedRequest;
  }
}
