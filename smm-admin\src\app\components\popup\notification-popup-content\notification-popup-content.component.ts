import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { CommonModule } from '@angular/common';
import { SafeHtml } from '@angular/platform-browser';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-notification-popup-content',
  standalone: true,
  imports: [TranslateModule, IconsModule, CommonModule],
  templateUrl: './notification-popup-content.component.html',
  styleUrl: './notification-popup-content.component.css'
})
export class NotificationPopupContentComponent {
  @Input() title: string = 'Notification';
  @Input() content: SafeHtml = '';
  @Input() notificationId: number | null = null;
  @Input() autoDismiss: boolean = false;
  @Input() dismissHours: number = 24;
  @Output() close = new EventEmitter<void>();
  @Output() dismiss = new EventEmitter<void>();

  constructor(private notificationService: NotificationService) {}

  onClose() {
    this.close.emit();
  }

  onDismiss() {
    if (this.autoDismiss && this.dismissHours && this.notificationId) {
      // Save to Redis with TTL
      this.notificationService.dismissNotificationWithTTL(this.notificationId, this.dismissHours).subscribe({
        next: () => {
          console.log(`Notification ${this.notificationId} dismissed for ${this.dismissHours} hours`);
        },
        error: (error) => {
          console.error('Error dismissing notification:', error);
        }
      });
    } else {
 this.dismiss.emit();
    }
   
  }
}
