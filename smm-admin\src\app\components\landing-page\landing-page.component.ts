import { Component, OnInit, HostListener, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { IconsModule } from '../../icons/icons.module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { SocialIconComponent } from '../common/social-icon/social-icon.component';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AuthUtilsService } from '../../core/services/auth-utils.service';

@Component({
  selector: 'app-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    TranslateModule,
    SocialIconComponent
  ],
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.css']
})
export class LandingPageComponent implements OnInit {
  // Language switcher
  currentLanguage = 'en';
  showLanguageDropdown = false;
  languages = [
    { code: 'en', name: 'English' },
    { code: 'vi', name: 'Tiếng Việt' }
  ];

  // Social media platforms
  socialPlatforms = [
    { name: 'Facebook', icon: 'facebook' as IconName },
    { name: 'Instagram', icon: 'instagram' as IconName },
    { name: 'Twitter', icon: 'twitter' as IconName },
    { name: 'YouTube', icon: 'youtube' as IconName },
    { name: 'Telegram', icon: 'telegram-plane' as IconName }
  ];

  // Service features
  serviceFeatures = [
    {
      icon: 'star' as IconName,
      title: 'Amazing quality',
      description: 'We monitor our services all the time. If quality does not match expectations, you will get full refund.'
    },
    {
      icon: 'dollar' as IconName,
      title: 'Affordable prices',
      description: 'Our panel works with direct suppliers only, you get cheapest prices from 400+ items.'
    },
    {
      icon: 'rocket' as IconName,
      title: 'High quality services',
      description: 'Convenient panel, great support and our API.'
    },
    {
      icon: 'phone' as IconName,
      title: '24/7 Support',
      description: 'Our support team is always ready to help you with any questions.'
    }
  ];

  constructor(
    private router: Router,
    private translateService: TranslateService,
    private authUtils: AuthUtilsService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    // Check if user is already authenticated and redirect if needed
    // if (isPlatformBrowser(this.platformId)) {
      if (this.authUtils.isAuthenticated() && !this.authUtils.isTokenExpired()) {
        console.log('LandingPage Component - User is already authenticated, redirecting to dashboard');
        this.router.navigate(['/panel/dashboard']);
        return;
      }
   // }

    // Get the browser language or use default
    const browserLang = this.translateService.getBrowserLang();
    const defaultLang = browserLang && ['en', 'vi'].includes(browserLang) ? browserLang : 'en';

    // Use the language from localStorage if available, otherwise use the default
    const savedLang = localStorage.getItem('language');
    this.currentLanguage = savedLang || defaultLang;

    // Set the current language
    this.translateService.use(this.currentLanguage);
  }

  // Toggle language dropdown
  toggleLanguageDropdown(): void {
    this.showLanguageDropdown = !this.showLanguageDropdown;
  }

  // Change language
  changeLanguage(langCode: string): void {
    this.currentLanguage = langCode;
    this.translateService.use(langCode);
    localStorage.setItem('language', langCode);
    this.showLanguageDropdown = false;
  }

  // Close language dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
      this.showLanguageDropdown = false;
    }
  }

  // Navigate to login page
  navigateToLogin() {
    this.router.navigate(['/auth/login']);
  }

  // Navigate to register page
  navigateToRegister() {
    this.router.navigate(['/auth/register']);
  }

  // Navigate to services page
  navigateToServices() {
    this.router.navigate(['/panel']);
  }

  // Navigate to dashboard
  navigateToDashboard() {
    this.router.navigate(['/panel']);
  }
}
