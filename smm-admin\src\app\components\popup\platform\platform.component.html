<div class="overlay-black" >
    <div class="layout-container">
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
        <div class="icon-container">
            <img src="https://dashboard.codeparrot.ai/api/image/Z-33zOGYgKEKiAYk/huge-ico.png" alt="Icon" />
        </div>
        <div class="header-section gap-4">
            <h1 class="title">Mạng xã hội</h1>
            <p class="description">G<PERSON>n mạng xã hội vào danh mục của bạn để khách hàng có thể dễ dàng điều hướng giữa các dịch vụ trong cửa
                hàng của bạn</p>
        </div>
        <div class="social-media-list">
            <div *ngFor="let item of socialMediaItems; let i = index" class="social-media-item">
                <div class="item-content">
                    <img src="https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/huge-ico-2.png" alt="more"
                        class="more-icon">
                    <img [src]="item.icon" [alt]="item.name" class="social-icon">
                    <span class="item-name">{{item.name}}</span>
                </div>
                <div class="item-actions">
                    <button class="action-btn" (click)="onEdit(i)">
                        <img src="https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/huge-ico-3.png" alt="edit"
                            class="action-icon">
                    </button>
                    <button class="action-btn" (click)="onDelete(i)">
                        <img src="https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/huge-ico-4.png"
                            alt="delete" class="action-icon">
                    </button>
                </div>
            </div>
        </div>

            <button class="add-social-media-btn">
                <img src="https://dashboard.codeparrot.ai/api/image/Z-340OGYgKEKiAYn/huge-ico-17.png" alt="Add Icon"
                    class="icon" />
                <span>Thêm mạng xã hội</span>
            </button>

    </div>
</div>