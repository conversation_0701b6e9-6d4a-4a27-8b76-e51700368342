<!-- MFA Verification Section -->
<section class="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0">
    <div class="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
    <div class="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
  </div>

  <div class="relative container mx-auto px-4 py-8">
    <div class="flex justify-center items-start min-h-screen pt-16">
      <div class="w-full max-w-md">
        <div class="bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl p-8 border border-white/20">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              {{ 'auth.mfa_verification' | translate }}
            </h2>
            <p class="text-gray-600">
              {{ 'auth.mfa_instruction' | translate }}
            </p>
          </div>

          <form [formGroup]="mfaForm" (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- MFA Code Field -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ 'auth.mfa_code' | translate }}</label>

              <div class="flex justify-between gap-2 mb-2">
                <input
                  #digit1
                  type="number"
                  min="0"
                  max="9"
                  (keyup)="onDigitInput($event, digit1, digit2)"
                  (input)="onDigitInput($event, digit1, digit2)"
                  (focus)="onFocus($event)"
                  (paste)="onPaste($event)"
                  class="mfa-digit-input"
                  placeholder=" "
                  [ngClass]="{'border-red-500': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}">

                <input
                  #digit2
                  type="number"
                  min="0"
                  max="9"
                  (keyup)="onDigitInput($event, digit2, digit3, digit1)"
                  (input)="onDigitInput($event, digit2, digit3, digit1)"
                  (focus)="onFocus($event)"
                  class="mfa-digit-input"
                  placeholder=" "
                  [ngClass]="{'border-red-500': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}">

                <input
                  #digit3
                  type="number"
                  min="0"
                  max="9"
                  (keyup)="onDigitInput($event, digit3, digit4, digit2)"
                  (input)="onDigitInput($event, digit3, digit4, digit2)"
                  (focus)="onFocus($event)"
                  class="mfa-digit-input"
                  placeholder=" "
                  [ngClass]="{'border-red-500': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}">

                <input
                  #digit4
                  type="number"
                  min="0"
                  max="9"
                  (keyup)="onDigitInput($event, digit4, digit5, digit3)"
                  (input)="onDigitInput($event, digit4, digit5, digit3)"
                  (focus)="onFocus($event)"
                  class="mfa-digit-input"
                  placeholder=" "
                  [ngClass]="{'border-red-500': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}">

                <input
                  #digit5
                  type="number"
                  min="0"
                  max="9"
                  (keyup)="onDigitInput($event, digit5, digit6, digit4)"
                  (input)="onDigitInput($event, digit5, digit6, digit4)"
                  (focus)="onFocus($event)"
                  class="mfa-digit-input"
                  placeholder=" "
                  [ngClass]="{'border-red-500': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}">

                <input
                  #digit6
                  type="number"
                  min="0"
                  max="9"
                  (keyup)="onDigitInput($event, digit6, null, digit5)"
                  (input)="onDigitInput($event, digit6, null, digit5)"
                  (focus)="onFocus($event)"
                  class="mfa-digit-input"
                  placeholder=" "
                  [ngClass]="{'border-red-500': mfaForm.get('code')?.invalid && mfaForm.get('code')?.touched}">
              </div>

              <!-- Hidden input to store the combined value -->
              <input type="hidden" formControlName="code">

              <div *ngIf="code.invalid && (code.dirty || code.touched)" class="text-red-500 text-xs mt-1">
                <div *ngIf="code.errors?.['required']">{{ 'auth.mfa_code_required' | translate }}</div>
                <div *ngIf="code.errors?.['minlength'] || code.errors?.['maxlength']">
                  {{ 'auth.mfa_code_length' | translate }}
                </div>
              </div>
            </div>

            <!-- Error Message -->
            <div *ngIf="mfaError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm animate-shake">
              {{ mfaError }}
            </div>

            <!-- Submit Button -->
            <button
              type="submit"
              class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium text-lg shadow-lg"
              [disabled]="mfaForm.invalid || isLoading"
              [ngClass]="{'opacity-50 cursor-not-allowed': mfaForm.invalid || isLoading}">
              <span *ngIf="!isLoading">{{ 'auth.verify' | translate }}</span>
              <span *ngIf="isLoading" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ 'auth.verifying' | translate }}
              </span>
            </button>

            <!-- Back to Login Link -->
            <div class="text-center">
              <p class="text-sm text-gray-600">
                <a routerLink="/auth/login" class="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                  {{ 'auth.back_to_login' | translate }}
                </a>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
