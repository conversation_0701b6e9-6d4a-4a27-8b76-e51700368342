import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { MfaStateService } from '../services/mfa-state.service';
import { ToastService } from '../services/toast.service';
import { NotifyType } from '../../constant/notify-type';

/**
 * Guard to protect the MFA route
 * Ensures that users can only access the MFA page when they have the required MFA data
 */
export const mfaGuard: CanActivateFn = (route, state) => {
  const mfaStateService = inject(MfaStateService);
  const router = inject(Router);
  const toastService = inject(ToastService);

  // Check if we have the required MFA data
  if (!mfaStateService.username || !mfaStateService.password || !mfaStateService.loginFirstFactor) {
    console.log('MFA Guard: Missing required data, redirecting to login page');
    toastService.showToast('MFA verification session expired. Please log in again.', NotifyType.WARNING);
    router.navigate(['/auth/login']);
    return false;
  }

  return true;
};
