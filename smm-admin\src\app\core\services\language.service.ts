import { Inject, Injectable, PLATFORM_ID, TransferState } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, firstValueFrom } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';
import { ConfigService } from './config.service';
import { LanguageReq } from '../../model/request/language-req.model';
import { AuthUtilsService } from './auth-utils.service';
import { TenantSettingsService } from './tenant-settings.service';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private apiUrl: string; // URL API backend
  private _translationsLoaded = new BehaviorSubject<boolean>(false);
  public translationsLoaded$ = this._translationsLoaded.asObservable();

  constructor(
    private translate: TranslateService,
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object,
    private configService: ConfigService,
    private authUtils: AuthUtilsService,
    private tenantSettingsService: TenantSettingsService
  ) {
    this.apiUrl = `${this.configService.apiUrl}/users/me/language`;
    // Set default language to prevent translation keys from showing
    this.translate.setDefaultLang('vi');
  }

  init(): Promise<void> {
    return new Promise(async (resolve) => {
      // Initialize translations and wait for them to load
      await this.initLanguage();
      resolve();
    });
  }


  // Khởi tạo ngôn ngữ
  async initLanguage(): Promise<void> {
    // Mark translations as not loaded yet
    this._translationsLoaded.next(false);

    if (!isPlatformBrowser(this.platformId)) {
      // For server-side rendering, just mark as loaded and return
      this._translationsLoaded.next(true);
      return;
    }

    try {
      // Get language from localStorage or default to 'vi'
      const cachedLang = localStorage.getItem('language') || 'vi';

      // Load translations and wait for them to complete
      await firstValueFrom(this.translate.use(cachedLang));

      // Mark translations as loaded
      this._translationsLoaded.next(true);

      // Only fetch language settings from API if user is authenticated
      if (this.authUtils.isAuthenticated() && !this.authUtils.isTokenExpired()) {
        // After translations are loaded, check for user settings
        this.getLanguageSettings().subscribe({
          next: (response: any) => {
            const apiLang = response.lang;
            if (apiLang && apiLang !== cachedLang) {
              this.translate.use(apiLang);
              localStorage.setItem('language', apiLang);
            }
          },
          error: (err) => {
            console.error('Error fetching language settings:', err);
          }
        });
      } else {
        // User not authenticated, try to get tenant default language
        console.log('User not authenticated, fetching tenant default language');
        this.tenantSettingsService.getTenantDefaultLanguage().subscribe({
          next: (response: {default_language: string}) => {
            const tenantDefaultLang = response.default_language || 'vi';
            if (tenantDefaultLang !== cachedLang) {
              this.translate.use(tenantDefaultLang);
              localStorage.setItem('language', tenantDefaultLang);
            }
          },
          error: (err) => {
            console.error('Error fetching tenant default language:', err);
            // Fallback to cached language or 'vi'
          }
        });
      }
    } catch (error) {
      console.error('Error loading translations:', error);
      // Even if there's an error, mark as loaded to prevent blocking the app
      this._translationsLoaded.next(true);
    }
  }

  // Gọi API lấy setting ngôn ngữ
  getLanguageSettings(): Observable<any> {
    return this.http.get(this.apiUrl);
  }

  // Hàm đổi ngôn ngữ thủ công
  async changeLanguage(lang: string): Promise<void> {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      // Mark translations as loading
      this._translationsLoaded.next(false);

      // Load new language translations
      await firstValueFrom(this.translate.use(lang));

      // Save to localStorage
      localStorage.setItem('language', lang);

      // Mark translations as loaded
      this._translationsLoaded.next(true);

      // Only update server settings if user is authenticated
      if (this.authUtils.isAuthenticated() && !this.authUtils.isTokenExpired()) {
        // Update server settings
        this.updateLanguageSettings(lang).subscribe({
          error: (err) => console.error('Error updating language settings:', err)
        });
      } else {
        console.log('User not authenticated, skipping language API update');
      }
    } catch (error) {
      console.error('Error changing language:', error);
      this._translationsLoaded.next(true);
    }
  }

  updateLanguageSettings(lang: string): Observable<any> {
    const req: LanguageReq = { lang };
    return this.http.patch(this.apiUrl, req);
  }
}