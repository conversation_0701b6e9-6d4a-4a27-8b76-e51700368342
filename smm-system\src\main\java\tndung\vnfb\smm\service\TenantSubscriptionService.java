package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.RenewalRequest;
import tndung.vnfb.smm.dto.RenewalResponse;
import tndung.vnfb.smm.dto.TenantSubscriptionDto;

import java.util.List;
import java.util.Optional;

public interface TenantSubscriptionService {
    RenewalResponse renewSubscription(RenewalRequest request);
    int updateExpiredTenants();

    void sendRenewalNotifications();
    List<TenantSubscriptionDto> getTenantsExpiringSoon(int daysAhead);
   // List<TenantSubscriptionDto> getAllTenantSubscriptions();

    TenantSubscriptionDto getTenantSubscription(String tenantId);

    void updateAutoRenewal(String tenantId, Boolean autoRenewal);

    int processAutoRenewals();

}
