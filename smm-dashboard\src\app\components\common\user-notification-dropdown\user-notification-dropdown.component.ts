import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { UserNotificationService } from '../../../core/services/user-notification.service';
import { UserNotificationRes } from '../../../model/response/user-notification-res.model';
import { IconsModule } from '../../../icons/icons.module';
import { faBell, faCheckCircle, faExclamationTriangle, faTimesCircle, faInfoCircle, IconDefinition } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-user-notification-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './user-notification-dropdown.component.html',
  styleUrl: './user-notification-dropdown.component.css'
})
export class UserNotificationDropdownComponent implements OnInit, On<PERSON><PERSON>roy {
  isOpen = false;
  unreadCount = 0;
  notifications: UserNotificationRes[] = [];
  loading = false;
  showAllNotifications = true;
  faBell = faBell;

  private subscriptions: Subscription[] = [];

  constructor(
    private userNotificationService: UserNotificationService,
    private elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    // Subscribe to unread count
    const unreadCountSub = this.userNotificationService.unreadCount$.subscribe(count => {
      this.unreadCount = count;
    });
    this.subscriptions.push(unreadCountSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  toggleDropdown(): void {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.loadNotifications();
    }
  }

  private loadNotifications(): void {
    this.loading = true;

    if (this.showAllNotifications) {
      this.userNotificationService.getNotifications(0, 10).subscribe({
        next: (response) => {
          this.notifications = response.content || [];
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading notifications:', error);
          this.loading = false;
        }
      });
    } else {
      this.userNotificationService.getUnreadNotifications().subscribe({
        next: (notifications) => {
          this.notifications = notifications;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading unread notifications:', error);
          this.loading = false;
        }
      });
    }
  }

  markAsRead(notification: UserNotificationRes): void {
    if (!notification.is_read) {
      this.userNotificationService.markAsRead(notification.id).subscribe({
        next: () => {
          notification.is_read = true;
          this.userNotificationService.updateUnreadCount();
        },
        error: (error) => {
          console.error('Error marking notification as read:', error);
        }
      });
    }
  }

  markAllAsRead(): void {
    this.userNotificationService.markAllAsRead().subscribe({
      next: () => {
        this.notifications.forEach(n => n.is_read = true);
        this.userNotificationService.updateUnreadCount();
      },
      error: (error) => {
        console.error('Error marking all notifications as read:', error);
      }
    });
  }

  toggleNotificationFilter(): void {
    this.showAllNotifications = !this.showAllNotifications;
    this.loadNotifications();
  }

  getNotificationIcon(type: string): IconDefinition {
    switch (type) {
      case 'SUCCESS': return faCheckCircle;
      case 'WARNING': return faExclamationTriangle;
      case 'ERROR': return faTimesCircle;
      default: return faInfoCircle;
    }
  }

  getNotificationColor(type: string): string {
    switch (type) {
      case 'SUCCESS': return 'text-green-500';
      case 'WARNING': return 'text-yellow-500';
      case 'ERROR': return 'text-red-500';
      default: return 'text-blue-500';
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} giờ trước`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} ngày trước`;

    return date.toLocaleDateString('vi-VN');
  }
}
