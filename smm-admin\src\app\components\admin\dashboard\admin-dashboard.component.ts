import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { IconName } from '@fortawesome/fontawesome-svg-core';
import { DashboardService } from '../../../core/services/dashboard.service';
import { DashboardStatsRes } from '../../../model/response/dashboard-stats.model';
import { TopServiceRes } from '../../../model/response/top-services.model';
import { LatestActivityRes } from '../../../model/response/latest-activity.model';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../../core/services/loading.service';
import { IconsModule } from '../../../icons/icons.module';
import { ToastService } from '../../../core/services/toast.service';

interface StatCard {
  title: string;
  value: string;
  icon: IconName;
  change: string;
  isIncrease: boolean;
}

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, IconsModule, TranslateModule],
  templateUrl: './admin-dashboard.component.html',
  styleUrl: './admin-dashboard.component.css'
})
export class AdminDashboardComponent implements OnInit {
  statCards: StatCard[] = [];
  isLoading: boolean = false;
  dashboardStats: DashboardStatsRes | null = null;
  topServices: TopServiceRes[] = [];
  latestActivities: LatestActivityRes[] = [];
  period: string = '';
  private subscriptions: Subscription[] = [];

  constructor(
    private dashboardService: DashboardService,
    private loadingService: LoadingService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    // Subscribe to loading state
    this.subscriptions.push(
      this.dashboardService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to dashboard stats
    this.subscriptions.push(
      this.dashboardService.dashboardStats$.subscribe(stats => {
        if (stats) {
          this.dashboardStats = stats;
          this.period = stats.period;
          this.updateStatCards();
        }
      })
    );

    // Subscribe to top services
    this.subscriptions.push(
      this.dashboardService.topServices$.subscribe(services => {
        this.topServices = services;
      })
    );

    // Subscribe to latest activities
    this.subscriptions.push(
      this.dashboardService.latestActivities$.subscribe(activities => {
        this.latestActivities = activities;
      })
    );

    // Load dashboard stats, top services, and latest activities
    this.loadDashboardStats();
    this.loadTopServices();
    this.loadLatestActivities();
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadDashboardStats() {
    this.dashboardService.getDashboardStats().subscribe({
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        this.toastService.showError(error?.message || 'Failed to load dashboard statistics');
        // Set default values in case of error
        this.setDefaultStatCards();
      }
    });
  }

  loadTopServices() {
    this.dashboardService.getTopServices().subscribe({
      error: (error) => {
        console.error('Error loading top services:', error);
        this.toastService.showError(error?.message || 'Failed to load top services');
      }
    });
  }

  loadLatestActivities() {
    this.dashboardService.getLatestActivities().subscribe({
      error: (error) => {
        console.error('Error loading latest activities:', error);
        this.toastService.showError(error?.message || 'Failed to load latest activities');
      }
    });
  }

  updateStatCards() {
    if (!this.dashboardStats) {
      this.setDefaultStatCards();
      return;
    }

    this.statCards = [
      {
        title: 'Total Orders',
        value: this.dashboardStats.orders.count.toLocaleString(),
        icon: 'shopping-cart' as IconName,
        change: this.formatPercentChange(this.dashboardStats.orders.percent_change),
        isIncrease: this.dashboardStats.orders.percent_change >= 0
      },
      {
        title: 'Total Revenue',
        value: `$${this.dashboardStats.revenue.current_revenue.toLocaleString()}`,
        icon: 'dollar-sign' as IconName,
        change: this.formatPercentChange(this.dashboardStats.revenue.percent_change),
        isIncrease: this.dashboardStats.revenue.percent_change >= 0
      },
      {
        title: 'Active Users',
        value: this.dashboardStats.active_users.count.toLocaleString(),
        icon: 'user' as IconName,
        change: this.formatPercentChange(this.dashboardStats.active_users.percent_change),
        isIncrease: this.dashboardStats.active_users.percent_change >= 0
      },
      {
        title: 'Pending Tickets',
        value: this.dashboardStats.pending_tickets.count.toLocaleString(),
        icon: 'comment' as IconName,
        change: this.formatPercentChange(this.dashboardStats.pending_tickets.percent_change),
        isIncrease: this.dashboardStats.pending_tickets.percent_change >= 0
      }
    ];
  }

  setDefaultStatCards() {
    this.statCards = [
      {
        title: 'Total Orders',
        value: '0',
        icon: 'shopping-cart' as IconName,
        change: '0%',
        isIncrease: true
      },
      {
        title: 'Total Revenue',
        value: '$0',
        icon: 'dollar-sign' as IconName,
        change: '0%',
        isIncrease: true
      },
      {
        title: 'Active Users',
        value: '0',
        icon: 'user' as IconName,
        change: '0%',
        isIncrease: true
      },
      {
        title: 'Pending Tickets',
        value: '0',
        icon: 'comment' as IconName,
        change: '0%',
        isIncrease: true
      }
    ];
  }

  formatPercentChange(percentChange: number): string {
    const absChange = Math.abs(percentChange);
    const formattedChange = absChange === 0 ? '0' : absChange.toFixed(1);
    return `${percentChange >= 0 ? '+' : '-'}${formattedChange}%`;
  }

  getActivityIcon(entityName: string): IconName {
    switch (entityName) {
      case 'OrderReq':
        return 'shopping-cart';
      case 'TicketReq':
        return 'comment';
      case 'UserReq':
        return 'user';
      case 'PaymentReq':
        return 'credit-card';
      default:
        return 'bell';
    }
  }

  getActivityColor(entityName: string): string {
    switch (entityName) {
      case 'OrderReq':
        return 'bg-red-600';
      case 'TicketReq':
        return 'bg-yellow-500';
      case 'UserReq':
        return 'bg-blue-600';
      case 'PaymentReq':
        return 'bg-green-600';
      default:
        return 'bg-purple-600';
    }
  }

  getActivityTitle(activity: LatestActivityRes): string {
    const operation = activity.operation.charAt(0) + activity.operation.slice(1).toLowerCase();
    let entityType = '';

    switch (activity.entity_name) {
      case 'OrderReq':
        entityType = 'order';
        break;
      case 'TicketReq':
        entityType = 'support ticket';
        break;
      case 'UserReq':
        entityType = 'user';
        break;
      case 'PaymentReq':
        entityType = 'payment';
        break;
      default:
        entityType = activity.entity_name.toLowerCase();
    }

    return `${operation} ${entityType}`;
  }

  private static readonly MAX_CHARACTERS = 70;

  getActivityDescription(activity: LatestActivityRes): string {
    try {
      const details = JSON.parse(activity.details);

      switch (activity.entity_name) {
        case 'OrderReq':
          return details.service?.name
            ? `Service: ${details.service.name.substring(0, AdminDashboardComponent.MAX_CHARACTERS)}${details.service.name.length > AdminDashboardComponent.MAX_CHARACTERS ? '...' : ''}`
            : `Order #${details.id}`;
        case 'TicketReq':
          return details.subject
            ? `Subject: ${details.subject.substring(0, AdminDashboardComponent.MAX_CHARACTERS)}${details.subject.length > AdminDashboardComponent.MAX_CHARACTERS ? '...' : ''}`
            : `Ticket #${details.id}`;
        case 'UserReq':
          return details.user_name
            ? `User: ${details.user_name}`
            : `User #${details.id}`;
        default:
          return `${activity.username} performed this action`;
      }
    } catch (e) {
      return activity.username;
    }
  }

  getActivityTimestamp(activity: LatestActivityRes): string {
    try {
      const details = JSON.parse(activity.details);
      return details.created_at || '2025-05-05T13:58:09';
    } catch (e) {
      return '2025-05-05T13:58:09';
    }
  }

  getTimeAgo(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffSec = Math.round(diffMs / 1000);
      const diffMin = Math.round(diffSec / 60);
      const diffHour = Math.round(diffMin / 60);
      const diffDay = Math.round(diffHour / 24);

      if (diffSec < 60) {
        return `${diffSec} seconds ago`;
      } else if (diffMin < 60) {
        return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
      } else if (diffHour < 24) {
        return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
      } else {
        return `${diffDay} ${diffDay === 1 ? 'day' : 'days'} ago`;
      }
    } catch (e) {
      return 'recently';
    }
  }
}
