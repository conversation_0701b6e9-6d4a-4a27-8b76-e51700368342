import { ServiceLabel } from "../response/service-label.model";

/**
 * Request model for creating or updating a service
 */
export interface SuperGeneralSvReq {
  // Basic service information
  name: string;
  description: string;
  price: number;
  min: number;
  max: number;
  cancel_button: boolean;

  // Service type and category
  type: string;
  add_type: string;
  category_id: number;

  // API related fields
  api_service_id: number | null;
  api_provider_id: number | null;

  // Price related fields
  original_price: number;
  price1: number;
  price2: number;
  percent: number;
  percent1: number;
  percent2: number;

  // Status and configuration
  auto_sync: boolean;

  // Sync settings
  sync_min_max?: boolean;
  sync_refill?: boolean;
  sync_cancel?: boolean;
  sync_status?: boolean;

  // Optional fields
  limit_from?: number;
  limit_to?: number;
  sort?: number;
  icon?: string;

  // Features
  refill: boolean;
  refill_days: string | null;
  average_time: number;

  // Labels
  labels: ServiceLabel[];

  // New fields
  is_overflow?: boolean;
  overflow?: number;
  sample_link?: string;
  is_fixed_price?: boolean;

  speed_per_day: number | null;
}
