import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { ToastService } from '../../../core/services/toast.service';
import { IconsModule } from '../../../icons/icons.module';
import { TenantCurrencyRes } from '../../../model/response/tenant-currency-res.model';
import { TenantCurrencyService } from '../../../core/services/tenant-currency.service';
import { Currency } from '../../../model/response/currency-res.model';


@Component({
  selector: 'app-currency-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './currency-settings.component.html',
  styleUrl: './currency-settings.component.css'
})
export class CurrencySettingsComponent implements OnInit {
  @Output() close = new EventEmitter<void>();

  isLoading = false;
  searchText = '';
  showCurrencyListModal = false;
  tenantCurrencies: TenantCurrencyRes | null = null;
  selectedCurrencies: Set<string> = new Set();
  tempSelectedCurrencies: Set<string> = new Set();

  constructor(
    private tenantCurrencyService: TenantCurrencyService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadTenantCurrencies();
  }

  loadTenantCurrencies(): void {
    this.isLoading = true;
    this.tenantCurrencyService.getTenantCurrencies().subscribe({
      next: (response) => {
        this.tenantCurrencies = response;
        this.selectedCurrencies = new Set(response.available_currencies);
        this.isLoading = false;
      },
      error: (error) => {
        this.toastService.showError(error?.message || 'Failed to load currencies');
        this.isLoading = false;
      }
    });
  }

  get filteredCurrencies(): Currency[] {
    if (!this.tenantCurrencies?.all_currencies) return [];

    return this.tenantCurrencies.all_currencies.filter(currency =>
      currency.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
      currency.code.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  toggleCurrency(currencyCode: string): void {
    if (currencyCode === 'USD') {
      // USD cannot be removed
      return;
    }

    // Use tempSelectedCurrencies when in modal, selectedCurrencies otherwise
    const targetSet = this.showCurrencyListModal ? this.tempSelectedCurrencies : this.selectedCurrencies;

    if (targetSet.has(currencyCode)) {
      targetSet.delete(currencyCode);
    } else {
      targetSet.add(currencyCode);
    }
  }

  isCurrencySelected(currencyCode: string): boolean {
    // Use tempSelectedCurrencies when in modal, selectedCurrencies otherwise
    const targetSet = this.showCurrencyListModal ? this.tempSelectedCurrencies : this.selectedCurrencies;
    return targetSet.has(currencyCode);
  }

  saveCurrencies(): void {
    this.isLoading = true;
    const availableCurrencies = Array.from(this.selectedCurrencies);

    this.tenantCurrencyService.updateTenantCurrencies({ available_currencies: availableCurrencies }).subscribe({
      next: () => {
        this.toastService.showSuccess('Currency settings updated successfully');
        this.close.emit();
      },
      error: (error) => {
        this.toastService.showError(error?.message || 'Failed to update currency settings');
        this.isLoading = false;
      }
    });
  }

  openCurrencyListModal(): void {
    this.tempSelectedCurrencies = new Set(this.selectedCurrencies);
    this.showCurrencyListModal = true;
    this.searchText = '';
  }

  closeCurrencyListModal(): void {
    this.showCurrencyListModal = false;
    this.searchText = '';
  }

  applyCurrencySelection(): void {
    this.selectedCurrencies = new Set(this.tempSelectedCurrencies);
    this.closeCurrencyListModal();
  }

  getSelectedCount(): number {
    return this.tempSelectedCurrencies.size;
  }

  getSecondaryCurrencies(): Currency[] {
    if (!this.tenantCurrencies?.all_currencies) return [];

    // Get all selected currencies except USD (main currency)
    const selectedCodes = Array.from(this.selectedCurrencies).filter(code => code !== 'USD');

    return this.tenantCurrencies.all_currencies.filter(currency =>
      selectedCodes.includes(currency.code)
    );
  }

  removeSecondaryCurrency(currencyCode: string): void {
    if (currencyCode === 'USD') {
      // Cannot remove USD (main currency)
      return;
    }

    this.selectedCurrencies.delete(currencyCode);
  }

  onClose(): void {
    this.close.emit();
  }
}
