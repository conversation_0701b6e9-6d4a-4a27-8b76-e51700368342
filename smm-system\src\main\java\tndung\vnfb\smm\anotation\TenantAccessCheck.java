package tndung.vnfb.smm.anotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that require tenant access validation.
 * When applied to a method, the TenantAccessCheckAspect will verify that the current user
 * has access to the specified tenant before method execution.
 * 
 * The tenant ID can be extracted from:
 * - Method parameter named "tenantId" 
 * - Method parameter with @TenantId annotation
 * - Request object with getTenantId() method
 * 
 * If the user doesn't have access to the tenant, an InvalidParameterException with 
 * TENANT_ACCESS_DENIED error code will be thrown.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantAccessCheck {
    /**
     * The name of the parameter containing the tenant ID.
     * If not specified, the aspect will look for:
     * 1. Parameter named "tenantId"
     * 2. Parameter annotated with @TenantId
     * 3. Request object with getTenantId() method
     */
    String parameterName() default "";
}
