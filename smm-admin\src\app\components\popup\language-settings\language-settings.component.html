<div class="popup-overlay" (click)="onClose()">
  <div class="popup-container" (click)="$event.stopPropagation()">
    <div class="popup-header">
      <h2 class="popup-title">{{ 'Language Settings' | translate }}</h2>
      <button class="close-button" (click)="onClose()">
        <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>

    <div class="popup-content">
      <!-- Available Languages Section -->
      <div class="setting-section">
        <div class="section-header">
          <h3 class="section-title">{{ 'Available Languages' | translate }}</h3>
          <button
            type="button"
            class="btn btn-outline btn-sm"
            (click)="showLanguageSelector = true">
            <fa-icon [icon]="['fas', 'plus']"></fa-icon>
            {{ 'Add Language' | translate }}
          </button>
        </div>
        <p class="section-description">
          {{ 'Languages available for users on this tenant.' | translate }}
        </p>

        <div class="selected-languages" *ngIf="selectedAvailableLanguages.length > 0">
          <div
            *ngFor="let languageCode of selectedAvailableLanguages"
            class="language-tag"
            [class.is-default]="selectedLanguage === languageCode">
            <div class="language-flag">
              <span [class]="getLanguageByCode(languageCode)?.flag" [title]="getLanguageByCode(languageCode)?.name"></span>
            </div>
            <span class="language-name">{{ getLanguageByCode(languageCode)?.name }}</span>

            <!-- Default indicator/selector -->
            <div class="default-selector">
              <input
                type="radio"
                [id]="'default-' + languageCode"
                name="defaultLanguage"
                [value]="languageCode"
                [checked]="selectedLanguage === languageCode"
                (change)="selectLanguage(languageCode)"
                class="default-radio">
              <label
                [for]="'default-' + languageCode"
                class="default-label"
                title="Set as default language">
                <span class="radio-indicator">
                  <span class="radio-dot"></span>
                </span>
              </label>
            </div>

            <button
              type="button"
              class="remove-btn"
              (click)="removeLanguage(languageCode)"
              [disabled]="selectedAvailableLanguages.length === 1"
              title="Remove language">
              <fa-icon [icon]="['fas', 'times']"></fa-icon>
            </button>
          </div>
        </div>

        <div *ngIf="selectedAvailableLanguages.length === 0" class="empty-state">
          <fa-icon [icon]="['fas', 'globe']"></fa-icon>
          <span>{{ 'No languages selected' | translate }}</span>
        </div>

        <div class="default-info" *ngIf="selectedAvailableLanguages.length > 0">
          <div class="info-icon">
            <fa-icon [icon]="['fas', 'info-circle']"></fa-icon>
          </div>
          <span class="info-text">
            {{ 'Click the radio button to set default language for new users and visitors.' | translate }}
          </span>
        </div>
      </div>
    </div>

    <div class="popup-footer">
      <button
        type="button"
        class="btn btn-secondary"
        (click)="onClose()"
        [disabled]="isLoading">
        {{ 'Cancel' | translate }}
      </button>
      <button
        type="button"
        class="btn btn-primary"
        (click)="saveSettings()"
        [disabled]="isLoading">
        <span *ngIf="isLoading" class="loading-spinner"></span>
        {{ 'Save Changes' | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Language Selector Popup -->
<app-language-selector
  *ngIf="showLanguageSelector"
  [selectedLanguages]="selectedAvailableLanguages"
  (close)="showLanguageSelector = false"
  (languageSelected)="addLanguage($event)">
</app-language-selector>
