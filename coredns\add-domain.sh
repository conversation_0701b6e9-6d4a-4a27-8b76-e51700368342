#!/bin/bash

# Script to add a new domain to CoreDNS
# Usage: ./add-domain.sh example.com **************

if [ "$#" -ne 2 ]; then
    echo "Usage: $0 domain_name ip_address"
    echo "Example: $0 example.com **************"
    exit 1
fi

DOMAIN=$1
IP=$2
ZONE_FILE="./zones/${DOMAIN}.db"

# Create zone file
cat > "$ZONE_FILE" << EOF
\$ORIGIN ${DOMAIN}.
\$TTL 3600
@       IN      SOA     ns1.autovnfb.com. admin.autovnfb.com. (
                        $(date +%Y%m%d01)  ; Serial
                        7200            ; Refresh
                        3600            ; Retry
                        1209600         ; Expire
                        3600            ; Minimum TTL
                        )

; Name servers
@       IN      NS      ns1.autovnfb.com.
@       IN      NS      ns2.autovnfb.com.

; A records for the domain
@       IN      A       ${IP}
www     IN      A       ${IP}
*       IN      A       ${IP}
EOF

# Update Corefile to include the new domain
if ! grep -q "${DOMAIN}" ./Corefile; then
    # Add new domain block before the last closing brace
    sed -i "\$i\\
${DOMAIN} {\\
    file /etc/coredns/zones/${DOMAIN}.db\\
    errors\\
    log\\
    reload 10s\\
}\\
" ./Corefile
fi

echo "Domain ${DOMAIN} added to CoreDNS configuration."
echo "Restart CoreDNS or reload configuration for changes to take effect."
