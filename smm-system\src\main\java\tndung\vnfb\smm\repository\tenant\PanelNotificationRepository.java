package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.PanelNotification;

import java.util.List;

@Repository
public interface PanelNotificationRepository extends TenantAwareRepository<PanelNotification, Long> {
    
    @Query("SELECT pn FROM PanelNotification pn WHERE pn.tenantId = :tenantId ORDER BY pn.createdAt DESC")
    Page<PanelNotification> findByTenantIdOrderByCreatedAtDesc(@Param("tenantId") String tenantId, Pageable pageable);
    
    @Query("SELECT pn FROM PanelNotification pn WHERE pn.tenantId = :tenantId AND pn.isRead = false ORDER BY pn.createdAt DESC")
    List<PanelNotification> findUnreadByTenantId(@Param("tenantId") String tenantId);
    
    @Query("SELECT COUNT(pn) FROM PanelNotification pn WHERE pn.tenantId = :tenantId AND pn.isRead = false")
    long countUnreadByTenantId(@Param("tenantId") String tenantId);
    
    @Query("SELECT pn FROM PanelNotification pn WHERE pn.tenantId = :tenantId AND pn.userId = :userId ORDER BY pn.createdAt DESC")
    Page<PanelNotification> findByTenantIdAndUserIdOrderByCreatedAtDesc(
            @Param("tenantId") String tenantId, 
            @Param("userId") Long userId, 
            Pageable pageable);
    
    @Query("SELECT pn FROM PanelNotification pn WHERE pn.tenantId = :tenantId AND pn.userId = :userId AND pn.isRead = false ORDER BY pn.createdAt DESC")
    List<PanelNotification> findUnreadByTenantIdAndUserId(@Param("tenantId") String tenantId, @Param("userId") Long userId);
    
    @Query("SELECT COUNT(pn) FROM PanelNotification pn WHERE pn.tenantId = :tenantId AND pn.userId = :userId AND pn.isRead = false")
    long countUnreadByTenantIdAndUserId(@Param("tenantId") String tenantId, @Param("userId") Long userId);
}
