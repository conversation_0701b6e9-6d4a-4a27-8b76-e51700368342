/**
 * DNS Manager for CoreDNS integration
 * This module provides functions to manage DNS records in CoreDNS
 */

const { exec } = require('child_process');
const path = require('path');
const util = require('util');
const config = require('./config/constants');
const logger = require('./utils/logger');
const FileUtils = require('./utils/file-utils');

const execPromise = util.promisify(exec);

class DNSManagerConfig {
  constructor() {
    this.isWindows = process.platform === 'win32';
    this.DOCKER_COREDNS_DIR = '/etc/coredns';
    this.LOCAL_COREDNS_DIR = this.isWindows ? './coredns' : './coredns';
    this.COREDNS_DIR = process.env.COREDNS_DIR || (this.isWindows ? this.LOCAL_COREDNS_DIR : this.DOCKER_COREDNS_DIR);
    this.ZONES_DIR = path.join(this.COREDNS_DIR, 'zones');
    this.SERVER_IP = config.SERVER_IP;
    this.ADMIN_EMAIL = config.ADMIN_EMAIL;

    this._initialize();
  }

  _initialize() {
    logger.info(`Running on platform: ${process.platform}`);
    logger.info(`Using CoreDNS directory: ${this.COREDNS_DIR}`);
    logger.info(`Using Zones directory: ${this.ZONES_DIR}`);

    // Ensure directories exist
    try {
      FileUtils.ensureDirectoryExists(this.COREDNS_DIR);
      FileUtils.ensureDirectoryExists(this.ZONES_DIR);
    } catch (error) {
      logger.error(`Error creating directories: ${error.message}`);
      throw error;
    }
  }
}

const dnsConfig = new DNSManagerConfig();

class DNSManager {
  /**
   * Add a domain to CoreDNS
   * @param {string} domain - The domain name to add
   * @param {string} ip - The IP address to point the domain to (defaults to SERVER_IP)
   * @returns {Promise<object>} - Result of the operation
   */
  static async addDomain(domain, ip = dnsConfig.SERVER_IP) {
    try {
      logger.info(`Adding domain ${domain} to CoreDNS with IP ${ip}`);

      // Create zone file
      const zoneFile = path.join(dnsConfig.ZONES_DIR, `${domain}.db`);
      const zoneContent = this._generateZoneContent(domain, ip);

      try {
        FileUtils.ensureDirectoryExists(dnsConfig.ZONES_DIR);
        FileUtils.writeFile(zoneFile, zoneContent, `Zone file for ${domain}`);
      } catch (writeError) {
        logger.error(`Error writing zone file: ${writeError.message}`);
        logger.error(`Attempted to write to: ${zoneFile}`);
        await this._logDirectoryStructure();
        throw writeError;
      }

      // Update Corefile
      await this._updateCorefile(domain);

      // Reload CoreDNS configuration
      await this.reloadConfig();

      return {
        success: true,
        message: `Domain ${domain} added to CoreDNS successfully`
      };
    } catch (error) {
      logger.error(`Error adding domain to CoreDNS: ${error.message}`);
      return {
        success: false,
        message: `Failed to add domain to CoreDNS: ${error.message}`
      };
    }
  }

  /**
   * Generate zone file content
   * @param {string} domain - Domain name
   * @param {string} ip - IP address
   * @returns {string} Zone file content
   * @private
   */
  static _generateZoneContent(domain, ip) {
    const serial = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 10);
    const dnsEmail = this._formatEmailForDNS(dnsConfig.ADMIN_EMAIL);

    logger.debug(`Using DNS admin email: ${dnsEmail}`);

    return `$ORIGIN ${domain}.
$TTL 3600
@       IN      SOA     ns1.${config.BASE_DOMAIN}. ${dnsEmail} (
                        ${serial}  ; Serial
                        7200            ; Refresh
                        3600            ; Retry
                        1209600         ; Expire
                        3600            ; Minimum TTL
                        )

; Name servers
@       IN      NS      ns1.${config.BASE_DOMAIN}.
@       IN      NS      ns2.${config.BASE_DOMAIN}.

; A records for name servers
ns1     IN      A       ${ip}
ns2     IN      A       ${ip}

; A records for the domain
@       IN      A       ${ip}
www     IN      A       ${ip}
*       IN      A       ${ip}
`;
  }

  /**
   * Format email address for DNS format
   * @param {string} email - Email address
   * @returns {string} DNS formatted email
   * @private
   */
  static _formatEmailForDNS(email) {
    const emailParts = email.split('@');
    return emailParts.length === 2 ?
      `${emailParts[0]}.${emailParts[1]}.` :
      `admin.${config.BASE_DOMAIN}.`;
  }

  /**
   * Update Corefile with new domain
   * @param {string} domain - Domain name
   * @private
   */
  static async _updateCorefile(domain) {
    const corefilePath = path.join(dnsConfig.COREDNS_DIR, 'Corefile');

    // Create default Corefile if it doesn't exist
    if (!FileUtils.fileExists(corefilePath)) {
      logger.info(`Creating default Corefile at ${corefilePath}`);
      const defaultCorefile = this._generateDefaultCorefile();
      FileUtils.writeFile(corefilePath, defaultCorefile, 'Default Corefile');
    }

    let corefileContent;
    try {
      corefileContent = FileUtils.readFile(corefilePath);
    } catch (readError) {
      logger.error(`Error reading Corefile: ${readError.message}`);
      throw readError;
    }

    // Check if domain is already in Corefile
    if (!corefileContent.includes(`${domain} {`)) {
      const domainBlock = this._generateDomainBlock(domain);

      // Add domain block before the last closing brace
      const lastBraceIndex = corefileContent.lastIndexOf('}');
      corefileContent = corefileContent.slice(0, lastBraceIndex + 1) +
                        domainBlock +
                        corefileContent.slice(lastBraceIndex + 1);

      try {
        FileUtils.writeFile(corefilePath, corefileContent, `Updated Corefile with domain ${domain}`);
      } catch (writeError) {
        logger.error(`Error writing Corefile: ${writeError.message}`);
        throw writeError;
      }
    } else {
      logger.info(`Domain ${domain} already exists in Corefile`);
    }
  }

  /**
   * Generate default Corefile content
   * @returns {string} Default Corefile content
   * @private
   */
  static _generateDefaultCorefile() {
    return `. {
    errors
    health {
        lameduck 5s
    }
    ready
    log
    prometheus :9153
    cache 30
    loop
    reload
    loadbalance

    # Forward all queries not handled by other plugins to external DNS servers
    forward . 8.8.8.8 8.8.4.4 {
        policy random
        health_check 5s
    }
}

# Configuration for your primary domain zone
${config.BASE_DOMAIN} {
    file /etc/coredns/zones/${config.BASE_DOMAIN}.db
    errors
    log
    reload 10s
}
`;
  }

  /**
   * Generate domain block for Corefile
   * @param {string} domain - Domain name
   * @returns {string} Domain block content
   * @private
   */
  static _generateDomainBlock(domain) {
    return `
${domain} {
    file /etc/coredns/zones/${domain}.db
    errors
    log
    reload 10s
}
`;
  }

  /**
   * Log directory structure for debugging
   * @private
   */
  static async _logDirectoryStructure() {
    try {
      const dirOutput = await execPromise(`ls -la ${dnsConfig.COREDNS_DIR}`);
      logger.error(`Current directory structure: ${dirOutput.stdout}`);
    } catch (lsError) {
      logger.error(`Could not list directory: ${lsError.message}`);
    }
  }

  /**
   * Remove a domain from CoreDNS
   * @param {string} domain - The domain name to remove
   * @returns {Promise<object>} - Result of the operation
   */
  static async removeDomain(domain) {
    try {
      logger.info(`Removing domain ${domain} from CoreDNS`);

      // Remove zone file
      const zoneFile = path.join(dnsConfig.ZONES_DIR, `${domain}.db`);
      FileUtils.deleteFile(zoneFile, `Zone file for ${domain}`);

      // Update Corefile
      await this._removeFromCorefile(domain);

      // Reload CoreDNS configuration
      await this.reloadConfig();

      return {
        success: true,
        message: `Domain ${domain} removed from CoreDNS successfully`
      };
    } catch (error) {
      logger.error(`Error removing domain from CoreDNS: ${error.message}`);
      return {
        success: false,
        message: `Failed to remove domain from CoreDNS: ${error.message}`
      };
    }
  }

  /**
   * Remove domain from Corefile
   * @param {string} domain - Domain name
   * @private
   */
  static async _removeFromCorefile(domain) {
    const corefilePath = path.join(dnsConfig.COREDNS_DIR, 'Corefile');

    if (!FileUtils.fileExists(corefilePath)) {
      logger.warn(`Corefile not found at ${corefilePath}, nothing to update`);
      return;
    }

    let corefileContent;
    try {
      corefileContent = FileUtils.readFile(corefilePath);
    } catch (readError) {
      logger.error(`Error reading Corefile: ${readError.message}`);
      throw new Error(`Failed to read Corefile: ${readError.message}`);
    }

    // Find and remove domain block
    const domainBlockRegex = new RegExp(`\\n${domain} \\{[^}]*\\}\\n`, 'g');
    if (domainBlockRegex.test(corefileContent)) {
      corefileContent = corefileContent.replace(domainBlockRegex, '\n');

      try {
        FileUtils.writeFile(corefilePath, corefileContent, `Updated Corefile (removed ${domain})`);
      } catch (writeError) {
        logger.error(`Error writing Corefile: ${writeError.message}`);
        throw new Error(`Failed to update Corefile: ${writeError.message}`);
      }
    } else {
      logger.warn(`Domain ${domain} not found in Corefile`);
    }
  }

  /**
   * Reload CoreDNS configuration
   * @returns {Promise<void>}
   */
  static async reloadConfig() {
    try {
      logger.info('Reloading CoreDNS configuration');

      // Check if we're running in Docker or locally
      const isDocker = FileUtils.fileExists('/.dockerenv');
      logger.info(`Running in Docker environment: ${isDocker}`);

      if (isDocker) {
        // In Docker, use docker kill command
        try {
          await execPromise('docker kill --signal=SIGUSR1 coredns');
          logger.success('CoreDNS configuration reloaded successfully via Docker signal');
        } catch (dockerError) {
          logger.warn(`Could not send reload signal to CoreDNS container: ${dockerError.message}`);
          logger.warn('CoreDNS will automatically reload configuration within 30 seconds');
        }
      } else {
        // For local development, try to use the reload script if available
        const reloadScript = dnsConfig.isWindows ? 'coredns\\reload-config.ps1' : './coredns/reload-config.sh';

        if (FileUtils.fileExists(reloadScript)) {
          try {
            const cmd = dnsConfig.isWindows ?
              `powershell -ExecutionPolicy Bypass -File ${reloadScript}` :
              `bash ${reloadScript}`;

            await execPromise(cmd);
            logger.success('CoreDNS configuration reloaded successfully via script');
          } catch (scriptError) {
            logger.warn(`Could not reload CoreDNS via script: ${scriptError.message}`);
            logger.warn('CoreDNS will automatically reload configuration within 30 seconds');
          }
        } else {
          logger.info(`Reload script not found at ${reloadScript}`);
          logger.info('CoreDNS will automatically reload configuration within 30 seconds');
        }
      }
    } catch (error) {
      logger.error(`Error in reloadConfig: ${error.message}`);
      // Don't throw the error, just log it - we don't want to fail the entire operation
      // if we can't reload the config, as CoreDNS will reload it automatically
    }
  }
}

module.exports = {
  addDomain: DNSManager.addDomain,
  removeDomain: DNSManager.removeDomain,
  reloadConfig: DNSManager.reloadConfig
};
