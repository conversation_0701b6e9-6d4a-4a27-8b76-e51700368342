/**
 * Custom Quill Styles
 * This file contains styles for the Quill editor
 * It's used to avoid direct imports of Quill CSS files in components
 */

/* Import Quill core styles */
@import 'quill/dist/quill.core.css';

/* Import Quill snow theme */
@import 'quill/dist/quill.snow.css';

/* Custom Quill styles */
.ql-toolbar.ql-snow {
  background-color: #f5f7fc;
  border: 1px solid #e2e8f0;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  font-family: inherit;
  width: 100%;
}

.ql-container.ql-snow {
  background-color: #f5f7fc;
  border: 1px solid #e2e8f0;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  font-family: inherit;
  width: 100%;
}

.ql-editor {
  min-height: 150px;
  font-family: inherit;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
}

/* Fix for Quill SVG icons */
.ql-snow .ql-stroke {
  stroke: #333;
  stroke-width: 1.5;
}

.ql-toolbar.ql-snow .ql-formats {
  display: inline-block;
  vertical-align: middle;
  margin-right: 15px;
}

.ql-snow .ql-fill {
  fill: #333;
}

.ql-snow .ql-picker {
  color: #333;
}

/* Full width editor */
.full-width-editor {
  width: 100% !important;
  display: block !important;
}

.quill-editor-full-width {
  width: 100% !important;
  display: block !important;
}

.quill-editor-full-width .ql-container,
.quill-editor-full-width .ql-toolbar {
  width: 100% !important;
}

/* Error state */
.quill-error .ql-container,
.quill-error .ql-toolbar {
  border-color: #ef4444 !important;
}
