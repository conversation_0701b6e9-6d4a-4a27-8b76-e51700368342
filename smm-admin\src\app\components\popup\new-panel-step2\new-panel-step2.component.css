.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: relative;
}

.back-button {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  color: var(--primary-color);
  font-weight: 500;
}

.back-button:hover {
  color: var(--primary-color-hover);
}

.icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  background-color: var(--primary-color);
  color: white;
}

.puzzle-icon {
  font-size: 1.25rem;
}

.header-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.description {
  color: #4b5563;
  font-size: 0.875rem;
  text-align: center;
}

.steps-container {
  width: 100%;
  margin-bottom: 1.5rem;
}

.steps-container > * + * {
  margin-top: 1rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 1.5rem;
  border-radius: 9999px;
  background-color: #f3f4f6;
  color: #1f2937;
  font-size: 0.875rem;
  font-weight: 500;
}

.step-text {
  color: #4b5563;
  font-size: 0.875rem;
}

.dns-servers {
  width: 100%;
  margin: 0.5rem 0;
  padding-left: 2.25rem;
}

.dns-servers > * + * {
  margin-top: 0.5rem;
}

.dns-server {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: #f5f7fc;
  border-radius: 0.5rem;
  color: #1f2937;
  font-size: 0.875rem;
}

.copy-button {
  color: #6b7280;
  padding: 0.25rem;
}

.copy-button:hover {
  color: var(--primary-color);
}

.next-button {
  width: 100%;
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
}

.next-button:hover {
  background-color: var(--primary-color-hover);
}

.next-button:active {
  background-color: var(--primary-color-active);
}
