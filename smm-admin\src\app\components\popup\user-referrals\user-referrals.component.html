<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-hidden">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b border-gray-200 bg-[#f8fafc]">
      <div class="flex items-center">
        <fa-icon [icon]="['fas', 'users']" class="text-[#3b82f6] mr-2"></fa-icon>
        <h2 class="text-xl font-bold text-gray-800">
          {{ 'Referral System' | translate }} - {{ userName }}
        </h2>
      </div>
      <button (click)="onCloseClick()" class="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3b82f6]"></div>
    </div>

    <!-- Content -->
    <div *ngIf="!isLoading" class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
      <!-- User info and stats -->
      <div class="mb-6 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
        <!-- Stats header -->
        <div class="bg-[#f8fafc] px-4 py-3 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">{{ 'Referral Statistics' | translate }}</h3>
        </div>

        <!-- Stats content -->
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Left column - Referral counts -->
            <div class="space-y-4">
              <!-- Total Referrals Card -->
              <div class="flex items-center p-4 bg-[#f0f7ff] rounded-lg border border-[#e0eeff] h-[88px]">
                <div class="flex-shrink-0 w-10 h-10 bg-[#3b82f6] rounded-full flex items-center justify-center text-white mr-3">
                  <fa-icon [icon]="['fas', 'user-friends']"></fa-icon>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">{{ 'Total Referrals' | translate }}</div>
                  <div class="text-2xl font-bold text-gray-800">{{ stats?.total_referrals || 0 }}</div>
                </div>
              </div>

              <!-- Total Earnings Card -->
              <div class="flex items-center p-4 bg-[#f0fff5] rounded-lg border border-[#e0ffe0] h-[88px]">
                <div class="flex-shrink-0 w-10 h-10 bg-[#10b981] rounded-full flex items-center justify-center text-white mr-3">
                  <fa-icon [icon]="['fas', 'dollar-sign']"></fa-icon>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">{{ 'Total Earnings' | translate }}</div>
                  <div class="text-2xl font-bold text-green-600">{{ stats?.total_earnings || 0 | currency }}</div>
                </div>
              </div>
            </div>

            <!-- Right column - Referral percentages -->
            <div class="space-y-4">
              <!-- Default Percent Card -->
              <div class="p-4 bg-[#f8fafc] rounded-lg border border-gray-200 h-[88px] flex flex-col justify-center">
                <div class="flex justify-between items-center">
                  <div class="text-sm font-medium text-gray-800">{{ 'Default Percent' | translate }}</div>
                  <div class="px-2 py-1 bg-gray-100 rounded-md text-xs font-medium text-gray-600">{{ 'System' | translate }}</div>
                </div>
                <div class="text-2xl font-bold text-gray-800 mt-1">{{ stats?.default_referral_rate || 0 }}%</div>
              </div>

              <!-- Custom Referral Percent Card -->
              <div class="p-4 bg-[#f8fafc] rounded-lg border border-gray-200"
                   [ngClass]="{'h-[88px] flex flex-col justify-center': !isEditingRate, 'min-h-[88px]': isEditingRate}">
                <div class="flex justify-between items-center">
                  <div class="text-sm font-medium text-gray-800">{{ 'Custom Referral Percent' | translate }}</div>
                  <button *ngIf="!isEditingRate" (click)="isEditingRate = true"
                    class="text-[#3b82f6] hover:text-[#2563eb] p-1 rounded hover:bg-blue-50">
                    <fa-icon [icon]="['fas', 'pen']"></fa-icon>
                  </button>
                </div>

                <div *ngIf="!isEditingRate" class="text-2xl font-bold text-gray-800 mt-1">
                  {{ stats?.custom_referral_rate || 0 }}%
                </div>

                <div *ngIf="isEditingRate" class="mt-2">
                  <div class="flex items-center">
                    <div class="relative flex-grow">
                      <input type="number" [(ngModel)]="customRate" min="0" max="100" step="1"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3b82f6]"
                        (input)="validateCustomRate()">
                      <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span class="text-gray-500">%</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">{{ 'Default' | translate }}: {{ stats?.default_referral_rate || 0 }}%</div>
                    <div class="flex space-x-2">
                      <button (click)="cancelEdit()"
                        class="px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                        {{ 'Cancel' | translate }}
                      </button>
                      <button (click)="saveCustomRate()"
                        class="px-3 py-1.5 text-xs bg-[#3b82f6] text-white rounded-md hover:bg-[#2563eb] transition-colors">
                        {{ 'Save' | translate }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Referrals list -->
      <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
        <div class="bg-[#f8fafc] px-4 py-3 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-800">{{ 'Referrals' | translate }}</h3>
          <div class="text-sm text-gray-500">{{ stats?.total_referrals || 0 }} {{ 'users' | translate }}</div>
        </div>

        <div *ngIf="referrals.length === 0" class="text-center py-12 px-4">
          <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
            <fa-icon [icon]="['fas', 'search']" class="text-gray-400 text-xl"></fa-icon>
          </div>
          <p class="text-gray-500 font-medium">{{ 'Referrals not found' | translate }}</p>
          <p class="text-gray-400 text-sm mt-1">{{ 'This user has no referrals yet' | translate }}</p>
        </div>

        <div *ngIf="referrals.length > 0" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-[#f8fafc]">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'User' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'Email' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'Balance' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'Status' | translate }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ 'Joined' | translate }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let referral of referrals" class="hover:bg-[#f8fafc] transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full object-cover border border-gray-200"
                        [src]="referral.avatar || 'assets/images/default-avatar.png'"
                        alt="{{ referral.user_name }}">
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ referral.user_name }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ referral.email }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"
                  [ngClass]="{'text-green-600': referral.balance > 0, 'text-gray-500': referral.balance <= 0}">
                  {{ referral.balance | currency }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span [ngClass]="{
                    'bg-green-100 text-green-800': referral.status === 'ACTIVATED',
                    'bg-red-100 text-red-800': referral.status === 'DEACTIVATED'
                  }" class="px-2 py-1 text-xs font-medium rounded-full">
                    {{ referral.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ referral.created_at | date:'medium' }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div *ngIf="referrals.length > 0" class="flex justify-between items-center px-6 py-3 bg-[#f8fafc] border-t border-gray-200">
          <div class="text-sm text-gray-700">
            {{ 'Showing' | translate }} <span class="font-medium">{{ pagination.pageNumber * pagination.pageSize + 1 }}</span> {{ 'to' | translate }}
            <span class="font-medium">{{ (pagination.pageNumber * pagination.pageSize) + referrals.length }}</span> {{ 'of' | translate }}
            <span class="font-medium">{{ pagination.totalElements }}</span> {{ 'results' | translate }}
          </div>
          <div class="flex space-x-2">
            <button
              [disabled]="pagination.pageNumber === 0"
              [ngClass]="{'opacity-50 cursor-not-allowed': pagination.pageNumber === 0}"
              (click)="loadReferrals(pagination.pageNumber - 1)"
              class="px-3 py-1.5 border border-gray-300 rounded-md text-sm hover:bg-white transition-colors">
              {{ 'Previous' | translate }}
            </button>
            <button
              [disabled]="pagination.pageNumber >= pagination.totalPages - 1"
              [ngClass]="{'opacity-50 cursor-not-allowed': pagination.pageNumber >= pagination.totalPages - 1}"
              (click)="loadReferrals(pagination.pageNumber + 1)"
              class="px-3 py-1.5 border border-gray-300 rounded-md text-sm hover:bg-white transition-colors">
              {{ 'Next' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
