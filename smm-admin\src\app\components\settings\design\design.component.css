/* Main Container */
.design-settings {
  @apply bg-white rounded-lg md:p-6 max-w-6xl mx-auto;
}

/* Header Styles */
.settings-header {
  @apply mb-8;
}

.settings-title {
  @apply text-2xl font-bold text-gray-800;
}

.settings-description {
  @apply text-gray-500 mt-1;
}

/* Card Styles */
.settings-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 mb-6 overflow-hidden;
}

.card-header {
  @apply bg-gray-50 px-6 py-4 border-b border-gray-200;
}

.card-title {
  @apply text-lg font-medium text-gray-800 flex items-center;
}

.section-icon {
  @apply text-gray-500 mr-3;
}

.card-content {
  @apply p-6 space-y-6;
}

/* Logo & Favicon Section */
.upload-item {
  @apply mb-6 last:mb-0;
}

.upload-item-header {
  @apply mb-2;
}

.upload-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.upload-format {
  @apply text-xs text-gray-500;
}

.upload-container {
  @apply flex items-center gap-4;
}

.preview-box {
  width: 200px;
  height: 64px;
  @apply rounded-md flex items-center justify-center overflow-hidden;
}

.light-bg {
  @apply bg-gray-100;
}

.dark-bg {
  @apply bg-gray-800;
}

.preview-placeholder {
  @apply text-gray-400 font-medium;
}

.preview-image {
  @apply max-h-full max-w-full;
}

.upload-button {
  @apply bg-blue-500 text-white px-4 py-2 rounded-md flex items-center gap-2 relative overflow-hidden hover:bg-blue-600 transition-colors;
}

.hidden-input {
  @apply absolute inset-0 opacity-0 cursor-pointer;
  width: 100%;
  height: 100%;
}

.upload-actions {
  @apply flex flex-col gap-2;
}

.upload-progress {
  @apply bg-gray-200 h-2 rounded-full overflow-hidden w-full mt-2;
}

.progress-bar {
  @apply bg-blue-500 h-full transition-all duration-300;
}

.upload-success {
  @apply text-green-500 flex items-center gap-1 text-sm mt-1;
}

.upload-error {
  @apply text-red-500 flex items-center gap-1 text-sm mt-1;
}

/* Favicon Specific Styles */
.favicon-container {
  @apply flex items-center gap-4;
}

.favicon-preview {
  @apply flex items-center gap-3;
}

.favicon-box {
  width: 40px;
  height: 40px;
  @apply bg-gray-100 rounded-md flex items-center justify-center overflow-hidden;
}

.favicon-placeholder {
  @apply text-xs text-gray-400;
}

.favicon-image {
  @apply max-h-full max-w-full;
}

.favicon-url-container {
  @apply relative;
}

.favicon-url {
  @apply px-3 py-2 border border-gray-200 rounded-md text-sm pr-16;
  width: 180px;
}

.favicon-actions {
  @apply absolute right-1 top-1/2 -translate-y-1/2 flex gap-1;
}

.action-button {
  @apply bg-gray-100 p-1.5 rounded text-gray-600 hover:bg-gray-200 transition-colors;
}

/* Colors Section */
.color-item {
  @apply mb-6 last:mb-0;
}

.color-header {
  @apply flex items-center justify-between mb-2;
}

.color-label {
  @apply block text-sm font-medium text-gray-700;
}

.toggle-item {
  @apply flex items-center justify-between mb-6;
}

.toggle-label {
  @apply text-sm font-medium text-gray-700;
}

.color-options {
  @apply flex flex-wrap gap-3;
}

.color-option {
  width: 36px;
  height: 36px;
  @apply rounded-full flex items-center justify-center cursor-pointer relative transition-all;
  border: 2px solid transparent;
}

.color-option.selected {
  @apply border-blue-500 transform scale-110;
}

.check-icon {
  @apply text-white text-xs;
}

/* Custom Color Input */
.custom-color-btn {
  @apply text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded flex items-center gap-1 hover:bg-gray-200 transition-colors;
}

/* Header Style Options */
.header-style-options {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.header-style-option {
  @apply border border-gray-200 rounded-lg p-3 cursor-pointer transition-all relative hover:border-gray-300;
}

.header-style-option.selected {
  @apply border-blue-500 bg-blue-50;
}

.header-style-preview {
  @apply bg-white rounded mb-2 h-24 flex items-center justify-center overflow-hidden;
}

.header-style-preview.active-preview {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

.preview-header-container {
  @apply w-full flex flex-col;
}

.preview-announcement {
  @apply bg-[var(--primary)] h-2 w-full;
}

.preview-header {
  @apply w-full h-12 flex justify-between items-center px-2;
}

.default-header {
  @apply bg-white;
}

.compact-header {
  @apply bg-gray-50 py-1;
}

.modern-header {
  @apply bg-white;
}

.minimal-header {
  @apply bg-white border-b border-gray-200;
}

.preview-left, .preview-right {
  @apply flex items-center gap-2;
}

.preview-nav {
  @apply bg-gray-700 h-2 w-10 rounded;
}

.preview-nav.small {
  @apply h-1.5 w-8;
}

.preview-nav.uppercase {
  @apply bg-gray-600;
}

.preview-nav.light {
  @apply bg-white;
}

.preview-nav.underlined {
  @apply border-b-2 border-[var(--primary)] bg-gray-800;
}

.preview-balance {
  @apply bg-gray-200 h-4 w-12 rounded;
}

.preview-balance.green {
  @apply bg-green-100;
}

.preview-balance.primary {
  @apply bg-[var(--primary)] bg-opacity-10 border border-[var(--primary)] border-opacity-20;
}

.preview-balance.blue {
  @apply bg-blue-50 border border-blue-200;
}

.preview-balance.small {
  @apply h-3 w-10;
}

.preview-balance.light {
  @apply bg-white bg-opacity-20;
}

.preview-balance.bordered {
  @apply bg-white border border-gray-200;
}

.preview-balance.bordered-left {
  @apply bg-transparent border-l border-gray-200 rounded-none;
}

.preview-icon {
  @apply bg-gray-200 h-5 w-5 rounded-full;
}

.preview-icon.small {
  @apply h-4 w-4;
}

.preview-icon.light {
  @apply bg-white bg-opacity-30;
}

.preview-logo {
  @apply bg-gray-700 h-4 w-16 rounded;
}

.preview-logo.small {
  @apply h-3 w-8;
}

.preview-button {
  @apply bg-[var(--primary)] h-5 w-10 rounded;
}

.header-style-label {
  @apply text-center text-sm font-medium text-gray-700;
}

.header-style-check {
  @apply absolute top-2 right-2 text-blue-500;
}

/* Sidebar Style Previews */
.preview-sidebar {
  @apply w-full h-full flex flex-col p-2;
}

.preview-sidebar-section {
  @apply flex flex-col gap-1;
}

.preview-sidebar-title {
  @apply bg-gray-300 h-2 w-16 rounded mb-2;
}

.preview-sidebar-title.small {
  @apply h-1.5 w-12;
}

.preview-sidebar-item {
  @apply bg-gray-200 h-3 w-full rounded-md mb-1;
}

.preview-sidebar-item.small {
  @apply h-2;
}

.preview-sidebar-item.active {
  @apply bg-[var(--primary)] bg-opacity-20 border-l-2 border-[var(--primary)];
}

.preview-sidebar-item.active-indicator {
  @apply bg-gray-200 h-3 w-full rounded-md mb-1 relative;
}

.preview-sidebar-item.active-indicator::after {
  content: '';
  @apply absolute right-0 top-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-[var(--primary)] rounded-full;
}

.preview-sidebar-grid {
  @apply grid grid-cols-2 gap-1;
}

.preview-sidebar-card {
  @apply bg-white h-8 rounded-md border border-gray-200 cursor-pointer;
}

.preview-sidebar-card.active {
  @apply border-l-2 border-[var(--primary)];
}

/* Sidebar Style Variants */
.default-sidebar {
  @apply bg-gray-100;
}

.default-sidebar .preview-sidebar-section {
  @apply bg-white rounded-lg p-2;
}

.compact-sidebar {
  @apply bg-gray-100;
}

.compact-sidebar .preview-sidebar-section {
  @apply bg-white rounded-lg p-2;
}

.modern-sidebar {
  @apply bg-gray-100;
}

.modern-sidebar .preview-sidebar-section {
  @apply bg-white rounded-lg shadow-sm p-2;
}

.minimal-sidebar {
  @apply bg-gray-100;
}

.minimal-sidebar .preview-sidebar-section {
  @apply bg-white rounded-lg p-2;
}

.card-sidebar {
  @apply bg-gray-100;
}

.card-sidebar .preview-sidebar-grid {
  @apply p-2 bg-white rounded-lg;
}

.custom-color-input {
  @apply flex gap-2 mb-3;
}

.hex-input {
  @apply px-3 py-2 border border-gray-200 rounded-md text-sm w-32;
}

.apply-color-btn {
  @apply bg-blue-500 text-white px-3 py-2 rounded-md flex items-center gap-1 hover:bg-blue-600 transition-colors text-sm;
}

.color-preview {
  @apply flex items-center gap-2 mt-3;
}

.preview-swatch {
  width: 24px;
  height: 24px;
  @apply rounded-md border border-gray-200;
}

.preview-value {
  @apply text-sm text-gray-600 font-mono;
}

/* Preview Section */
.preview-container {
  @apply bg-gray-50 p-6 rounded-lg flex flex-wrap items-center justify-between gap-6;
}

.preview-item {
  @apply flex flex-col items-center gap-3;
}

.preview-label {
  @apply text-sm font-medium text-gray-600;
}

.active-link-preview {
  @apply font-medium;
}

.button-preview {
  @apply px-4 py-2 rounded-md flex items-center gap-2;
}

.icon-preview {
  @apply p-3 rounded-md;
}

.primary-button-preview {
  @apply px-4 py-2 rounded-md flex items-center gap-2 shadow-sm;
}

.primary-elements-preview {
  @apply flex gap-2;
}

.primary-elements-preview .element {
  width: 24px;
  height: 24px;
  @apply rounded-md;
}

/* Order View Section */
.order-view-item {
  @apply mb-6 last:mb-0;
}

.order-view-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.social-networks {
  @apply space-y-3;
}

.social-network-item {
  @apply flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer;
}

.social-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-white;
}

.social-icon.telegram {
  @apply bg-blue-500;
}

.social-icon.youtube {
  @apply bg-red-500;
}

.social-icon.instagram {
  @apply bg-pink-500;
}

.social-network-name {
  @apply font-medium;
}

.chevron-icon {
  @apply ml-auto text-gray-400;
}

.service-selection {
  @apply mb-4;
}

.service-item {
  @apply flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer;
}

.service-icon {
  @apply text-yellow-500;
}

.service-name {
  @apply font-medium;
}

.view-toggle {
  @apply flex gap-6;
}

.view-option {
  @apply flex flex-col items-center gap-2 cursor-pointer;
}

.view-icon {
  @apply w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center transition-colors;
}

.view-icon.active {
  @apply bg-blue-500 text-white;
}

.view-option-label {
  @apply text-sm font-medium;
}

/* Landing Section */
.landing-settings {
  @apply flex flex-col md:flex-row gap-6;
}

.landing-options {
  @apply md:w-1/3;
}

.landing-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.landing-types {
  @apply flex flex-wrap gap-2 mb-4;
}

.landing-type {
  @apply px-4 py-2 border border-gray-200 rounded-md cursor-pointer hover:border-gray-300 transition-colors;
}

.landing-type.selected {
  @apply bg-blue-500 text-white border-blue-500;
}

.edit-text-btn {
  @apply bg-white border border-gray-200 text-gray-700 px-4 py-2 rounded-md flex items-center gap-2 hover:bg-gray-50 transition-colors;
}

.landing-preview-wrapper {
  @apply md:w-2/3;
}

.landing-preview {
  @apply relative;
}

.landing-preview-container {
  @apply bg-gray-900 text-white p-6 rounded-lg h-72 flex flex-col;
}

.landing-header {
  @apply flex justify-between items-center mb-8;
}

.landing-logo {
  @apply font-bold text-lg;
}

.landing-actions {
  @apply flex gap-4 text-sm;
}

.landing-content {
  @apply flex-1 flex items-center;
}

.landing-title {
  @apply text-3xl font-bold;
}

.landing-scroll-indicator {
  @apply absolute bottom-4 right-4 w-6 h-12 border-2 border-white rounded-full opacity-50;
}

/* Save Button */
.save-section {
  @apply flex justify-end mt-8;
}

.save-button {
  @apply bg-blue-500 text-white px-6 py-3 rounded-md font-medium flex items-center gap-2 hover:bg-blue-600 transition-colors;
}
