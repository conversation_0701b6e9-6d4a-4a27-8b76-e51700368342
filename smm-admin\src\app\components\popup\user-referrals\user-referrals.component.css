/* Custom styles for the user referrals popup */
:host {
  display: block;
}

/* Ensure the popup is above other elements */
.fixed {
  z-index: 1000;
}

/* Add smooth transitions */
button, tr, .hover\:bg-white, .hover\:bg-gray-50, .hover\:bg-gray-100, .hover\:text-gray-700, .hover\:text-gray-800, .hover\:bg-blue-50 {
  transition: all 0.2s ease;
}

/* Custom scrollbar for the content area */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Card hover effects */
.bg-white.border {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bg-white.border:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .max-w-3xl {
    width: 95%;
  }

  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .text-2xl {
    font-size: 1.25rem;
  }

  /* Ensure buttons have enough space on mobile */
  .flex.space-x-2 {
    gap: 0.5rem;
  }

  /* Improve input field appearance on mobile */
  input[type="number"] {
    font-size: 16px; /* Prevents iOS zoom on focus */
    padding: 0.5rem;
  }

  /* Ensure the custom referral card has enough height in edit mode */
  /* .min-h-88 {
    min-height: 120px;
  } */
}

/* Animation for loading spinner */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
