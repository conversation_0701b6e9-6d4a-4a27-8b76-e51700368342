import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpRequest
} from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

export function errorInterceptorFn(request: HttpRequest<unknown>, next: HttpHandlerFn): Observable<any> {
  const router = inject(Router);

  return next(request).pipe(
    catchError((err: HttpErrorResponse) => {
      // Log the error for debugging
      console.error('HTTP Error:', err);

      // Check if we're already on an error page to prevent infinite loops
      const currentUrl = router.url;
      const isOnErrorPage = currentUrl.startsWith('/error/');

      // Handle different error status codes
      switch (err.status) {
        // case 404:
        //   // Not Found
        //   if (!isOnErrorPage) {
        //     router.navigate(['/error/404']).then();
        //   }
        //   break;

        case 403:
          // Forbidden
          if (!isOnErrorPage) {
            router.navigate(['/error/403']).then();
          }
          break;

        case 401:
          // Unauthorized - Let JWT interceptor handle most 401 errors
          // Only handle specific cases here
          if (request.url.includes('access/refresh')) {
            // For token refresh failures, redirect to login
            console.log('Error Interceptor - Refresh token failure, redirecting to login');
            const isOnAuthPage = router.url.includes('/auth/');
            if (!isOnAuthPage) {
              localStorage.removeItem('user');
              router.navigate(['/auth/login']).then();
            }
          } else if (!request.url.includes('access/login') && !request.url.includes('access/refresh')) {
            // Only handle 401 errors that JWT interceptor didn't handle
            const isOnAuthPage = router.url.includes('/auth/');
            if (!isOnErrorPage && !isOnAuthPage) {
              console.log('Error Interceptor - 401 error not handled by JWT interceptor, redirecting to login');
              // Clear user data and redirect to login
              localStorage.removeItem('user');
              router.navigate(['/auth/login']).then();
              return throwError(() => err);
            }
          }
          break;

        case 502:
        case 503:
        case 504:
          // Server errors
          if (!isOnErrorPage) {
            router.navigate(['/error/502']).then();
          }
          break;
      }

      // Return the error for further handling
      const error = err.error || err.statusText;
      return throwError(() => error);
    })
  );
}
