import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize, switchMap, tap } from 'rxjs';
import { TicketPageRes, TicketReply, TicketRes } from '../../model/response/ticket-res.model';
import { TicketFilter } from '../../model/request/ticket-filter.model';
import { ConfigService } from './config.service';

interface CreateTicketRequest {
  subject: string;
  description: string;
  additionInfo?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TicketService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _tickets$ = new BehaviorSubject<TicketRes[]>([]);
  private _pagination$ = new BehaviorSubject<any>({
    pageNumber: 0,
    pageSize: 20,
    totalElements: 0,
    totalPages: 0
  });
  private _search$ = new Subject<string>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Set up subscription for search
    this._search$
      .pipe(
        tap(() => this._loading$.next(true)),
        switchMap(keyword => this.searchTickets(keyword))
      )
      .subscribe((result: TicketPageRes) => {
        this._tickets$.next(result.content);
        this._pagination$.next({
          pageNumber: result.pageable.page_number,
          pageSize: result.pageable.page_size,
          totalElements: result.total_elements,
          totalPages: result.total_pages
        });
        this._loading$.next(false);
      });
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get tickets$(): BehaviorSubject<TicketRes[]> {
    return this._tickets$;
  }

  get pagination$(): BehaviorSubject<any> {
    return this._pagination$;
  }

  /**
   * Trigger a search for tickets
   * @param keyword The search keyword
   */
  search(keyword: string): void {
    this._search$.next(keyword);
  }

  /**
   * Get all tickets for the current user
   */
  getMyTickets(page: number = 0, size: number = 20): Observable<TicketPageRes> {
    this._loading$.next(true);
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http
      .get<TicketPageRes>(`${this.configService.apiUrl}/tickets/me/search`, { params })
      .pipe(
        tap(result => {
          this._tickets$.next(result.content);
          this._pagination$.next({
            pageNumber: result.pageable.page_number,
            pageSize: result.pageable.page_size,
            totalElements: result.total_elements,
            totalPages: result.total_pages
          });
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Search tickets by keyword
   * @param keyword The search keyword
   */
  searchTickets(keyword: string, page: number = 0, size: number = 20): Observable<TicketPageRes> {
    let params = new HttpParams()
      .set('keyword', keyword)
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http
      .get<TicketPageRes>(`${this.configService.apiUrl}/tickets/me/search`, { params });
  }

  /**
   * Get tickets by filters for admin
   * @param filter The ticket filter
   * @param page Page number
   * @param size Page size
   */
  getTicketsByFilters(filter: TicketFilter, page: number = 0, size: number = 20): Observable<TicketPageRes> {
    this._loading$.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (filter.status) {
      params = params.set('status', filter.status);
    }

    if (filter.userName) {
      params = params.set('userName', filter.userName);
    }

    if (filter.startDate) {
      params = params.set('startDate', filter.startDate);
    }

    if (filter.endDate) {
      params = params.set('endDate', filter.endDate);
    }

    return this.http
      .get<TicketPageRes>(`${this.configService.apiUrl}/tickets/search`, { params })
      .pipe(
        tap(result => {
          this._tickets$.next(result.content);
          this._pagination$.next({
            pageNumber: result.pageable.page_number,
            pageSize: result.pageable.page_size,
            totalElements: result.total_elements,
            totalPages: result.total_pages
          });
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Get a specific ticket by ID
   * @param id The ticket ID
   */
  getTicketById(id: number): Observable<TicketRes> {
    return this.http.get<TicketRes>(`${this.configService.apiUrl}/tickets/${id}`);
  }

  /**
   * Reply to a ticket
   * @param ticketId The ticket ID
   * @param content The reply content
   */
  replyToTicket(ticketId: number, content: string): Observable<TicketReply> {
    return this.http.post<TicketReply>(`${this.configService.apiUrl}/tickets/${ticketId}/replies`, { content });
  }

  /**
   * Create a new ticket
   * @param subject The ticket subject
   * @param description The ticket description
   * @param additionInfo Additional information (optional)
   */
  createTicket(subject: string, description: string, additionInfo?: string): Observable<TicketRes> {
    this._loading$.next(true);

    const requestBody: CreateTicketRequest = {
      subject,
      description,
      ...(additionInfo && { additionInfo })
    };

    return this.http
      .post<TicketRes>(`${this.configService.apiUrl}/tickets`, requestBody)
      .pipe(
        tap(newTicket => {
          // Update the tickets list with the new ticket
          const currentTickets = this._tickets$.value;
          // Add the new ticket to the end of the list
          this._tickets$.next([...currentTickets, newTicket]);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Update ticket status
   * @param ticketId The ticket ID
   * @param status The new status
   */
  updateTicketStatus(ticketId: number, status: string): Observable<TicketRes> {
    return this.http
      .put<TicketRes>(`${this.configService.apiUrl}/tickets/${ticketId}/status`, status)
      .pipe(
        tap(updatedTicket => {
          // Update the ticket in the list
          const currentTickets = this._tickets$.value;
          const index = currentTickets.findIndex(t => t.id === ticketId);
          if (index !== -1) {
            currentTickets[index] = updatedTicket;
            this._tickets$.next([...currentTickets]);
          }
        })
      );
  }
}
