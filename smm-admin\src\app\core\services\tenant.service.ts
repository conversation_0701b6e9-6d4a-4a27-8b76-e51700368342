import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { ConfigService } from './config.service';
import { AuthService } from './auth.service';
import { tap } from 'rxjs/operators';
import { JwtHelperService } from '@auth0/angular-jwt';
import { Tokens } from '../../model/tokens.model';

export interface TenantInfo {
  id: string;
  name: string;
  domain: string;
  status: string;
   subscription_start_date: string;
  subscription_end_date: string;
  days_until_expiration: number;
  expiration_alert: string;
  auto_renewal: boolean;
  subscription_plan: string;
  contact_email: string;
}

export interface TenantSubscriptionDto {
  id: string;
  domain: string;
  status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  days_until_expiration: number;
  expiration_alert: string;
  auto_renewal: boolean;
  subscription_plan: string;
  contact_email: string;
}



@Injectable({
  providedIn: 'root'
})
export class TenantService {
  private _currentTenant$ = new BehaviorSubject<string | null>(null);
  private _accessibleTenants$ = new BehaviorSubject<TenantInfo[]>([]);

  public currentTenant$ = this._currentTenant$.asObservable();
  public accessibleTenants$ = this._accessibleTenants$.asObservable();

  /**
   * Get current tenant value synchronously
   */
  public get currentTenantValue(): string | null {
    return this._currentTenant$.value;
  }

  /**
   * Get accessible tenants value synchronously
   */
  public get accessibleTenantsValue(): TenantInfo[] {
    return this._accessibleTenants$.value;
  }

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private authService: AuthService,
    private jwtHelper: JwtHelperService
  ) {
    // Initialize tenant information from token
    this.updateTenantInfoFromToken();

    // Subscribe to auth changes to update tenant info
    this.authService.auth$.subscribe(user => {
      if (user && user.tokens && user.tokens.access_token) {
        this.updateTenantInfoFromToken();
      } else {
        this._currentTenant$.next(null);
        this._accessibleTenants$.next([]);
      }
    });
  }

  /**
   * Extract tenant information from the JWT token
   */
  private updateTenantInfoFromToken(): void {
    const user = this.authService.authValue;
    if (user && user.tokens && user.tokens.access_token) {
      try {
        const tokenPayload = this.jwtHelper.decodeToken(user.tokens.access_token);
        console.log('TenantService - Token payload:', tokenPayload);

        // Extract current tenant
        const currentTenant = tokenPayload.current_tenant_id;
        if (currentTenant) {
          this._currentTenant$.next(currentTenant);
           if (typeof localStorage !== 'undefined') {
              localStorage.setItem('tenant_id', currentTenant);
           }
        }

        // Extract accessible tenants from token
        // We'll use the API to get the full tenant info with domains
      } catch (error) {
        console.error('Error extracting tenant info from token:', error);
      }
    }
  }

  /**
   * Switch to a different tenant
   * @param tenantId The ID of the tenant to switch to
   */
  switchTenant(tenantId: string): Observable<Tokens> {
    return this.http.post<Tokens>(
      `${this.configService.apiUrl}/access/switch/${tenantId}`,
      {}
    ).pipe(
      tap(response => {

        // // Update the token in local storage
        const user = this.authService.authValue;
        if (user && user.tokens) {
          user.tokens.access_token = response.access_token;
          user.tokens.refresh_token = response.refresh_token;
          user.tokens.client_id = response.client_id;
          this.authService.updateUser(user);
        }

         if (typeof localStorage !== 'undefined') {
          // Clear design settings
          localStorage.removeItem('app_design_settings');

          // Clear tenant ID
        //  localStorage.removeItem('tenant_id');
//
          // Clear general settings
          localStorage.removeItem('general_settings');
        }

        // Update tenant information
        this.updateTenantInfoFromToken();

                // Clear tenant-specific settings from localStorage


        // Refresh the list of accessible tenants with domain info
        // this.getAccessibleTenants().subscribe(tenants => {
        //   this._accessibleTenants$.next(tenants);
        // });
      })
    );
  }

  /**
   * Reload necessary data after tenant switch without page refresh
   * This method should be called after switching tenants to update the UI
   */
  reloadDataAfterTenantSwitch(): void {
    // Update tenant information from token
    this.updateTenantInfoFromToken();

    // Refresh the list of accessible tenants
    this.getAccessibleTenants().subscribe();

    // You can add more data refresh logic here as needed
    // For example, reload current page data based on the new tenant
  }

  /**
   * Get information about all accessible tenants
   */
  getAccessibleTenants(): Observable<TenantInfo[]> {
    return this.http.get<TenantInfo[]>(`${this.configService.apiUrl}/access/tenants/accessible`)
      .pipe(
        tap(tenants => {
          this._accessibleTenants$.next(tenants);
        })
      );
  }

  /**
   * Get all tenant subscriptions
   */
  getAllTenantSubscriptions(): Observable<TenantSubscriptionDto[]> {
    return this.http.get<TenantSubscriptionDto[]>(`${this.configService.apiUrl}/tenant-subscriptions`);
  }

  /**
   * Toggle auto-renewal for a tenant
   */
  toggleAutoRenewal(tenantId: string, auto_renewal: boolean): Observable<any> {
    return this.http.put(`${this.configService.apiUrl}/tenant-subscriptions/${tenantId}/auto-renewal`, {
      auto_renewal
    });
  }
}
