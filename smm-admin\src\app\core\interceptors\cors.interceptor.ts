import {
  HttpHandlerFn,
  HttpRequest
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

/**
 * Interceptor to handle CORS issues
 * This interceptor modifies requests to handle CORS issues
 */
export function corsInterceptorFn(request: HttpRequest<unknown>, next: HttpHandlerFn): Observable<any> {
  // Only apply CORS modifications to API requests
  if (request.url.startsWith(environment.apiUrl)) {
    console.log('CORS Interceptor - Processing API request:', request.url);

    // Clone the request with CORS settings
    const corsRequest = request.clone({
      // Don't set CORS headers here as they should be set by the server
      // Instead, ensure credentials are properly handled
      withCredentials: false // Set to false to avoid CORS preflight issues
    });

    return next(corsRequest);
  }

  // For non-API requests, pass through unchanged
  return next(request);
}
