.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.category-form {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)];
}

.form-input {
  @apply w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent;
}

.input-error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-500 mt-1;
}

.error-alert {
  @apply p-3 bg-red-100 text-red-700 rounded-lg text-sm;
}

.create-button {
  @apply w-full bg-[var(--primary)] text-white font-medium py-3 px-4 rounded-lg hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed;
}

.loading-spinner-small {
  @apply inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}
