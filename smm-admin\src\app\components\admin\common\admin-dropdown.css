/* Admin dropdown menu styling */
.admin-dropdown-menu, .dropdown-menu {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: fixed; /* Fixed position to ensure it stays in view when scrolling */
  transform: translateZ(0); /* Force hardware acceleration */
  z-index: 99999; /* Very high z-index to ensure it's above all other elements */
  width: auto; /* Allow width to adjust based on content */
  min-width: 224px; /* Minimum width to maintain consistency */
  max-width: 400px; /* Maximum width to prevent overly wide menus */
}

/* Dropdown container */
.dropdown-container {
  position: relative;
  display: inline-block;
  z-index: 9999; /* High z-index for the container */
}

/* Ensure the parent container has position relative */
.relative {
  position: relative !important;
}

/* Ensure inline-block for proper positioning */
.inline-block {
  display: inline-block !important;
}

/* Menu item styling */
.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s;
  user-select: none; /* Prevent text selection */
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
  box-sizing: border-box; /* Ensure padding is included in height calculation */
  min-height: 44px; /* Ensure minimum height for touch targets */
  white-space: nowrap; /* Prevent text wrapping to keep menu items on single line */
}

.menu-item:hover {
  background-color: #f3f4f6;
}

.menu-item:active {
  background-color: #e5e7eb; /* Darker background when clicked */
}

.menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.menu-item.disabled:hover {
  background-color: transparent;
}

.menu-item-icon {
  margin-right: 0.75rem;
  width: 1rem;
}

/* Divider */
.menu-divider {
  border-top: 1px solid #e5e7eb;
  margin: 0.25rem 0;
}

/* Action menu button */
.action-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-menu-button:hover {
  background-color: #f3f4f6;
}

.action-menu-button.menu-open {
  background-color: #f3f4f6;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .action-menu-button {
    min-width: 44px;
    min-height: 44px;
  }

  .menu-item {
    padding: 12px 16px;
    min-height: 44px;
  }

  .admin-dropdown-menu, .dropdown-menu {
    max-width: 90vw; /* Limit width on mobile to prevent overflow */
    min-width: 200px; /* Smaller minimum width on mobile */
  }
}
