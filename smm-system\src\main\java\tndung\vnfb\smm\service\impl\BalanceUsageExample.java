package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.TransactionSource;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.service.BalanceService;

import java.math.BigDecimal;

/**
 * Example class showing how to use BalanceService properly.
 * This ensures all balance changes are logged in GTransaction.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BalanceUsageExample {

    private final BalanceService balanceService;

    /**
     * Example: Adding bonus to user
     */
    public void addBonusToUser(GUser user, BigDecimal bonusAmount, String reason) {
        log.info("Adding bonus to user: userId={}, amount={}, reason={}", 
                user.getId(), bonusAmount, reason);
        
        // This will automatically create a transaction record
        balanceService.addBalance(user, bonusAmount, TransactionSource.BONUS, 
                "Bonus: " + reason);
    }

    /**
     * Example: Processing payment for service
     */
    public void processServicePayment(GUser user, BigDecimal servicePrice, String serviceName) {
        log.info("Processing service payment: userId={}, amount={}, service={}", 
                user.getId(), servicePrice, serviceName);
        
        // Check if user has sufficient balance first
        if (!balanceService.hasSufficientBalance(user, servicePrice)) {
            throw new RuntimeException("Insufficient balance for service: " + serviceName);
        }
        
        // This will automatically create a transaction record
        balanceService.deductBalance(user, servicePrice, TransactionType.Spent, TransactionSource.REMOVE,
                "Payment for service: " + serviceName);
    }

    /**
     * Example: Processing order refund
     */
    public void processOrderRefund(GOrder order) {
        log.info("Processing order refund: orderId={}", order.getId());
        
        // This will automatically handle refund calculation and transaction logging
        balanceService.processRefund(order);
    }

    /**
     * Example: Manual balance adjustment by admin
     */
    public void adminBalanceAdjustment(GUser user, BigDecimal adjustmentAmount, 
                                     String adminNote, boolean isAddition) {
        log.info("Admin balance adjustment: userId={}, amount={}, isAddition={}, note={}", 
                user.getId(), adjustmentAmount, isAddition, adminNote);
        
        if (isAddition) {
            balanceService.addBalance(user, adjustmentAmount, TransactionSource.BONUS, 
                    "Admin adjustment: " + adminNote);
        } else {
            balanceService.deductBalance(user, adjustmentAmount, TransactionType.Spent, TransactionSource.REMOVE,
                    "Admin adjustment: " + adminNote);
        }
    }

    /**
     * Example: Processing deposit
     */
    public void processDeposit(GUser user, BigDecimal depositAmount, String paymentMethod) {
        log.info("Processing deposit: userId={}, amount={}, method={}", 
                user.getId(), depositAmount, paymentMethod);
        
        String depositNote = String.format("Deposit via %s: %s", paymentMethod, depositAmount);
        balanceService.addBalance(user, depositAmount, TransactionSource.BONUS, depositNote);
    }

    /**
     * Example: Processing withdrawal
     */
    public void processWithdrawal(GUser user, BigDecimal withdrawalAmount, String withdrawalMethod) {
        log.info("Processing withdrawal: userId={}, amount={}, method={}", 
                user.getId(), withdrawalAmount, withdrawalMethod);
        
        // Check sufficient balance
        if (!balanceService.hasSufficientBalance(user, withdrawalAmount)) {
            throw new RuntimeException("Insufficient balance for withdrawal");
        }
        
        String withdrawalNote = String.format("Withdrawal via %s: %s", withdrawalMethod, withdrawalAmount);
        balanceService.deductBalance(user, withdrawalAmount, TransactionType.Spent, TransactionSource.REMOVE, withdrawalNote);
    }

    /**
     * Example: Commission payment
     */
    public void payCommission(GUser user, BigDecimal commissionAmount, String referralInfo) {
        log.info("Paying commission: userId={}, amount={}, referral={}", 
                user.getId(), commissionAmount, referralInfo);
        
        String commissionNote = String.format("Commission from referral: %s", referralInfo);
        balanceService.addBalance(user, commissionAmount, TransactionSource.BONUS, commissionNote);
    }

    /**
     * Example: Penalty deduction
     */
    public void applyPenalty(GUser user, BigDecimal penaltyAmount, String penaltyReason) {
        log.info("Applying penalty: userId={}, amount={}, reason={}", 
                user.getId(), penaltyAmount, penaltyReason);
        
        // Check sufficient balance
        if (!balanceService.hasSufficientBalance(user, penaltyAmount)) {
            log.warn("User has insufficient balance for penalty, applying partial penalty");
            penaltyAmount = user.getBalance(); // Apply maximum possible penalty
        }
        
        String penaltyNote = String.format("Penalty: %s", penaltyReason);
        balanceService.deductBalance(user, penaltyAmount, TransactionType.Spent, TransactionSource.REMOVE, penaltyNote);
    }

    /**
     * Example: Transfer between users (for future use)
     */
    public void transferBetweenUsers(GUser fromUser, GUser toUser, BigDecimal amount, String reason) {
        log.info("Transferring balance: fromUserId={}, toUserId={}, amount={}, reason={}", 
                fromUser.getId(), toUser.getId(), amount, reason);
        
        // This will handle both deduction and addition with proper transaction logging
        GUser[] updatedUsers = balanceService.transferBalance(fromUser, toUser, amount, reason);
        
        log.info("Transfer completed: fromUserNewBalance={}, toUserNewBalance={}", 
                updatedUsers[0].getBalance(), updatedUsers[1].getBalance());
    }
}
