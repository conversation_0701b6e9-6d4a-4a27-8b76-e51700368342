import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthUtilsService } from '../services/auth-utils.service';


export const authRedirectGuard: CanActivateFn = (route, state) => {
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);

  console.log('authRedirectGuard - Checking authentication status');

  try {
    // If user is already authenticated and token is valid, redirect to home
    if (authUtils.isAuthenticated() && !authUtils.isTokenExpired()) {
      console.log('authRedirectGuard - User is authenticated, redirecting to home');

      // Get the stored redirect URL or default to dashboard
      const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/panel/dashboard';
      console.log('authRedirectGuard - Redirecting to:', redirectUrl);
      router.navigate([redirectUrl]);
      return false;
    }
  } catch (error) {
    console.error('authRedirectGuard - Error checking authentication:', error);
    // If there's an error checking authentication, allow access to auth pages
    return true;
  }

  // User is not authenticated, allow access to auth pages
  console.log('authRedirectGuard - User is not authenticated, allowing access to auth page');
  return true;
};
