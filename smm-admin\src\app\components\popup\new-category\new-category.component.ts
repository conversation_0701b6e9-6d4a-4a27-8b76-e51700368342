import { Component, EventEmitter, Input, Output, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { IconDropdownComponent } from '../../common/icon-dropdown/icon-dropdown.component';
import { CategoriesService } from '../../../core/services/categories.service';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { CategoryReq } from '../../../model/request/category-req.model';
import { IconBaseModel } from '../../../model/base-model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';

@Component({
  selector: 'app-new-category',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule,
    IconDropdownComponent
  ],
  templateUrl: './new-category.component.html',
  styleUrls: ['./new-category.component.css']
})
export class NewCategoryComponent implements OnInit, OnChanges {
  @Input() categoryToEdit: SuperCategoryRes | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() categoryAdded = new EventEmitter<SuperCategoryRes>();
  @Output() categoryUpdated = new EventEmitter<SuperCategoryRes>();

  categoryForm!: FormGroup;
  platforms: SuperPlatformRes[] = [];
  platformOptions: IconBaseModel[] = [];
  selectedPlatform: IconBaseModel | undefined;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;

  constructor(
    private formBuilder: FormBuilder,
    private categoriesService: CategoriesService,
    private adminService: AdminServiceService
  ) { }

  ngOnInit(): void {
    this.loadPlatforms();
    this.initializeForm();
  }

  // Flag to track if we need to set up edit mode once the form is initialized
  private needsEditModeSetup = false;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['categoryToEdit'] && changes['categoryToEdit'].currentValue) {
      this.isEditMode = true;

      // If the form is already initialized, set up edit mode immediately
      if (this.categoryForm) {
        this.setupEditMode();
      } else {
        // Otherwise, flag that we need to set up edit mode once the form is initialized
        this.needsEditModeSetup = true;
      }
    }
  }

  initializeForm(): void {
    this.categoryForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      platform_id: [null, [Validators.required]]
    });

    // If we need to set up edit mode and the form is now initialized, do it
    if (this.needsEditModeSetup && this.isEditMode && this.categoryToEdit) {
      this.setupEditMode();
      this.needsEditModeSetup = false;
    }
  }

  setupEditMode(): void {
    // Safety check: ensure both the form and category data exist
    if (!this.categoryForm || !this.categoryToEdit) {
      console.warn('Cannot set up edit mode: form or category data is missing');
      return;
    }

    // Find the platform ID from the category's platform name
    const platform = this.platforms.find(p => p.name === this.categoryToEdit?.platformName);
    const platformId = platform ? platform.id : null;

    // Set form values
    this.categoryForm.patchValue({
      name: this.categoryToEdit.name,
      platform_id: platformId
    });

    // Find and set the selected platform
    if (this.platformOptions.length > 0 && platformId) {
      const platformOption = this.platformOptions.find(p => parseInt(p.id) === platformId);
      if (platformOption) {
        this.selectedPlatform = platformOption;
      }
    }
  }

  loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms) => {
        this.platforms = platforms.filter(platform => !platform.hide);

        // Convert platforms to dropdown options
        this.platformOptions = this.platforms.map(platform => ({
          id: platform.id.toString(),
          label: platform.name,
          icon: platform.icon,
          sort: 0
        }));

        // If in edit mode and the form is initialized, set up the form with the category data
        if (this.isEditMode && this.categoryToEdit && this.categoryForm) {
          this.setupEditMode();
          this.needsEditModeSetup = false;
        }
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.errorMessage = 'Failed to load platforms';
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onPlatformSelected(platform: IconBaseModel): void {
    this.selectedPlatform = platform;
    this.categoryForm.patchValue({
      platform_id: parseInt(platform.id)
    });
  }

  onSubmit(): void {
    if (this.categoryForm.invalid) {
      this.markFormGroupTouched(this.categoryForm);
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const categoryData: CategoryReq = {
      name: this.categoryForm.value.name,
      platform_id: this.categoryForm.value.platform_id
    };

    if (this.isEditMode && this.categoryToEdit) {
      // Edit existing category
      this.adminService.editCategory(this.categoryToEdit.id, categoryData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.categoryUpdated.emit(response);
          this.onClose();
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = error.message || 'Failed to update category. Please try again.';
          console.error('Error updating category:', error);
        }
      });
    } else {
      // Add new category
      this.adminService.addCategory(categoryData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.categoryAdded.emit(response);
          this.onClose();
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = error.message || 'Failed to add category. Please try again.';
          console.error('Error adding category:', error);
        }
      });
    }
  }

  // Helper method to mark all form controls as touched
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if ((control as any).controls) {
        this.markFormGroupTouched(control as FormGroup);
      }
    });
  }
}
