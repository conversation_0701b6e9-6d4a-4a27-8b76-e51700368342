import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize, map, catchError, of } from 'rxjs';
import { ConfigService } from './config.service';
import { NotificationRes, NotificationReq } from '../../model/response/notification-res.model';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private _notifications$ = new BehaviorSubject<NotificationRes[]>([]);
  private _popupNotifications$ = new BehaviorSubject<NotificationRes[]>([]);
  private _fixedNotifications$ = new BehaviorSubject<NotificationRes[]>([]);
  private _loading$ = new BehaviorSubject<boolean>(false);

  // Public observables
  public notifications$ = this._notifications$.asObservable();
  public popupNotifications$ = this._popupNotifications$.asObservable();
  public fixedNotifications$ = this._fixedNotifications$.asObservable();
  public loading$ = this._loading$.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    console.log('NotificationService initialized');
  }

  // Get all notifications
  getAllNotifications(): Observable<NotificationRes[]> {
    this._loading$.next(true);
    console.log('NotificationService - Fetching notifications from API');

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications`;
    console.log('NotificationService - API URL:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .get<any>(apiUrl)
      .pipe(
        map(response => {
          console.log('NotificationService - API response:', response);

          let notifications: NotificationRes[];

          // Check if response is already an array
          if (Array.isArray(response)) {
            console.log('NotificationService - Response is already an array');
            notifications = response;
          }
          // Check if response has data property that is an array
          else if (response && Array.isArray(response.data)) {
            console.log('NotificationService - Response has data array property');
            notifications = response.data;
          }
          // Handle success response with no data (code: 200, message: 'OK')
          else if (response && response.code === 200 && response.message === 'OK') {
            console.log('NotificationService - Response is a success message with no data');
            // No notifications available, return an empty array
            notifications = [];
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid response format:', response);
            return [];
          }

          // Update the BehaviorSubjects
          this._notifications$.next(notifications);

          // Split notifications by type
          const popupNotifications = notifications.filter(n => n.type === 'Popup');
          const fixedNotifications = notifications.filter(n => n.type === 'Fixed');

          this._popupNotifications$.next(popupNotifications);
          // Update fixed notifications - include all fixed notifications, not just active ones
          this._fixedNotifications$.next(fixedNotifications);

          return notifications;
        }),
        catchError(error => {
          console.error('NotificationService - Error fetching notifications:', error);
          // Return an empty array to prevent the observable from completing with an error
          this._notifications$.next([]);
          this._popupNotifications$.next([]);
          this._fixedNotifications$.next([]);
          return of([]);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Get popup notifications
  getPopupNotifications(): Observable<NotificationRes[]> {
    this._loading$.next(true);
    console.log('NotificationService - Fetching popup notifications from API');

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/popup`;
    console.log('NotificationService - Popup API URL:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .get<any>(apiUrl)
      .pipe(
        map(response => {
          console.log('NotificationService - Popup API response:', response);

          let popupNotifications: NotificationRes[];

          // Check if response is already an array
          if (Array.isArray(response)) {
            console.log('NotificationService - Response is already an array');
            popupNotifications = response;
          }
          // Check if response has data property that is an array
          else if (response && Array.isArray(response.data)) {
            console.log('NotificationService - Response has data array property');
            popupNotifications = response.data;
          }
          // Handle success response with no data (code: 200, message: 'OK')
          else if (response && response.code === 200 && response.message === 'OK') {
            console.log('NotificationService - Response is a success message with no data');
            // No popup notifications available, return an empty array
            popupNotifications = [];
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid response format:', response);
            return [];
          }

          this._popupNotifications$.next(popupNotifications);
          return popupNotifications;
        }),
        catchError(error => {
          console.error('NotificationService - Error fetching popup notifications:', error);
          // Return an empty array to prevent the error from propagating
          this._popupNotifications$.next([]);
          return of([]);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Get fixed notifications
  getFixedNotifications(): Observable<NotificationRes[]> {
    this._loading$.next(true);
    console.log('NotificationService - Fetching fixed notifications from API');

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/fixed`;
    console.log('NotificationService - Fixed API URL:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .get<any>(apiUrl)
      .pipe(
        map(response => {
          console.log('NotificationService - Fixed API response:', response);

          // Handle different response formats
          let fixedNotifications: NotificationRes[] = [];

          // Check if response is already an array
          if (Array.isArray(response)) {
            console.log('NotificationService - Response is already an array');
            fixedNotifications = response;
          }
          // Check if response is a direct notification object (has id and type properties)
          else if (response && response.id !== undefined && response.type) {
            console.log('NotificationService - Response is a direct notification object');
            fixedNotifications = [response as NotificationRes];
          }
          // Check if response has data property that is an array
          else if (response && Array.isArray(response.data)) {
            console.log('NotificationService - Response has data array property');
            fixedNotifications = response.data;
          }
          // Check if response has data property that is a single object
          else if (response && response.data && response.data.id !== undefined) {
            console.log('NotificationService - Response has data object property');
            fixedNotifications = [response.data];
          }
          // Handle success response with no data (code: 200, message: 'OK')
          else if (response && response.code === 200 && response.message === 'OK') {
            console.log('NotificationService - Response is a success message with no data');
            // No fixed notifications available, return empty array
            this._fixedNotifications$.next([]);
            return [];
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid response format:', response);
            throw new Error('Invalid response format');
          }

          this._fixedNotifications$.next(fixedNotifications);
          return fixedNotifications;
        }),
        catchError(error => {
          console.error('NotificationService - Error fetching fixed notifications:', error);
          // Return empty array to prevent the error from propagating
          this._fixedNotifications$.next([]);
          return of([]);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Create a new notification
  createNotification(notification: NotificationReq): Observable<NotificationRes> {
    this._loading$.next(true);
    console.log('NotificationService - Creating notification:', notification);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications`;
    console.log('NotificationService - API URL for create:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .post<any>(apiUrl, notification)
      .pipe(
        map(response => {
          console.log('NotificationService - Create response:', response);

          // Handle different response formats
          let newNotification: NotificationRes;

          // Check if response is a direct notification object (has id and type properties)
          if (response && response.id !== undefined && response.type) {
            console.log('NotificationService - Response is a direct notification object');
            newNotification = response as NotificationRes;
          }
          // Check if response has data property
          else if (response && response.data) {
            console.log('NotificationService - Response has data property');
            newNotification = response.data;
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid create response:', response);
            throw new Error('Invalid response format');
          }

          // Update notifications arrays
          const currentNotifications = this._notifications$.value;
          const updatedNotifications = [...currentNotifications, newNotification];
          this._notifications$.next(updatedNotifications);

          // Update type-specific arrays
          if (newNotification.type === 'Popup') {
            const currentPopups = this._popupNotifications$.value;
            this._popupNotifications$.next([...currentPopups, newNotification]);
          } else if (newNotification.type === 'Fixed') {
            // For Fixed notifications, add to the array
            const currentFixed = this._fixedNotifications$.value;
            this._fixedNotifications$.next([...currentFixed, newNotification]);
          }

          return newNotification;
        }),
        catchError(error => {
          console.error('NotificationService - Error creating notification:', error);
          throw error;
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Update an existing notification
  updateNotification(id: number, notification: NotificationReq): Observable<NotificationRes> {
    this._loading$.next(true);
    console.log(`NotificationService - Updating notification ${id}:`, notification);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/${id}`;
    console.log('NotificationService - API URL for update:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .put<any>(apiUrl, notification)
      .pipe(
        map(response => {
          console.log('NotificationService - Update response:', response);

          // Handle different response formats
          let updatedNotification: NotificationRes;

          // Check if response is a direct notification object (has id and type properties)
          if (response && response.id !== undefined && response.type) {
            console.log('NotificationService - Response is a direct notification object');
            updatedNotification = response as NotificationRes;
          }
          // Check if response has data property
          else if (response && response.data) {
            console.log('NotificationService - Response has data property');
            updatedNotification = response.data;
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid update response:', response);
            throw new Error('Invalid response format');
          }

          this.updateNotificationInArrays(updatedNotification);
          return updatedNotification;
        }),
        catchError(error => {
          console.error('NotificationService - Error updating notification:', error);
          throw error;
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Toggle notification status (show/hide)
  toggleNotificationStatus(id: number, show: boolean): Observable<NotificationRes> {
    this._loading$.next(true);
    console.log(`NotificationService - Toggling notification ${id} to show=${show}`);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/${id}?show=${show}`;
    console.log('NotificationService - API URL for toggle:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .patch<any>(apiUrl, {})
      .pipe(
        map(response => {
          console.log('NotificationService - Toggle response:', response);

          // Handle different response formats
          let updatedNotification: NotificationRes;

          // Check if response is a direct notification object (has id and type properties)
          if (response && response.id !== undefined && response.type) {
            console.log('NotificationService - Response is a direct notification object');
            updatedNotification = response as NotificationRes;
          }
          // Check if response has data property
          else if (response && response.data) {
            console.log('NotificationService - Response has data property');
            updatedNotification = response.data;
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid toggle response:', response);
            throw new Error('Invalid response format');
          }

          this.updateNotificationInArrays(updatedNotification);
          return updatedNotification;
        }),
        catchError(error => {
          console.error('NotificationService - Error toggling notification:', error);
          throw error;
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Helper method to update notification in arrays
  private updateNotificationInArrays(updatedNotification: NotificationRes): void {
    // Update notifications array
    const currentNotifications = this._notifications$.value;
    const updatedNotifications = currentNotifications.map(n =>
      n.id === updatedNotification.id ? updatedNotification : n
    );
    this._notifications$.next(updatedNotifications);

    // Update type-specific arrays
    if (updatedNotification.type === 'Popup') {
      const currentPopups = this._popupNotifications$.value;
      this._popupNotifications$.next(currentPopups.map(n =>
        n.id === updatedNotification.id ? updatedNotification : n
      ));
    } else if (updatedNotification.type === 'Fixed') {
      // For Fixed notifications, update the array
      const currentFixed = this._fixedNotifications$.value;
      const updatedFixed = currentFixed.map(n =>
        n.id === updatedNotification.id ? updatedNotification : n
      );
      this._fixedNotifications$.next(updatedFixed);
    }
  }

  // Get a notification by ID
  getNotificationById(id: number): Observable<NotificationRes | null> {
    this._loading$.next(true);
    console.log(`NotificationService - Getting notification with ID ${id}`);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/${id}`;
    console.log('NotificationService - API URL for get:', apiUrl);

    // Make the API request with any type to handle different response formats
    return this.http
      .get<any>(apiUrl)
      .pipe(
        map(response => {
          console.log('NotificationService - Get response:', response);

          // Handle different response formats
          let notification: NotificationRes | null = null;

          // Check if response is a direct notification object (has id and type properties)
          if (response && response.id !== undefined && response.type) {
            console.log('NotificationService - Response is a direct notification object');
            notification = response as NotificationRes;
          }
          // Check if response has data property
          else if (response && response.data) {
            console.log('NotificationService - Response has data property');
            notification = response.data;
          }
          // Handle success response with no data (code: 200, message: 'OK')
          else if (response && response.code === 200 && response.message === 'OK') {
            console.log('NotificationService - Response is a success message with no data');
            // No notification available, return null
            return null;
          }
          // Invalid response format
          else {
            console.error('NotificationService - Invalid response format:', response);
            throw new Error('Invalid response format');
          }

          return notification;
        }),
        catchError(error => {
          console.error('NotificationService - Error getting notification:', error);
          return of(null);
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Delete a notification
  deleteNotification(id: number): Observable<void> {
    this._loading$.next(true);
    console.log(`NotificationService - Deleting notification ${id}`);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/${id}`;
    console.log('NotificationService - API URL for delete:', apiUrl);

    // Make the API request
    return this.http
      .delete<{ code: number; message: string }>(apiUrl)
      .pipe(
        map(response => {
          console.log('NotificationService - Delete response:', response);

          // Remove from notifications arrays
          this.removeNotificationFromArrays(id);

          // Return void
          return;
        }),
        catchError(error => {
          console.error('NotificationService - Error deleting notification:', error);
          throw error;
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Helper method to remove notification from arrays
  private removeNotificationFromArrays(id: number): void {
    // Find the notification to determine its type
    const currentNotifications = this._notifications$.value;
    const notificationToRemove = currentNotifications.find(n => n.id === id);

    // Remove from main notifications array
    const updatedNotifications = currentNotifications.filter(n => n.id !== id);
    this._notifications$.next(updatedNotifications);

    // Remove from type-specific arrays
    if (notificationToRemove?.type === 'Popup') {
      const currentPopups = this._popupNotifications$.value;
      this._popupNotifications$.next(currentPopups.filter(n => n.id !== id));
    } else if (notificationToRemove?.type === 'Fixed') {
      // Remove the notification from the fixed notifications array
      const currentFixed = this._fixedNotifications$.value;
      this._fixedNotifications$.next(currentFixed.filter(n => n.id !== id));
    }
  }

  // Mark a notification as viewed
  markAsViewed(id: number): Observable<void> {
    this._loading$.next(true);
    console.log(`NotificationService - Marking notification ${id} as viewed`);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/${id}/view`;
    console.log('NotificationService - API URL for marking as viewed:', apiUrl);

    // Make the API request
    return this.http
      .post<{ code: number; message: string }>(apiUrl, {})
      .pipe(
        map(response => {
          console.log('NotificationService - Mark as viewed response:', response);
          return;
        }),
        catchError(error => {
          console.error('NotificationService - Error marking notification as viewed:', error);
          throw error;
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Dismiss notification with TTL (save to Redis)
  dismissNotificationWithTTL(id: number, hours: number): Observable<void> {
    this._loading$.next(true);
    console.log(`NotificationService - Dismissing notification ${id} for ${hours} hours`);

    // Use the configService URL
    const apiUrl = `${this.configService.apiUrl}/notifications/${id}/dismiss`;
    console.log('NotificationService - API URL for dismissing with TTL:', apiUrl);

    // Make the API request
    return this.http
      .post<{ code: number; message: string }>(apiUrl, { hours: hours })
      .pipe(
        map(response => {
          console.log('NotificationService - Dismiss with TTL response:', response);
          return;
        }),
        catchError(error => {
          console.error('NotificationService - Error dismissing notification with TTL:', error);
          throw error;
        }),
        finalize(() => this._loading$.next(false))
      );
  }
}
