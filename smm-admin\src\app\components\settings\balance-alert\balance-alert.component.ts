import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconsModule } from '../../../icons/icons.module';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ProviderRes } from '../../../model/response/provider-res.model';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-balance-alert',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    FaIconComponent
  ],
  templateUrl: './balance-alert.component.html',
  styleUrl: './balance-alert.component.css'
})
export class BalanceAlertComponent implements OnInit {
  @Input() provider: ProviderRes | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() balanceAlertUpdated = new EventEmitter<ProviderRes>();

  balanceAlert: number = 0;
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor(private adminService: AdminServiceService) {}

  ngOnInit(): void {
    if (this.provider) {
      this.balanceAlert = parseFloat(this.provider.balance_alert) || 0;
    }
  }

  closePopup(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.closePopup();
    }
  }

  saveBalanceAlert(): void {
    if (!this.provider) {
      this.errorMessage = 'No provider selected';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.adminService.changeProviderBalanceAlert(this.provider.id, this.balanceAlert)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          // Update the provider object with the new balance alert
          const updatedProvider: ProviderRes = {
            ...this.provider!,
            balance_alert: this.balanceAlert.toString()
          };
          this.balanceAlertUpdated.emit(updatedProvider);
          this.closePopup();
        },
        error: (error) => {
          console.error('Error changing balance alert:', error);
          this.errorMessage = 'Failed to change balance alert. Please try again.';
        }
      });
  }
}
