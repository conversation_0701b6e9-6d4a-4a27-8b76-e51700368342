import {ChangeDetectionStrategy, Component, Input, OnInit} from '@angular/core';
import { OrderStatus } from '../../../constant/order-status';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-status-badges',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule],
    templateUrl: './status-badges.component.html',
    styleUrls: ['status-badges.component.scss'],
})
export class StatusBadgesComponent implements OnInit {
    constructor() {}
    @Input() status = 'PENDING';

  protected readonly OrderStatus = OrderStatus;
    ngOnInit() {}
}
