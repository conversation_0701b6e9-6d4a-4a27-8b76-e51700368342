import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';
import { CreateTokenForUserReq } from '../../model/request/create-token-for-user-req.model';
import { TokenPairRes } from '../../model/response/token-pair-res.model';

@Injectable({
  providedIn: 'root'
})
export class AccessAdminService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Create token for a specific user
   * @param request The request containing userId and optional tenantId
   * @returns Observable<TokenPairRes> containing the token information
   */
  createTokenForUser(request: CreateTokenForUserReq): Observable<TokenPairRes> {
    return this.http.post<TokenPairRes>(
      `${this.configService.apiUrl}/access/create-token`,
      request
    );
  }
}
