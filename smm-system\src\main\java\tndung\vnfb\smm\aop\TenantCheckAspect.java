package tndung.vnfb.smm.aop;


import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;


@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class TenantCheckAspect {

    /**
     * Advice that checks tenant configuration before executing methods annotated with @TenantCheck
     */
    @Before("@annotation(tndung.vnfb.smm.anotation.TenantCheck)")
    public void checkTenant(JoinPoint joinPoint) {
        log.debug("Checking tenant configuration for method: {}", joinPoint.getSignature().getName());

        String currentTenant = TenantContext.getCurrentTenant();
        if (currentTenant == null) {
            log.warn("Tenant not configured for method: {}", joinPoint.getSignature().getName());
            throw new InvalidParameterException(IdErrorCode.PANEL_NOT_CONFIGURED);
        }

        log.debug("Tenant check passed for tenant: {}", currentTenant);
    }
}
