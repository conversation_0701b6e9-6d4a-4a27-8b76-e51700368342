package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.service.*;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SMMSvServiceImpl implements SMMSvService {

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final OrderRepository orderRepository;
    private final GUserService gUserService;
    private final OrderService orderService;
    private final ApiProviderService providerService;

    @Override
    @Async
    public void triggerRun() {
//        Pageable pageReq = PageRequest.of(0, 100);
//        boolean keepGoing = true;
//        while (keepGoing) {
//            final Page<GOrder> page = run(pageReq);
//            keepGoing = page.hasNext();
//            pageReq = page.nextPageable();
//        }
    }

//    public Page<GOrder> run(Pageable pageReq) {
//        Page<GOrder> page =
//                this.orderRepository
//                        .findOrderToReady(List.of(OrderStatus.PENDING, OrderStatus.IN_PROGRESS, OrderStatus.PROCESSING), pageReq);
//        for (GOrder gOrder : page.getContent()) {
//            threadPoolTaskExecutor.execute(() -> executeJob(gOrder));
//        }
//        return page;
//    }

    // @Transactional
//    public void executeJob(GOrder gOrder) {
//
//        try {
//            gOrder = orderService.fetchStatus(gOrder);
//        } catch (Exception e) {
//            log.error("Order {} fail with exception {}", gOrder.getId(), e.getMessage(), e);
//            gOrder.setStatus(OrderStatus.FAILED);
//        }
//
//        gUserService.refund(gOrder);
//        providerService.checkBalance(gOrder.getApiProvider().getId());
//        orderRepository.save(gOrder);
//    }


}
