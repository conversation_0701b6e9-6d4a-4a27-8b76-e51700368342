import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize, tap, switchMap } from 'rxjs';
import { ConfigService } from './config.service';
import { OrderRes } from '../../model/response/order-res.model';
import { PageResponse } from '../../model/response/page-response.model';

export interface OrderSearchReq {
  keyword?: string;
  status?: string;
  serviceId?: number;
  categoryId?: number;
  from?: string;
  to?: string;
  page?: number;
  size?: number;
  userId?: number;
}

@Injectable({
  providedIn: 'root'
})
export class AdminOrderService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _orders$ = new BehaviorSubject<OrderRes[]>([]);
  private _pagination$ = new BehaviorSubject<any>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });
  private _search$ = new Subject<OrderSearchReq>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Set up subscription for search
    this._search$
      .pipe(
        tap(() => this._loading$.next(true)),
        switchMap(filter => this.searchOrders(filter))
      )
      .subscribe((result: PageResponse<OrderRes>) => {
        this._orders$.next(result.content);
        this._pagination$.next({
          pageNumber: result.pageable.page_number,
          pageSize: result.pageable.page_size,
          totalElements: result.total_elements,
          totalPages: result.total_pages
        });
        this._loading$.next(false);
      });
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get orders$(): BehaviorSubject<OrderRes[]> {
    return this._orders$;
  }

  get pagination$(): BehaviorSubject<any> {
    return this._pagination$;
  }

  /**
   * Trigger a search for orders with filters
   * @param filter The search filter
   */
  search(filter: OrderSearchReq): void {
    this._search$.next(filter);
  }

  /**
   * Load orders with pagination
   * @param page Page number (0-based)
   * @param size Page size
   */
  loadOrders(page: number = 0, size: number = 10): void {
    this.search({ page, size });
  }

  /**
   * Search orders with filters
   * @param filter The search filter
   */
  searchOrders(filter: OrderSearchReq): Observable<PageResponse<OrderRes>> {
    this._loading$.next(true);

    // Default page and size if not provided
    const page = filter.page !== undefined ? filter.page : 0;
    const size = filter.size !== undefined ? filter.size : 10;

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (filter.keyword) {
      params = params.set('keyword', filter.keyword);
    }

    if (filter.status && filter.status !== 'all') {
      params = params.set('status', filter.status);
    }

    if (filter.serviceId) {
      params = params.set('serviceId', filter.serviceId.toString());
    }

    if (filter.categoryId) {
      params = params.set('categoryId', filter.categoryId.toString());
    }

    if (filter.from) {
      params = params.set('from', filter.from);
    }

    if (filter.to) {
      params = params.set('to', filter.to);
    }

    if (filter.userId) {
      params = params.set('userId', filter.userId.toString());
    }

    // Use the user orders endpoint for the orders component
    return this.http
      .get<PageResponse<OrderRes>>(`${this.configService.apiUrl}/orders/search`, { params })
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Get a specific order by ID
   * @param id The order ID
   */
  getOrderById(id: number): Observable<OrderRes> {
    return this.http.get<OrderRes>(`${this.configService.apiUrl}/orders/${id}`);
  }

  /**
   * Update order status
   * @param id The order ID
   * @param status The new status
   */
  // updateOrderStatus(id: number, status: string): Observable<OrderRes> {
  //   return this.http
  //     .put<OrderRes>(`${this.configService.apiUrl}/orders/${id}/status`, { status })
  //     .pipe(
  //       tap(updatedOrder => {
  //         // Update the order in the orders array
  //         const currentOrders = this._orders$.value;
  //         const index = currentOrders.findIndex(order => order.id === id);
  //         if (index !== -1) {
  //           currentOrders[index] = updatedOrder;
  //           this._orders$.next([...currentOrders]);
  //         }
  //       })
  //     );
  // }

  /**
   * Update order status with remains (using query parameters)
   * @param id The order ID
   * @param status The new status
   * @param remains The remains value (optional)
   */
  updateOrderStatusWithRemains(id: number, status: string, remains?: number): Observable<OrderRes> {
    let params = `status=${status}`;
    if (remains !== undefined && remains !== null) {
      params += `&remains=${remains}`;
    }

    return this.http
      .put<OrderRes>(`${this.configService.apiUrl}/orders/${id}/status?${params}`, {})
      .pipe(
        tap(updatedOrder => {
          // Update the order in the orders array
          const currentOrders = this._orders$.value;
          const index = currentOrders.findIndex(order => order.id === id);
          if (index !== -1) {
            currentOrders[index] = updatedOrder;
            this._orders$.next([...currentOrders]);
          }
        })
      );
  }

  /**
   * Update order link
   * @param id The order ID
   * @param link The new link
   */
  updateOrderLink(id: number, link: string): Observable<OrderRes> {
    return this.http
      .patch<OrderRes>(`${this.configService.apiUrl}/orders/${id}/link`, { link })
      .pipe(
        tap(updatedOrder => {
          // Update the order in the orders array
          const currentOrders = this._orders$.value;
          const index = currentOrders.findIndex(order => order.id === id);
          if (index !== -1) {
            currentOrders[index] = updatedOrder;
            this._orders$.next([...currentOrders]);
          }
        })
      );
  }

  /**
   * Update order start count
   * @param id The order ID
   * @param startCount The new start count
   */
  updateOrderStartCount(id: number, startCount: number): Observable<OrderRes> {
    return this.http
      .patch<OrderRes>(`${this.configService.apiUrl}/orders/${id}/start-count`, { startCount: startCount })
      .pipe(
        tap(updatedOrder => {
          // Update the order in the orders array
          const currentOrders = this._orders$.value;
          const index = currentOrders.findIndex(order => order.id === id);
          if (index !== -1) {
            currentOrders[index] = updatedOrder;
            this._orders$.next([...currentOrders]);
          }
        })
      );
  }

  /**
   * Cancel an order with refund
   * @param id The order ID
   * @returns Observable with the updated order
   */
  cancelOrder(id: number): Observable<OrderRes> {
    return this.http
      .put<OrderRes>(`${this.configService.apiUrl}/orders/${id}/cancel`, {})
      .pipe(
        tap(updatedOrder => {
          // Update the order in the orders array
          // const currentOrders = this._orders$.value;
          // const index = currentOrders.findIndex(order => order.id === id);
          // if (index !== -1) {
          //   currentOrders[index] = updatedOrder;
          //   this._orders$.next([...currentOrders]);
          // }
        })
      );
  }
}
