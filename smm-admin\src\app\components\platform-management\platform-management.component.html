<div class="overlay-black" >
  <div class="platform-management-container">
    <div class="header-section">
      <div class="icon-container">
        <img src="assets/images/folder-icon.svg" alt="Folder Icon" class="folder-icon" />
      </div>
      <div class="text-container">
        <h1 class="title">{{ 'Social networks' | translate }}</h1>
        <p class="description">{{ 'Attach social networks to your categories so customers can easily navigate between services in your store' | translate }}</p>
      </div>
      <button class="close-btn" (click)="onClose()">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <!-- Platform List with Drag and Drop -->
  <div
    *ngIf="!loading"
    cdkDropList
    class="platform-list"
    (cdkDropListDropped)="onDrop($event)">
    <div
      *ngFor="let platform of platforms"
      class="platform-item"
      cdkDrag
      (cdkDragStarted)="onDragStarted($event)">
      <div class="item-content">
        <div class="drag-handle" cdkDragHandle>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="handle-icon">
            <circle cx="9" cy="7" r="1"></circle>
            <circle cx="9" cy="12" r="1"></circle>
            <circle cx="9" cy="17" r="1"></circle>
            <circle cx="15" cy="7" r="1"></circle>
            <circle cx="15" cy="12" r="1"></circle>
            <circle cx="15" cy="17" r="1"></circle>
          </svg>
        </div>
        <div class="platform-icon">
          <app-social-icon [icon]="platform.icon"></app-social-icon>
        </div>
        <span class="platform-name">{{ platform.name }}</span>
      </div>
      <div class="item-actions">
        <button class="action-btn edit-btn" (click)="onEdit(platform)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="action-icon">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </button>
        <button class="action-btn delete-btn" (click)="onDelete(platform)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="action-icon">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
        </button>
      </div>

      <!-- Custom drag preview (shows the entire row) -->
      <div *cdkDragPreview class="platform-item-preview">
        <div class="item-content">
          <div class="drag-handle">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="handle-icon">
              <circle cx="9" cy="7" r="1"></circle>
              <circle cx="9" cy="12" r="1"></circle>
              <circle cx="9" cy="17" r="1"></circle>
              <circle cx="15" cy="7" r="1"></circle>
              <circle cx="15" cy="12" r="1"></circle>
              <circle cx="15" cy="17" r="1"></circle>
            </svg>
          </div>
          <div class="platform-icon">
            <app-social-icon [icon]="platform.icon"></app-social-icon>
          </div>
          <span class="platform-name">{{ platform.name }}</span>
        </div>
        <div class="item-actions-preview">
          <!-- Empty div to maintain spacing -->
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && platforms.length === 0" class="empty-state">
    <p>{{ 'No platforms found. Add your first platform.' | translate }}</p>
  </div>

  <!-- Add Platform Button -->
  <button class="add-platform-btn" (click)="openAddPlatformModal()">
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="plus-icon">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
    <span>{{ 'Add network' | translate }}</span>
  </button>

  <!-- Add Platform Modal -->
  <app-add-platform-light
    *ngIf="showAddPlatformModal"
    (close)="closeAddPlatformModal()"
    (platformAdded)="onPlatformAdded($event)">
  </app-add-platform-light>

  <!-- Edit Platform Modal -->
  <app-add-platform-light
    *ngIf="showEditPlatformModal && platformToEdit"
    [platform]="platformToEdit"
    (close)="closeEditPlatformModal()"
    (platformAdded)="onPlatformUpdated($event)">
  </app-add-platform-light>

  <!-- Delete Confirmation Modal -->
  <div *ngIf="showDeleteConfirmation && platformToDelete" class="delete-confirmation-overlay">
    <div class="delete-confirmation-modal">
      <h2 class="confirmation-title">{{ 'Confirm Deletion' | translate }}</h2>
      <p class="confirmation-message">
        {{ 'Are you sure you want to delete the platform' | translate }} <strong>{{ platformToDelete.name }}</strong>?
        {{ 'This action cannot be undone.' | translate }}
      </p>
      <div class="confirmation-actions">
        <button
          class="cancel-btn"
          [disabled]="loadingDelete"
          (click)="closeDeleteConfirmation()">
          {{ 'Cancel' | translate }}
        </button>
        <button
          class="confirm-btn"
          [disabled]="loadingDelete"
          (click)="confirmDelete()">
          <span *ngIf="!loadingDelete">{{ 'Delete' | translate }}</span>
          <div *ngIf="loadingDelete" class="btn-spinner"></div>
        </button>
      </div>
    </div>
  </div>
  </div>
</div>
