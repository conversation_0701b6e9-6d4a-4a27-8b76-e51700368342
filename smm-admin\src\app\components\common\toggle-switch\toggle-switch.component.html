<label class="relative inline-flex items-center" [class.cursor-pointer]="!disabled" [class.cursor-not-allowed]="disabled">
  <input
    type="checkbox"
    [checked]="isChecked"
    (change)="onToggle()"
    [disabled]="disabled"
    class="sr-only peer"
  >
  <div class="w-11 h-6 rounded-full peer
              peer-focus:ring-4 peer-focus:ring-blue-200
              peer-checked:after:translate-x-full
              peer-checked:after:border-white
              after:content-['']
              after:absolute
              after:top-0.5
              after:left-[2px]
              after:border-gray-200
              after:border
              after:rounded-full
              after:h-5
              after:w-5
              after:transition-all
              after:bg-[var(--tw-after-bg)]"
       [ngStyle]="{
         'background-color': isChecked ? toggledBgColor : (disabled ? '' : ''),
         '--tw-after-bg': circleColor
       }"
       [class.bg-gray-100]="!disabled && !isChecked"
       [class.bg-gray-200]="disabled"
       [class.opacity-70]="disabled"
       [style.--tw-after-bg-opacity]="1">
  </div>
  <span class="ml-3 text-sm font-medium" [class.text-gray-800]="!disabled" [class.text-gray-500]="disabled">
    {{ label }}
  </span>
</label>