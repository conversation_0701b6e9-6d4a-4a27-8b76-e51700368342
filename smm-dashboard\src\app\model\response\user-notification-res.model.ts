export interface UserNotificationRes {
  id: number;
  title: string;
  content: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  category: 'SYSTEM' | 'RENEWAL' | 'SETUP' | 'ORDER' | 'BALANCE';
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserNotificationReq {
  title: string;
  content: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  category: 'SYSTEM' | 'RENEWAL' | 'SETUP' | 'ORDER' | 'BALANCE';
  userId?: number;
}
