import { Component, EventEmitter, Input, OnInit, Output, OnDestroy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SpecialPriceRes } from '../../../model/response/special-price-res.model';
import { DiscountType } from '../../../model/request/custom-discount-service-req.model';
import { finalize } from 'rxjs';
import { UIStateService } from '../../../core/services/ui-state.service';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { CurrencyService } from '../../../core/services/currency.service';

@Component({
  selector: 'app-special-prices-service',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
  ],
  templateUrl: './special-prices-service.component.html',
  styleUrls: ['./special-prices-service.component.css']
})
export class SpecialPricesServiceComponent implements OnInit, OnDestroy {
  @Input() selectedService: SuperGeneralSvRes | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() specialPriceAdded = new EventEmitter<SpecialPriceRes[]>();

  // For displaying existing special prices
  specialPrices: SpecialPriceRes[] = [];
  isLoadingPrices = false;
  activeActionMenu: number | null = null;

  // User data cache
  users: { [id: number]: string } = {};

  constructor(
    private uiStateService: UIStateService,
    private adminService: AdminServiceService,
    private currencyService: CurrencyService
  ) {}

  @HostListener('document:click')
  handleDocumentClick(): void {
    if (this.activeActionMenu !== null) {
      this.closeActionMenu();
    }
  }

  ngOnInit(): void {
    console.log('SpecialPricesServiceComponent initialized');
    console.log('selectedService:', this.selectedService);
    console.log('selectedServiceForAction from UIStateService:', this.uiStateService.selectedServiceForAction);

    // If selectedService is null but we have it in the UIStateService, use that instead
    if (!this.selectedService && this.uiStateService.selectedServiceForAction) {
      console.log('Using selectedServiceForAction from UIStateService as fallback');
      this.selectedService = this.uiStateService.selectedServiceForAction;
    }

    // Load special prices for the selected service
    this.loadSpecialPrices();
  }

  ngOnDestroy(): void {
    // Clean up any subscriptions or timers
  }

  loadSpecialPrices(): void {
    if (!this.selectedService) {
      console.error('Cannot load special prices: selectedService is null');
      // Try one more time to get the service from UIStateService
      if (this.uiStateService.selectedServiceForAction) {
        console.log('Attempting to recover using selectedServiceForAction from UIStateService');
        this.selectedService = this.uiStateService.selectedServiceForAction;
      } else {
        console.error('Recovery failed: selectedServiceForAction is also null');
        return;
      }
    }

    console.log('Loading special prices for service:', this.selectedService);
    this.isLoadingPrices = true;

    // Call the service to get special prices for the selected service
    const serviceId = this.selectedService.id;

    this.adminService.getSpecialPricesForService(serviceId)
      .pipe(
        finalize(() => {
          this.isLoadingPrices = false;
        })
      )
      .subscribe(specialPrices => {
        console.log('Special prices loaded:', specialPrices);
        this.specialPrices = specialPrices;

        // Extract user data for display
        specialPrices.forEach(price => {
          const userId = price.userId || (price.user && price.user.id);
          if (userId) {
            // In a real implementation, you might want to fetch user details if not included in the response
            // For now, we'll just use the user ID as the username
            this.users[userId] = price.user && price.user.user_name ? price.user.user_name : `User #${userId}`;
          }
        });
      });
  }

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  toggleActionMenu(priceId: number, event: MouseEvent): void {
    event.stopPropagation();

    // Close if already open for this price
    if (this.activeActionMenu === priceId) {
      this.closeActionMenu();
      return;
    }

    // Open the menu for this price
    this.activeActionMenu = priceId;
  }

  closeActionMenu(): void {
    this.activeActionMenu = null;
  }

  editSpecialPrice(priceId: number): void {
    console.log('Edit special price:', priceId);

    // Find the special price to edit
    const specialPriceToEdit = this.specialPrices.find(price => price.id === priceId);
    if (!specialPriceToEdit) {
      console.error('Special price not found:', priceId);
      return;
    }

    // Close the action menu
    this.closeActionMenu();

    // Close this component
    this.onClose();

    // Set the special price to edit in the UIStateService
    this.uiStateService.specialPriceToEdit = specialPriceToEdit;

    // Open the new-special-prices component in edit mode
    this.uiStateService.openNewSpecialPricesModalInEditMode();
  }

  deleteSpecialPrice(priceId: number): void {
    console.log('Deleting special price:', priceId);

    // Show loading indicator
    this.isLoadingPrices = true;

    // Call the API to delete the special price
    this.adminService.deleteSpecialPrice(priceId)
      .pipe(
        finalize(() => {
          this.isLoadingPrices = false;
        })
      )
      .subscribe({
        next: () => {
          console.log('Special price deleted successfully');
          // Remove the deleted price from the local array
          this.specialPrices = this.specialPrices.filter(price => price.id !== priceId);
          // Emit event to notify parent component
          this.specialPriceAdded.emit(this.specialPrices);
        },
        error: (error: any) => {
          console.error('Error deleting special price:', error);
          // You could add error handling here, such as displaying an error message
        }
      });

    this.closeActionMenu();
  }

  getUserName(userId: number | undefined): string {
    if (userId === undefined) {
      return 'Unknown User';
    }
    return this.users[userId] || `User #${userId}`;
  }

  getFormattedPrice(price: SpecialPriceRes): string {
    // Use discount_value if customDiscount is not available
    const discountValue = price.customDiscount !== undefined ? price.customDiscount : price.discount_value;

    if (price.type === DiscountType.FIXED || price.discount_type === 'FIXED') {
      return `$${this.currencyService.formatBalance(discountValue)}`;
    } else {
      return `${discountValue}%`;
    }
  }

  openAddNewSpecialPrice(): void {
    // Close this component and open the new-special-prices component
    this.onClose();
    this.uiStateService.openNewSpecialPricesModal();
  }
}

