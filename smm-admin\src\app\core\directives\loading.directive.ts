import { Directive, ElementRef, Input, OnChanges, OnInit, Renderer2, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[appLoading]',
  standalone: true
})
export class LoadingDirective implements OnInit, OnChanges {
  @Input('appLoading') loading: boolean = false;
  @Input() loadingSize: 'sm' | 'md' = 'sm';
  
  private spinner: HTMLElement | null = null;
  private originalPosition: string = '';
  private originalDisplay: string = '';
  
  constructor(private el: ElementRef, private renderer: Renderer2) {}
  
  ngOnInit(): void {
    // Store original styles
    this.originalPosition = this.el.nativeElement.style.position;
    this.originalDisplay = this.el.nativeElement.style.display;
    
    // Set position relative if not already positioned
    if (this.originalPosition !== 'absolute' && this.originalPosition !== 'fixed' && this.originalPosition !== 'relative') {
      this.renderer.setStyle(this.el.nativeElement, 'position', 'relative');
    }
    
    // Apply loading state if needed
    if (this.loading) {
      this.showLoading();
    }
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['loading']) {
      if (changes['loading'].currentValue) {
        this.showLoading();
      } else {
        this.hideLoading();
      }
    }
  }
  
  private showLoading(): void {
    if (this.spinner) return;
    
    // Create spinner element
    this.spinner = this.renderer.createElement('div');
    
    // Add spinner styles
    this.renderer.addClass(this.spinner, 'absolute');
    this.renderer.addClass(this.spinner, 'inset-0');
    this.renderer.addClass(this.spinner, 'flex');
    this.renderer.addClass(this.spinner, 'items-center');
    this.renderer.addClass(this.spinner, 'justify-center');
    this.renderer.addClass(this.spinner, 'bg-white');
    this.renderer.addClass(this.spinner, 'bg-opacity-70');
    this.renderer.addClass(this.spinner, 'z-10');
    
    // Create the actual spinner
    const spinnerInner = this.renderer.createElement('div');
    this.renderer.addClass(spinnerInner, 'loading-spinner');
    
    // Apply size
    if (this.loadingSize === 'sm') {
      this.renderer.addClass(spinnerInner, 'w-5');
      this.renderer.addClass(spinnerInner, 'h-5');
    } else {
      this.renderer.addClass(spinnerInner, 'w-8');
      this.renderer.addClass(spinnerInner, 'h-8');
    }
    
    // Set spinner styles
    this.renderer.setStyle(spinnerInner, 'border-radius', '50%');
    this.renderer.setStyle(spinnerInner, 'border', '3px solid rgba(var(--primary-rgb, 48, 176, 199), 0.2)');
    this.renderer.setStyle(spinnerInner, 'border-top-color', 'var(--primary, #30B0C7)');
    this.renderer.setStyle(spinnerInner, 'animation', 'spin 1s ease-in-out infinite');
    
    // Add spinner to container
    this.renderer.appendChild(this.spinner, spinnerInner);
    
    // Add spinner to element
    this.renderer.appendChild(this.el.nativeElement, this.spinner);
  }
  
  private hideLoading(): void {
    if (this.spinner) {
      this.renderer.removeChild(this.el.nativeElement, this.spinner);
      this.spinner = null;
    }
  }
}
