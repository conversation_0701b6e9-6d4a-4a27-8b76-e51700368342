import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';

@Injectable({
  providedIn: 'root'
})
export class ServiceSelectionService {
  private serviceSelectedSource = new BehaviorSubject<SuperGeneralSvRes | undefined>(undefined);
  
  // Observable that components can subscribe to
  serviceSelected$ = this.serviceSelectedSource.asObservable();

  // Method to call when a service is selected
  selectService(service: SuperGeneralSvRes | undefined): void {
    this.serviceSelectedSource.next(service);
  }
}
