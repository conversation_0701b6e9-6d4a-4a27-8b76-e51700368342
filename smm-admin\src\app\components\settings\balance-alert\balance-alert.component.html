<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" (click)="onOverlayClick($event)">
  <div class="bg-white rounded-lg w-full max-w-md p-6 shadow-lg">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">
        Provider balance notifications
      </h2>
      <button class="text-gray-500 hover:text-gray-700" (click)="closePopup()">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Form -->
    <form (ngSubmit)="saveBalanceAlert()">
      <!-- Balance Alert -->
      <div class="mb-6">
        <label for="balanceAlert" class="block text-sm font-medium text-gray-700 mb-1">Set balance limit</label>
        <input
          type="number"
          id="balanceAlert"
          name="balanceAlert"
          [(ngModel)]="balanceAlert"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter balance limit"
          min="0"
          step="0.01"
          required>
      </div>

      <!-- Info text -->
      <div class="mb-6 text-sm text-gray-500">
        You will receive notifications once in 30 minutes
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <!-- Buttons -->
      <div class="flex justify-center">
        <button
          type="submit"
          [disabled]="isLoading"
          class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center justify-center">
          <span *ngIf="isLoading" class="mr-2 animate-spin">
            <fa-icon [icon]="['fas', 'spinner']"></fa-icon>
          </span>
          Change
        </button>
      </div>
    </form>
  </div>
</div>
