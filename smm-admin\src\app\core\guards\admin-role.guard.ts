import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthUtilsService } from '../services/auth-utils.service';
import { isPlatformBrowser } from '@angular/common';
import { PLATFORM_ID } from '@angular/core';
import { ToastService } from '../services/toast.service';
import { NotifyType } from '../../constant/notify-type';

export const adminRoleGuard: CanActivateFn = (route, state) => {
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);
  const platformId = inject(PLATFORM_ID);
  const isBrowser = isPlatformBrowser(platformId);

  // If we're not in a browser environment, deny access (for SSR)
  if (!isBrowser) {
    return false;
  }

  // First check if user is authenticated
  if (!authUtils.isAuthenticated() || authUtils.isTokenExpired()) {
    console.log('adminRoleGuard - User is not authenticated or token is expired');

    // Store the intended URL for redirection after login
    localStorage.setItem('redirectAfterLogin', state.url);

    // Redirect to login page
    router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  // Check if user has admin role
  const toastService = inject(ToastService);

  if (authUtils.isAdmin()) {
    console.log('adminRoleGuard - User has ADMIN role, allowing access');
    return true;
  } else {
    console.log('adminRoleGuard - User does not have ADMIN role, denying access');
    toastService.showToast('Bạn không có quyền truy cập trang quản trị', NotifyType.ERROR);
    router.navigate(['/error/403']);
    return false;
  }
};
