import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faBell } from '@fortawesome/free-solid-svg-icons';
import { ToastService } from '../../../core/services/toast.service';
import { Subscription } from 'rxjs';

interface NotificationItem {
  title: string;
  message: string;
}

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './notification.component.html',
  styleUrl: './notification.component.css'
})
export class NotificationComponent implements OnInit, OnDestroy {
  isOpen = false;
  faBell = faBell;
  currentHeaderStyle: string = 'standard';

  constructor(
    private toastService: ToastService
  ) {}

  ngOnInit() {
    // Use default header style since ThemeService is removed
    this.currentHeaderStyle = 'standard';
  }

  ngOnDestroy() {
    // No subscriptions to clean up
  }

  toggleMenu() {
    this.isOpen = !this.isOpen;
  }
 notifications: NotificationItem[] = [
    {
      title: '',
      message: 'Đã nạp tiền vào tài khoản: **** 8943\nThành công'
    },
    {
      title: '',
      message: 'Bạn được cộng +676.000đ vào tài khoản\nvì được nhận đơn : Quảng cáo FB'
    },
    {
      title: '',
      message: 'Bạn được cộng +676.000đ vào tài khoản\nvì được nhận đơn : Quảng cáo FB'
    },
    {
      title: '',
      message: 'Bạn được cộng +676.000đ vào tài khoản\nvì được nhận đơn : Quảng cáo FB'
    },
    {
      title: '',
      message: 'Bạn được cộng +676.000đ vào tài khoản\nvì được nhận đơn : Quảng cáo FB'
    }
  ];
}
