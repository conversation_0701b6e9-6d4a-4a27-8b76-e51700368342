/* Profile Container */
.profile-container {
  @apply w-full bg-white rounded-lg shadow-sm p-2 md:p-8;
}

/* Profile Header Card */
.profile-header-card {
  @apply flex flex-col md:flex-row justify-between items-center bg-white rounded-lg p-6 mb-8 shadow-sm;
}

.profile-header-content {
  @apply flex items-center gap-6;
}

.profile-avatar {
  @apply relative;
}

.avatar-image {
  @apply w-20 h-20 rounded-full object-cover border-2 border-[var(--primary)];
}

.profile-info {
  @apply flex flex-col;
}

.profile-name {
  @apply text-xl font-semibold text-gray-800;
}

.profile-email {
  @apply text-sm text-gray-600 mt-1;
}

.profile-status {
  @apply flex items-center text-sm text-green-600 mt-2;
}

.online-dot {
  width: 8px;
  height: 8px;
  background-color: #10b981;
  border-radius: 50%;
  margin-right: 8px;
  display: inline-block;
  position: relative;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.profile-stats {
  @apply flex gap-8 mt-6 md:mt-0;
}

.stat-item {
  @apply flex flex-col items-center;
}

.stat-value {
  @apply text-xl font-semibold text-gray-800;
}

.stat-label {
  @apply text-xs text-gray-500;
}

/* Tab Navigation */
.profile-tabs {
  @apply flex flex-wrap gap-2 mb-6 border-b border-gray-200;
}

.tab-button {
  @apply px-4 py-3 text-sm font-medium rounded-t-lg transition-colors duration-200 flex items-center gap-2;
}

.tab-button:not(.active) {
  @apply text-gray-600 hover:bg-gray-100;
}

.tab-button.active {
  @apply text-[var(--primary)] border-b-2 border-[var(--primary)] bg-blue-50;
}

.tab-button i {
  @apply text-base;
}

/* Tab Content */
.tab-content {
  @apply mb-8;
}

.content-card {
  @apply bg-white rounded-lg md:p-6 p-2 shadow-sm;
}

/* Section Title */
.section-title {
  @apply text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2;
}

.subsection-title {
  @apply text-lg font-medium text-gray-700 mb-4 flex items-center gap-2;
}

/* Form Layout */
.profile-form {
  @apply w-full;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-column {
  @apply space-y-6;
}

/* Form Elements */
.form-group {
  @apply flex flex-col;
}

.form-label {
  @apply text-sm font-medium text-gray-700 mb-1 flex items-center gap-2;
}

.form-input {
  @apply w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 ;
  height: 46px;
}

.input-error {
  @apply border-red-500;
}

.error-message {
  @apply text-red-500 text-xs mt-1;
}

/* Alert styles */
.alert {
  @apply p-4 rounded-lg;
}

.alert-danger {
  @apply bg-red-50 text-red-700 border border-red-200;
}

/* Input with Button */
.input-with-button {
  @apply flex items-center;
}

.input-with-button .form-input {
  @apply flex-grow rounded-r-none;
}

.verify-button {
  @apply bg-yellow-400 text-black font-medium px-4 py-2 rounded-r-lg transition-colors duration-200 flex items-center gap-1;
  height: 46px;
}

.verify-button:hover {
  @apply bg-yellow-500;
}

.verify-button:disabled {
  @apply bg-gray-300 cursor-not-allowed;
}

/* Update Button */
.button-container {
  @apply flex justify-end mt-8;
}

.update-button {
  @apply bg-blue-500 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 flex items-center gap-2;
}

.update-button:hover {
  @apply bg-blue-600;
}

.update-button:disabled {
  @apply bg-gray-300 cursor-not-allowed;
}

/* Security Section */
.security-section {
  @apply mt-10 pt-8 border-t border-gray-200;
}

.security-item {
  @apply flex flex-col md:flex-row justify-between py-6 border-b border-gray-100;
}

.security-content {
  @apply flex-grow pr-4;
}

.security-header {
  @apply flex flex-col md:flex-row md:items-center justify-between mb-2;
}

.security-title {
  @apply text-base font-medium text-gray-800 flex items-center gap-2;
}

.security-status {
  @apply text-sm font-medium mt-1 md:mt-0 px-2 py-1 rounded-full;
}

.status-enabled {
  @apply text-green-600 bg-green-50;
}

.status-disabled {
  @apply text-gray-500 bg-gray-50;
}

.security-description {
  @apply text-sm text-gray-600 mb-4;
}

.security-action {
  @apply flex items-center mt-4 md:mt-0;
}

.security-button {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200;
}

.enable-button {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.disable-button {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
}

/* MFA Buttons */
.mfa-button {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center;
  min-width: 200px;
  height: 40px;
}

.mfa-enable-button {
  @apply bg-red-500 text-white hover:bg-red-600;
}

.mfa-disable-button {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.generate-button {
  @apply bg-green-500 text-white hover:bg-green-600;
}

.api-key-container {
  @apply flex items-center bg-gray-50 p-3 rounded-lg mb-3;
}

.api-key-value {
  @apply flex-grow font-mono text-sm text-gray-700 mr-2 overflow-x-auto;
}

.copy-button {
  @apply flex items-center space-x-1 text-xs bg-white px-3 py-1 rounded border border-gray-200 hover:bg-gray-100;
}

.security-warning {
  @apply text-xs text-red-500 mt-2;
}

/* Settings Section */
.settings-section {
  @apply mt-10 pt-8 border-t border-gray-200;
}

.setting-item {
  @apply flex flex-col md:flex-row md:items-center py-4 border-b border-gray-100;
}

.setting-label {
  @apply text-sm font-medium text-gray-700 mb-2 md:mb-0 md:w-1/4;
}

.setting-dropdown {
  @apply w-full md:w-3/4;
}

/* Login History Section */
.login-history-section {
  @apply mt-10 pt-8 border-t border-gray-200;
}

.loading-indicator {
  @apply flex items-center justify-center py-8;
}

.spinner {
  @apply w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2;
}

.no-history {
  @apply text-gray-500 py-6 text-center flex items-center justify-center gap-2;
}

.history-table-container {
  @apply overflow-x-auto;
}

.history-table {
  @apply w-full border-collapse;
}

.history-table th {
  @apply bg-gray-50 text-left py-3 px-4 font-medium text-gray-700 border-b border-gray-200;
}

.history-table td {
  @apply py-3 px-4 border-b border-gray-100 text-gray-800;
}

.device-info {
  @apply text-sm max-w-md truncate;
}

/* Mobile Card View */
.history-mobile-cards {
  @apply space-y-4;
}

.history-mobile-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 transition-all duration-150;
}

.history-mobile-card-header {
  @apply flex items-center mb-3 pb-2 border-b border-gray-100;
}

.history-mobile-icon {
  @apply text-gray-500 mr-2;
}

.history-mobile-time {
  @apply text-sm font-medium text-gray-800;
}

.history-mobile-card-content {
  @apply space-y-3;
}

.history-mobile-item {
  @apply flex flex-col;
}

.history-mobile-label {
  @apply flex items-center text-xs text-gray-500 mb-1;
}

.history-mobile-label i {
  @apply mr-1;
}

.history-mobile-value {
  @apply text-sm text-gray-700 break-words;
}

/* Password Input */
.password-input-container {
  @apply relative flex items-center;
}

.password-input-container .form-input {
  @apply pr-10;
}

.toggle-password {
  @apply absolute right-3 text-gray-500 hover:text-gray-700 focus:outline-none;
}

/* Login Status */
.login-status {
  @apply text-xs font-medium px-2 py-1 rounded-full;
}

.login-status.success {
  @apply bg-green-50 text-green-600;
}

/* History Footer */
.history-footer {
  @apply flex justify-between items-center mt-4;
}

.history-count {
  @apply text-sm text-gray-500;
}

.pagination {
  @apply flex items-center gap-2;
}

.pagination-button {
  @apply p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.pagination-info {
  @apply text-sm text-gray-600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-header-card {
    @apply flex-col items-center text-center;
  }

  .profile-header-content {
    @apply flex-col;
  }

  .profile-stats {
    @apply mt-6 w-full justify-center;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .button-container {
    @apply justify-center;
  }

  .update-button {
    @apply w-full;
  }

  .setting-item {
    @apply flex-col;
  }

  .setting-label {
    @apply w-full mb-2;
  }

  .setting-dropdown {
    @apply w-full;
  }

  .history-table {
    @apply text-sm;
  }

  .history-table th,
  .history-table td {
    @apply px-2 py-2;
  }

  .device-info {
    @apply max-w-[150px];
  }

  .history-footer {
    @apply flex-col gap-2;
  }

  /* Mobile history cards */
  .history-mobile-card {
    @apply mb-4;
  }

  .history-mobile-value {
    @apply max-w-full overflow-hidden;
  }
}
