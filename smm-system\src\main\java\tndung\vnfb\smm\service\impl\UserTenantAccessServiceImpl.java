package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.entity.UserTenantAccess;
import tndung.vnfb.smm.repository.nontenant.UserTenantAccessRepository;
import tndung.vnfb.smm.service.UserTenantAccessService;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserTenantAccessServiceImpl implements UserTenantAccessService {

    private final UserTenantAccessRepository userTenantAccessRepository;



    @Override
    public List<String> getAccessibleTenantIds(Long userId) {
        return userTenantAccessRepository.findByUserId(userId)
                .stream()
                .map(UserTenantAccess::getTenantId)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasAccessToTenant(Long userId, String tenantId) {
        return userTenantAccessRepository.existsByUserIdAndTenantId(userId, tenantId);
    }
    
    @Override
    public void grantTenantAccess(Long userId, String tenantId) {
        if (!hasAccessToTenant(userId, tenantId)) {
            UserTenantAccess userTenantAccess = new UserTenantAccess();
            userTenantAccess.setUserId(userId);
            userTenantAccess.setTenantId(tenantId);
            userTenantAccessRepository.save(userTenantAccess);
            log.info("Granted access to tenant {} for user {}", tenantId, userId);
        }
    }
    
    @Override
    public void revokeTenantAccess(Long userId, String tenantId) {
        userTenantAccessRepository.deleteByUserIdAndTenantId(userId, tenantId);
        log.info("Revoked access to tenant {} for user {}", tenantId, userId);
    }
}
