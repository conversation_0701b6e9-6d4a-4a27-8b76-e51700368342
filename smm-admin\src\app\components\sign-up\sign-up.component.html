<!-- Registration Section -->
<section class="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0">
    <div class="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
    <div class="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
  </div>

  <div class="relative container mx-auto px-4 py-16">
    <div class="grid lg:grid-cols-2 gap-12 items-center min-h-screen">
      <!-- Left Side - Content -->
      <div class="space-y-8">
        <div class="space-y-6">
          <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
            {{ 'auth.create_your_panel' | translate }}
          </h1>
          <p class="text-xl text-gray-600 leading-relaxed">
            {{ 'auth.join_thousands' | translate }}
          </p>
        </div>

        <!-- Benefits List -->
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">{{ 'auth.start_your_business' | translate }}</span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Only $7.7/month - No setup fees</span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">Launch in 5 minutes</span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-gray-700">24/7 support & maintenance included</span>
          </div>
        </div>

        <!-- Stats -->
        <div class="flex space-x-8">
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900">105K+</div>
            <div class="text-sm text-gray-600">Panels created</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900">84M+</div>
            <div class="text-sm text-gray-600">Orders completed</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900">99.9%</div>
            <div class="text-sm text-gray-600">Uptime</div>
          </div>
        </div>
      </div>

      <!-- Right Side - Registration Form -->
      <div class="flex justify-center lg:justify-end">
        <div class="w-full max-w-md">
          <div class="bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl p-8 border border-white/20">
            <div class="text-center mb-8">
              <h2 class="text-2xl font-bold text-gray-900 mb-2">
                {{ 'signup.create_account' | translate }}
              </h2>
              <p class="text-gray-600">
                {{ 'signup.join_community' | translate }}
              </p>
            </div>

            <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()" class="space-y-6">
              <!-- Email Field -->
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ 'signup.email' | translate }}
                </label>
                <input
                  id="email"
                  type="email"
                  formControlName="email"
                  placeholder="{{ 'signup.enter_email' | translate }}"
                  class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 backdrop-blur-sm"
                  [ngClass]="{'border-red-500 focus:ring-red-500': registrationForm.get('email')?.invalid && registrationForm.get('email')?.touched}">
                <div *ngIf="registrationForm.get('email')?.invalid && registrationForm.get('email')?.touched" class="text-red-500 text-sm mt-1">
                  <div *ngIf="registrationForm.get('email')?.errors?.['required']">{{ 'signup.email_required' | translate }}</div>
                  <div *ngIf="registrationForm.get('email')?.errors?.['email']">{{ 'signup.email_invalid' | translate }}</div>
                </div>
              </div>

              <!-- Username Field -->
              <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ 'signup.username' | translate }}
                </label>
                <input
                  id="username"
                  type="text"
                  formControlName="username"
                  placeholder="{{ 'signup.enter_username' | translate }}"
                  class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 backdrop-blur-sm"
                  [ngClass]="{'border-red-500 focus:ring-red-500': registrationForm.get('username')?.invalid && registrationForm.get('username')?.touched}">
                <div *ngIf="registrationForm.get('username')?.invalid && registrationForm.get('username')?.touched" class="text-red-500 text-sm mt-1">
                  {{ 'signup.username_required' | translate }}
                </div>
              </div>

              <!-- Password Field -->
              <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ 'signup.password' | translate }}
                </label>
                <input
                  id="password"
                  type="password"
                  formControlName="password"
                  placeholder="{{ 'signup.enter_password' | translate }}"
                  class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 backdrop-blur-sm"
                  [ngClass]="{'border-red-500 focus:ring-red-500': registrationForm.get('password')?.invalid && registrationForm.get('password')?.touched}">
                <div *ngIf="registrationForm.get('password')?.invalid && registrationForm.get('password')?.touched" class="text-red-500 text-sm mt-1">
                  <div *ngIf="registrationForm.get('password')?.errors?.['required']">{{ 'signup.password_required' | translate }}</div>
                  <div *ngIf="registrationForm.get('password')?.errors?.['minlength']">{{ 'signup.password_min_length' | translate }}</div>
                </div>
              </div>

              <!-- Confirm Password Field -->
              <div>
                <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ 'signup.confirm_password' | translate }}
                </label>
                <input
                  id="confirmPassword"
                  type="password"
                  formControlName="confirmPassword"
                  placeholder="{{ 'signup.enter_confirm_password' | translate }}"
                  class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 backdrop-blur-sm"
                  [ngClass]="{'border-red-500 focus:ring-red-500': registrationForm.get('confirmPassword')?.invalid && registrationForm.get('confirmPassword')?.touched}">
                <div *ngIf="registrationForm.get('confirmPassword')?.invalid && registrationForm.get('confirmPassword')?.touched" class="text-red-500 text-sm mt-1">
                  <div *ngIf="registrationForm.get('confirmPassword')?.errors?.['required']">{{ 'signup.confirm_password_required' | translate }}</div>
                  <div *ngIf="registrationForm.get('confirmPassword')?.errors?.['passwordMismatch']">{{ 'signup.passwords_not_match' | translate }}</div>
                </div>
              </div>

              <!-- Terms and Conditions -->
              <div class="flex items-start">
                <input
                  id="termsAgreed"
                  type="checkbox"
                  formControlName="termsAgreed"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1">
                <label for="termsAgreed" class="ml-2 block text-sm text-gray-700">
                  {{ 'signup.terms_agree' | translate }}
                </label>
              </div>
              <div *ngIf="registrationForm.get('termsAgreed')?.invalid && registrationForm.get('termsAgreed')?.touched" class="text-red-500 text-sm">
                {{ 'signup.terms_required' | translate }}
              </div>

              <!-- Error Message -->
              <div *ngIf="registrationError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm animate-shake">
                {{ registrationError }}
              </div>

              <!-- Register Button -->
              <button
                type="submit"
                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium text-lg shadow-lg"
                [disabled]="registrationForm.invalid || isLoading"
                [ngClass]="{'opacity-50 cursor-not-allowed': registrationForm.invalid || isLoading}">
                <span *ngIf="!isLoading">{{ 'signup.register' | translate }}</span>
                <span *ngIf="isLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ 'signup.registering' | translate }}
                </span>
              </button>

              <!-- Login Link -->
              <div class="text-center">
                <p class="text-sm text-gray-600">
                  {{ 'signup.already_have_account' | translate }}
                  <a routerLink="/auth/login" class="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                    {{ 'signup.login' | translate }}
                  </a>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>