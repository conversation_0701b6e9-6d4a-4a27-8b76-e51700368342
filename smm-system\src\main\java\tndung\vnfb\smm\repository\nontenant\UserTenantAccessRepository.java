package tndung.vnfb.smm.repository.nontenant;

import org.springframework.data.jpa.repository.JpaRepository;
import tndung.vnfb.smm.entity.UserTenantAccess;

import java.util.List;


public interface UserTenantAccessRepository extends JpaRepository<UserTenantAccess, Long> {

    /**
     * Find all user-tenant relationships for a specific user
     *
     * @param userId The user ID
     * @return List of UserTenant entities
     */
    List<UserTenantAccess> findByUserId(Long userId);

    /**
     * Check if a user has access to a specific tenant
     *
     * @param userId The user ID
     * @param tenantId The tenant ID
     * @return true if the relationship exists, false otherwise
     */
    boolean existsByUserIdAndTenantId(Long userId, String tenantId);

    /**
     * Delete a user-tenant relationship
     *
     * @param userId The user ID
     * @param tenantId The tenant ID
     */
    void deleteByUserIdAndTenantId(Long userId, String tenantId);

    /**
     * Find all user-tenant relationships for a specific tenant
     *
     * @param tenantId The tenant ID
     * @return List of UserTenantAccess entities
     */
    List<UserTenantAccess> findByTenantId(String tenantId);
}
