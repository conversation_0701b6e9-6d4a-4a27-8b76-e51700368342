package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.TenantLanguageSettingsReq;

import tndung.vnfb.smm.dto.response.TenantLanguageSettingsRes;
import tndung.vnfb.smm.dto.response.TenantDefaultLanguageRes;
import tndung.vnfb.smm.service.TenantSettingsService;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/tenant-settings")
@RequiredArgsConstructor
public class TenantSettingsController {

    private final TenantSettingsService tenantSettingsService;

    @GetMapping("/language")
    public ApiResponseEntity<TenantLanguageSettingsRes> getLanguageSettings() {
        TenantLanguageSettingsRes settings = tenantSettingsService.getLanguageSettings();
        return ApiResponseEntity.success(settings);
    }

    @PutMapping("/language")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantLanguageSettingsRes> updateLanguageSettings(
            @Valid @RequestBody TenantLanguageSettingsReq request) {
        TenantLanguageSettingsRes settings = tenantSettingsService.updateLanguageSettings(request);
        return ApiResponseEntity.success(settings);
    }

    @GetMapping("/language/default")
    public ApiResponseEntity<TenantDefaultLanguageRes> getTenantDefaultLanguage() {
        TenantDefaultLanguageRes defaultLanguage = tenantSettingsService.getTenantDefaultLanguage();
        return ApiResponseEntity.success(defaultLanguage);
    }

    @GetMapping("/language/available")
    public ApiResponseEntity<List<String>> getTenantAvailableLanguages() {
        List<String> availableLanguages = tenantSettingsService.getTenantAvailableLanguages();
        return ApiResponseEntity.success(availableLanguages);
    }
}
