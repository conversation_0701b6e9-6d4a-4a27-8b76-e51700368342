<div class="settings-layout">
  <!-- Desktop Sidebar -->
  <div class="sidebar-container hidden md:block">
    <app-settings-sidebar [isOpen]="true"></app-settings-sidebar>
  </div>

  <!-- Mobile Settings Button -->
  <button
    class="settings-toggle-btn md:hidden fixed top-4 right-4 bg-blue-500 text-white rounded-lg px-4 py-2 shadow-lg z-50 flex items-center gap-2"
    (click)="toggleSidebar()">
    <fa-icon [icon]="['fas', 'cog']" class="text-sm"></fa-icon>
    <span>Settings</span>
  </button>

  <!-- Mobile Sidebar (slide-in from right) -->
  <div class="mobile-sidebar-container md:hidden" [ngClass]="{'open': isOpen}">
    <!-- Overlay -->
    <div class="sidebar-overlay" (click)="closeSidebar()"></div>

    <!-- Sidebar Content -->
    <div class="mobile-sidebar">
      <!-- Header with close button -->
      <div class="mobile-sidebar-header">
        <h2>Settings Menu</h2>
        <button class="close-button" (click)="closeSidebar()">
          <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
        </button>
      </div>

      <!-- Navigation -->
      <div class="mobile-sidebar-content">
        <app-settings-sidebar [isOpen]="true"></app-settings-sidebar>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content-layout bg-white rounded-lg p-6 shadow-sm">
    <router-outlet></router-outlet>
  </div>
</div>
