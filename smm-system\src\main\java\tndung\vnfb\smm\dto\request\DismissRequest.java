package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class DismissRequest {
    @NotNull(message = "Hours is required")
    @Min(value = 1, message = "Hours must be at least 1")
    @Max(value = 168, message = "Hours must be at most 168 (7 days)")
    private Integer hours;
}
