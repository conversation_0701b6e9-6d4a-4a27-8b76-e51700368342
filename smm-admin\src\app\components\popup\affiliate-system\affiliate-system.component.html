<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <!-- Header -->
      <div class="modal-header">
        <div class="flex items-center">
          <div class="icon-container">
            <fa-icon [icon]="['fas', 'user-friends']" class="text-white"></fa-icon>
          </div>
          <h2 class="modal-title">{{ 'affiliates.affiliate_system' | translate }}</h2>
        </div>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Description -->
      <div class="description-section">
        <p>{{ 'affiliates.loyalty_program' | translate }}</p>
      </div>

      <!-- Settings Form -->
      <div class="settings-form">
        <!-- Enable on panel -->
        <div class="setting-item">
          <div class="setting-label">{{ 'affiliates.enable_on_panel' | translate }}</div>
          <div class="toggle-switch">
            <label class="switch">
              <input type="checkbox" [(ngModel)]="enableOnPanel">
              <span class="slider round"></span>
            </label>
          </div>
        </div>

        <!-- Referral percentage -->
        <div class="setting-item">
          <div class="setting-label">{{ 'affiliates.set_percentage' | translate }}</div>
          <div class="input-container">
            <input
              type="number"
              [(ngModel)]="referralPercentage"
              min="1"
              max="100"
              class="percentage-input"
              placeholder="5">
          </div>
        </div>
      </div>

      <!-- Footer with Save Button -->
      <div class="modal-footer">
        <button class="save-button" (click)="saveSettings()">
          {{ 'affiliates.save' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
