import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthUtilsService } from '../services/auth-utils.service';
import { PLATFORM_ID } from '@angular/core';
import { Location } from '@angular/common';

export const authGuard: CanActivateFn = (route, state) => {
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);
  // const platformId = inject(PLATFORM_ID);
  const location = inject(Location);
 

  // Skip auth check for login and register routes
  const currentPath = location.path();
  if (currentPath.includes('/auth/login') || currentPath.includes('/auth/register')) {
    return true;
  }

  // Check if user is authenticated and token is valid
  if (authUtils.isAuthenticated() && !authUtils.isTokenExpired()) {
    return true;
  }

  // Store the intended URL for redirection after login (only in browser)
  // if (isBrowser) {
    // Don't store login or register URLs
    if (!state.url.includes('/auth/login') && !state.url.includes('/auth/register')) {
      localStorage.setItem('redirectAfterLogin', state.url);
    }
 /// }

  // Redirect to login page with return url
  router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
  return false;
};
