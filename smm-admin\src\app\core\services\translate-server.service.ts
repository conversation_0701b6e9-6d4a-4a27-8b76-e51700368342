import { Injectable } from '@angular/core';
import { TransferState, makeStateKey } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';

const TRANSLATE_KEY = makeStateKey<string>('translate');

@Injectable()
export class TranslateServerService {
  constructor(
    private translate: TranslateService,
    private transferState: TransferState
  ) {}

  initLanguage(lang: string) {
    if (this.transferState.hasKey(TRANSLATE_KEY)) {
      // Client: Lấy dữ liệu từ server
      this.translate.use(this.transferState.get(TRANSLATE_KEY, lang));
    } else {
      // Server: Thiết lập và lưu dữ liệu
      this.translate.use(lang).subscribe(() => {
        this.transferState.set(TRANSLATE_KEY, lang);
      });
    }
  }
}