import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { RouterModule, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SidebarService } from '../../../core/services/sidebar.service';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AuthService } from '../../../core/services/auth.service';
import { UserService } from '../../../core/services/user.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';
import { TenantSwitcherComponent } from '../../tenant-switcher/tenant-switcher.component';
import { NotificationDropdownComponent } from '../notification-dropdown/notification-dropdown.component';

interface NavItem {
  icon: IconName;
  label: string;
  link: string;
  isActive: boolean;
}

interface NavSection {
  title: string;
  items: NavItem[];
}

@Component({
  selector: 'app-admin-header',
  standalone: true,
  imports: [CommonModule, IconsModule, RouterModule, TranslateModule, TenantSwitcherComponent, NotificationDropdownComponent],
  templateUrl: './admin-header.component.html',
  styleUrl: './admin-header.component.css'
})
export class AdminHeaderComponent implements OnInit, OnDestroy {
  user: UserRes | undefined;
  private userSubscription: Subscription | undefined;

  adminNavItems: NavSection[] = [
    {
      title: 'Main Menu',
      items: [
        {
          icon: 'user' as IconName,
          label: 'Users',
          link: '/panel/users',
          isActive: false
        },
        {
          icon: 'shopping-cart' as IconName,
          label: 'Orders',
          link: '/panel/orders',
          isActive: false
        },
        {
          icon: 'tag' as IconName,
          label: 'Services',
          link: '/panel/services',
          isActive: false
        },
        {
          icon: 'headset' as IconName,
          label: 'Support',
          link: '/panel/tickets',
          isActive: false
        },
        {
          icon: 'chart-line' as IconName,
          label: 'Statistics',
          link: '/panel/statistics',
          isActive: false
        }
      ]
    },
    {
      title: 'More',
      items: [
        {
          icon: 'chart-bar' as IconName,
          label: 'Dashboard',
          link: '/panel/dashboard',
          isActive: false
        },
        {
          icon: 'cog' as IconName,
          label: 'Settings',
          link: '/panel/settings',
          isActive: false
        },

      ]
    }
  ];

  constructor(
    private sidebarService: SidebarService,
    private router: Router,
    private authService: AuthService,
    private userService: UserService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit() {
    this.updateActiveState();

    // Subscribe to user information
    this.userSubscription = this.userService.user$.subscribe(user => {
      this.user = user;
    });

    // Trigger getting user information if needed
    this.userService.get$.next();
  }

  ngOnDestroy() {
    // Clean up subscription when component is destroyed
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  updateActiveState() {
    const currentUrl = this.router.url;

    this.adminNavItems.forEach(section => {
      section.items.forEach(item => {
        // Check if the current URL starts with the item's link
        // This handles both exact matches and child routes (like /admin/tickets/123)
        if (item.link === '/panel/tickets') {
          item.isActive = currentUrl.startsWith(item.link);
        } else {
          item.isActive = currentUrl === item.link;
        }
      });
    });
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  getItemsBySection(sectionTitle: string): NavItem[] {
    const section = this.adminNavItems.find(section => section.title === sectionTitle);
    return section ? section.items : [];
  }

  // Navigate to profile page
  goToProfile(): void {
    this.router.navigate(['/panel/settings/profile']);
  }

  // Navigate to settings page
  goToSettings(): void {
    this.router.navigate(['/panel/settings']);
  }

  // Logout the user
  logout(): void {
    this.authService.logout();
  }

  // Navigate to add funds page
  goToAddFunds(): void {
    this.router.navigate(['/panel/settings/funds']);
  }

  // Navigate to transaction history
  goToHistory(): void {
    this.router.navigate(['/panel/settings/transactions']);
  }

  // Navigate to commission info
  goToCommissionInfo(): void {
    this.router.navigate(['/panel/settings/commission']);
  }

  // Format balance using the common currency service
  formatBalance(balance: number | undefined): string {
    return this.currencyService.formatBalance(balance);
  }

    getAvatarPath(): string {
    if (!this.user || !this.user.avatar) {
      // Default avatar if no user or no avatar
      return 'assets/images/profile-1.png';
    }

    // Return the avatar path based on user.avatar
    return `assets/images/${this.user.avatar}.png`;
  }
}
