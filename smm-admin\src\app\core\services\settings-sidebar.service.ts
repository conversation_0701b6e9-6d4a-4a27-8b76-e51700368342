import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class SettingsSidebarService {
  private sidebarOpenSubject: BehaviorSubject<boolean>;
  public sidebarOpen$: Observable<boolean>;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    // Default to closed on mobile, open on desktop
    const initialState = this.isBrowser() && window.innerWidth >= 768;
    this.sidebarOpenSubject = new BehaviorSubject<boolean>(initialState);
    this.sidebarOpen$ = this.sidebarOpenSubject.asObservable();

    // Only handle on browser
    if (this.isBrowser()) {
      // Check initial size
      this.handleResize();
      // Add resize event listener
      window.addEventListener('resize', () => {
        this.handleResize();
      });
    }
  }

  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  private handleResize(): void {
    const isDesktop = window.innerWidth >= 768; // md breakpoint
    // Only auto-open on desktop, don't auto-close if manually opened
    if (isDesktop && !this.sidebarOpenSubject.value) {
      this.sidebarOpenSubject.next(true);
    }
  }

  toggleSidebar(): void {
    if (this.isBrowser()) {
      const currentState = this.sidebarOpenSubject.value;
      this.sidebarOpenSubject.next(!currentState);
    }
  }

  setSidebarState(state: boolean): void {
    this.sidebarOpenSubject.next(state);
  }

  getSidebarState(): boolean {
    return this.sidebarOpenSubject.value;
  }
}
