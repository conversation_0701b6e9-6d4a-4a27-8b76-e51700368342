export interface LogoFileRes {
  url: string;
  file_type: string;
}

export interface ColorSchemeRes {
  primary: string;
  text_on_buttons: string;
}

export interface LandingSettingsRes {
  type: string;
}

export interface HeaderSettingsRes {
  type: 'standard' | 'compact' | 'modern' | 'minimal';
}

export interface SidebarSettingsRes {
  type: 'standard' | 'compact' | 'modern' | 'minimal' | 'card';
}

export interface DesignSettingsRes {
  id: string;

  // Logo & Favicon
  logo: LogoFileRes;
  favicon: LogoFileRes;

  // Color scheme
  color_scheme: ColorSchemeRes;

  // Layout settings
  landing_settings: LandingSettingsRes;
  header_settings: HeaderSettingsRes;
  sidebar_settings: SidebarSettingsRes;
}
