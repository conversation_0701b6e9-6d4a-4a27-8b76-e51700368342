import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { PromotionService, PromotionRes } from '../../../core/services/promotion.service';
import { ToastService } from '../../../core/services/toast.service';
import { DeleteConfirmationComponent } from '../delete-confirmation/delete-confirmation.component';
import { LoadingComponent } from '../../common/loading/loading.component';

@Component({
  selector: 'app-promotions',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconsModule,
    DeleteConfirmationComponent,
    LoadingComponent
  ],
  templateUrl: './promotions.component.html',
  styleUrl: './promotions.component.css'
})
export class PromotionsComponent implements OnInit {
  promotions: PromotionRes[] = [];
  isLoading: boolean = false;
  showConfirmationPopup: boolean = false;
  promotionToDelete: number | null = null;
  currentPage: number = 1;
  totalPages: number = 1;

  constructor(
    private promotionService: PromotionService,
    private toastService: ToastService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadPromotions();
  }

  /**
   * Load promotions from the server
   */
  loadPromotions(): void {
    this.isLoading = true;
    this.promotionService.getPromotions().subscribe({
      next: (promotions) => {
        this.promotions = promotions;
        this.calculatePagination();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading promotions:', error);
        this.toastService.showError('Failed to load promotions');
        this.isLoading = false;
      }
    });
  }

  /**
   * Calculate pagination based on promotions count
   */
  calculatePagination(): void {
    // Simple pagination logic - 5 items per page
    this.totalPages = Math.ceil(this.promotions.length / 5);
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
    }
  }

  /**
   * Get promotions for the current page
   */
  getCurrentPagePromotions(): PromotionRes[] {
    const startIndex = (this.currentPage - 1) * 5;
    const endIndex = startIndex + 5;
    return this.promotions.slice(startIndex, endIndex);
  }

  /**
   * Navigate to a specific page
   */
  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Get status class based on promotion dates
   */
  getStatusClass(promotion: PromotionRes): string {
    const now = new Date();
    const startDate = new Date(promotion.start_date);
    const endDate = new Date(promotion.end_date);

    if (!promotion.active) {
      return 'inactive';
    } else if (now < startDate) {
      return 'upcoming';
    } else if (now > endDate) {
      return 'expired';
    } else {
      return 'active';
    }
  }

  /**
   * Get status text based on promotion dates
   */
  getStatusText(promotion: PromotionRes): string {
    const now = new Date();
    const startDate = new Date(promotion.start_date);
    const endDate = new Date(promotion.end_date);

    if (!promotion.active) {
      return 'Inactive';
    } else if (now < startDate) {
      return 'Sắp diễn ra';
    } else if (now > endDate) {
      return 'Đã kết thúc';
    } else {
      return 'Đang chạy';
    }
  }

  /**
   * Navigate to create new promotion
   */
  createNewPromotion(): void {
    this.router.navigate(['/panel/settings/promotions/new']);
  }

  /**
   * Navigate to edit promotion
   */
  editPromotion(id: number): void {
    this.router.navigate([`/panel/settings/promotions/edit/${id}`]);
  }

  /**
   * Show confirmation popup before deleting
   */
  confirmDelete(id: number): void {
    this.promotionToDelete = id;
    this.showConfirmationPopup = true;
  }

  /**
   * Delete promotion after confirmation
   */
  deletePromotion(): void {
    if (this.promotionToDelete) {
      this.promotionService.deletePromotion(this.promotionToDelete).subscribe({
        next: () => {
          this.toastService.showSuccess('Promotion deleted successfully');
          this.loadPromotions();
          this.closeConfirmationPopup();
        },
        error: (error) => {
          console.error('Error deleting promotion:', error);
          this.toastService.showError('Failed to delete promotion');
          this.closeConfirmationPopup();
        }
      });
    }
  }

  /**
   * Close confirmation popup
   */
  closeConfirmationPopup(): void {
    this.showConfirmationPopup = false;
    this.promotionToDelete = null;
  }
}
