package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.dto.PanelNotificationReq;
import tndung.vnfb.smm.dto.PanelNotificationRes;
import tndung.vnfb.smm.entity.PanelNotification;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.mapper.PanelNotificationMapper;
import tndung.vnfb.smm.repository.tenant.PanelNotificationRepository;
import tndung.vnfb.smm.service.PanelNotificationService;
import tndung.vnfb.smm.config.TenantContext;

import java.util.List;
import java.util.Optional;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@RequiredArgsConstructor
@Service
@Slf4j
public class PanelNotificationServiceImpl implements PanelNotificationService {

    private final PanelNotificationRepository panelNotificationRepository;
    private final PanelNotificationMapper panelNotificationMapper;
    private final RedisTemplate<String, String> redisTemplate;

    private static final String REDIS_KEY_PREFIX = "panel_notification:read:";

    @Override
    public PanelNotificationRes createNotification(PanelNotificationReq req) {
        PanelNotification notification = panelNotificationMapper.toEntity(req);
        notification.setTenantId(MAIN_TENANT);

        PanelNotification saved = panelNotificationRepository.save(notification);
        return panelNotificationMapper.toDto(saved);
    }

    @Override
    public Page<PanelNotificationRes> getNotifications(Pageable pageable) {

        Page<PanelNotification> notifications = panelNotificationRepository
                .findByTenantIdOrderByCreatedAtDesc(MAIN_TENANT, pageable);
        return notifications.map(panelNotificationMapper::toDto);
    }

    @Override
    public List<PanelNotificationRes> getUnreadNotifications() {

        List<PanelNotification> notifications = panelNotificationRepository
                .findUnreadByTenantId(MAIN_TENANT);
        return panelNotificationMapper.toDto(notifications);
    }

    @Override
    public long getUnreadCount() {

        return panelNotificationRepository.countUnreadByTenantId(MAIN_TENANT);
    }

    @Override
    public void markAsRead(Long notificationId) {
        Optional<PanelNotification> notification = panelNotificationRepository.findById(notificationId);
        if (notification.isPresent()) {
            PanelNotification notif = notification.get();
            notif.setIsRead(true);
            panelNotificationRepository.save(notif);
        }
    }

    @Override
    public void markAllAsRead() {

        List<PanelNotification> unreadNotifications = panelNotificationRepository
                .findUnreadByTenantId(MAIN_TENANT);

        for (PanelNotification notification : unreadNotifications) {
            notification.setIsRead(true);
        }
        panelNotificationRepository.saveAll(unreadNotifications);
    }

    @Override
    public void createTenantSetupSuccessNotification(Tenant tenant) {
        PanelNotificationReq req = new PanelNotificationReq();
        req.setTitle("Thiết lập thành công");
        req.setContent(String.format("Domain %s đã được thiết lập thành công và sẵn sàng sử dụng.", tenant.getDomain()));
        req.setType(PanelNotification.NotificationType.SUCCESS);
        req.setCategory(PanelNotification.NotificationCategory.SETUP);


        createNotification(req);
        log.info("Created setup success notification for tenant: {}", tenant.getDomain());

    }

    @Override
    public void createTenantSetupFailureNotification(Tenant tenant, String errorMessage) {
        PanelNotificationReq req = new PanelNotificationReq();
        req.setTitle("Thiết lập thất bại");
        req.setContent(String.format("Có lỗi xảy ra khi thiết lập domain %s: %s", tenant.getDomain(), errorMessage));
        req.setType(PanelNotification.NotificationType.ERROR);
        req.setCategory(PanelNotification.NotificationCategory.SETUP);


        TenantContext.setCurrentTenant(tenant.getId());
        createNotification(req);
        log.info("Created setup failure notification for tenant: {}", tenant.getDomain());

    }

    @Override
    public void createRenewalReminderNotification(Tenant tenant) {
        PanelNotificationReq req = new PanelNotificationReq();
        req.setTitle("Sắp hết hạn subscription");
        req.setContent(String.format("Subscription của domain %s sẽ hết hạn vào %s. Vui lòng gia hạn để tiếp tục sử dụng dịch vụ.",
                tenant.getDomain(), tenant.getSubscriptionEndDate()));
        req.setType(PanelNotification.NotificationType.WARNING);
        req.setCategory(PanelNotification.NotificationCategory.RENEWAL);

        // Temporarily set tenant context for this notification

        createNotification(req);
        log.info("Created renewal reminder notification for tenant: {}", tenant.getDomain());

    }

    @Override
    public boolean isNotificationReadByUser(Long userId, Long notificationId) {
        String redisKey = REDIS_KEY_PREFIX + userId + ":" + notificationId;
        return Boolean.TRUE.equals(redisTemplate.hasKey(redisKey));
    }

    @Override
    public void markNotificationAsReadByUser(Long userId, Long notificationId) {
        String redisKey = REDIS_KEY_PREFIX + userId + ":" + notificationId;
        redisTemplate.opsForValue().set(redisKey, "read");
        // Set expiration to 30 days
        redisTemplate.expire(redisKey, 30, java.util.concurrent.TimeUnit.DAYS);
    }

    @Override
    public void sendRenewalNotification(Tenant tenant) {
        // This method now delegates to createRenewalReminderNotification
        createRenewalReminderNotification(tenant);
    }

    @Override
    public void sendExpirationNotification(Tenant tenant) {
        PanelNotificationReq req = new PanelNotificationReq();
        req.setTitle("Subscription đã hết hạn");
        req.setContent(String.format("Subscription của domain %s đã hết hạn vào %s. Bạn có %d ngày grace period để gia hạn trước khi tài khoản bị tạm ngưng.",
                tenant.getDomain(), tenant.getSubscriptionEndDate(), tenant.getGracePeriodDays()));
        req.setType(PanelNotification.NotificationType.ERROR);
        req.setCategory(PanelNotification.NotificationCategory.RENEWAL);

        TenantContext.setCurrentTenant(tenant.getId());
        createNotification(req);
        log.info("Created expiration notification for tenant: {}", tenant.getDomain());

    }


}
