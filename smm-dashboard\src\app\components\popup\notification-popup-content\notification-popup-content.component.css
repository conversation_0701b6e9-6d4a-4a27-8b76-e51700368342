.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #dcddff;
}

.close-button {
  @apply p-2 rounded-full text-gray-500 hover:text-gray-700 ;
}

.title {
  font-family: Inter, sans-serif;
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  color: #212529;
}

.close-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.modal-content {
  padding: 16px 0;
  border-bottom: 1px solid #dcddff;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.blue-text {
  font-family: Inter, sans-serif;
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
  color: var(--primary);
}

.description {
  font-family: Inter, sans-serif;
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
  color: #586280;
  white-space: pre-line;
}

.description a {
  color: var(--primary);
  text-decoration: none;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border-radius: 8px;
  font-family: Inter, sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary);
  color: #fff;
  border: 1px solid var(--primary);
}

.btn-secondary {
  background: #bfbfbf;
  color: #fff;
}

.btn-primary:hover, .btn-secondary:hover {
  opacity: 0.9;
}

.modal-container {
  min-width: 400px;
  max-width: 802px;
  background: #fff;
  border-radius: 16px;
  border: 1px solid #dcddff;
  padding: 32px 40px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Styles for content rendered with innerHTML */
.content-section ::ng-deep a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  word-break: break-all;
}

.content-section ::ng-deep a:hover {
  text-decoration: underline;
}

.content-section ::ng-deep p {
  margin-bottom: 12px;
  font-family: Inter, sans-serif;
  font-size: 16px;
  line-height: 24px;
  color: #586280;
}

.content-section ::ng-deep ul,
.content-section ::ng-deep ol {
  margin-left: 20px;
  margin-bottom: 12px;
}

.content-section ::ng-deep li {
  margin-bottom: 8px;
}

.content-section ::ng-deep strong,
.content-section ::ng-deep b {
  font-weight: 600;
}

.content-section ::ng-deep em,
.content-section ::ng-deep i {
  font-style: italic;
}
