.providers-container {
  @apply max-w-5xl mx-auto md:p-6 p-2;
}

.provider-item {
  @apply flex items-center justify-between bg-white rounded-lg p-4 shadow-sm;
}

.provider-icon {
  @apply w-8 h-8 flex-shrink-0;
}

.provider-name {
  @apply text-gray-800 font-medium;
}

.provider-balance {
  @apply text-gray-700 font-medium;
}


.menu-btn {
  @apply text-gray-500 hover:text-gray-700;
}

.add-provider-btn {
  @apply mt-6 bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm flex items-center justify-center w-40;
}

/* Action Menu Styles */
.dropdown-menu {
  @apply bg-white rounded-lg shadow-lg overflow-hidden;
  min-width: 160px;
}

.dropdown-menu a {
  @apply flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 transition-colors duration-200;
}

.dropdown-menu a fa-icon {
  @apply mr-3 w-4;
}

.action-menu-button {
  @apply text-gray-500 hover:text-gray-700 cursor-pointer;
}
