import { Component, EventEmitter, Output, HostListener, ElementRef, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastService } from '../../../core/services/toast.service';
import { NotifyType } from '../../../constant/notify-type';
import { IconsModule } from '../../../icons/icons.module';
import { MfaService, MfaVerifyRequest } from '../../../core/services/mfa.service';

@Component({
  selector: 'app-mfa-setting',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconsModule
  ],
  templateUrl: './mfa-setting.component.html',
  styleUrl: './mfa-setting.component.css'
})
export class MfaSettingComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() enabled = new EventEmitter<void>();

  mfaForm: FormGroup;
  isLoading = false;
  qrCodeImage: string = '';
  secretKey: string = '';
  showPassword = false;

  constructor(
    private fb: FormBuilder,
    private elementRef: ElementRef,
    private mfaService: MfaService,
    private toastService: ToastService
  ) {
    this.mfaForm = this.fb.group({
      code: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6), Validators.pattern(/^\d{6}$/)]],
      password: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.generateMfaCode();
  }

  /**
   * Generate MFA QR code and secret key
   */
  generateMfaCode(): void {
    this.isLoading = true;

    this.mfaService.generateMfa()
      .subscribe({
        next: (response) => {
          if (response) {
            this.qrCodeImage = response.image;
            this.secretKey = response.secret_key;
          }
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Error generating MFA code:', error);
          this.toastService.showToast(error.message || 'Failed to generate MFA code. Please try again.', NotifyType.ERROR);
          this.isLoading = false;
        }
      });
  }

  /**
   * Verify MFA code and enable 2FA
   */
  onSubmit(): void {
    if (this.mfaForm.invalid) {
      // Mark all form controls as touched to show validation errors
      Object.keys(this.mfaForm.controls).forEach(key => {
        const control = this.mfaForm.get(key);
        if (control) {
          control.markAsTouched();
        }
      });
      return;
    }

    this.isLoading = true;

    const verifyData: MfaVerifyRequest = {
      password: this.mfaForm.value.password,
      secret_key: this.secretKey,
      code: this.mfaForm.value.code
    };

    this.mfaService.verifyMfa(verifyData)
      .subscribe({
        next: () => {
          this.toastService.showToast('Two-factor authentication enabled successfully!', NotifyType.SUCCESS);
          this.isLoading = false;
          this.enabled.emit();
          this.onClose();
        },
        error: (error: any) => {
          console.error('Error verifying MFA code:', error);
          this.toastService.showToast(error.message || 'Failed to verify MFA code. Please check your code and try again.', NotifyType.ERROR);
          this.isLoading = false;
        }
      });
  }

  /**
   * Close the popup when clicking outside
   */
  // @HostListener('document:click', ['$event'])
  // onOverlayClick(event: MouseEvent): void {
  //   console.log('MFA Setting - onOverlayClick called');
  //   const clickedInside = this.elementRef.nativeElement.contains(event.target);
  //   console.log('Clicked inside:', clickedInside);
  //   if (!clickedInside) {
  //     this.onClose();
  //   }
  // }

  /**
   * Prevent event propagation when clicking inside the popup
   */
  onPopupClick(event: MouseEvent): void {
    event.stopPropagation();
  }

  /**
   * Close the popup
   */
  onClose(): void {
    console.log('MFA Setting - onClose called');
    this.close.emit();
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
