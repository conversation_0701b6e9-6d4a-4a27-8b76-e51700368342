import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DropdownService {
  private activeDropdownId = new BehaviorSubject<string | null>(null);

  // Subject to emit when a global click occurs to close all dropdowns
  private closeAllDropdowns$ = new Subject<void>();

  // Subject to emit when a specific dropdown should be excluded from closing
  private excludeDropdownId$ = new BehaviorSubject<string | null>(null);

  // Observable that components can subscribe to
  public closeAllDropdowns = this.closeAllDropdowns$.asObservable();

  // Track the currently active dropdown instance
  private currentActiveDropdownInstance: any = null;

  constructor() {
    // Add a global click handler to close all dropdowns
    document.addEventListener('click', (event) => {
      // Only close dropdowns if the click is not on a dropdown toggle button
      // This check helps prevent immediate closing when clicking to open
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        this.closeAll();
      }
    });
  }

  getActiveDropdown() {
    return this.activeDropdownId.asObservable();
  }

  openDropdown(id: string, instance: any) {
    // If there's already an active dropdown and it's not the same instance,
    // close it before opening the new one
    if (this.currentActiveDropdownInstance &&
        this.currentActiveDropdownInstance !== instance) {
      // Set the exclude ID to prevent the new dropdown from being closed
      this.excludeDropdownId$.next(id);

      // Close all other dropdowns
      this.closeAll();

      // Reset the exclude ID
      this.excludeDropdownId$.next(null);
    }

    // Set the new active dropdown
    this.activeDropdownId.next(id);
    this.currentActiveDropdownInstance = instance;
  }

  closeDropdown() {
    this.activeDropdownId.next(null);
    this.currentActiveDropdownInstance = null;
  }

  // Method to trigger closing all dropdowns except the excluded one
  closeAll(): void {
    this.closeAllDropdowns$.next();
    this.currentActiveDropdownInstance = null;
  }

  // Get the current excluded dropdown ID
  getExcludedDropdownId() {
    return this.excludeDropdownId$.value;
  }
}
