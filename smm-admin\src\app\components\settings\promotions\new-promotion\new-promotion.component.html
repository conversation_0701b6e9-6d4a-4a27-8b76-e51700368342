<div class="page-container">
  <div class="content-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="flex items-center gap-3">
        <button class="back-button" (click)="goBack()">
          <fa-icon [icon]="['fas', 'arrow-left']"></fa-icon>
        </button>
        <h1 class="page-title">{{ pageTitle }}</h1>
      </div>
    </div>

    <!-- Form Container -->
    <div class="promotion-form-container">
      <!-- Form -->
      <form [formGroup]="promotionForm" (ngSubmit)="onSubmit()" class="promotion-form">
        <!-- Name -->
        <div class="form-group">
          <label class="form-label">{{ 'Tên khuyến mãi' | translate }}</label>
          <input
            type="text"
            class="form-input"
            formControlName="name"
            [class.input-error]="promotionForm.get('name')?.invalid && promotionForm.get('name')?.touched"
            placeholder="{{ 'Nhập tên khuyến mãi' | translate }}">
          <div *ngIf="promotionForm.get('name')?.invalid && promotionForm.get('name')?.touched" class="validation-error">
            {{ 'Tên khuyến mãi là bắt buộc' | translate }}
          </div>
        </div>

        <!-- Description -->
        <div class="form-group">
          <label class="form-label">{{ 'Mô tả' | translate }}</label>
          <textarea
            class="form-textarea"
            formControlName="description"
            [class.input-error]="promotionForm.get('description')?.invalid && promotionForm.get('description')?.touched"
            placeholder="{{ 'Nhập thêm 10% khi nạp tiền từ 100.000 VND' | translate }}">
          </textarea>
          <div *ngIf="promotionForm.get('description')?.invalid && promotionForm.get('description')?.touched" class="validation-error">
            {{ 'Mô tả là bắt buộc' | translate }}
          </div>
        </div>

        <!-- Time Period -->
        <div class="form-group">
          <label class="form-label">{{ 'Thời gian áp dụng' | translate }}</label>
          <div class="date-range-container">
            <div class="date-input-container">
              <input
                type="date"
                class="form-input date-input"
                formControlName="start_date"
                [class.input-error]="promotionForm.get('start_date')?.invalid && promotionForm.get('start_date')?.touched">
              <div *ngIf="promotionForm.get('start_date')?.invalid && promotionForm.get('start_date')?.touched" class="validation-error">
                {{ 'Ngày bắt đầu là bắt buộc' | translate }}
              </div>
            </div>
            <span class="date-separator">{{ 'đến' | translate }}</span>
            <div class="date-input-container">
              <input
                type="date"
                class="form-input date-input"
                formControlName="end_date"
                [class.input-error]="promotionForm.get('end_date')?.invalid && promotionForm.get('end_date')?.touched">
              <div *ngIf="promotionForm.get('end_date')?.invalid && promotionForm.get('end_date')?.touched" class="validation-error">
                {{ 'Ngày kết thúc là bắt buộc' | translate }}
              </div>
            </div>
          </div>
        </div>

        <!-- Promotion Percentage -->
        <div class="form-group">
          <label class="form-label">{{ 'Tỷ lệ khuyến mãi (%)' | translate }}</label>
          <div class="percentage-input-container">
            <input
              type="number"
              class="form-input percentage-input"
              formControlName="promotion_percentage"
              [class.input-error]="promotionForm.get('promotion_percentage')?.invalid && promotionForm.get('promotion_percentage')?.touched"
              min="0"
              max="100"
              placeholder="10">
            <span class="percentage-symbol">%</span>
          </div>
          <div *ngIf="promotionForm.get('promotion_percentage')?.invalid && promotionForm.get('promotion_percentage')?.touched" class="validation-error">
            <span *ngIf="promotionForm.get('promotion_percentage')?.errors?.['required']">
              {{ 'Tỷ lệ khuyến mãi là bắt buộc' | translate }}
            </span>
            <span *ngIf="promotionForm.get('promotion_percentage')?.errors?.['min'] || promotionForm.get('promotion_percentage')?.errors?.['max']">
              {{ 'Tỷ lệ khuyến mãi phải từ 0 đến 100' | translate }}
            </span>
          </div>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          class="save-button"
          [disabled]="isSubmitting">
          <span *ngIf="!isSubmitting">{{ submitButtonText }}</span>
          <span *ngIf="isSubmitting">
            <fa-icon [icon]="['fas', 'spinner']" [spin]="true"></fa-icon>
            {{ (isEditMode ? 'Đang cập nhật...' : 'Đang tạo...') | translate }}
          </span>
        </button>
      </form>
    </div>
  </div>
</div>
