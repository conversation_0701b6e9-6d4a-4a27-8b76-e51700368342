<div class="notifications-container">
  <div class="header-section">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">{{ 'Notifications Management' | translate }}</h1>
    </div>
    <p class="text-gray-600 mb-8">{{ 'Manage notifications that will be displayed to users on your platform.' | translate }}</p>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
  </div>



  <!-- Popup Notifications Section -->
  <div class="section-container mb-8" *ngIf="!loading">
    <div class="section-header">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">{{ 'Popup Notifications' | translate }}</h2>
        <button
          (click)="addPopupNotification()"
          class="add-button flex items-center gap-2 bg-[var(--primary)] text-white px-4 py-2 rounded-lg hover:bg-[#2599a8] transition-colors">
          <fa-icon [icon]="['fas', 'plus']"></fa-icon>
          <span>{{ 'Add Popup' | translate }}</span>
        </button>
      </div>
      <p class="text-gray-600 mb-4">{{ 'These notifications appear as popups. Multiple popup notifications can be active at the same time.' | translate }}</p>
    </div>

    <div class="notification-list">
      <div *ngIf="popupNotifications.length === 0" class="empty-state">
        <p>{{ 'No popup notifications found. Click "Add Popup" to create one.' | translate }}</p>
      </div>

      <div *ngFor="let notification of popupNotifications" class="notification-item">
        <div class="notification-content">
          <div class="notification-icon" [ngClass]="getNotificationTypeClass(notification.type)">
            <fa-icon [icon]="['fas', getNotificationTypeIcon(notification.type)]"></fa-icon>
          </div>
          <div class="notification-details">
            <h3 class="notification-title">{{ notification.title || 'Notification' }}</h3>
            <div>
              <p class="notification-text" [class.collapsed]="!expandedContents[notification.id]" [innerHTML]="notification.content"></p>
              <span *ngIf="isContentLong(notification.content)" class="content-toggle" (click)="toggleContentExpansion(notification.id)">
                {{ expandedContents[notification.id] ? ('Show less' | translate) : ('Show more' | translate) }}
              </span>
            </div>
            <div class="notification-meta">
              <span class="meta-item" *ngIf="notification.offer_ending_type">
                <fa-icon [icon]="['fas', 'clock']" class="meta-icon"></fa-icon>
                {{ 'Expires' | translate }}: {{ formatDate(notification.offer_end_date) }}
              </span>
              <span *ngIf="notification.only_for_customers" class="meta-item">
                <fa-icon [icon]="['fas', 'user-check']" class="meta-icon"></fa-icon>
                {{ 'Only for customers' | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="notification-actions">
          <app-toggle-switch
            [isChecked]="notification.show"
            (toggled)="toggleNotificationStatus(notification)"
            [circleColor]="'#FFF'"
            [toggledBgColor]="'var(--primary)'">
          </app-toggle-switch>
          <button class="action-button edit" title="{{ 'Edit' | translate }}" (click)="editNotification(notification)">
            <fa-icon [icon]="['fas', 'edit']"></fa-icon>
          </button>
          <button class="action-button delete" title="{{ 'Delete' | translate }}" (click)="deleteNotification('popup', notification)">
            <fa-icon [icon]="['fas', 'trash']"></fa-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Fixed Notifications Section -->
  <div class="section-container" *ngIf="!loading">
    <div class="section-header">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">{{ 'Fixed Notifications' | translate }}</h2>
        <button
          (click)="addFixedNotification()"
          class="add-button flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
          <fa-icon [icon]="['fas', 'plus']"></fa-icon>
          <span>{{ 'Add Fixed' | translate }}</span>
        </button>
      </div>
      <p class="text-gray-600 mb-4">{{ 'These notifications appear in a fixed position. Only one fixed notification can be active at a time.' | translate }}</p>
    </div>

    <div class="notification-list">
      <div *ngIf="fixedNotifications.length === 0" class="empty-state">
        <p>{{ 'No fixed notifications found. Click "Add Fixed" to create one.' | translate }}</p>
      </div>

      <div *ngFor="let notification of fixedNotifications" class="notification-item">
        <div class="notification-content">
          <div class="notification-icon" [ngClass]="getNotificationTypeClass(notification.type)">
            <fa-icon [icon]="['fas', getNotificationTypeIcon(notification.type)]"></fa-icon>
          </div>
          <div class="notification-details">
            <h3 class="notification-title">{{ notification.title || 'Notification' }}</h3>
            <div>
              <p class="notification-text" [class.collapsed]="!expandedContents[notification.id]" [innerHTML]="notification.content"></p>
              <span *ngIf="isContentLong(notification.content)" class="content-toggle" (click)="toggleContentExpansion(notification.id)">
                {{ expandedContents[notification.id] ? ('Show less' | translate) : ('Show more' | translate) }}
              </span>
            </div>
            <div class="notification-meta">
              <span class="meta-item" *ngIf="notification.offer_ending_type">
                <fa-icon [icon]="['fas', 'clock']" class="meta-icon"></fa-icon>
                {{ 'Expires' | translate }}: {{ formatDate(notification.offer_end_date) }}
              </span>
              <span *ngIf="notification.only_for_customers" class="meta-item">
                <fa-icon [icon]="['fas', 'user-check']" class="meta-icon"></fa-icon>
                {{ 'Only for customers' | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="notification-actions">
          <app-toggle-switch
            [isChecked]="notification.show"
            (toggled)="toggleNotificationStatus(notification)"
            [circleColor]="'#FFF'"
            [toggledBgColor]="'var(--primary)'">
          </app-toggle-switch>
          <button class="action-button edit" title="{{ 'Edit' | translate }}" (click)="editNotification(notification)">
            <fa-icon [icon]="['fas', 'edit']"></fa-icon>
          </button>
          <button class="action-button delete" title="{{ 'Delete' | translate }}" (click)="deleteNotification('fixed', notification)">
            <fa-icon [icon]="['fas', 'trash']"></fa-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<app-delete-confirmation
  *ngIf="showDeleteConfirmation && notificationToDelete"
  [itemName]="notificationToDelete.title || 'this notification'"
  [isLoading]="isDeleting"
  (close)="closeDeleteConfirmation()"
  (confirm)="confirmDeleteNotification()">
</app-delete-confirmation>