import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Injectable, Inject, PLATFORM_ID} from '@angular/core';
import {Router} from '@angular/router';
import { JwtHelperService } from '@auth0/angular-jwt';
import { isPlatformBrowser } from '@angular/common';

import {BehaviorSubject, Observable} from 'rxjs';
import {finalize, first, tap} from 'rxjs/operators';
import { UserRes } from '../../model/response/user-res.model';
import { UserReq } from '../../model/request/user-req.model';
import { Role } from '../../constant/role';
import { ConfigService } from './config.service';

@Injectable({providedIn: 'root'})
export class AuthService {



  public readonly _authSubject$: BehaviorSubject<UserRes | undefined>;
  public readonly _auth$: Observable<UserRes | undefined>;
  private _loading$ = new BehaviorSubject<boolean>(false);

  constructor(
    private router: Router,
    private http: HttpClient,
    private jwtHelper: JwtHelperService,
    @Inject(PLATFORM_ID) private platformId: Object,
    private configService: ConfigService
  ) {
    // Initialize with undefined or get from localStorage if in browser
    let storedUser: UserRes | undefined = undefined;

    if (isPlatformBrowser(this.platformId)) {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          storedUser = JSON.parse(userStr);
        } catch (e) {
          console.error('Error parsing user from localStorage', e);
        }
      }
    }

    this._authSubject$ = new BehaviorSubject<UserRes | undefined>(storedUser);
    this._auth$ = this._authSubject$.asObservable();
  }

  public get authValue(): UserRes | undefined {
    return this._authSubject$.getValue();
  }
  public isAdmin(): boolean {
    const roles = this.getRoles();
    if (!roles) return false;
    return roles.every(r => [Role.PANEL].includes(r));
  }

  public getRoles(): string[] | undefined {
    const user = this.authValue;
    if (user) {
      const tokenPayload = this.jwtHelper.decodeToken(user.tokens.access_token);
      return tokenPayload.roles;
    }
    return undefined;
  }

  public isAuthenticated(): boolean {
    const user = this.authValue;
    return !!(user && user.tokens.access_token);
  }

  public isTokenExpired(): boolean {
    // @ts-ignore
    return this.isAuthenticated() && this.jwtHelper.isTokenExpired(this.authValue.tokens.access_token);
  }
  register(authReq: UserReq): Observable<UserRes> {
    this.loading$.next(true);
    return this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/users/register`,
        authReq
      )
      .pipe(
        first(),
        tap(user => this.updateUser(user)),
        finalize(() => this.loading$.next(false))
      );
  }

  updateUser(user: UserRes) {
    // store user details and jwt token in local storage to keep user logged in between page refreshes
    this._authSubject$.next(user);
    console.log('AuthService - updateUser called, user:', user?.id);

    if (isPlatformBrowser(this.platformId)) {
      try {
        localStorage.setItem('user', JSON.stringify(user));
        console.log('AuthService - User saved to localStorage');
      } catch (error) {
        console.error('AuthService - Error saving user to localStorage:', error);
      }
    }
  }

  refreshToken(refreshToken: string): Observable<UserRes> {
    this.loading$.next(true);
    const headers = new HttpHeaders({'x-refresh-token': refreshToken});

    return this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/access/refresh`, null, {headers}
      )
      .pipe(
        first(),
        tap(user => this.updateUser(user)),
        finalize(() => this.loading$.next(false)));
  }



  login(userName: string | null, password: string | null): Observable<UserRes> {
    console.log('AuthService - login method called');
    this.loading$.next(true);
    return this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/access/login`,
        {
          user_name: userName,
          password
        }
      )
      .pipe(
        first(),
        tap(user => {
          console.log('AuthService - Login successful, updating user');
          this.updateUser(user);
        }),
        finalize(() => {
          console.log('AuthService - Login finalized');
          this.loading$.next(false);
        })
      );
  }

  logout() {
    this.loading$.next(true);
    this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/access/logout`, null
      )
      .pipe(
        first(), finalize(() => {

          this.loading$.next(false);
          this._authSubject$.next(undefined);

          if (isPlatformBrowser(this.platformId)) {
            localStorage.removeItem('user');
          }

          this.redirectTo('/auth/login');
        })).subscribe();

  }

  redirectTo(uri: string) {
    this.router.navigate([uri]);
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get auth$(): Observable<UserRes | undefined> {
    return this._auth$;
  }
}
