package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class TenantLanguageSettingsReq {

    @NotBlank(message = "Default language is required")
  //  @Pattern(regexp = "^(vi|en)$", message = "Default language must be 'vi' or 'en'")
    private String defaultLanguage;

    @NotEmpty(message = "Available languages list cannot be empty")
    private List<String> availableLanguages;
}
