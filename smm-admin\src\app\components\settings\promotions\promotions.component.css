.promotions-container {
  @apply max-w-full mx-auto md:p-6 p-2;
}

.promotions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.promotions-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.add-promotion-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-promotion-button:hover {
  background-color: #4338ca;
}

/* Loading and Empty States */
.loading-container, .empty-container {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Desktop Table Styles */
.promotions-table-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1rem;
}

.promotions-table {
  width: 100%;
  border-collapse: collapse;
}

.promotions-table th {
  background-color: #f9fafb;
  color: #6b7280;
  font-weight: 500;
  text-align: left;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
}

.promotions-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.875rem;
}

.promotions-table tr:last-child td {
  border-bottom: none;
}

/* Mobile Card Styles */
.promotion-cards-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.promotion-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.promotion-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.promotion-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.promotion-card-content {
  padding: 0.75rem 1rem;
}

.promotion-card-info {
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.info-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.info-value {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.promotion-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.mobile-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.upcoming {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge.expired {
  background-color: #fee2e2;
  color: #b91c1c;
}

.status-badge.inactive {
  background-color: #f3f4f6;
  color: #6b7280;
}

.actions-cell {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-button {
  background-color: #dbeafe;
  color: #1e40af;
}

.edit-button:hover {
  background-color: #bfdbfe;
}

.delete-button {
  background-color: #fee2e2;
  color: #b91c1c;
}

.delete-button:hover {
  background-color: #fecaca;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.pagination {
  display: flex;
  gap: 0.25rem;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.pagination-button.active {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .promotions-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .add-promotion-button {
    width: 100%;
    justify-content: center;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
