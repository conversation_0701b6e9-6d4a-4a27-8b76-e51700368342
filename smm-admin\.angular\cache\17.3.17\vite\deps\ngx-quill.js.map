{"version": 3, "sources": ["../../../../../node_modules/ngx-quill/fesm2022/ngx-quill-config.mjs", "../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../node_modules/ngx-quill/fesm2022/ngx-quill.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, NgModule, makeEnvironmentProviders } from '@angular/core';\nconst defaultModules = {\n  toolbar: [['bold', 'italic', 'underline', 'strike'],\n  // toggled buttons\n  ['blockquote', 'code-block'], [{\n    header: 1\n  }, {\n    header: 2\n  }],\n  // custom button values\n  [{\n    list: 'ordered'\n  }, {\n    list: 'bullet'\n  }], [{\n    script: 'sub'\n  }, {\n    script: 'super'\n  }],\n  // superscript/subscript\n  [{\n    indent: '-1'\n  }, {\n    indent: '+1'\n  }],\n  // outdent/indent\n  [{\n    direction: 'rtl'\n  }],\n  // text direction\n  [{\n    size: ['small', false, 'large', 'huge']\n  }],\n  // custom dropdown\n  [{\n    header: [1, 2, 3, 4, 5, 6, false]\n  }], [{\n    color: []\n  }, {\n    background: []\n  }],\n  // dropdown with defaults from theme\n  [{\n    font: []\n  }], [{\n    align: []\n  }], ['clean'],\n  // remove formatting button\n  ['link', 'image', 'video'],\n  // link and image, video\n  ['table']]\n};\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst QUILL_CONFIG_TOKEN = new InjectionToken('config', {\n  providedIn: 'root',\n  factory: () => ({\n    modules: defaultModules\n  })\n});\n\n/**\n * This `NgModule` provides a global Quill config on the root level, e.g., in `AppModule`.\n * But this eliminates the need to import the entire `ngx-quill` library into the main bundle.\n * The `quill-editor` itself may be rendered in any lazy-loaded module, but importing `QuillModule`\n * into the `AppModule` will bundle the `ngx-quill` into the vendor.\n */\nclass QuillConfigModule {\n  static forRoot(config) {\n    return {\n      ngModule: QuillConfigModule,\n      providers: [{\n        provide: QUILL_CONFIG_TOKEN,\n        useValue: config\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function QuillConfigModule_Factory(t) {\n      return new (t || QuillConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: QuillConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillConfigModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Provides Quill configuration at the root level:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideQuillConfig(...)]\n * });\n * ```\n */\nconst provideQuillConfig = config => makeEnvironmentProviders([{\n  provide: QUILL_CONFIG_TOKEN,\n  useValue: config\n}]);\n\n/*\n * Public API Surface of ngx-quill/config\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QUILL_CONFIG_TOKEN, QuillConfigModule, defaultModules, provideQuillConfig };\n", "/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable(observer => {\n        const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    constructor(source) {\n        this.source = source;\n        this.destroyed = false;\n        this.destroyRef = inject(DestroyRef);\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: value => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n    const destroyRef = ɵgetOutputDestroyRef(ref);\n    return new Observable(observer => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe(v => observer.next(v));\n        return () => subscription.unsubscribe();\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n    !options?.injector && assertInInjectionContext(toObservable);\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n    const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue });\n    }\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: value => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: error => {\n            if (options?.rejectErrors) {\n                // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n                // the error to end up as an uncaught exception.\n                throw error;\n            }\n            state.set({ kind: 2 /* StateKind.Error */, error });\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n                throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };\n\n", "import { defaultModules, QUILL_CONFIG_TOKEN } from 'ngx-quill/config';\nexport * from 'ngx-quill/config';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, input, EventEmitter, signal, inject, ElementRef, ChangeDetectorRef, PLATFORM_ID, Renderer2, NgZone, DestroyRef, SecurityContext, Directive, Output, forwardRef, Component, ViewEncapsulation, NgModule } from '@angular/core';\nimport { DOCUMENT, isPlatformServer, NgClass } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { Observable, defer, isObservable, firstValueFrom, Subscription, fromEvent } from 'rxjs';\nimport { shareReplay, mergeMap, debounceTime } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nconst _c0 = [[[\"\", \"above-quill-editor-toolbar\", \"\"]], [[\"\", \"quill-editor-toolbar\", \"\"]], [[\"\", \"below-quill-editor-toolbar\", \"\"]]];\nconst _c1 = [\"[above-quill-editor-toolbar]\", \"[quill-editor-toolbar]\", \"[below-quill-editor-toolbar]\"];\nfunction QuillEditorComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 0);\n  }\n}\nfunction QuillEditorComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 0);\n  }\n}\nconst getFormat = (format, configFormat) => {\n  const passedFormat = format || configFormat;\n  return passedFormat || 'html';\n};\nconst raf$ = () => {\n  return new Observable(subscriber => {\n    const rafId = requestAnimationFrame(() => {\n      subscriber.next();\n      subscriber.complete();\n    });\n    return () => cancelAnimationFrame(rafId);\n  });\n};\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nclass QuillService {\n  constructor(injector, config) {\n    this.config = config;\n    this.quill$ = defer(async () => {\n      if (!this.Quill) {\n        // Quill adds events listeners on import https://github.com/quilljs/quill/blob/develop/core/emitter.js#L8\n        // We'd want to use the unpatched `addEventListener` method to have all event callbacks to be run outside of zone.\n        // We don't know yet if the `zone.js` is used or not, just save the value to restore it back further.\n        const maybePatchedAddEventListener = this.document.addEventListener;\n        // There're 2 types of Angular applications:\n        // 1) zone-full (by default)\n        // 2) zone-less\n        // The developer can avoid importing the `zone.js` package and tells Angular that he/she is responsible for running\n        // the change detection by himself. This is done by \"nooping\" the zone through `CompilerOptions` when bootstrapping\n        // the root module. We fallback to `document.addEventListener` if `__zone_symbol__addEventListener` is not defined,\n        // this means the `zone.js` is not imported.\n        // The `__zone_symbol__addEventListener` is basically a native DOM API, which is not patched by zone.js, thus not even going\n        // through the `zone.js` task lifecycle. You can also access the native DOM API as follows `target[Zone.__symbol__('methodName')]`.\n        this.document.addEventListener =\n        // eslint-disable-next-line @typescript-eslint/dot-notation\n        this.document['__zone_symbol__addEventListener'] || this.document.addEventListener;\n        const quillImport = await import('quill');\n        this.document.addEventListener = maybePatchedAddEventListener;\n        this.Quill =\n        // seems like esmodules have nested \"default\"\n        quillImport.default?.default ?? quillImport.default ?? quillImport;\n      }\n      // Only register custom options and modules once\n      this.config.customOptions?.forEach(customOption => {\n        const newCustomOption = this.Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        this.Quill.register(newCustomOption, true, this.config.suppressGlobalRegisterWarning);\n      });\n      return await this.registerCustomModules(this.Quill, this.config.customModules, this.config.suppressGlobalRegisterWarning);\n    }).pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    this.document = injector.get(DOCUMENT);\n    if (!this.config) {\n      this.config = {\n        modules: defaultModules\n      };\n    }\n  }\n  getQuill() {\n    return this.quill$;\n  }\n  /**\n   * Marked as internal so it won't be available for `ngx-quill` consumers, this is only\n   * internal method to be used within the library.\n   *\n   * @internal\n   */\n  async registerCustomModules(Quill, customModules, suppressGlobalRegisterWarning) {\n    if (Array.isArray(customModules)) {\n      // eslint-disable-next-line prefer-const\n      for (let {\n        implementation,\n        path\n      } of customModules) {\n        // The `implementation` might be an observable that resolves the actual implementation,\n        // e.g. if it should be lazy loaded.\n        if (isObservable(implementation)) {\n          implementation = await firstValueFrom(implementation);\n        }\n        Quill.register(path, implementation, suppressGlobalRegisterWarning);\n      }\n    }\n    // Return `Quill` constructor so we'll be able to re-use its return value except of using\n    // `map` operators, etc.\n    return Quill;\n  }\n  static {\n    this.ɵfac = function QuillService_Factory(t) {\n      return new (t || QuillService)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(QUILL_CONFIG_TOKEN, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: QuillService,\n      factory: QuillService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [QUILL_CONFIG_TOKEN]\n    }]\n  }], null);\n})();\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass QuillEditorBase {\n  constructor() {\n    this.format = input(undefined);\n    this.theme = input(undefined);\n    this.modules = input(undefined);\n    this.debug = input(false);\n    this.readOnly = input(false);\n    this.placeholder = input(undefined);\n    this.maxLength = input(undefined);\n    this.minLength = input(undefined);\n    this.required = input(false);\n    this.formats = input(undefined);\n    this.customToolbarPosition = input('top');\n    this.sanitize = input(false);\n    this.beforeRender = input(undefined);\n    this.styles = input(null);\n    this.registry = input(undefined);\n    this.bounds = input(undefined);\n    this.customOptions = input([]);\n    this.customModules = input([]);\n    this.trackChanges = input(undefined);\n    this.classes = input(undefined);\n    this.trimOnValidation = input(false);\n    this.linkPlaceholder = input(undefined);\n    this.compareValues = input(false);\n    this.filterNull = input(false);\n    this.debounceTime = input(undefined);\n    /*\n    https://github.com/KillerCodeMonkey/ngx-quill/issues/1257 - fix null value set\n           provide default empty value\n    by default null\n           e.g. defaultEmptyValue=\"\" - empty string\n           <quill-editor\n      defaultEmptyValue=\"\"\n      formControlName=\"message\"\n    ></quill-editor>\n    */\n    this.defaultEmptyValue = input(null);\n    this.onEditorCreated = new EventEmitter();\n    this.onEditorChanged = new EventEmitter();\n    this.onContentChanged = new EventEmitter();\n    this.onSelectionChanged = new EventEmitter();\n    this.onFocus = new EventEmitter();\n    this.onBlur = new EventEmitter();\n    this.onNativeFocus = new EventEmitter();\n    this.onNativeBlur = new EventEmitter();\n    this.disabled = false; // used to store initial value before ViewInit\n    this.toolbarPosition = signal('top');\n    this.subscription = null;\n    this.quillSubscription = null;\n    this.elementRef = inject(ElementRef);\n    this.document = inject(DOCUMENT);\n    this.cd = inject(ChangeDetectorRef);\n    this.domSanitizer = inject(DomSanitizer);\n    this.platformId = inject(PLATFORM_ID);\n    this.renderer = inject(Renderer2);\n    this.zone = inject(NgZone);\n    this.service = inject(QuillService);\n    this.destroyRef = inject(DestroyRef);\n    this.valueGetter = input(quillEditor => {\n      let html = quillEditor.getSemanticHTML();\n      if (this.isEmptyValue(html)) {\n        html = this.defaultEmptyValue();\n      }\n      let modelValue = html;\n      const format = getFormat(this.format(), this.service.config.format);\n      if (format === 'text') {\n        modelValue = quillEditor.getText();\n      } else if (format === 'object') {\n        modelValue = quillEditor.getContents();\n      } else if (format === 'json') {\n        try {\n          modelValue = JSON.stringify(quillEditor.getContents());\n        } catch (e) {\n          modelValue = quillEditor.getText();\n        }\n      }\n      return modelValue;\n    });\n    this.valueSetter = input((quillEditor, value) => {\n      const format = getFormat(this.format(), this.service.config.format);\n      if (format === 'html') {\n        const sanitize = [true, false].includes(this.sanitize()) ? this.sanitize() : this.service.config.sanitize || false;\n        if (sanitize) {\n          value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n        }\n        return quillEditor.clipboard.convert({\n          html: value\n        });\n      } else if (format === 'json') {\n        try {\n          return JSON.parse(value);\n        } catch (e) {\n          return [{\n            insert: value\n          }];\n        }\n      }\n      return value;\n    });\n    this.selectionChangeHandler = (range, oldRange, source) => {\n      const trackChanges = this.trackChanges() || this.service.config.trackChanges;\n      const shouldTriggerOnModelTouched = !range && !!this.onModelTouched && (source === 'user' || trackChanges && trackChanges === 'all');\n      // only emit changes when there's any listener\n      if (!this.onBlur.observed && !this.onFocus.observed && !this.onSelectionChanged.observed && !shouldTriggerOnModelTouched) {\n        return;\n      }\n      this.zone.run(() => {\n        if (range === null) {\n          this.onBlur.emit({\n            editor: this.quillEditor,\n            source\n          });\n        } else if (oldRange === null) {\n          this.onFocus.emit({\n            editor: this.quillEditor,\n            source\n          });\n        }\n        this.onSelectionChanged.emit({\n          editor: this.quillEditor,\n          oldRange,\n          range,\n          source\n        });\n        if (shouldTriggerOnModelTouched) {\n          this.onModelTouched();\n        }\n        this.cd.markForCheck();\n      });\n    };\n    this.textChangeHandler = (delta, oldDelta, source) => {\n      // only emit changes emitted by user interactions\n      const text = this.quillEditor.getText();\n      const content = this.quillEditor.getContents();\n      let html = this.quillEditor.getSemanticHTML();\n      if (this.isEmptyValue(html)) {\n        html = this.defaultEmptyValue();\n      }\n      const trackChanges = this.trackChanges() || this.service.config.trackChanges;\n      const shouldTriggerOnModelChange = (source === 'user' || trackChanges && trackChanges === 'all') && !!this.onModelChange;\n      // only emit changes when there's any listener\n      if (!this.onContentChanged.observed && !shouldTriggerOnModelChange) {\n        return;\n      }\n      this.zone.run(() => {\n        if (shouldTriggerOnModelChange) {\n          const valueGetter = this.valueGetter();\n          this.onModelChange(valueGetter(this.quillEditor));\n        }\n        this.onContentChanged.emit({\n          content,\n          delta,\n          editor: this.quillEditor,\n          html,\n          oldDelta,\n          source,\n          text\n        });\n        this.cd.markForCheck();\n      });\n    };\n    // eslint-disable-next-line max-len\n    this.editorChangeHandler = (event, current, old, source) => {\n      // only emit changes when there's any listener\n      if (!this.onEditorChanged.observed) {\n        return;\n      }\n      // only emit changes emitted by user interactions\n      if (event === 'text-change') {\n        const text = this.quillEditor.getText();\n        const content = this.quillEditor.getContents();\n        let html = this.quillEditor.getSemanticHTML();\n        if (this.isEmptyValue(html)) {\n          html = this.defaultEmptyValue();\n        }\n        this.zone.run(() => {\n          this.onEditorChanged.emit({\n            content,\n            delta: current,\n            editor: this.quillEditor,\n            event,\n            html,\n            oldDelta: old,\n            source,\n            text\n          });\n          this.cd.markForCheck();\n        });\n      } else {\n        this.zone.run(() => {\n          this.onEditorChanged.emit({\n            editor: this.quillEditor,\n            event,\n            oldRange: old,\n            range: current,\n            source\n          });\n          this.cd.markForCheck();\n        });\n      }\n    };\n  }\n  static normalizeClassNames(classes) {\n    const classList = classes.trim().split(' ');\n    return classList.reduce((prev, cur) => {\n      const trimmed = cur.trim();\n      if (trimmed) {\n        prev.push(trimmed);\n      }\n      return prev;\n    }, []);\n  }\n  ngOnInit() {\n    this.toolbarPosition.set(this.customToolbarPosition());\n  }\n  ngAfterViewInit() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    // The `quill-editor` component might be destroyed before the `quill` chunk is loaded and its code is executed\n    // this will lead to runtime exceptions, since the code will be executed on DOM nodes that don't exist within the tree.\n    this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => {\n      const promises = [this.service.registerCustomModules(Quill, this.customModules())];\n      const beforeRender = this.beforeRender() ?? this.service.config.beforeRender;\n      if (beforeRender) {\n        promises.push(beforeRender());\n      }\n      return Promise.all(promises).then(() => Quill);\n    })).subscribe(Quill => {\n      this.editorElem = this.elementRef.nativeElement.querySelector('[quill-editor-element]');\n      const toolbarElem = this.elementRef.nativeElement.querySelector('[quill-editor-toolbar]');\n      const modules = Object.assign({}, this.modules() || this.service.config.modules);\n      if (toolbarElem) {\n        modules.toolbar = toolbarElem;\n      } else if (modules.toolbar === undefined) {\n        modules.toolbar = defaultModules.toolbar;\n      }\n      let placeholder = this.placeholder() !== undefined ? this.placeholder() : this.service.config.placeholder;\n      if (placeholder === undefined) {\n        placeholder = 'Insert text here ...';\n      }\n      const styles = this.styles();\n      if (styles) {\n        Object.keys(styles).forEach(key => {\n          this.renderer.setStyle(this.editorElem, key, styles[key]);\n        });\n      }\n      if (this.classes()) {\n        this.addClasses(this.classes());\n      }\n      this.customOptions().forEach(customOption => {\n        const newCustomOption = Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        Quill.register(newCustomOption, true);\n      });\n      let bounds = this.bounds() && this.bounds() === 'self' ? this.editorElem : this.bounds();\n      if (!bounds) {\n        bounds = this.service.config.bounds ? this.service.config.bounds : this.document.body;\n      }\n      let debug = this.debug();\n      if (!debug && debug !== false && this.service.config.debug) {\n        debug = this.service.config.debug;\n      }\n      let readOnly = this.readOnly();\n      if (!readOnly && this.readOnly() !== false) {\n        readOnly = this.service.config.readOnly !== undefined ? this.service.config.readOnly : false;\n      }\n      let defaultEmptyValue = this.defaultEmptyValue;\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.service.config.hasOwnProperty('defaultEmptyValue')) {\n        defaultEmptyValue = this.service.config.defaultEmptyValue;\n      }\n      let formats = this.formats();\n      if (!formats && formats === undefined) {\n        formats = this.service.config.formats ? [...this.service.config.formats] : this.service.config.formats === null ? null : undefined;\n      }\n      this.zone.runOutsideAngular(() => {\n        this.quillEditor = new Quill(this.editorElem, {\n          bounds,\n          debug: debug,\n          formats: formats,\n          modules,\n          placeholder,\n          readOnly,\n          defaultEmptyValue,\n          registry: this.registry(),\n          theme: this.theme() || (this.service.config.theme ? this.service.config.theme : 'snow')\n        });\n        if (this.onNativeBlur.observed) {\n          // https://github.com/quilljs/quill/issues/2186#issuecomment-533401328\n          this.quillEditor.scroll.domNode.addEventListener('blur', () => this.onNativeBlur.next({\n            editor: this.quillEditor,\n            source: 'dom'\n          }));\n          // https://github.com/quilljs/quill/issues/2186#issuecomment-803257538\n          const toolbar = this.quillEditor.getModule('toolbar');\n          toolbar.container?.addEventListener('mousedown', e => e.preventDefault());\n        }\n        if (this.onNativeFocus.observed) {\n          this.quillEditor.scroll.domNode.addEventListener('focus', () => this.onNativeFocus.next({\n            editor: this.quillEditor,\n            source: 'dom'\n          }));\n        }\n        // Set optional link placeholder, Quill has no native API for it so using workaround\n        if (this.linkPlaceholder()) {\n          const tooltip = this.quillEditor?.theme?.tooltip;\n          const input = tooltip?.root?.querySelector('input[data-link]');\n          if (input?.dataset) {\n            input.dataset.link = this.linkPlaceholder();\n          }\n        }\n      });\n      if (this.content) {\n        const format = getFormat(this.format(), this.service.config.format);\n        if (format === 'text') {\n          this.quillEditor.setText(this.content, 'silent');\n        } else {\n          const valueSetter = this.valueSetter();\n          const newValue = valueSetter(this.quillEditor, this.content);\n          this.quillEditor.setContents(newValue, 'silent');\n        }\n        const history = this.quillEditor.getModule('history');\n        history.clear();\n      }\n      // initialize disabled status based on this.disabled as default value\n      this.setDisabledState();\n      this.addQuillEventListeners();\n      // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n      // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n      if (!this.onEditorCreated.observed && !this.onValidatorChanged) {\n        return;\n      }\n      // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n      // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n      // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n      raf$().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n        if (this.onValidatorChanged) {\n          this.onValidatorChanged();\n        }\n        this.onEditorCreated.emit(this.quillEditor);\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.dispose();\n    this.quillSubscription?.unsubscribe();\n    this.quillSubscription = null;\n  }\n  ngOnChanges(changes) {\n    if (!this.quillEditor) {\n      return;\n    }\n    /* eslint-disable @typescript-eslint/dot-notation */\n    if (changes.readOnly) {\n      this.quillEditor.enable(!changes.readOnly.currentValue);\n    }\n    if (changes.placeholder) {\n      this.quillEditor.root.dataset.placeholder = changes.placeholder.currentValue;\n    }\n    if (changes.defaultEmptyValue) {\n      this.quillEditor.root.dataset.defaultEmptyValue = changes.defaultEmptyValue.currentValue;\n    }\n    if (changes.styles) {\n      const currentStyling = changes.styles.currentValue;\n      const previousStyling = changes.styles.previousValue;\n      if (previousStyling) {\n        Object.keys(previousStyling).forEach(key => {\n          this.renderer.removeStyle(this.editorElem, key);\n        });\n      }\n      if (currentStyling) {\n        Object.keys(currentStyling).forEach(key => {\n          this.renderer.setStyle(this.editorElem, key, this.styles()[key]);\n        });\n      }\n    }\n    if (changes.classes) {\n      const currentClasses = changes.classes.currentValue;\n      const previousClasses = changes.classes.previousValue;\n      if (previousClasses) {\n        this.removeClasses(previousClasses);\n      }\n      if (currentClasses) {\n        this.addClasses(currentClasses);\n      }\n    }\n    // We'd want to re-apply event listeners if the `debounceTime` binding changes to apply the\n    // `debounceTime` operator or vice-versa remove it.\n    if (changes.debounceTime) {\n      this.addQuillEventListeners();\n    }\n    /* eslint-enable @typescript-eslint/dot-notation */\n  }\n  addClasses(classList) {\n    QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n      this.renderer.addClass(this.editorElem, c);\n    });\n  }\n  removeClasses(classList) {\n    QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n      this.renderer.removeClass(this.editorElem, c);\n    });\n  }\n  writeValue(currentValue) {\n    // optional fix for https://github.com/angular/angular/issues/14988\n    if (this.filterNull() && currentValue === null) {\n      return;\n    }\n    this.content = currentValue;\n    if (!this.quillEditor) {\n      return;\n    }\n    const format = getFormat(this.format(), this.service.config.format);\n    const valueSetter = this.valueSetter();\n    const newValue = valueSetter(this.quillEditor, currentValue);\n    if (this.compareValues()) {\n      const currentEditorValue = this.quillEditor.getContents();\n      if (JSON.stringify(currentEditorValue) === JSON.stringify(newValue)) {\n        return;\n      }\n    }\n    if (currentValue) {\n      if (format === 'text') {\n        this.quillEditor.setText(currentValue);\n      } else {\n        this.quillEditor.setContents(newValue);\n      }\n      return;\n    }\n    this.quillEditor.setText('');\n  }\n  setDisabledState(isDisabled = this.disabled) {\n    // store initial value to set appropriate disabled status after ViewInit\n    this.disabled = isDisabled;\n    if (this.quillEditor) {\n      if (isDisabled) {\n        this.quillEditor.disable();\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'disabled', 'disabled');\n      } else {\n        if (!this.readOnly()) {\n          this.quillEditor.enable();\n        }\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'disabled');\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  registerOnValidatorChange(fn) {\n    this.onValidatorChanged = fn;\n  }\n  validate() {\n    if (!this.quillEditor) {\n      return null;\n    }\n    const err = {};\n    let valid = true;\n    const text = this.quillEditor.getText();\n    // trim text if wanted + handle special case that an empty editor contains a new line\n    const textLength = this.trimOnValidation() ? text.trim().length : text.length === 1 && text.trim().length === 0 ? 0 : text.length - 1;\n    const deltaOperations = this.quillEditor.getContents().ops;\n    const onlyEmptyOperation = !!deltaOperations && deltaOperations.length === 1 && ['\\n', ''].includes(deltaOperations[0].insert?.toString());\n    if (this.minLength() && textLength && textLength < this.minLength()) {\n      err.minLengthError = {\n        given: textLength,\n        minLength: this.minLength()\n      };\n      valid = false;\n    }\n    if (this.maxLength() && textLength > this.maxLength()) {\n      err.maxLengthError = {\n        given: textLength,\n        maxLength: this.maxLength()\n      };\n      valid = false;\n    }\n    if (this.required() && !textLength && onlyEmptyOperation) {\n      err.requiredError = {\n        empty: true\n      };\n      valid = false;\n    }\n    return valid ? null : err;\n  }\n  addQuillEventListeners() {\n    this.dispose();\n    // We have to enter the `<root>` zone when adding event listeners, so `debounceTime` will spawn the\n    // `AsyncAction` there w/o triggering change detections. We still re-enter the Angular's zone through\n    // `zone.run` when we emit an event to the parent component.\n    this.zone.runOutsideAngular(() => {\n      this.subscription = new Subscription();\n      this.subscription.add(\n      // mark model as touched if editor lost focus\n      fromEvent(this.quillEditor, 'selection-change').subscribe(([range, oldRange, source]) => {\n        this.selectionChangeHandler(range, oldRange, source);\n      }));\n      // The `fromEvent` supports passing JQuery-style event targets, the editor has `on` and `off` methods which\n      // will be invoked upon subscription and teardown.\n      let textChange$ = fromEvent(this.quillEditor, 'text-change');\n      let editorChange$ = fromEvent(this.quillEditor, 'editor-change');\n      if (typeof this.debounceTime() === 'number') {\n        textChange$ = textChange$.pipe(debounceTime(this.debounceTime()));\n        editorChange$ = editorChange$.pipe(debounceTime(this.debounceTime()));\n      }\n      this.subscription.add(\n      // update model if text changes\n      textChange$.subscribe(([delta, oldDelta, source]) => {\n        this.textChangeHandler(delta, oldDelta, source);\n      }));\n      this.subscription.add(\n      // triggered if selection or text changed\n      editorChange$.subscribe(([event, current, old, source]) => {\n        this.editorChangeHandler(event, current, old, source);\n      }));\n    });\n  }\n  dispose() {\n    if (this.subscription !== null) {\n      this.subscription.unsubscribe();\n      this.subscription = null;\n    }\n  }\n  isEmptyValue(html) {\n    return html === '<p></p>' || html === '<div></div>' || html === '<p><br></p>' || html === '<div><br></div>';\n  }\n  static {\n    this.ɵfac = function QuillEditorBase_Factory(t) {\n      return new (t || QuillEditorBase)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: QuillEditorBase,\n      inputs: {\n        format: [i0.ɵɵInputFlags.SignalBased, \"format\"],\n        theme: [i0.ɵɵInputFlags.SignalBased, \"theme\"],\n        modules: [i0.ɵɵInputFlags.SignalBased, \"modules\"],\n        debug: [i0.ɵɵInputFlags.SignalBased, \"debug\"],\n        readOnly: [i0.ɵɵInputFlags.SignalBased, \"readOnly\"],\n        placeholder: [i0.ɵɵInputFlags.SignalBased, \"placeholder\"],\n        maxLength: [i0.ɵɵInputFlags.SignalBased, \"maxLength\"],\n        minLength: [i0.ɵɵInputFlags.SignalBased, \"minLength\"],\n        required: [i0.ɵɵInputFlags.SignalBased, \"required\"],\n        formats: [i0.ɵɵInputFlags.SignalBased, \"formats\"],\n        customToolbarPosition: [i0.ɵɵInputFlags.SignalBased, \"customToolbarPosition\"],\n        sanitize: [i0.ɵɵInputFlags.SignalBased, \"sanitize\"],\n        beforeRender: [i0.ɵɵInputFlags.SignalBased, \"beforeRender\"],\n        styles: [i0.ɵɵInputFlags.SignalBased, \"styles\"],\n        registry: [i0.ɵɵInputFlags.SignalBased, \"registry\"],\n        bounds: [i0.ɵɵInputFlags.SignalBased, \"bounds\"],\n        customOptions: [i0.ɵɵInputFlags.SignalBased, \"customOptions\"],\n        customModules: [i0.ɵɵInputFlags.SignalBased, \"customModules\"],\n        trackChanges: [i0.ɵɵInputFlags.SignalBased, \"trackChanges\"],\n        classes: [i0.ɵɵInputFlags.SignalBased, \"classes\"],\n        trimOnValidation: [i0.ɵɵInputFlags.SignalBased, \"trimOnValidation\"],\n        linkPlaceholder: [i0.ɵɵInputFlags.SignalBased, \"linkPlaceholder\"],\n        compareValues: [i0.ɵɵInputFlags.SignalBased, \"compareValues\"],\n        filterNull: [i0.ɵɵInputFlags.SignalBased, \"filterNull\"],\n        debounceTime: [i0.ɵɵInputFlags.SignalBased, \"debounceTime\"],\n        defaultEmptyValue: [i0.ɵɵInputFlags.SignalBased, \"defaultEmptyValue\"],\n        valueGetter: [i0.ɵɵInputFlags.SignalBased, \"valueGetter\"],\n        valueSetter: [i0.ɵɵInputFlags.SignalBased, \"valueSetter\"]\n      },\n      outputs: {\n        onEditorCreated: \"onEditorCreated\",\n        onEditorChanged: \"onEditorChanged\",\n        onContentChanged: \"onContentChanged\",\n        onSelectionChanged: \"onSelectionChanged\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onNativeFocus: \"onNativeFocus\",\n        onNativeBlur: \"onNativeBlur\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillEditorBase, [{\n    type: Directive\n  }], null, {\n    onEditorCreated: [{\n      type: Output\n    }],\n    onEditorChanged: [{\n      type: Output\n    }],\n    onContentChanged: [{\n      type: Output\n    }],\n    onSelectionChanged: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onNativeFocus: [{\n      type: Output\n    }],\n    onNativeBlur: [{\n      type: Output\n    }]\n  });\n})();\nclass QuillEditorComponent extends QuillEditorBase {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵQuillEditorComponent_BaseFactory;\n      return function QuillEditorComponent_Factory(t) {\n        return (ɵQuillEditorComponent_BaseFactory || (ɵQuillEditorComponent_BaseFactory = i0.ɵɵgetInheritedFactory(QuillEditorComponent)))(t || QuillEditorComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: QuillEditorComponent,\n      selectors: [[\"quill-editor\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }, {\n        multi: true,\n        provide: NG_VALIDATORS,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 2,\n      consts: [[\"quill-editor-element\", \"\"]],\n      template: function QuillEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵtemplate(0, QuillEditorComponent_Conditional_0_Template, 1, 0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵtemplate(4, QuillEditorComponent_Conditional_4_Template, 1, 0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.toolbarPosition() !== \"top\" ? 0 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(4, ctx.toolbarPosition() === \"top\" ? 4 : -1);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{display:inline-block}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillEditorComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.Emulated,\n      providers: [{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }, {\n        multi: true,\n        provide: NG_VALIDATORS,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }],\n      selector: 'quill-editor',\n      template: `\n    @if (toolbarPosition() !== 'top') {\n        <div quill-editor-element></div>\n    }\n\n    <ng-content select=\"[above-quill-editor-toolbar]\"></ng-content>\n    <ng-content select=\"[quill-editor-toolbar]\"></ng-content>\n    <ng-content select=\"[below-quill-editor-toolbar]\"></ng-content>\n\n    @if (toolbarPosition() === 'top') {\n        <div quill-editor-element></div>\n    }\n  `,\n      standalone: true,\n      styles: [\":host{display:inline-block}\\n\"]\n    }]\n  }], null, null);\n})();\nclass QuillViewHTMLComponent {\n  constructor(sanitizer, service) {\n    this.sanitizer = sanitizer;\n    this.service = service;\n    this.content = input('');\n    this.theme = input(undefined);\n    this.sanitize = input(false);\n    this.innerHTML = signal('');\n    this.themeClass = signal('ql-snow');\n  }\n  ngOnChanges(changes) {\n    if (changes.theme) {\n      const theme = changes.theme.currentValue || (this.service.config.theme ? this.service.config.theme : 'snow');\n      this.themeClass.set(`ql-${theme} ngx-quill-view-html`);\n    } else if (!this.theme()) {\n      const theme = this.service.config.theme ? this.service.config.theme : 'snow';\n      this.themeClass.set(`ql-${theme} ngx-quill-view-html`);\n    }\n    if (changes.content) {\n      const content = changes.content.currentValue;\n      const sanitize = [true, false].includes(this.sanitize()) ? this.sanitize() : this.service.config.sanitize || false;\n      const innerHTML = sanitize ? content : this.sanitizer.bypassSecurityTrustHtml(content);\n      this.innerHTML.set(innerHTML);\n    }\n  }\n  static {\n    this.ɵfac = function QuillViewHTMLComponent_Factory(t) {\n      return new (t || QuillViewHTMLComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(QuillService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: QuillViewHTMLComponent,\n      selectors: [[\"quill-view-html\"]],\n      inputs: {\n        content: [i0.ɵɵInputFlags.SignalBased, \"content\"],\n        theme: [i0.ɵɵInputFlags.SignalBased, \"theme\"],\n        sanitize: [i0.ɵɵInputFlags.SignalBased, \"sanitize\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"ql-container\", 3, \"ngClass\"], [1, \"ql-editor\", 3, \"innerHTML\"]],\n      template: function QuillViewHTMLComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.themeClass());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"innerHTML\", ctx.innerHTML(), i0.ɵɵsanitizeHtml);\n        }\n      },\n      dependencies: [NgClass],\n      styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillViewHTMLComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'quill-view-html',\n      template: `\n  <div class=\"ql-container\" [ngClass]=\"themeClass()\">\n    <div class=\"ql-editor\" [innerHTML]=\"innerHTML()\">\n    </div>\n  </div>\n`,\n      standalone: true,\n      imports: [NgClass],\n      styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DomSanitizer\n  }, {\n    type: QuillService\n  }], null);\n})();\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nclass QuillViewComponent {\n  constructor(elementRef, renderer, zone, service, domSanitizer, platformId) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.zone = zone;\n    this.service = service;\n    this.domSanitizer = domSanitizer;\n    this.platformId = platformId;\n    this.format = input(undefined);\n    this.theme = input(undefined);\n    this.modules = input(undefined);\n    this.debug = input(false);\n    this.formats = input(undefined);\n    this.sanitize = input(false);\n    this.beforeRender = input(undefined);\n    this.strict = input(true);\n    this.content = input();\n    this.customModules = input([]);\n    this.customOptions = input([]);\n    this.onEditorCreated = new EventEmitter();\n    this.quillSubscription = null;\n    this.destroyRef = inject(DestroyRef);\n    this.valueSetter = (quillEditor, value) => {\n      const format = getFormat(this.format(), this.service.config.format);\n      let content = value;\n      if (format === 'text') {\n        quillEditor.setText(content);\n      } else {\n        if (format === 'html') {\n          const sanitize = [true, false].includes(this.sanitize()) ? this.sanitize() : this.service.config.sanitize || false;\n          if (sanitize) {\n            value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n          }\n          content = quillEditor.clipboard.convert({\n            html: value\n          });\n        } else if (format === 'json') {\n          try {\n            content = JSON.parse(value);\n          } catch (e) {\n            content = [{\n              insert: value\n            }];\n          }\n        }\n        quillEditor.setContents(content);\n      }\n    };\n  }\n  ngOnChanges(changes) {\n    if (!this.quillEditor) {\n      return;\n    }\n    if (changes.content) {\n      this.valueSetter(this.quillEditor, changes.content.currentValue);\n    }\n  }\n  ngAfterViewInit() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => {\n      const promises = [this.service.registerCustomModules(Quill, this.customModules())];\n      const beforeRender = this.beforeRender() ?? this.service.config.beforeRender;\n      if (beforeRender) {\n        promises.push(beforeRender());\n      }\n      return Promise.all(promises).then(() => Quill);\n    })).subscribe(Quill => {\n      const modules = Object.assign({}, this.modules() || this.service.config.modules);\n      modules.toolbar = false;\n      this.customOptions().forEach(customOption => {\n        const newCustomOption = Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        Quill.register(newCustomOption, true);\n      });\n      let debug = this.debug();\n      if (!debug && debug !== false && this.service.config.debug) {\n        debug = this.service.config.debug;\n      }\n      let formats = this.formats();\n      if (!formats && formats === undefined) {\n        formats = this.service.config.formats ? [...this.service.config.formats] : this.service.config.formats === null ? null : undefined;\n      }\n      const theme = this.theme() || (this.service.config.theme ? this.service.config.theme : 'snow');\n      this.editorElem = this.elementRef.nativeElement.querySelector('[quill-view-element]');\n      this.zone.runOutsideAngular(() => {\n        this.quillEditor = new Quill(this.editorElem, {\n          debug: debug,\n          formats: formats,\n          modules,\n          readOnly: true,\n          strict: this.strict(),\n          theme\n        });\n      });\n      this.renderer.addClass(this.editorElem, 'ngx-quill-view');\n      if (this.content()) {\n        this.valueSetter(this.quillEditor, this.content());\n      }\n      // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n      // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n      if (!this.onEditorCreated.observed) {\n        return;\n      }\n      // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n      // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n      // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n      raf$().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n        this.onEditorCreated.emit(this.quillEditor);\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.quillSubscription?.unsubscribe();\n    this.quillSubscription = null;\n  }\n  static {\n    this.ɵfac = function QuillViewComponent_Factory(t) {\n      return new (t || QuillViewComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(QuillService), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: QuillViewComponent,\n      selectors: [[\"quill-view\"]],\n      inputs: {\n        format: [i0.ɵɵInputFlags.SignalBased, \"format\"],\n        theme: [i0.ɵɵInputFlags.SignalBased, \"theme\"],\n        modules: [i0.ɵɵInputFlags.SignalBased, \"modules\"],\n        debug: [i0.ɵɵInputFlags.SignalBased, \"debug\"],\n        formats: [i0.ɵɵInputFlags.SignalBased, \"formats\"],\n        sanitize: [i0.ɵɵInputFlags.SignalBased, \"sanitize\"],\n        beforeRender: [i0.ɵɵInputFlags.SignalBased, \"beforeRender\"],\n        strict: [i0.ɵɵInputFlags.SignalBased, \"strict\"],\n        content: [i0.ɵɵInputFlags.SignalBased, \"content\"],\n        customModules: [i0.ɵɵInputFlags.SignalBased, \"customModules\"],\n        customOptions: [i0.ɵɵInputFlags.SignalBased, \"customOptions\"]\n      },\n      outputs: {\n        onEditorCreated: \"onEditorCreated\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"quill-view-element\", \"\"]],\n      template: function QuillViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n        }\n      },\n      styles: [\".ql-container.ngx-quill-view{border:0}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillViewComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'quill-view',\n      template: `\n  <div quill-view-element></div>\n`,\n      standalone: true,\n      styles: [\".ql-container.ngx-quill-view{border:0}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: QuillService\n  }, {\n    type: i1.DomSanitizer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], {\n    onEditorCreated: [{\n      type: Output\n    }]\n  });\n})();\nclass QuillModule {\n  static forRoot(config) {\n    return {\n      ngModule: QuillModule,\n      providers: [{\n        provide: QUILL_CONFIG_TOKEN,\n        useValue: config\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function QuillModule_Factory(t) {\n      return new (t || QuillModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: QuillModule,\n      imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n      exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillModule, [{\n    type: NgModule,\n    args: [{\n      imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n      exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-quill\n */\n// Re-export everything from the secondary entry-point so we can be backwards-compatible\n// and don't introduce breaking changes for consumers.\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QuillEditorBase, QuillEditorComponent, QuillModule, QuillService, QuillViewComponent, QuillViewHTMLComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IAAC,CAAC,QAAQ,UAAU,aAAa,QAAQ;AAAA;AAAA,IAElD,CAAC,cAAc,YAAY;AAAA,IAAG,CAAC;AAAA,MAC7B,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IAAG,CAAC;AAAA,MACH,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,WAAW;AAAA,IACb,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,MAAM,CAAC,SAAS,OAAO,SAAS,MAAM;AAAA,IACxC,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK;AAAA,IAClC,CAAC;AAAA,IAAG,CAAC;AAAA,MACH,OAAO,CAAC;AAAA,IACV,GAAG;AAAA,MACD,YAAY,CAAC;AAAA,IACf,CAAC;AAAA;AAAA,IAED,CAAC;AAAA,MACC,MAAM,CAAC;AAAA,IACT,CAAC;AAAA,IAAG,CAAC;AAAA,MACH,OAAO,CAAC;AAAA,IACV,CAAC;AAAA,IAAG,CAAC,OAAO;AAAA;AAAA,IAEZ,CAAC,QAAQ,SAAS,OAAO;AAAA;AAAA,IAEzB,CAAC,OAAO;AAAA,EAAC;AACX;AAGA,IAAM,qBAAqB,IAAI,eAAe,UAAU;AAAA,EACtD,YAAY;AAAA,EACZ,SAAS,OAAO;AAAA,IACd,SAAS;AAAA,EACX;AACF,CAAC;AAQD,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,QAAQ,QAAQ;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAcF;AAZI,mBAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,SAAO,KAAK,KAAK,oBAAmB;AACtC;AAGA,mBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AACR,CAAC;AAGD,mBAAK,OAAyB,iBAAiB,CAAC,CAAC;AArBrD,IAAM,oBAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,qBAAqB,YAAU,yBAAyB,CAAC;AAAA,EAC7D,SAAS;AAAA,EACT,UAAU;AACZ,CAAC,CAAC;;;ACzFF,SAAS,mBAAmB,YAAY;AACpC,MAAI,CAAC,YAAY;AACb,6BAAyB,kBAAkB;AAC3C,iBAAa,OAAO,UAAU;AAAA,EAClC;AACA,QAAM,aAAa,IAAI,WAAW,cAAY;AAC1C,UAAM,eAAe,WAAW,UAAU,SAAS,KAAK,KAAK,QAAQ,CAAC;AACtE,WAAO;AAAA,EACX,CAAC;AACD,SAAO,CAAC,WAAW;AACf,WAAO,OAAO,KAAK,UAAU,UAAU,CAAC;AAAA,EAC5C;AACJ;;;ACrBA,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;AACnI,IAAM,MAAM,CAAC,gCAAgC,0BAA0B,8BAA8B;AACrG,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,IAAM,YAAY,CAAC,QAAQ,iBAAiB;AAC1C,QAAM,eAAe,UAAU;AAC/B,SAAO,gBAAgB;AACzB;AACA,IAAM,OAAO,MAAM;AACjB,SAAO,IAAI,WAAW,gBAAc;AAClC,UAAM,QAAQ,sBAAsB,MAAM;AACxC,iBAAW,KAAK;AAChB,iBAAW,SAAS;AAAA,IACtB,CAAC;AACD,WAAO,MAAM,qBAAqB,KAAK;AAAA,EACzC,CAAC;AACH;AAGA,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,YAAY,UAAU,QAAQ;AAC5B,SAAK,SAAS;AACd,SAAK,SAAS,MAAM,MAAY;AAzCpC;AA0CM,UAAI,CAAC,KAAK,OAAO;AAIf,cAAM,+BAA+B,KAAK,SAAS;AAUnD,aAAK,SAAS;AAAA,QAEd,KAAK,SAAS,iCAAiC,KAAK,KAAK,SAAS;AAClE,cAAM,cAAc,MAAM,OAAO,qBAAO;AACxC,aAAK,SAAS,mBAAmB;AACjC,aAAK;AAAA,SAEL,6BAAY,YAAZ,mBAAqB,YAArB,YAAgC,YAAY,YAA5C,YAAuD;AAAA,MACzD;AAEA,iBAAK,OAAO,kBAAZ,mBAA2B,QAAQ,kBAAgB;AACjD,cAAM,kBAAkB,KAAK,MAAM,OAAO,aAAa,MAAM;AAC7D,wBAAgB,YAAY,aAAa;AACzC,aAAK,MAAM,SAAS,iBAAiB,MAAM,KAAK,OAAO,6BAA6B;AAAA,MACtF;AACA,aAAO,MAAM,KAAK,sBAAsB,KAAK,OAAO,KAAK,OAAO,eAAe,KAAK,OAAO,6BAA6B;AAAA,IAC1H,EAAC,EAAE,KAAK,YAAY;AAAA,MAClB,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,CAAC;AACF,SAAK,WAAW,SAAS,IAAI,QAAQ;AACrC,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,sBAAsB,OAAO,eAAe,+BAA+B;AAAA;AAC/E,UAAI,MAAM,QAAQ,aAAa,GAAG;AAEhC,iBAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF,KAAK,eAAe;AAGlB,cAAI,aAAa,cAAc,GAAG;AAChC,6BAAiB,MAAM,eAAe,cAAc;AAAA,UACtD;AACA,gBAAM,SAAS,MAAM,gBAAgB,6BAA6B;AAAA,QACpE;AAAA,MACF;AAGA,aAAO;AAAA,IACT;AAAA;AAaF;AAXI,cAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,SAAO,KAAK,KAAK,eAAiB,SAAY,QAAQ,GAAM,SAAS,oBAAoB,CAAC,CAAC;AAC7F;AAGA,cAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,cAAa;AAAA,EACtB,YAAY;AACd,CAAC;AAnFL,IAAM,eAAN;AAAA,CAsFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,SAAS,MAAM,MAAS;AAC7B,SAAK,QAAQ,MAAM,MAAS;AAC5B,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,QAAQ,MAAM,KAAK;AACxB,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,cAAc,MAAM,MAAS;AAClC,SAAK,YAAY,MAAM,MAAS;AAChC,SAAK,YAAY,MAAM,MAAS;AAChC,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,wBAAwB,MAAM,KAAK;AACxC,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,eAAe,MAAM,MAAS;AACnC,SAAK,SAAS,MAAM,IAAI;AACxB,SAAK,WAAW,MAAM,MAAS;AAC/B,SAAK,SAAS,MAAM,MAAS;AAC7B,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,eAAe,MAAM,MAAS;AACnC,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,mBAAmB,MAAM,KAAK;AACnC,SAAK,kBAAkB,MAAM,MAAS;AACtC,SAAK,gBAAgB,MAAM,KAAK;AAChC,SAAK,aAAa,MAAM,KAAK;AAC7B,SAAK,eAAe,MAAM,MAAS;AAWnC,SAAK,oBAAoB,MAAM,IAAI;AACnC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,WAAW;AAChB,SAAK,kBAAkB,OAAO,KAAK;AACnC,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,KAAK,OAAO,iBAAiB;AAClC,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,aAAa,OAAO,WAAW;AACpC,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,OAAO,OAAO,MAAM;AACzB,SAAK,UAAU,OAAO,YAAY;AAClC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,cAAc,MAAM,iBAAe;AACtC,UAAI,OAAO,YAAY,gBAAgB;AACvC,UAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,eAAO,KAAK,kBAAkB;AAAA,MAChC;AACA,UAAI,aAAa;AACjB,YAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAI,WAAW,QAAQ;AACrB,qBAAa,YAAY,QAAQ;AAAA,MACnC,WAAW,WAAW,UAAU;AAC9B,qBAAa,YAAY,YAAY;AAAA,MACvC,WAAW,WAAW,QAAQ;AAC5B,YAAI;AACF,uBAAa,KAAK,UAAU,YAAY,YAAY,CAAC;AAAA,QACvD,SAAS,GAAG;AACV,uBAAa,YAAY,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,cAAc,MAAM,CAAC,aAAa,UAAU;AAC/C,YAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAI,WAAW,QAAQ;AACrB,cAAM,WAAW,CAAC,MAAM,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,YAAY;AAC7G,YAAI,UAAU;AACZ,kBAAQ,KAAK,aAAa,SAAS,gBAAgB,MAAM,KAAK;AAAA,QAChE;AACA,eAAO,YAAY,UAAU,QAAQ;AAAA,UACnC,MAAM;AAAA,QACR,CAAC;AAAA,MACH,WAAW,WAAW,QAAQ;AAC5B,YAAI;AACF,iBAAO,KAAK,MAAM,KAAK;AAAA,QACzB,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,YACN,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,yBAAyB,CAAC,OAAO,UAAU,WAAW;AACzD,YAAM,eAAe,KAAK,aAAa,KAAK,KAAK,QAAQ,OAAO;AAChE,YAAM,8BAA8B,CAAC,SAAS,CAAC,CAAC,KAAK,mBAAmB,WAAW,UAAU,gBAAgB,iBAAiB;AAE9H,UAAI,CAAC,KAAK,OAAO,YAAY,CAAC,KAAK,QAAQ,YAAY,CAAC,KAAK,mBAAmB,YAAY,CAAC,6BAA6B;AACxH;AAAA,MACF;AACA,WAAK,KAAK,IAAI,MAAM;AAClB,YAAI,UAAU,MAAM;AAClB,eAAK,OAAO,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH,WAAW,aAAa,MAAM;AAC5B,eAAK,QAAQ,KAAK;AAAA,YAChB,QAAQ,KAAK;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH;AACA,aAAK,mBAAmB,KAAK;AAAA,UAC3B,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,6BAA6B;AAC/B,eAAK,eAAe;AAAA,QACtB;AACA,aAAK,GAAG,aAAa;AAAA,MACvB,CAAC;AAAA,IACH;AACA,SAAK,oBAAoB,CAAC,OAAO,UAAU,WAAW;AAEpD,YAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,YAAM,UAAU,KAAK,YAAY,YAAY;AAC7C,UAAI,OAAO,KAAK,YAAY,gBAAgB;AAC5C,UAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,eAAO,KAAK,kBAAkB;AAAA,MAChC;AACA,YAAM,eAAe,KAAK,aAAa,KAAK,KAAK,QAAQ,OAAO;AAChE,YAAM,8BAA8B,WAAW,UAAU,gBAAgB,iBAAiB,UAAU,CAAC,CAAC,KAAK;AAE3G,UAAI,CAAC,KAAK,iBAAiB,YAAY,CAAC,4BAA4B;AAClE;AAAA,MACF;AACA,WAAK,KAAK,IAAI,MAAM;AAClB,YAAI,4BAA4B;AAC9B,gBAAM,cAAc,KAAK,YAAY;AACrC,eAAK,cAAc,YAAY,KAAK,WAAW,CAAC;AAAA,QAClD;AACA,aAAK,iBAAiB,KAAK;AAAA,UACzB;AAAA,UACA;AAAA,UACA,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,GAAG,aAAa;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,SAAK,sBAAsB,CAAC,OAAO,SAAS,KAAK,WAAW;AAE1D,UAAI,CAAC,KAAK,gBAAgB,UAAU;AAClC;AAAA,MACF;AAEA,UAAI,UAAU,eAAe;AAC3B,cAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,cAAM,UAAU,KAAK,YAAY,YAAY;AAC7C,YAAI,OAAO,KAAK,YAAY,gBAAgB;AAC5C,YAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,iBAAO,KAAK,kBAAkB;AAAA,QAChC;AACA,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,gBAAgB,KAAK;AAAA,YACxB;AAAA,YACA,OAAO;AAAA,YACP,QAAQ,KAAK;AAAA,YACb;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,UACF,CAAC;AACD,eAAK,GAAG,aAAa;AAAA,QACvB,CAAC;AAAA,MACH,OAAO;AACL,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,gBAAgB,KAAK;AAAA,YACxB,QAAQ,KAAK;AAAA,YACb;AAAA,YACA,UAAU;AAAA,YACV,OAAO;AAAA,YACP;AAAA,UACF,CAAC;AACD,eAAK,GAAG,aAAa;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,oBAAoB,SAAS;AAClC,UAAM,YAAY,QAAQ,KAAK,EAAE,MAAM,GAAG;AAC1C,WAAO,UAAU,OAAO,CAAC,MAAM,QAAQ;AACrC,YAAM,UAAU,IAAI,KAAK;AACzB,UAAI,SAAS;AACX,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAAA,EACvD;AAAA,EACA,kBAAkB;AAChB,QAAI,iBAAiB,KAAK,UAAU,GAAG;AACrC;AAAA,IACF;AAGA,SAAK,oBAAoB,KAAK,QAAQ,SAAS,EAAE,KAAK,SAAS,WAAS;AA/W5E;AAgXM,YAAM,WAAW,CAAC,KAAK,QAAQ,sBAAsB,OAAO,KAAK,cAAc,CAAC,CAAC;AACjF,YAAM,gBAAe,UAAK,aAAa,MAAlB,YAAuB,KAAK,QAAQ,OAAO;AAChE,UAAI,cAAc;AAChB,iBAAS,KAAK,aAAa,CAAC;AAAA,MAC9B;AACA,aAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,MAAM,KAAK;AAAA,IAC/C,CAAC,CAAC,EAAE,UAAU,WAAS;AACrB,WAAK,aAAa,KAAK,WAAW,cAAc,cAAc,wBAAwB;AACtF,YAAM,cAAc,KAAK,WAAW,cAAc,cAAc,wBAAwB;AACxF,YAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,OAAO;AAC/E,UAAI,aAAa;AACf,gBAAQ,UAAU;AAAA,MACpB,WAAW,QAAQ,YAAY,QAAW;AACxC,gBAAQ,UAAU,eAAe;AAAA,MACnC;AACA,UAAI,cAAc,KAAK,YAAY,MAAM,SAAY,KAAK,YAAY,IAAI,KAAK,QAAQ,OAAO;AAC9F,UAAI,gBAAgB,QAAW;AAC7B,sBAAc;AAAA,MAChB;AACA,YAAM,SAAS,KAAK,OAAO;AAC3B,UAAI,QAAQ;AACV,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,eAAK,SAAS,SAAS,KAAK,YAAY,KAAK,OAAO,GAAG,CAAC;AAAA,QAC1D,CAAC;AAAA,MACH;AACA,UAAI,KAAK,QAAQ,GAAG;AAClB,aAAK,WAAW,KAAK,QAAQ,CAAC;AAAA,MAChC;AACA,WAAK,cAAc,EAAE,QAAQ,kBAAgB;AAC3C,cAAM,kBAAkB,MAAM,OAAO,aAAa,MAAM;AACxD,wBAAgB,YAAY,aAAa;AACzC,cAAM,SAAS,iBAAiB,IAAI;AAAA,MACtC,CAAC;AACD,UAAI,SAAS,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,SAAS,KAAK,aAAa,KAAK,OAAO;AACvF,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,QAAQ,OAAO,SAAS,KAAK,QAAQ,OAAO,SAAS,KAAK,SAAS;AAAA,MACnF;AACA,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,CAAC,SAAS,UAAU,SAAS,KAAK,QAAQ,OAAO,OAAO;AAC1D,gBAAQ,KAAK,QAAQ,OAAO;AAAA,MAC9B;AACA,UAAI,WAAW,KAAK,SAAS;AAC7B,UAAI,CAAC,YAAY,KAAK,SAAS,MAAM,OAAO;AAC1C,mBAAW,KAAK,QAAQ,OAAO,aAAa,SAAY,KAAK,QAAQ,OAAO,WAAW;AAAA,MACzF;AACA,UAAI,oBAAoB,KAAK;AAE7B,UAAI,KAAK,QAAQ,OAAO,eAAe,mBAAmB,GAAG;AAC3D,4BAAoB,KAAK,QAAQ,OAAO;AAAA,MAC1C;AACA,UAAI,UAAU,KAAK,QAAQ;AAC3B,UAAI,CAAC,WAAW,YAAY,QAAW;AACrC,kBAAU,KAAK,QAAQ,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,OAAO,IAAI,KAAK,QAAQ,OAAO,YAAY,OAAO,OAAO;AAAA,MAC3H;AACA,WAAK,KAAK,kBAAkB,MAAM;AAtaxC;AAuaQ,aAAK,cAAc,IAAI,MAAM,KAAK,YAAY;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,KAAK,SAAS;AAAA,UACxB,OAAO,KAAK,MAAM,MAAM,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AAAA,QAClF,CAAC;AACD,YAAI,KAAK,aAAa,UAAU;AAE9B,eAAK,YAAY,OAAO,QAAQ,iBAAiB,QAAQ,MAAM,KAAK,aAAa,KAAK;AAAA,YACpF,QAAQ,KAAK;AAAA,YACb,QAAQ;AAAA,UACV,CAAC,CAAC;AAEF,gBAAM,UAAU,KAAK,YAAY,UAAU,SAAS;AACpD,wBAAQ,cAAR,mBAAmB,iBAAiB,aAAa,OAAK,EAAE,eAAe;AAAA,QACzE;AACA,YAAI,KAAK,cAAc,UAAU;AAC/B,eAAK,YAAY,OAAO,QAAQ,iBAAiB,SAAS,MAAM,KAAK,cAAc,KAAK;AAAA,YACtF,QAAQ,KAAK;AAAA,YACb,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAEA,YAAI,KAAK,gBAAgB,GAAG;AAC1B,gBAAM,WAAU,gBAAK,gBAAL,mBAAkB,UAAlB,mBAAyB;AACzC,gBAAMA,UAAQ,wCAAS,SAAT,mBAAe,cAAc;AAC3C,cAAIA,UAAA,gBAAAA,OAAO,SAAS;AAClB,YAAAA,OAAM,QAAQ,OAAO,KAAK,gBAAgB;AAAA,UAC5C;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,KAAK,SAAS;AAChB,cAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,YAAI,WAAW,QAAQ;AACrB,eAAK,YAAY,QAAQ,KAAK,SAAS,QAAQ;AAAA,QACjD,OAAO;AACL,gBAAM,cAAc,KAAK,YAAY;AACrC,gBAAM,WAAW,YAAY,KAAK,aAAa,KAAK,OAAO;AAC3D,eAAK,YAAY,YAAY,UAAU,QAAQ;AAAA,QACjD;AACA,cAAM,UAAU,KAAK,YAAY,UAAU,SAAS;AACpD,gBAAQ,MAAM;AAAA,MAChB;AAEA,WAAK,iBAAiB;AACtB,WAAK,uBAAuB;AAG5B,UAAI,CAAC,KAAK,gBAAgB,YAAY,CAAC,KAAK,oBAAoB;AAC9D;AAAA,MACF;AAIA,WAAK,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC/D,YAAI,KAAK,oBAAoB;AAC3B,eAAK,mBAAmB;AAAA,QAC1B;AACA,aAAK,gBAAgB,KAAK,KAAK,WAAW;AAAA,MAC5C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AA1ehB;AA2eI,SAAK,QAAQ;AACb,eAAK,sBAAL,mBAAwB;AACxB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AAEA,QAAI,QAAQ,UAAU;AACpB,WAAK,YAAY,OAAO,CAAC,QAAQ,SAAS,YAAY;AAAA,IACxD;AACA,QAAI,QAAQ,aAAa;AACvB,WAAK,YAAY,KAAK,QAAQ,cAAc,QAAQ,YAAY;AAAA,IAClE;AACA,QAAI,QAAQ,mBAAmB;AAC7B,WAAK,YAAY,KAAK,QAAQ,oBAAoB,QAAQ,kBAAkB;AAAA,IAC9E;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,iBAAiB,QAAQ,OAAO;AACtC,YAAM,kBAAkB,QAAQ,OAAO;AACvC,UAAI,iBAAiB;AACnB,eAAO,KAAK,eAAe,EAAE,QAAQ,SAAO;AAC1C,eAAK,SAAS,YAAY,KAAK,YAAY,GAAG;AAAA,QAChD,CAAC;AAAA,MACH;AACA,UAAI,gBAAgB;AAClB,eAAO,KAAK,cAAc,EAAE,QAAQ,SAAO;AACzC,eAAK,SAAS,SAAS,KAAK,YAAY,KAAK,KAAK,OAAO,EAAE,GAAG,CAAC;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,QAAQ,SAAS;AACnB,YAAM,iBAAiB,QAAQ,QAAQ;AACvC,YAAM,kBAAkB,QAAQ,QAAQ;AACxC,UAAI,iBAAiB;AACnB,aAAK,cAAc,eAAe;AAAA,MACpC;AACA,UAAI,gBAAgB;AAClB,aAAK,WAAW,cAAc;AAAA,MAChC;AAAA,IACF;AAGA,QAAI,QAAQ,cAAc;AACxB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EAEF;AAAA,EACA,WAAW,WAAW;AACpB,qBAAgB,oBAAoB,SAAS,EAAE,QAAQ,OAAK;AAC1D,WAAK,SAAS,SAAS,KAAK,YAAY,CAAC;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,cAAc,WAAW;AACvB,qBAAgB,oBAAoB,SAAS,EAAE,QAAQ,OAAK;AAC1D,WAAK,SAAS,YAAY,KAAK,YAAY,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,WAAW,cAAc;AAEvB,QAAI,KAAK,WAAW,KAAK,iBAAiB,MAAM;AAC9C;AAAA,IACF;AACA,SAAK,UAAU;AACf,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAM,cAAc,KAAK,YAAY;AACrC,UAAM,WAAW,YAAY,KAAK,aAAa,YAAY;AAC3D,QAAI,KAAK,cAAc,GAAG;AACxB,YAAM,qBAAqB,KAAK,YAAY,YAAY;AACxD,UAAI,KAAK,UAAU,kBAAkB,MAAM,KAAK,UAAU,QAAQ,GAAG;AACnE;AAAA,MACF;AAAA,IACF;AACA,QAAI,cAAc;AAChB,UAAI,WAAW,QAAQ;AACrB,aAAK,YAAY,QAAQ,YAAY;AAAA,MACvC,OAAO;AACL,aAAK,YAAY,YAAY,QAAQ;AAAA,MACvC;AACA;AAAA,IACF;AACA,SAAK,YAAY,QAAQ,EAAE;AAAA,EAC7B;AAAA,EACA,iBAAiB,aAAa,KAAK,UAAU;AAE3C,SAAK,WAAW;AAChB,QAAI,KAAK,aAAa;AACpB,UAAI,YAAY;AACd,aAAK,YAAY,QAAQ;AACzB,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,YAAY,UAAU;AAAA,MAClF,OAAO;AACL,YAAI,CAAC,KAAK,SAAS,GAAG;AACpB,eAAK,YAAY,OAAO;AAAA,QAC1B;AACA,aAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,UAAU;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,WAAW;AA1lBb;AA2lBI,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,CAAC;AACb,QAAI,QAAQ;AACZ,UAAM,OAAO,KAAK,YAAY,QAAQ;AAEtC,UAAM,aAAa,KAAK,iBAAiB,IAAI,KAAK,KAAK,EAAE,SAAS,KAAK,WAAW,KAAK,KAAK,KAAK,EAAE,WAAW,IAAI,IAAI,KAAK,SAAS;AACpI,UAAM,kBAAkB,KAAK,YAAY,YAAY,EAAE;AACvD,UAAM,qBAAqB,CAAC,CAAC,mBAAmB,gBAAgB,WAAW,KAAK,CAAC,MAAM,EAAE,EAAE,UAAS,qBAAgB,CAAC,EAAE,WAAnB,mBAA2B,UAAU;AACzI,QAAI,KAAK,UAAU,KAAK,cAAc,aAAa,KAAK,UAAU,GAAG;AACnE,UAAI,iBAAiB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW,KAAK,UAAU;AAAA,MAC5B;AACA,cAAQ;AAAA,IACV;AACA,QAAI,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU,GAAG;AACrD,UAAI,iBAAiB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW,KAAK,UAAU;AAAA,MAC5B;AACA,cAAQ;AAAA,IACV;AACA,QAAI,KAAK,SAAS,KAAK,CAAC,cAAc,oBAAoB;AACxD,UAAI,gBAAgB;AAAA,QAClB,OAAO;AAAA,MACT;AACA,cAAQ;AAAA,IACV;AACA,WAAO,QAAQ,OAAO;AAAA,EACxB;AAAA,EACA,yBAAyB;AACvB,SAAK,QAAQ;AAIb,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,eAAe,IAAI,aAAa;AACrC,WAAK,aAAa;AAAA;AAAA,QAElB,UAAU,KAAK,aAAa,kBAAkB,EAAE,UAAU,CAAC,CAAC,OAAO,UAAU,MAAM,MAAM;AACvF,eAAK,uBAAuB,OAAO,UAAU,MAAM;AAAA,QACrD,CAAC;AAAA,MAAC;AAGF,UAAI,cAAc,UAAU,KAAK,aAAa,aAAa;AAC3D,UAAI,gBAAgB,UAAU,KAAK,aAAa,eAAe;AAC/D,UAAI,OAAO,KAAK,aAAa,MAAM,UAAU;AAC3C,sBAAc,YAAY,KAAK,aAAa,KAAK,aAAa,CAAC,CAAC;AAChE,wBAAgB,cAAc,KAAK,aAAa,KAAK,aAAa,CAAC,CAAC;AAAA,MACtE;AACA,WAAK,aAAa;AAAA;AAAA,QAElB,YAAY,UAAU,CAAC,CAAC,OAAO,UAAU,MAAM,MAAM;AACnD,eAAK,kBAAkB,OAAO,UAAU,MAAM;AAAA,QAChD,CAAC;AAAA,MAAC;AACF,WAAK,aAAa;AAAA;AAAA,QAElB,cAAc,UAAU,CAAC,CAAC,OAAO,SAAS,KAAK,MAAM,MAAM;AACzD,eAAK,oBAAoB,OAAO,SAAS,KAAK,MAAM;AAAA,QACtD,CAAC;AAAA,MAAC;AAAA,IACJ,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,SAAS,aAAa,SAAS,iBAAiB,SAAS,iBAAiB,SAAS;AAAA,EAC5F;AAoDF;AAlDI,iBAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,SAAO,KAAK,KAAK,kBAAiB;AACpC;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,QAAQ;AAAA,IACN,QAAQ,CAAI,WAAa,aAAa,QAAQ;AAAA,IAC9C,OAAO,CAAI,WAAa,aAAa,OAAO;AAAA,IAC5C,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,OAAO,CAAI,WAAa,aAAa,OAAO;AAAA,IAC5C,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,IAClD,aAAa,CAAI,WAAa,aAAa,aAAa;AAAA,IACxD,WAAW,CAAI,WAAa,aAAa,WAAW;AAAA,IACpD,WAAW,CAAI,WAAa,aAAa,WAAW;AAAA,IACpD,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,IAClD,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,uBAAuB,CAAI,WAAa,aAAa,uBAAuB;AAAA,IAC5E,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,IAClD,cAAc,CAAI,WAAa,aAAa,cAAc;AAAA,IAC1D,QAAQ,CAAI,WAAa,aAAa,QAAQ;AAAA,IAC9C,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,IAClD,QAAQ,CAAI,WAAa,aAAa,QAAQ;AAAA,IAC9C,eAAe,CAAI,WAAa,aAAa,eAAe;AAAA,IAC5D,eAAe,CAAI,WAAa,aAAa,eAAe;AAAA,IAC5D,cAAc,CAAI,WAAa,aAAa,cAAc;AAAA,IAC1D,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,kBAAkB,CAAI,WAAa,aAAa,kBAAkB;AAAA,IAClE,iBAAiB,CAAI,WAAa,aAAa,iBAAiB;AAAA,IAChE,eAAe,CAAI,WAAa,aAAa,eAAe;AAAA,IAC5D,YAAY,CAAI,WAAa,aAAa,YAAY;AAAA,IACtD,cAAc,CAAI,WAAa,aAAa,cAAc;AAAA,IAC1D,mBAAmB,CAAI,WAAa,aAAa,mBAAmB;AAAA,IACpE,aAAa,CAAI,WAAa,aAAa,aAAa;AAAA,IACxD,aAAa,CAAI,WAAa,aAAa,aAAa;AAAA,EAC1D;AAAA,EACA,SAAS;AAAA,IACP,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA,UAAU,CAAI,oBAAoB;AACpC,CAAC;AApkBL,IAAM,kBAAN;AAAA,CAukBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,8BAA6B,gBAAgB;AA+CnD;AA7CI,sBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,6BAA6B,GAAG;AAC9C,YAAQ,sCAAsC,oCAAuC,sBAAsB,qBAAoB,IAAI,KAAK,qBAAoB;AAAA,EAC9J;AACF,GAAG;AAGH,sBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,OAAO;AAAA,IACP,SAAS;AAAA;AAAA,IAET,aAAa,WAAW,MAAM,qBAAoB;AAAA,EACpD,GAAG;AAAA,IACD,OAAO;AAAA,IACP,SAAS;AAAA;AAAA,IAET,aAAa,WAAW,MAAM,qBAAoB;AAAA,EACpD,CAAC,CAAC,GAAM,4BAA+B,mBAAmB;AAAA,EAC1D,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,wBAAwB,EAAE,CAAC;AAAA,EACrC,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB,GAAG;AACtB,MAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC5E,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa,GAAG,CAAC;AACpB,MAAG,aAAa,GAAG,CAAC;AACpB,MAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAAA,IAC9E;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,GAAG,IAAI,gBAAgB,MAAM,QAAQ,IAAI,EAAE;AAC5D,MAAG,UAAU,CAAC;AACd,MAAG,cAAc,GAAG,IAAI,gBAAgB,MAAM,QAAQ,IAAI,EAAE;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,wCAAwC;AACnD,CAAC;AA7CL,IAAM,uBAAN;AAAA,CAgDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,oBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA;AAAA,QAET,aAAa,WAAW,MAAM,oBAAoB;AAAA,MACpD,GAAG;AAAA,QACD,OAAO;AAAA,QACP,SAAS;AAAA;AAAA,QAET,aAAa,WAAW,MAAM,oBAAoB;AAAA,MACpD,CAAC;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,YAAY;AAAA,MACZ,QAAQ,CAAC,+BAA+B;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,0BAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,WAAW,SAAS;AAC9B,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU,MAAM,EAAE;AACvB,SAAK,QAAQ,MAAM,MAAS;AAC5B,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,YAAY,OAAO,EAAE;AAC1B,SAAK,aAAa,OAAO,SAAS;AAAA,EACpC;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,OAAO;AACjB,YAAM,QAAQ,QAAQ,MAAM,iBAAiB,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AACrG,WAAK,WAAW,IAAI,MAAM,KAAK,sBAAsB;AAAA,IACvD,WAAW,CAAC,KAAK,MAAM,GAAG;AACxB,YAAM,QAAQ,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AACtE,WAAK,WAAW,IAAI,MAAM,KAAK,sBAAsB;AAAA,IACvD;AACA,QAAI,QAAQ,SAAS;AACnB,YAAM,UAAU,QAAQ,QAAQ;AAChC,YAAM,WAAW,CAAC,MAAM,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,YAAY;AAC7G,YAAM,YAAY,WAAW,UAAU,KAAK,UAAU,wBAAwB,OAAO;AACrF,WAAK,UAAU,IAAI,SAAS;AAAA,IAC9B;AAAA,EACF;AAqCF;AAnCI,wBAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,SAAO,KAAK,KAAK,yBAA2B,kBAAqB,YAAY,GAAM,kBAAkB,YAAY,CAAC;AACpH;AAGA,wBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,EAC/B,QAAQ;AAAA,IACN,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,OAAO,CAAI,WAAa,aAAa,OAAO;AAAA,IAC5C,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,EACpD;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,EAC1D,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,SAAS,GAAG,CAAC,GAAG,aAAa,GAAG,WAAW,CAAC;AAAA,EAC5E,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,UAAU,GAAG,OAAO,CAAC;AACxB,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,WAAW,IAAI,WAAW,CAAC;AACzC,MAAG,UAAU;AACb,MAAG,WAAW,aAAa,IAAI,UAAU,GAAM,cAAc;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,cAAc,CAAC,OAAO;AAAA,EACtB,QAAQ,CAAC,+CAA+C;AAAA,EACxD,eAAe;AACjB,CAAC;AA3DL,IAAM,yBAAN;AAAA,CA8DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,YAAY;AAAA,MACZ,SAAS,CAAC,OAAO;AAAA,MACjB,QAAQ,CAAC,+CAA+C;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,YAAY,UAAU,MAAM,SAAS,cAAc,YAAY;AACzE,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,SAAS,MAAM,MAAS;AAC7B,SAAK,QAAQ,MAAM,MAAS;AAC5B,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,QAAQ,MAAM,KAAK;AACxB,SAAK,UAAU,MAAM,MAAS;AAC9B,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,eAAe,MAAM,MAAS;AACnC,SAAK,SAAS,MAAM,IAAI;AACxB,SAAK,UAAU,MAAM;AACrB,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7B,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,oBAAoB;AACzB,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,cAAc,CAAC,aAAa,UAAU;AACzC,YAAM,SAAS,UAAU,KAAK,OAAO,GAAG,KAAK,QAAQ,OAAO,MAAM;AAClE,UAAI,UAAU;AACd,UAAI,WAAW,QAAQ;AACrB,oBAAY,QAAQ,OAAO;AAAA,MAC7B,OAAO;AACL,YAAI,WAAW,QAAQ;AACrB,gBAAM,WAAW,CAAC,MAAM,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,YAAY;AAC7G,cAAI,UAAU;AACZ,oBAAQ,KAAK,aAAa,SAAS,gBAAgB,MAAM,KAAK;AAAA,UAChE;AACA,oBAAU,YAAY,UAAU,QAAQ;AAAA,YACtC,MAAM;AAAA,UACR,CAAC;AAAA,QACH,WAAW,WAAW,QAAQ;AAC5B,cAAI;AACF,sBAAU,KAAK,MAAM,KAAK;AAAA,UAC5B,SAAS,GAAG;AACV,sBAAU,CAAC;AAAA,cACT,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAAA,QACF;AACA,oBAAY,YAAY,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,SAAS;AACnB,WAAK,YAAY,KAAK,aAAa,QAAQ,QAAQ,YAAY;AAAA,IACjE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,iBAAiB,KAAK,UAAU,GAAG;AACrC;AAAA,IACF;AACA,SAAK,oBAAoB,KAAK,QAAQ,SAAS,EAAE,KAAK,SAAS,WAAS;AA59B5E;AA69BM,YAAM,WAAW,CAAC,KAAK,QAAQ,sBAAsB,OAAO,KAAK,cAAc,CAAC,CAAC;AACjF,YAAM,gBAAe,UAAK,aAAa,MAAlB,YAAuB,KAAK,QAAQ,OAAO;AAChE,UAAI,cAAc;AAChB,iBAAS,KAAK,aAAa,CAAC;AAAA,MAC9B;AACA,aAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,MAAM,KAAK;AAAA,IAC/C,CAAC,CAAC,EAAE,UAAU,WAAS;AACrB,YAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,OAAO;AAC/E,cAAQ,UAAU;AAClB,WAAK,cAAc,EAAE,QAAQ,kBAAgB;AAC3C,cAAM,kBAAkB,MAAM,OAAO,aAAa,MAAM;AACxD,wBAAgB,YAAY,aAAa;AACzC,cAAM,SAAS,iBAAiB,IAAI;AAAA,MACtC,CAAC;AACD,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,CAAC,SAAS,UAAU,SAAS,KAAK,QAAQ,OAAO,OAAO;AAC1D,gBAAQ,KAAK,QAAQ,OAAO;AAAA,MAC9B;AACA,UAAI,UAAU,KAAK,QAAQ;AAC3B,UAAI,CAAC,WAAW,YAAY,QAAW;AACrC,kBAAU,KAAK,QAAQ,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,OAAO,IAAI,KAAK,QAAQ,OAAO,YAAY,OAAO,OAAO;AAAA,MAC3H;AACA,YAAM,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AACvF,WAAK,aAAa,KAAK,WAAW,cAAc,cAAc,sBAAsB;AACpF,WAAK,KAAK,kBAAkB,MAAM;AAChC,aAAK,cAAc,IAAI,MAAM,KAAK,YAAY;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV,QAAQ,KAAK,OAAO;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,SAAS,SAAS,KAAK,YAAY,gBAAgB;AACxD,UAAI,KAAK,QAAQ,GAAG;AAClB,aAAK,YAAY,KAAK,aAAa,KAAK,QAAQ,CAAC;AAAA,MACnD;AAGA,UAAI,CAAC,KAAK,gBAAgB,UAAU;AAClC;AAAA,MACF;AAIA,WAAK,EAAE,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC/D,aAAK,gBAAgB,KAAK,KAAK,WAAW;AAAA,MAC5C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AAhhChB;AAihCI,eAAK,sBAAL,mBAAwB;AACxB,SAAK,oBAAoB;AAAA,EAC3B;AAwCF;AAtCI,oBAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,SAAO,KAAK,KAAK,qBAAuB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,YAAY,GAAM,kBAAqB,YAAY,GAAM,kBAAkB,WAAW,CAAC;AAC7P;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,EAC1B,QAAQ;AAAA,IACN,QAAQ,CAAI,WAAa,aAAa,QAAQ;AAAA,IAC9C,OAAO,CAAI,WAAa,aAAa,OAAO;AAAA,IAC5C,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,OAAO,CAAI,WAAa,aAAa,OAAO;AAAA,IAC5C,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,IAClD,cAAc,CAAI,WAAa,aAAa,cAAc;AAAA,IAC1D,QAAQ,CAAI,WAAa,aAAa,QAAQ;AAAA,IAC9C,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,IAChD,eAAe,CAAI,WAAa,aAAa,eAAe;AAAA,IAC5D,eAAe,CAAI,WAAa,aAAa,eAAe;AAAA,EAC9D;AAAA,EACA,SAAS;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,EAC1D,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;AAAA,EACnC,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,GAAG,OAAO,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,0CAA0C;AAAA,EACnD,eAAe;AACjB,CAAC;AA1JL,IAAM,qBAAN;AAAA,CA6JC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA,MAGV,YAAY;AAAA,MACZ,QAAQ,CAAC,0CAA0C;AAAA,IACrD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,aAAY;AAAA,EAChB,OAAO,QAAQ,QAAQ;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAgBF;AAdI,aAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,SAAO,KAAK,KAAK,cAAa;AAChC;AAGA,aAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,EAC1E,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAC5E,CAAC;AAGD,aAAK,OAAyB,iBAAiB,CAAC,CAAC;AAvBrD,IAAM,cAAN;AAAA,CA0BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,MAC1E,SAAS,CAAC,sBAAsB,oBAAoB,sBAAsB;AAAA,IAC5E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["input"]}