import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TranslateModule } from '@ngx-translate/core';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ProviderRes } from '../../../model/response/provider-res.model';
import { FormsModule } from '@angular/forms';
import { AddProviderComponent } from '../add-provider/add-provider.component';
import { BalanceAlertComponent } from '../balance-alert/balance-alert.component';
import { DeleteConfirmationComponent } from '../delete-confirmation/delete-confirmation.component';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { ClickOutsideDirective } from '../../../shared/directives/click-outside.directive';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-providers',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    TranslateModule,
    FormsModule,
    AddProviderComponent,
    BalanceAlertComponent,
    DeleteConfirmationComponent,
    FaIconComponent,
    ClickOutsideDirective
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './providers.component.html',
  styleUrl: './providers.component.css'
})
export class ProvidersComponent implements OnInit {
  providers: ProviderRes[] = [];
  isLoading: boolean = false;
  showAddProviderModal: boolean = false;
  selectedProvider: ProviderRes | null = null;

  // Action menu properties
  activeActionMenu: number | null = null;
  menuPosition: { top: number; left: number } = { top: 0, left: 0 };
  showChangeKeyModal: boolean = false;
  showNotificationsModal: boolean = false;
  showBalanceAlertModal: boolean = false;
  showDeleteConfirmation: boolean = false;
  providerToDelete: ProviderRes | null = null;
  isChangeKeyMode: boolean = false;
  isDeleting: boolean = false;

  // Array of background colors for provider icons
  providerColors: string[] = [
    'bg-blue-500',
    'bg-purple-500',
    'bg-green-500',
    'bg-pink-500',
    'bg-yellow-500',
    'bg-indigo-500',
    'bg-red-500',
    'bg-teal-500',
    'bg-orange-500',
    'bg-cyan-500'
  ];

  constructor(private adminService: AdminServiceService) {}

  ngOnInit(): void {
    this.loadProviders();
  }

  loadProviders(): void {
    this.isLoading = true;
    this.adminService.getProviders().subscribe({
      next: (providers) => {
        this.providers = providers;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading providers:', error);
        this.isLoading = false;
      }
    });
  }

  openAddProviderModal(): void {
    this.selectedProvider = null;
    this.showAddProviderModal = true;
  }

  openEditProviderModal(provider: ProviderRes): void {
    this.selectedProvider = provider;
    this.showAddProviderModal = true;
  }

  closeAddProviderModal(): void {
    this.showAddProviderModal = false;
    this.selectedProvider = null;
    this.isChangeKeyMode = false;
  }

  onProviderAdded(provider: ProviderRes): void {
    // If it's an edit, update the existing provider
    if (this.selectedProvider) {
      const index = this.providers.findIndex(p => p.id === provider.id);
      if (index !== -1) {
        this.providers[index] = provider;
      }
    } else {
      // Otherwise add the new provider to the list
      this.providers.push(provider);
    }

    this.closeAddProviderModal();
  }

  addProvider(): void {
    this.openAddProviderModal();
  }

  openProviderMenu(provider: ProviderRes): void {
    // Implement menu functionality
    console.log('Menu clicked for provider:', provider);
  }

  getProviderColor(providerId: number): string {
    // Use the provider ID to deterministically select a color
    const colorIndex = providerId % this.providerColors.length;
    return this.providerColors[colorIndex];
  }

  // Context menu methods
  toggleActionMenu(event: MouseEvent, provider: ProviderRes): void {
    event.stopPropagation();

    if (this.activeActionMenu === provider.id) {
      this.closeActionMenu();
      return;
    }

    // Set the active menu and position
    this.activeActionMenu = provider.id;

    // Calculate position
    const button = event.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();

    this.menuPosition = {
      top: rect.bottom + window.scrollY,
      left: rect.left + window.scrollX - 150 // Offset to position menu properly
    };
  }

  closeActionMenu(): void {
    this.activeActionMenu = null;
  }

  // Handle document clicks to close menus when clicking outside
  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.action-menu-button') && !target.closest('.dropdown-menu')) {
      this.closeActionMenu();
    }
  }

  // Change key modal
  openChangeKeyModal(provider: ProviderRes): void {
    this.selectedProvider = provider;
    this.isChangeKeyMode = true;
    this.showAddProviderModal = true;
    this.closeActionMenu();
  }


  // Balance Alert modal
  openBalanceAlertModal(provider: ProviderRes): void {
    this.selectedProvider = provider;
    this.showBalanceAlertModal = true;
    this.closeActionMenu();
  }

  closeBalanceAlertModal(): void {
    this.showBalanceAlertModal = false;
    this.selectedProvider = null;
  }

  onBalanceAlertUpdated(provider: ProviderRes): void {
    // If it's an edit, update the existing provider
    const index = this.providers.findIndex(p => p.id === provider.id);
    if (index !== -1) {
      this.providers[index] = provider;
    }
    this.closeBalanceAlertModal();
  }

  // Delete provider - Show confirmation dialog
  deleteProvider(provider: ProviderRes): void {
    this.providerToDelete = provider;
    this.showDeleteConfirmation = true;
    this.closeActionMenu();
  }

  // Close delete confirmation dialog
  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.providerToDelete = null;
    this.isDeleting = false;
  }

  // Confirm delete provider
  confirmDeleteProvider(): void {
    if (!this.providerToDelete) {
      return;
    }

    this.isDeleting = true;

    this.adminService.deleteProvider(this.providerToDelete.id)
      .pipe(finalize(() => this.isDeleting = false))
      .subscribe({
        next: () => {
          // Remove the provider from the list
          this.providers = this.providers.filter(p => p.id !== this.providerToDelete?.id);
          this.closeDeleteConfirmation();
        },
        error: (error) => {
          console.error('Error deleting provider:', error);
          // Keep the dialog open to allow retry
        }
      });
  }
}
