# API Tạo Token Cho User

## Mô tả
API này cho phép tạo token cho một user cụ thể với các kiểm tra bảo mật:
- <PERSON><PERSON><PERSON> tra user có thuộc tenant hiện tại không (thông qua bảng `user_tenant`)
- <PERSON><PERSON><PERSON> tra tenant context có được thiết lập không
- Ki<PERSON><PERSON> tra quyền truy cập tenant của user

## Endpoint
```
POST /v1/access/create-token
```

## Authorization
- Yêu cầu role: `ROLE_PANEL`
- Header: `Authorization: Bearer <access_token>`

## Request Body
```json
{
  "userId": 123,
  "tenantId": "tenant-uuid-optional"
}
```

### Tham số:
- `userId` (required): ID của user cần tạo token
- `tenantId` (optional): ID của tenant. Nếu không cung cấp, sẽ sử dụng tenant đầu tiên mà user có quyền truy cập

## Response

### Thành công (200 OK)
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "clientId": "client-uuid"
  },
  "message": null,
  "errorCode": null
}
```

### Lỗi

#### 1. Tenant không được cấu hình (400 Bad Request)
```json
{
  "success": false,
  "data": null,
  "message": "Panel not configured",
  "errorCode": "PANEL_NOT_CONFIGURED"
}
```

#### 2. User không thuộc tenant hiện tại (403 Forbidden)
```json
{
  "success": false,
  "data": null,
  "message": "Tenant access denied",
  "errorCode": "TENANT_ACCESS_DENIED"
}
```

#### 3. User không tồn tại (404 Not Found)
```json
{
  "success": false,
  "data": null,
  "message": "User not found",
  "errorCode": "USER_NOT_FOUND"
}
```

#### 4. User không có quyền truy cập tenant được chỉ định (403 Forbidden)
```json
{
  "success": false,
  "data": null,
  "message": "Tenant access denied",
  "errorCode": "TENANT_ACCESS_DENIED"
}
```

## Luồng xử lý

1. **Kiểm tra tenant context**: Lấy tenant hiện tại từ `TenantContext.getCurrentTenant()`
2. **Kiểm tra user thuộc tenant**: Sử dụng `UserTenantRepository.existsByUserIdAndTenantId()`
3. **Tìm user**: Kiểm tra user có tồn tại trong hệ thống
4. **Kiểm tra quyền truy cập tenant**: Nếu `tenantId` được cung cấp, kiểm tra user có quyền truy cập không
5. **Tạo token**: Sử dụng `KeyTokenService.createKeyPairPanel()` để tạo token

## Ví dụ sử dụng

### Tạo token với tenant mặc định
```bash
curl -X POST http://localhost:8080/v1/access/create-token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your_token>" \
  -d '{
    "userId": 123
  }'
```

### Tạo token với tenant cụ thể
```bash
curl -X POST http://localhost:8080/v1/access/create-token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your_token>" \
  -d '{
    "userId": 123,
    "tenantId": "550e8400-e29b-41d4-a716-446655440000"
  }'
```

## Bảng liên quan

### user_tenant
Bảng này lưu trữ mối quan hệ giữa user và tenant:
```sql
CREATE TABLE user_tenant (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_tenant (user_id, tenant_id)
);
```

### user_tenant_access
Bảng này lưu trữ quyền truy cập của user đối với các tenant:
```sql
CREATE TABLE user_tenant_access (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_tenant_access (user_id, tenant_id)
);
```

## Lưu ý bảo mật

1. **Kiểm tra kép**: API thực hiện kiểm tra cả `user_tenant` và `user_tenant_access`
2. **Tenant context**: Phải có tenant được thiết lập trong context
3. **Authorization**: Chỉ user có role `ROLE_PANEL` mới có thể sử dụng API này
4. **Logging**: Tất cả các hoạt động đều được ghi log để audit
