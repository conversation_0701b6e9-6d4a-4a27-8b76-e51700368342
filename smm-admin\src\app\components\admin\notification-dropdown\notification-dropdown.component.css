.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for notification list */
.max-h-80::-webkit-scrollbar {
  width: 6px;
}

.max-h-80::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-80::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-80::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for dropdown */
.notification-dropdown-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.notification-dropdown-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Pulse animation for unread badge */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

/* Dropdown container */
.notification-dropdown {
  display: flex;
  flex-direction: column;
  min-height: 200px;
  max-height: 500px;
}

/* Simple notification styles matching the design */
.notification-item {
  transition: all 0.2s ease;
  background: white;
  position: relative;
  z-index: 1;
}

/* Unread notifications - clean blue accent */
.notification-item.unread {
  background: rgba(239, 246, 255, 0.9);
  border-left: 3px solid #3b82f6;
}

.notification-item.unread:hover {
  background: rgba(239, 246, 255, 1);
}

/* Read notifications - subtle gray */
.notification-item.read {
  background: white;
}

.notification-item.read:hover {
  background: rgba(249, 250, 251, 0.8);
}

/* Ensure text is always visible with high contrast */
.notification-title {
  color: #111827 !important;
  font-weight: 600 !important;
  z-index: 2;
  position: relative;
  line-height: 1.4;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.8);
}

.notification-item.read .notification-title {
  color: #374151 !important;
  font-weight: 500 !important;
}

.notification-content {
  color: #4b5563 !important;
  z-index: 2;
  position: relative;
  line-height: 1.5;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.8);
}

.notification-item.read .notification-content {
  color: #6b7280 !important;
}

.notification-time {
  color: #2563eb !important;
  font-weight: 600 !important;
  z-index: 2;
  position: relative;
  font-size: 11px;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.8);
}

.notification-item.read .notification-time {
  color: #6b7280 !important;
  font-weight: 400 !important;
}

/* Additional styling for better visibility */
.notification-item * {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced scrollbar for notification list */
.notification-dropdown .overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.notification-dropdown .overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.notification-dropdown .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notification-dropdown .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notification-dropdown {
    width: calc(100vw - 16px) !important;
    right: 8px !important;
    left: 8px !important;
    max-height: 80vh !important;
    position: fixed !important;
    top: 60px !important;
  }

  .notification-dropdown .overflow-y-auto {
    max-height: calc(80vh - 140px) !important;
  }

  .notification-item {
    padding: 12px 16px !important;
  }

  .notification-title {
    font-size: 14px !important;
  }

  .notification-content {
    font-size: 13px !important;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    width: calc(100vw - 8px) !important;
    right: 4px !important;
    left: 4px !important;
  }
}
