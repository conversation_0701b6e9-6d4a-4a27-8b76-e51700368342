/* Standard Header Style */
.profile {
    @apply items-center gap-4 p-4 py-2 rounded-2xl border border-[#dcddff] hidden md:flex bg-[var(--primary-light)] cursor-pointer hover:bg-[var(--primary-light-hover)] transition-colors duration-200;
}

/* Compact Header Style */
.profile-compact {
    @apply items-center gap-2 p-2 py-1 rounded-lg border border-gray-200 hidden md:flex bg-white cursor-pointer hover:bg-gray-50 transition-colors duration-200;
}

/* Modern Header Style */
.profile-modern {
    @apply items-center gap-3 p-3 py-2 rounded-xl shadow-sm hidden md:flex bg-blue-50 cursor-pointer hover:bg-blue-100 transition-colors duration-200;
}

/* Minimal Header Style */
.profile-minimal {
    @apply items-center gap-2 p-2 hidden md:flex cursor-pointer hover:bg-gray-50 transition-colors duration-200 rounded-full;
}



.user-menu-container {
    @apply relative inline-block;
}

.menu-sidebar {
    @apply w-[280px] bg-white rounded-[20px] p-6 px-7 shadow-[0_0_1px_#e9eaec] border border-[#e9eaec] absolute right-0 hidden z-[1000];
    top: calc(100% + 10px);
}

.profile-menu {
    @apply items-center gap-4 px-4 flex mb-4;
}

.menu-sidebar.show {
    @apply block;
}

.profile-img {
    @apply w-[40px] h-[40px] rounded-full;
}

.profile-info {
    @apply flex flex-col;
}

.profile-name {
    @apply font-semibold text-[14px];
}

.profile-role {
    @apply font-normal text-[12px] text-[#a0aec0];
}

.menu-items {
    @apply flex flex-col gap-4;
}

.menu-item {
    @apply flex items-center gap-2 font-medium text-[14px] cursor-pointer hover:bg-[#f5f7fc] py-2 px-1 rounded-lg transition-colors duration-200;
}

.menu-icon {
    @apply w-[24px] h-[24px];
}

.balance-info {
    @apply flex justify-between border-[#a0aec0] ;
}

.balance, .orders {
    @apply flex flex-col items-center;
}

.logout {
    @apply flex items-center gap-2 text-[#e53e3e] font-medium text-[14px] mt-4 cursor-pointer hover:bg-[#fff1f1] py-2 px-1 rounded-lg transition-colors duration-200;
}

.logout-icon {
    @apply w-[24px] h-[24px];
}

.overlay {
    @apply fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-30 z-[900] cursor-pointer;
}

