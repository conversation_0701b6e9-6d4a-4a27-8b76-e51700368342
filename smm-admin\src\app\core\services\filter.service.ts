import { Injectable } from '@angular/core';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { ExtendedCategoryRes } from '../../model/extended/extended-category.model';
import { ExtendedIconBaseModel } from '../../model/extended/extended-icon-base.model';

@Injectable({
  providedIn: 'root'
})
export class FilterService {
  constructor() {}

  /**
   * Applies filter based on search text
   * @param searchText The search text to filter by
   * @param displayCategories The display categories array
   * @param currentCategory The current category
   * @param unfilteredDisplayCategories The unfiltered display categories array
   * @param unfilteredCurrentCategory The unfiltered current category
   * @returns The filtered categories and current category
   */
  applyFilter(
    searchText: string,
    displayCategories: ExtendedCategoryRes[],
    currentCategory: ExtendedCategoryRes | null,
    unfilteredDisplayCategories: ExtendedCategoryRes[],
    unfilteredCurrentCategory: ExtendedCategoryRes | null
  ): {
    displayCategories: ExtendedCategoryRes[],
    currentCategory: ExtendedCategoryRes | null,
    unfilteredDisplayCategories: ExtendedCategoryRes[],
    unfilteredCurrentCategory: ExtendedCategoryRes | null,
    isFilterApplied: boolean,
    searchTerm: string
  } {
    // Initialize the search term
    const searchTerm = searchText.toLowerCase().trim();
    let isFilterApplied = false;

    // Save unfiltered state if this is the first filter operation
    if (searchTerm && unfilteredDisplayCategories.length === 0) {
      unfilteredDisplayCategories = [...displayCategories];
      unfilteredCurrentCategory = currentCategory;
      isFilterApplied = true;
    }

    // Only apply search filter if there's a search term
    if (searchTerm) {
      // Filter display categories
      displayCategories = displayCategories
        .map(category => ({
          ...category,
          services: this.filterServicesByName(category.services, searchTerm)
        }))
        .filter(category => category.services.length > 0); // Only keep categories with matching services

      // Filter current category
      if (currentCategory) {
        const filteredServices = this.filterServicesByName(currentCategory.services, searchTerm);

        if (filteredServices.length > 0) {
          // Create a new category object with filtered services
          currentCategory = {
            ...currentCategory,
            services: filteredServices
          };
        } else {
          // No matching services in current category
          currentCategory = null;
        }
      }
    } else if (unfilteredDisplayCategories.length > 0) {
      // If search term is empty and we have unfiltered data, restore it
      displayCategories = [...unfilteredDisplayCategories];
      currentCategory = unfilteredCurrentCategory;
      unfilteredDisplayCategories = [];
      unfilteredCurrentCategory = null;
      isFilterApplied = false;
    }

    return {
      displayCategories,
      currentCategory,
      unfilteredDisplayCategories,
      unfilteredCurrentCategory,
      isFilterApplied,
      searchTerm
    };
  }

  /**
   * Filters services by name using the current search term
   * @param services The services to filter
   * @param searchTerm The search term to filter by
   * @returns Filtered services array
   */
  private filterServicesByName(services: SuperGeneralSvRes[], searchTerm: string): SuperGeneralSvRes[] {
    return services.filter(service =>
      service.name.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Resets all filters and returns to default view
   * @param allPlatforms All platforms
   * @param isFilterApplied Whether a filter is currently applied
   * @param unfilteredDisplayCategories The unfiltered display categories array
   * @param unfilteredCurrentCategory The unfiltered current category
   * @returns The reset state
   */
  resetFilter(
    allPlatforms: any[],
    isFilterApplied: boolean,
    unfilteredDisplayCategories: ExtendedCategoryRes[],
    unfilteredCurrentCategory: ExtendedCategoryRes | null
  ): {
    displayCategories: ExtendedCategoryRes[],
    currentCategory: ExtendedCategoryRes | null,
    unfilteredDisplayCategories: ExtendedCategoryRes[],
    unfilteredCurrentCategory: ExtendedCategoryRes | null,
    isFilterApplied: boolean,
    searchTerm: string,
    selectedCategory: ExtendedIconBaseModel
  } {
    // Clear the search term
    const searchTerm = '';

    // Restore original state if filtering was applied
    let displayCategories: ExtendedCategoryRes[] = [];
    let currentCategory: ExtendedCategoryRes | null = null;

    if (isFilterApplied) {
      displayCategories = [...unfilteredDisplayCategories];
      currentCategory = unfilteredCurrentCategory;
      unfilteredDisplayCategories = [];
      unfilteredCurrentCategory = null;
      isFilterApplied = false;
    }

    // Create a default "All Categories" option
    const allCategoriesOption = {
      id: 'all',
      label: 'filter.all_categories',
      icon: '',
      sort: 0
    };

    return {
      displayCategories,
      currentCategory,
      unfilteredDisplayCategories,
      unfilteredCurrentCategory,
      isFilterApplied,
      searchTerm,
      selectedCategory: allCategoriesOption
    };
  }

  /**
   * Updates category dropdown options to show all categories from all platforms
   * @param allPlatforms All platforms
   * @returns The category options
   */
  updateCategoryOptions(allPlatforms: any[]): ExtendedIconBaseModel[] {
    // Reset category options
    const categoryOptions: ExtendedIconBaseModel[] = [];

    // Add "All Categories" option
    categoryOptions.push({
      id: 'all',
      label: 'filter.all_categories',
      icon: '', // No icon,
      sort: 0
    });

    // Add all categories from all platforms
    allPlatforms.forEach(platform => {
      // Get categories and sort them by the sort field
      const sortedCategories = [...platform.categories]
        .filter((category: any) => !category.hide)
        .sort((a: any, b: any) => a.sort - b.sort);

      sortedCategories.forEach((category: any) => {
        categoryOptions.push({
          id: `${platform.id}_${category.id}`,
          label: `${category.name}`,
          icon: platform.icon,
          platformId: platform.id.toString(),
          sort: category.sort
        });
      });
    });

    return categoryOptions;
  }

  /**
   * Displays all categories from all platforms
   * @param allPlatforms All platforms
   * @param adminService The admin service
   * @returns The display categories
   */
  displayAllCategories(allPlatforms: any[], adminService: any): ExtendedCategoryRes[] {
    // Get the current services by category from AdminServiceService
    const servicesByCategory = adminService.getServicesByCategoryValue();
    const displayCategories: ExtendedCategoryRes[] = [];

    allPlatforms.forEach(platform => {
      // Get categories and sort them by the sort field
      const sortedCategories = [...platform.categories]
        .filter((category: any) => !category.hide);

      sortedCategories.forEach((category: any) => {
        // Create the category with platform info
        const categoryWithPlatform: ExtendedCategoryRes = {
          ...category,
          platformName: platform.name,
          platformIcon: platform.icon,
          isAllPlatforms: false,
          isAllCategories: false
        };

        // Update the services from the AdminServiceService
        const categoryId = category.id.toString();
        if (servicesByCategory[categoryId]) {
          categoryWithPlatform.services = servicesByCategory[categoryId];
        }

        displayCategories.push(categoryWithPlatform);
      });
    });

    return displayCategories.sort((a, b) => a.sort - b.sort);
  }
}
