import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize, tap, switchMap } from 'rxjs';
import { ConfigService } from './config.service';
import { OrderRes } from '../../model/response/order-res.model';
import { PageResponse } from '../../model/response/page-response.model';

export interface OrderSearchReq {
  keyword?: string;
  status?: string;
  serviceId?: number;
  categoryId?: number;
  from?: string;
  to?: string;
  page?: number;
  size?: number;
}

export interface CreateOrderReq {
  service_id: number;
  link: string;
  quantity: number;
  comments?: string[];
  voucher_code?: string;
}

@Injectable({
  providedIn: 'root' // This ensures the service is provided at the root level and uses the configured HttpClient with interceptors
})
export class OrderService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _orders$ = new BehaviorSubject<OrderRes[]>([]);
  private _pagination$ = new BehaviorSubject<any>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });
  private _search$ = new Subject<OrderSearchReq>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Set up subscription for search
    this._search$
      .pipe(
        tap(() => this._loading$.next(true)),
        switchMap(filter => this.searchOrders(filter))
      )
      .subscribe((result: PageResponse<OrderRes>) => {
        this._orders$.next(result.content);
        this._pagination$.next({
          pageNumber: result.pageable.page_number,
          pageSize: result.pageable.page_size,
          totalElements: result.total_elements,
          totalPages: result.total_pages
        });
        this._loading$.next(false);
      });
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get orders$(): BehaviorSubject<OrderRes[]> {
    return this._orders$;
  }

  get pagination$(): BehaviorSubject<any> {
    return this._pagination$;
  }

  /**
   * Trigger a search for orders with filters
   * @param filter The search filter
   */
  search(filter: OrderSearchReq): void {
    console.log('Order Service - Triggering search with filter:', filter);
    this._search$.next(filter);
  }

  /**
   * Load orders with pagination
   * @param page Page number (0-based)
   * @param size Page size
   */
  loadOrders(page: number = 0, size: number = 10): void {
    this.search({ page, size });
  }

  /**
   * Search orders with filters
   * @param filter The search filter
   */
  searchOrders(filter: OrderSearchReq): Observable<PageResponse<OrderRes>> {
    this._loading$.next(true);

    // Default page and size if not provided
    const page = filter.page !== undefined ? filter.page : 0;
    const size = filter.size !== undefined ? filter.size : 10;

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (filter.keyword) {
      params = params.set('keyword', filter.keyword);
    }

    if (filter.status && filter.status !== 'all') {
      params = params.set('status', filter.status);
    }

    if (filter.serviceId) {
      params = params.set('service_id', filter.serviceId.toString());
    }

    if (filter.categoryId) {
      params = params.set('category_id', filter.categoryId.toString());
    }

    if (filter.from) {
      params = params.set('from', filter.from);
    }

    if (filter.to) {
      params = params.set('to', filter.to);
    }

    // Use the user orders endpoint for regular users
    const url = `${this.configService.apiUrl}/orders/me/search`;
    console.log('Order Service - API URL:', url);
    console.log('Order Service - Full params:', params.toString());

    // Debug log to check if this request will go through JWT interceptor
    console.log('Order Service - Making HTTP request - This should go through JWT interceptor');

    return this.http
      .get<PageResponse<OrderRes>>(url, { params })
      .pipe(
        tap(response => {
          console.log('Order Service - Received response from API');
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Create a new order
   * @param orderData The order data to create
   * @returns Observable with the created order
   */
  createOrder(orderData: CreateOrderReq): Observable<OrderRes> {
    this._loading$.next(true);

    const url = `${this.configService.apiUrl}/orders`;
    console.log('Creating order with data:', orderData);

    return this.http.post<OrderRes>(url, orderData)
      .pipe(
        finalize(() => this._loading$.next(false))
      );
  }
}
