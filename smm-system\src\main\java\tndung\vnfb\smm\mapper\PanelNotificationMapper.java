package tndung.vnfb.smm.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import tndung.vnfb.smm.dto.PanelNotificationReq;
import tndung.vnfb.smm.dto.PanelNotificationRes;
import tndung.vnfb.smm.entity.PanelNotification;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PanelNotificationMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isRead", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    PanelNotification toEntity(PanelNotificationReq req);
    
    PanelNotificationRes toDto(PanelNotification entity);
    
    List<PanelNotificationRes> toDto(List<PanelNotification> entities);
}
