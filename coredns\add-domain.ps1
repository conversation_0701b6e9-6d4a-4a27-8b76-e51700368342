param (
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$IpAddress
)

# Script to add a new domain to CoreDNS
# Usage: .\add-domain.ps1 -Domain example.com -IpAddress **************

$ZoneFile = ".\zones\$Domain.db"
$Serial = Get-Date -Format "yyyyMMdd01"

# Create zone file
$ZoneContent = @"
`$ORIGIN $Domain.
`$TTL 3600
@       IN      SOA     ns1.autovnfb.com. admin.autovnfb.com. (
                        $Serial  ; Serial
                        7200            ; Refresh
                        3600            ; Retry
                        1209600         ; Expire
                        3600            ; Minimum TTL
                        )

; Name servers
@       IN      NS      ns1.autovnfb.com.
@       IN      NS      ns2.autovnfb.com.

; A records for the domain
@       IN      A       $IpAddress
www     IN      A       $IpAddress
*       IN      A       $IpAddress
"@

# Create the file
Set-Content -Path $ZoneFile -Value $ZoneContent

# Update Corefile to include the new domain
$CorefileContent = Get-Content -Path .\Corefile -Raw
if (-not $CorefileContent.Contains($Domain)) {
    # Find the last closing brace
    $lastBraceIndex = $CorefileContent.LastIndexOf("}")
    
    # Insert new domain block before the last closing brace
    $newDomainBlock = @"

$Domain {
    file /etc/coredns/zones/$Domain.db
    errors
    log
    reload 10s
}
"@
    
    $updatedContent = $CorefileContent.Insert($lastBraceIndex + 1, $newDomainBlock)
    Set-Content -Path .\Corefile -Value $updatedContent
}

Write-Host "Domain $Domain added to CoreDNS configuration."
Write-Host "Restart CoreDNS or reload configuration for changes to take effect."
