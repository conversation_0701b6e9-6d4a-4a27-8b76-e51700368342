/* Modal overlay */
.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  padding: 20px;
  overflow-y: auto; /* Enable scrolling on the overlay */
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

/* Modal container */
.modal-container {
  @apply relative;
  animation: modalSlideIn 0.3s ease-out;
  margin: 20px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Role selection styling */
.role-option {
  transition: all 0.2s ease;
}

.role-option:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Custom radio button styling */
.role-radio {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

.role-radio:checked {
  border-color: var(--primary);
  background-color: var(--primary);
}

.role-radio:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

/* Panel checkbox styling */
input[type="checkbox"] {
  accent-color: var(--primary);
}

/* Form input focus states */
input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* Disabled state for role options */
.disabled {
  pointer-events: none;
  opacity: 0.6;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .modal-container {
    @apply w-full mx-4;
  }
}
