<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Special Prices for User: {{ user?.id || undefined }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Nested New Special Prices Component -->
      <app-new-special-prices
        *ngIf="showNewSpecialPricesPopup"
        [selectedServices]="selectedServices"
        [selectedService]="specialPriceToEdit && specialPriceToEdit.service ? convertToSuperGeneralSvRes(specialPriceToEdit.service) : null"
        [editMode]="isEditMode"
        [specialPriceToEdit]="specialPriceToEdit"
        [fromUserView]="fromUserView"
        [userIdFromParent]="userIdNumber"
        (close)="closeNewSpecialPricesPopup()"
        (specialPriceAdded)="onSpecialPriceAdded($event)">
      </app-new-special-prices>

      <!-- Set Discount Input -->
      <div class="set-discount-container mb-4">
        <h3 class="set-discount-title mb-2">Set discount</h3>
        <input
          type="number"
          class="set-discount-input"
          placeholder="0"
          [(ngModel)]="globalDiscount"
          (change)="saveGlobalDiscount()"
          min="0"
          max="100"
        >
        <div class="text-sm text-gray-600 mt-1">This discount overrides status discount</div>
      </div>

      <!-- Loading indicator -->
      <div *ngIf="isLoadingPrices" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--primary)]"></div>
      </div>

      <!-- Special prices table -->
      <div *ngIf="!isLoadingPrices" class="special-prices-table mb-6">
        <div class="table-container">
          <table class="w-full">
            <thead>
              <tr>
                <th class="text-left py-2 px-4">ID</th>
                <th class="text-left py-2 px-4">Service</th>
                <th class="text-right py-2 px-4">Price</th>
                <th class="w-10 py-2 px-4"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let price of specialPrices" class="hover:bg-gray-50">
                <td class="py-3 px-4">{{ price.service.id }}</td>
                <td class="py-3 px-4">{{ price.service.name }}</td>
                <td class="py-3 px-4 text-right">
                  <span class="font-medium">{{ getFormattedPrice(price) }}</span>
                </td>
                <td class="py-3 px-4 text-right">
                  <div class="relative menu-container">
                    <button class="action-menu-button" (click)="toggleActionMenu(price.id, $event)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="1"></circle>
                        <circle cx="12" cy="5" r="1"></circle>
                        <circle cx="12" cy="19" r="1"></circle>
                      </svg>
                    </button>

                    <!-- Action Menu -->
                    <div *ngIf="activeActionMenu === price.id"
                         class="absolute dropdown-menu z-50 w-40 bg-white shadow-lg right-0 mt-1 rounded-lg overflow-hidden">
                      <div class="py-1">
                        <a (click)="editSpecialPrice(price.id); $event.stopPropagation()" class="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 text-gray-700">
                          <svg class="mr-2 w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                          Edit
                        </a>
                        <a (click)="deleteSpecialPrice(price.id); $event.stopPropagation()" class="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 text-red-600">
                          <svg class="mr-2 w-4 h-4 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                          Delete
                        </a>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr *ngIf="specialPrices.length === 0">
                <td colspan="4" class="py-6 text-center text-gray-500">
                  No special prices found for this user
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Add New Special Price Button -->
      <button
        type="button"
        class="add-special-price-button"
        (click)="openAddNewSpecialPrice()"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
        Add new special price
      </button>
    </div>
  </div>
</div>
