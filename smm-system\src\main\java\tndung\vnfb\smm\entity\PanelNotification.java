package tndung.vnfb.smm.entity;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import tndung.vnfb.smm.entity.audit.AbstractTenantEntity;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "panel_notification")
@Getter
@Setter
@NoArgsConstructor
@FilterDef(name = "tenantFilter", parameters = { @ParamDef(name = "tenantId", type = "string") ,  @ParamDef(name = "wildcardTenant", type = "string") })
@Filter(name = "tenantFilter", condition = "(tenant_id = :tenantId  OR tenant_id = :wildcardTenant)")
public class PanelNotification extends AbstractTenantEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(nullable = false)
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationType type = NotificationType.INFO;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationCategory category = NotificationCategory.SYSTEM;

    @Column(name = "is_read", nullable = false)
    private Boolean isRead = false;





    public enum NotificationType {
        INFO, SUCCESS, WARNING, ERROR
    }

    public enum NotificationCategory {
        SYSTEM, RENEWAL, SETUP
    }
}
