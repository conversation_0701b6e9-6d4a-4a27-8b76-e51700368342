import { Component, EventEmitter, Output, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-new-panel-step1',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './new-panel-step1.component.html',
  styleUrl: './new-panel-step1.component.css'
})
export class NewPanelStep1Component {
  @Input() domain: string = '';
  @Output() close = new EventEmitter<void>();
  @Output() next = new EventEmitter<string>();

  onClose(): void {
    this.close.emit();
  }

  onNext(): void {
    if (this.domain && this.domain.trim()) {
      this.next.emit(this.domain);
    }
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }
}
