import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { GUserSuperRes, CommonStatus } from '../../../../model/response/g-user-super-res.model';
import { UserAdminService } from '../../../../core/services/user-admin.service';
import { Subscription, forkJoin, of } from 'rxjs';

@Component({
  selector: 'app-ban-account',
  standalone: true,
  imports: [CommonModule, FormsModule, IconsModule, TranslateModule],
  templateUrl: './ban-account.component.html',
  styleUrl: './ban-account.component.css'
})
export class BanAccountComponent implements OnInit, On<PERSON><PERSON>roy {
  @Input() userId: number = 0;
  @Input() userName: string = '';
  @Input() userStatus: CommonStatus = CommonStatus.ACTIVATED;
  @Input() userIds: number[] = []; // New input for multiple users
  @Input() users: GUserSuperRes[] = []; // New input for user objects
  @Output() close = new EventEmitter<void>();
  @Output() statusChanged = new EventEmitter<{userIds: number[], status: CommonStatus, reason?: string}>();

  reason: string = '';
  isBanning: boolean = true;
  isLoading: boolean = false;
  errorMessage: string = '';
  isBulkAction: boolean = false;
  selectedCount: number = 0;

  private subscriptions: Subscription[] = [];

  constructor(private userAdminService: UserAdminService) {}

  ngOnInit(): void {
    // Check if this is a bulk action
    this.isBulkAction = this.userIds.length > 0;
    this.selectedCount = this.userIds.length;

    if (this.isBulkAction) {
      // For bulk actions, we assume we're banning if at least one user is active
      const hasActiveUsers = this.users.some(user =>
        this.userIds.includes(user.id) && user.status === CommonStatus.ACTIVATED
      );
      this.isBanning = hasActiveUsers;
    } else {
      // For single user, determine based on current status
      this.isBanning = this.userStatus === CommonStatus.ACTIVATED;
    }

    // Subscribe to loading state
    this.subscriptions.push(
      this.userAdminService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  submitAction(): void {
    if (this.isLoading) return;

    this.errorMessage = '';
    this.isLoading = true;

    if (this.isBulkAction) {
      // Handle bulk action
      this.processBulkAction();
    } else {
      // Handle single user action
      this.processSingleUserAction();
    }
  }

  private processSingleUserAction(): void {
    if (this.isBanning) {
      // Deactivate user
      this.userAdminService.deactivateUser(this.userId, this.reason).subscribe({
        next: (response) => {
          console.log('User deactivated successfully:', response);
          this.statusChanged.emit({
            userIds: [this.userId],
            status: CommonStatus.DEACTIVATED,
            reason: this.reason
          });
          this.closeModal();
        },
        error: (error) => {
          console.error('Error deactivating user:', error);
          this.errorMessage = error.message || 'Failed to deactivate user. Please try again.';
          this.isLoading = false;
        }
      });
    } else {
      // Activate user
      this.userAdminService.activateUser(this.userId).subscribe({
        next: (response) => {
          console.log('User activated successfully:', response);
          this.statusChanged.emit({
            userIds: [this.userId],
            status: CommonStatus.ACTIVATED
          });
          this.closeModal();
        },
        error: (error) => {
          console.error('Error activating user:', error);
          this.errorMessage = error.message || 'Failed to activate user. Please try again.';
          this.isLoading = false;
        }
      });
    }
  }

  private processBulkAction(): void {
    if (this.isBanning) {
      // Get users that are currently activated
      const usersToDeactivate = this.users
        .filter(user => this.userIds.includes(user.id) && user.status === CommonStatus.ACTIVATED)
        .map(user => user.id);

      if (usersToDeactivate.length === 0) {
        this.errorMessage = 'No active users selected to ban.';
        this.isLoading = false;
        return;
      }

      // Create an array of observables for each user to deactivate
      const requests = usersToDeactivate.map(userId =>
        this.userAdminService.deactivateUser(userId, this.reason)
      );

      // Use forkJoin to execute all requests in parallel
      forkJoin(requests).subscribe({
        next: (responses) => {
          console.log('Users deactivated successfully:', responses);
          this.statusChanged.emit({
            userIds: usersToDeactivate,
            status: CommonStatus.DEACTIVATED,
            reason: this.reason
          });
          this.closeModal();
        },
        error: (error) => {
          console.error('Error deactivating users:', error);
          this.errorMessage = error.message || 'Failed to deactivate some users. Please try again.';
          this.isLoading = false;
        }
      });
    } else {
      // Get users that are currently deactivated
      const usersToActivate = this.users
        .filter(user => this.userIds.includes(user.id) && user.status === CommonStatus.DEACTIVATED)
        .map(user => user.id);

      if (usersToActivate.length === 0) {
        this.errorMessage = 'No banned users selected to unban.';
        this.isLoading = false;
        return;
      }

      // Create an array of observables for each user to activate
      const requests = usersToActivate.map(userId =>
        this.userAdminService.activateUser(userId)
      );

      // Use forkJoin to execute all requests in parallel
      forkJoin(requests).subscribe({
        next: (responses) => {
          console.log('Users activated successfully:', responses);
          this.statusChanged.emit({
            userIds: usersToActivate,
            status: CommonStatus.ACTIVATED
          });
          this.closeModal();
        },
        error: (error) => {
          console.error('Error activating users:', error);
          this.errorMessage = error.message || 'Failed to activate some users. Please try again.';
          this.isLoading = false;
        }
      });
    }
  }

  closeModal(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close the modal if the user clicks on the overlay (outside the modal content)
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }
}
