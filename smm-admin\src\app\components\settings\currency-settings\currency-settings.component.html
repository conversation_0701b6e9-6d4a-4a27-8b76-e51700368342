<div class="currency-settings-overlay" (click)="onClose()">
  <div class="currency-settings-popup" (click)="$event.stopPropagation()">
    <!-- Header -->
    <div class="popup-header">
      <div class="header-content">
        <div class="header-icon">
          <fa-icon [icon]="['fas', 'money-bill-wave']"></fa-icon>
        </div>
        <div class="header-text">
          <h2 class="popup-title">{{ 'My currencies' | translate }}</h2>
          <p class="popup-subtitle">{{ 'Configure available currencies for your users' | translate }}</p>
        </div>
      </div>
      <button class="close-button" (click)="onClose()">
        <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>

    <!-- Main Currency Display -->
    <div class="main-currency-section">
      <div class="main-currency-item">
        <div class="currency-info">
          <span class="currency-code">USD</span>
          <span class="currency-name">US Dollar</span>
        </div>
        <div class="main-currency-badge">
          <fa-icon [icon]="['fas', 'star']"></fa-icon>
          <span>{{ 'Main currency' | translate }}</span>
        </div>
      </div>

      <!-- Secondary Currencies -->
      <div class="secondary-currencies" *ngIf="getSecondaryCurrencies().length > 0">
        <div class="secondary-currencies-header">
          <span class="secondary-title">{{ 'Secondary currencies' | translate }}</span>
          <span class="secondary-count">({{ getSecondaryCurrencies().length }})</span>
        </div>
        <div class="secondary-currencies-list">
          <div
            class="secondary-currency-item"
            *ngFor="let currency of getSecondaryCurrencies()"
          >
            <div class="currency-info">
              <span class="currency-code">{{ currency.code }}</span>
              <span class="currency-name">{{ currency.name }}</span>
            </div>
            <div class="currency-actions">
              <div class="currency-symbol" *ngIf="currency.symbol">
                {{ currency.symbol }}
              </div>
              <button
                class="remove-currency-btn"
                (click)="removeSecondaryCurrency(currency.code)"
                [title]="'Remove currency' | translate"
              >
                <fa-icon [icon]="['fas', 'times']"></fa-icon>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- No Secondary Currencies Message -->
      <div class="no-secondary-currencies" *ngIf="getSecondaryCurrencies().length === 0">
        <div class="no-currencies-content">
          <fa-icon [icon]="['fas', 'coins']" class="no-currencies-icon"></fa-icon>
          <span class="no-currencies-text">{{ 'No secondary currencies selected' | translate }}</span>
          <span class="no-currencies-hint">{{ 'Click "List of currencies" to add more' | translate }}</span>
        </div>
      </div>
    </div>

    <!-- List of Currencies Button -->
    <div class="list-currencies-section">
      <button class="list-currencies-button" (click)="openCurrencyListModal()">
        <fa-icon [icon]="['fas', 'list']"></fa-icon>
        <span>{{ 'List of currencies' | translate }}</span>
        <fa-icon [icon]="['fas', 'chevron-right']" class="arrow-icon"></fa-icon>
      </button>
    </div>

    <!-- Footer -->
    <div class="popup-footer">
      <button class="cancel-button" (click)="onClose()" [disabled]="isLoading">
        {{ 'Cancel' | translate }}
      </button>
      <button class="save-button" (click)="saveCurrencies()" [disabled]="isLoading">
        <fa-icon [icon]="['fas', 'save']" *ngIf="!isLoading"></fa-icon>
        <div class="spinner-small" *ngIf="isLoading"></div>
        {{ isLoading ? ('Saving...' | translate) : ('Save' | translate) }}
      </button>
    </div>
  </div>
</div>

<!-- Currency List Modal -->
<div class="currency-list-overlay" *ngIf="showCurrencyListModal" (click)="closeCurrencyListModal()">
  <div class="currency-list-modal" (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="modal-header">
      <div class="header-content">
        <div class="header-icon">
          <fa-icon [icon]="['fas', 'coins']"></fa-icon>
        </div>
        <div class="header-text">
          <h2 class="modal-title">{{ 'Selecting the secondary currencies' | translate }}</h2>
          <p class="modal-subtitle">{{ 'Choose additional currencies for your users' | translate }}</p>
        </div>
      </div>
      <button class="close-button" (click)="closeCurrencyListModal()">
        <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>

    <!-- Search Section -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <fa-icon [icon]="['fas', 'search']" class="search-icon"></fa-icon>
        <input
          type="text"
          class="search-input"
          [(ngModel)]="searchText"
          placeholder="{{ 'Search for currency' | translate }}"
        >
      </div>
    </div>

    <!-- Currency List -->
    <div class="modal-content">
      <div class="currency-grid" *ngIf="!isLoading">
        <div
          class="currency-card"
          *ngFor="let currency of filteredCurrencies"
          [class.selected]="isCurrencySelected(currency.code)"
          [class.disabled]="currency.code === 'USD'"
          (click)="toggleCurrency(currency.code)"
        >
          <div class="currency-info">
            <div class="currency-header">
              <span class="currency-code">{{ currency.code }}</span>
              <div class="currency-toggle">
                <div class="toggle-switch" [class.active]="isCurrencySelected(currency.code)">
                  <div class="toggle-circle"></div>
                </div>
              </div>
            </div>
            <span class="currency-name">{{ currency.name }}</span>
            <span class="currency-symbol" *ngIf="currency.symbol">{{ currency.symbol }}</span>
          </div>
        </div>
      </div>

      <!-- Loading -->
      <div class="loading-container" *ngIf="isLoading">
        <div class="spinner"></div>
        <span>{{ 'Loading currencies...' | translate }}</span>
      </div>

      <!-- No Results -->
      <div class="no-results" *ngIf="!isLoading && filteredCurrencies.length === 0">
        <fa-icon [icon]="['fas', 'search']" class="no-results-icon"></fa-icon>
        <p>{{ 'No currencies found' | translate }}</p>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="modal-footer">
      <div class="selected-count">
        {{ getSelectedCount() }} {{ 'currencies selected' | translate }}
      </div>
      <div class="footer-buttons">
        <button class="cancel-button" (click)="closeCurrencyListModal()">
          {{ 'Cancel' | translate }}
        </button>
        <button class="apply-button" (click)="applyCurrencySelection()">
          <fa-icon [icon]="['fas', 'check']"></fa-icon>
          {{ 'Apply' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
