.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal, 1000);
  padding: 20px;
  overflow-y: auto; /* Enable scrolling on the overlay */
  display: flex;
  align-items: flex-start;
  justify-content: center;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.modal-container {
  @apply w-full max-w-md mx-auto;
  margin: 20px 0;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.modal-body {
  @apply space-y-4;
}

.section-title {
  @apply text-base font-medium text-gray-700;
}

.app-download-section {
  @apply flex justify-center space-x-4 my-4;
}

.app-download-button {
  @apply flex items-center justify-center px-4 py-2 rounded-lg text-white font-medium transition-colors;
  min-width: 140px;
}

.app-download-button.android {
  @apply bg-[#3ddc84] hover:bg-[#32b36b];
}

.app-download-button.ios {
  @apply bg-[#000000] hover:bg-[#333333];
}

.app-download-button i {
  @apply mr-2 text-lg;
}

.instruction-text {
  @apply text-sm text-gray-600 text-center;
}

.qr-code-container {
  @apply flex justify-center items-center my-4;
  min-height: 200px;
}

.qr-code-image {
  @apply max-w-full;
  max-height: 200px;
}

.manual-entry-section {
  @apply my-4;
}

.alert-box {
  @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start;
}

.alert-icon {
  @apply flex items-center justify-center w-6 h-6 rounded-full bg-yellow-400 text-white font-bold mr-3 flex-shrink-0;
}

.alert-content {
  @apply text-sm text-gray-700;
}

.secret-key {
  @apply font-mono font-bold mt-1 text-gray-800;
}

.mfa-form {
  @apply mt-6 space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-[var(--gray-700)];
}

.form-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.input-error {
  @apply border-red-500;
}

.error-message {
  @apply text-red-500 text-xs;
}

.password-input-container {
  @apply relative;
}

.toggle-password {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700;
}

.form-actions {
  @apply flex justify-end mt-6;
}

.submit-button {
  @apply bg-[var(--primary)] text-white py-2 px-6 rounded-lg hover:opacity-90 transition-colors font-medium;
}

.submit-button:disabled {
  @apply opacity-70 cursor-not-allowed;
}

.loading-spinner {
  @apply flex justify-center items-center;
}

.spinner {
  @apply w-10 h-10 border-4 border-gray-200 rounded-full;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
}

.loading-spinner-small {
  @apply inline-block w-4 h-4 border-2 border-white rounded-full;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile-specific styles */
@media (max-width: 640px) {
  .overlay-black {
      @apply items-start;
    padding: 1rem 0;

  }

  .modal-container {
    width: 95%;
    max-width: 100%;
    margin: 10px 0;
  }

  .modal-content {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .app-download-section {
    @apply flex-col space-x-0 space-y-2;
  }

  .app-download-button {
    width: 100%;
  }

  .form-input {
    font-size: 16px; /* Prevents iOS zoom on focus */
  }

  /* Improve touch targets */
  .close-button, .toggle-password, .submit-button {
    min-height: 44px;
    min-width: 44px;
  }
}
