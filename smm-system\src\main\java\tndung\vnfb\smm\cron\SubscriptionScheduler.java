package tndung.vnfb.smm.cron;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.service.PanelNotificationService;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.service.TenantSubscriptionService;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!local") // Disable in local environment
public class SubscriptionScheduler {

    private final TenantSubscriptionService subscriptionService;
    private final TenantRepository tenantRepository;
    private final TenantService tenantService;
    private final PanelNotificationService panelNotificationService;
    private final RestTemplate restTemplate;

    @Value("${domain-manager.url:http://domain-manager:3000}")
    private String domainManagerUrl;

    // Run every day at 9 AM
    @Scheduled(cron = "0 0 9 * * *")
    public void updateExpiredTenants() {
        log.info("Starting scheduled task: Update expired tenants");
        try {
            int updatedCount = subscriptionService.updateExpiredTenants();
            log.info("Completed scheduled task: Updated {} tenants", updatedCount);
        } catch (Exception e) {
            log.error("Error in scheduled task: Update expired tenants", e);
        }
    }

    // Run every day at 10 AM
    @Scheduled(cron = "0 0 10 * * *")
    public void sendRenewalNotifications() {
        log.info("Starting scheduled task: Send renewal notifications");
        try {
            subscriptionService.sendRenewalNotifications();
            log.info("Completed scheduled task: Send renewal notifications");
        } catch (Exception e) {
            log.error("Error in scheduled task: Send renewal notifications", e);
        }
    }

    // Run every 3 minutes to check for auto-renewal
    @Scheduled(fixedRate = 180000) // 3 minutes = 180,000 milliseconds
    public void processAutoRenewals() {
        log.info("Starting scheduled task: Process auto-renewals");
        try {
            int renewedCount = subscriptionService.processAutoRenewals();
            log.info("Completed scheduled task: Auto-renewed {} tenants", renewedCount);
        } catch (Exception e) {
            log.error("Error in scheduled task: Process auto-renewals", e);
        }
    }

    // Run every 30 minutes to check for expired tenants and suspend them
    @Scheduled(fixedRate = 1800000) // 30 minutes = 1,800,000 milliseconds
    public void checkAndSuspendExpiredTenants() {
        log.info("Starting scheduled task: Check and suspend expired tenants");
        try {
            // Find all expired tenants that are still active
            List<Tenant> expiredTenants = tenantRepository.findExpiredTenants();
            log.info("Found {} expired tenants to process", expiredTenants.size());

            int processedCount = 0;
            for (Tenant tenant : expiredTenants) {
                try {
                    // Update tenant status to Expired
                    tenant.setStatus(TenantStatus.Expired);
                    tenantService.save(tenant);
                    log.info("Updated tenant {} status to Expired", tenant.getDomain());

                    // Call domain manager API to suspend the domain
                    String suspendUrl = domainManagerUrl + "/domains/" + tenant.getDomain() + "/suspend";
                    restTemplate.postForObject(suspendUrl, null, Object.class);
                    log.info("Successfully called suspend API for domain: {}", tenant.getDomain());

                    // Send expiration notification to user
                    panelNotificationService.sendExpirationNotification(tenant);
                    log.info("Sent expiration notification for tenant: {}", tenant.getDomain());

                    processedCount++;
                } catch (Exception e) {
                    log.error("Error processing expired tenant {}: {}", tenant.getDomain(), e.getMessage(), e);
                    // Continue with next tenant instead of failing the entire batch
                }
            }

            log.info("Completed scheduled task: Processed {} expired tenants", processedCount);
        } catch (Exception e) {
            log.error("Error in scheduled task: Check and suspend expired tenants", e);
        }
    }
}