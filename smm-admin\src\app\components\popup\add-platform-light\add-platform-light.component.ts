import { Component, EventEmitter, Output, HostListener, ElementRef, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { PlatformReq } from '../../../model/request/platform-req.model';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { fontAwesomeBrandsIcons, brandColors } from '../../../icons/icons.font-awesome-brands';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/fontawesome-svg-core';

@Component({
  selector: 'app-add-platform-light',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormsModule, ReactiveFormsModule, IconsModule],
  templateUrl: './add-platform-light.component.html',
  styleUrl: './add-platform-light.component.css'
})
export class AddPlatformLightComponent implements OnInit {
  @Input() platform: SuperPlatformRes | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() platformAdded = new EventEmitter<any>();

  platformForm: FormGroup;
  isSubmitting = false;
  errorMessage = '';
  showIconGrid = false;
  isEditMode = false;

  // We'll use brandIcons directly instead of a predefined list

  // All brand icons from Font Awesome
  brandIcons: { id: number, name: string, value: IconName }[] = [];

  // Default selected icon (no icon)
  selectedIcon = { id: 0, name: 'No icon', value: 'no-icon' as IconName };

  constructor(
    private fb: FormBuilder,
    private adminService: AdminServiceService,
    private elementRef: ElementRef
  ) {
    this.platformForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      icon: [0, Validators.required]
    });

    this.initializeBrandIcons();
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    if (this.platform) {
      this.isEditMode = true;

      // Find the icon in our brandIcons array or add it if it doesn't exist
      let iconObj = this.brandIcons.find(icon => icon.value === this.platform?.icon);

      if (!iconObj && this.platform.icon) {
        // Convert the icon name to the proper format
        const iconValue = this.toKebabCase(this.platform.icon) as IconName;

        // Create a new icon entry
        const newIcon = {
          id: this.brandIcons.length,
          name: this.platform.name,
          value: iconValue
        };

        // Add to the brandIcons array
        this.brandIcons.push(newIcon);
        iconObj = newIcon;
      }

      // Set the selected icon
      if (iconObj) {
        this.selectedIcon = iconObj;
      }

      // Populate the form with platform data
      this.platformForm.patchValue({
        name: this.platform.name,
        icon: this.selectedIcon.id
      });
    }
  }

  // Initialize all brand icons from Font Awesome
  private initializeBrandIcons() {
    // Add the "No icon" option as the first item
    this.brandIcons.push({
      id: 0,
      name: 'No icon',
      value: 'no-icon' as IconName
    });

    // Extract icon names from fontAwesomeBrandsIcons object
    let idCounter = 1;
    Object.keys(fontAwesomeBrandsIcons).forEach((key) => {
      // Convert from faIconName to iconName (e.g., faFacebook -> facebook)
      const iconValue = this.toKebabCase(key) as IconName;

      // Convert camelCase to normal text with spaces (e.g., facebookF -> Facebook F)
      const displayName = key.replace(/^fa/, '').charAt(0).toLowerCase() + key.replace(/^fa/, '').slice(1);

      this.brandIcons.push({
        id: idCounter++,
        name: displayName,
        value: iconValue
      });
    });

    // Sort icons alphabetically (except the first "No icon" option)
    const noIcon = this.brandIcons.shift();
    this.brandIcons.sort((a, b) => a.name.localeCompare(b.name));
    this.brandIcons.unshift(noIcon!);
  }

  onClose() {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent) {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  selectIcon(icon: any) {
    // Simply select the icon that was clicked
    this.selectedIcon = icon;
    this.platformForm.patchValue({ icon: icon.id });
    this.closeIconGrid();
  }

  toggleIconGrid(event?: MouseEvent) {
    this.showIconGrid = !this.showIconGrid;

    // Ensure the icon grid is positioned correctly when opened
    if (this.showIconGrid && event) {
      setTimeout(() => {
        const iconGridContainer = document.querySelector('.icon-grid-container') as HTMLElement;
        const iconSelector = document.querySelector('.selected-icon') as HTMLElement;

        if (iconGridContainer && iconSelector) {
          // Get the position of the icon selector
          const rect = iconSelector.getBoundingClientRect();

          // Position the grid above the selector
          // Get the actual height of the grid
          const gridHeight = Math.min(400, iconGridContainer.scrollHeight || 400);

          // Calculate position to place it above the selector
          // Add a small gap (10px) between the grid and the selector
          iconGridContainer.style.top = `${rect.top + window.scrollY - gridHeight - 10}px`;
          iconGridContainer.style.left = `${rect.left + window.scrollX}px`;
          iconGridContainer.style.width = `${rect.width}px`;

          // Ensure the grid is above all other elements
          iconGridContainer.style.zIndex = '10000';

          // Append to body to ensure it's not constrained by parent containers
          document.body.appendChild(iconGridContainer);
        }
      }, 0);
    }
  }

  // Check if an icon is valid in Font Awesome
  isValidIcon(iconName: string): boolean {
    return Object.keys(fontAwesomeBrandsIcons)
      .map(key => key.replace(/^fa/, '').toLowerCase())
      .includes(iconName.toLowerCase());
  }

  // Get color for an icon
  getIconColor(iconName: string): string {
    //const formattedName = iconName.charAt(0).toUpperCase() + iconName.slice(1);
    return brandColors[iconName] || '#000000';
  }

  // Convert camelCase to kebab-case for FontAwesome icons
  toKebabCase(str: string): IconName {
    str = str.replace(/^fa/, '');
    return str
      .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
      .replace(/([A-Z])([A-Z])(?=[a-z])/g, '$1-$2')
      .toLowerCase() as IconName;
  }

  // Close icon grid when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedInside = this.elementRef.nativeElement.contains(event.target);
    const iconGridContainer = document.querySelector('.icon-grid-container');
    const clickedInsideGrid = iconGridContainer && iconGridContainer.contains(event.target as Node);

    if (!clickedInside && !clickedInsideGrid && this.showIconGrid) {
      this.closeIconGrid();
    }
  }

  // Close the icon grid and clean up
  closeIconGrid() {
    this.showIconGrid = false;

    // Return the icon grid to its original container if it was moved to body
    setTimeout(() => {
      const iconGridContainer = document.querySelector('.icon-grid-container');
      const iconSelector = document.querySelector('.icon-selector');

      if (iconGridContainer && iconSelector && iconGridContainer.parentElement === document.body) {
        // Reset styles
        iconGridContainer.removeAttribute('style');

        // Move back to original container
        iconSelector.appendChild(iconGridContainer);
      }
    }, 100);
  }

  onSubmit() {
    if (this.platformForm.invalid) {
      this.markFormGroupTouched(this.platformForm);
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    // Get the selected icon value (string) instead of the ID (number)
    const iconValue = this.selectedIcon.id === 0 ? '' : this.selectedIcon.value;

    const platformData: PlatformReq = {
      name: this.platformForm.value.name,
      icon: iconValue
    };

    if (this.isEditMode && this.platform) {
      // Add the platform ID for update
      const updateData = {
        ...platformData,
        id: this.platform.id
      };

      this.adminService.updatePlatform(updateData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.platformAdded.emit(response);
          this.onClose();
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = error.message || 'Failed to update platform. Please try again.';
          console.error('Error updating platform:', error);
        }
      });
    } else {
      // Add new platform
      this.adminService.addPlatform(platformData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.platformAdded.emit(response);
          this.onClose();
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = error.message || 'Failed to add platform. Please try again.';
          console.error('Error adding platform:', error);
        }
      });
    }
  }

  // Helper method to mark all form controls as touched
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if ((control as any).controls) {
        this.markFormGroupTouched(control as FormGroup);
      }
    });
  }
}
