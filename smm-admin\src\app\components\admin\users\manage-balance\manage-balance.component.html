<div class="overlay-black" >
  <div class="modal-container bg-white text-gray-800 rounded-2xl shadow-lg w-[600px] max-w-full">
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold">Edit balance</h2>
      <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700">
        <fa-icon [icon]="['fas', 'times']" class="text-lg" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Current Balance -->
    <div class="p-6 text-center">
      <div class="text-4xl font-bold text-[var(--primary)]">${{ currentBalance }}</div>
    </div>

    <!-- Balance Actions -->
    <div class="px-6 pb-4">
      <div class="flex gap-4">
        <div class="flex-1">
          <input
            type="text"
            [(ngModel)]="amount"
            placeholder="Enter amount"
            class="w-full bg-[var(--background)] border border-gray-200 rounded-lg p-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
          >
        </div>
        <button
          (click)="addBalance()"
          class="bg-[var(--primary)] hover:bg-[var(--primary-hover)] text-white px-4 py-2 rounded-lg flex items-center"
        >
          <fa-icon [icon]="['fas', 'plus']" class="mr-2"></fa-icon>
          Add
        </button>
        <button
          (click)="removeBalance()"
          class="bg-[var(--error)] hover:opacity-90 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <fa-icon [icon]="['fas', 'minus']" class="mr-2"></fa-icon>
          Remove
        </button>
      </div>
    </div>

    <!-- Tabs -->
    <div class="px-6 pb-4">
      <div class="flex border-b border-gray-200">
        <button
          (click)="switchTab('payments')"
          class="py-2 px-4 font-medium"
          [ngClass]="{'text-[var(--primary)] border-b-2 border-[var(--primary)]': activeTab === 'payments', 'text-gray-500': activeTab !== 'payments'}"
        >
          Payments
        </button>
        <button
          (click)="switchTab('orders')"
          class="py-2 px-4 font-medium"
          [ngClass]="{'text-[var(--primary)] border-b-2 border-[var(--primary)]': activeTab === 'orders', 'text-gray-500': activeTab !== 'orders'}"
        >
          Orders
        </button>
      </div>
    </div>

    <!-- Search -->
    <div class="px-6 pb-4">
      <div class="relative">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Search by note or transaction ID"
          class="w-full bg-[var(--background)] border border-gray-200 rounded-lg p-3 pl-10 text-gray-800 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-[var(--primary)]"
          (keyup.enter)="searchTransactions()"
        >
        <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"></fa-icon>
        <button (click)="searchTransactions()" class="absolute right-3 top-1/2 transform -translate-y-1/2">
          <fa-icon [icon]="['fas', 'search']" class="text-[var(--primary)]"></fa-icon>
        </button>
      </div>
    </div>

    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="px-6 pb-6 flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
    </div>

    <!-- Transaction History -->
    <div *ngIf="!isLoading" class="px-6 pb-6 max-h-[300px] overflow-y-auto">
      <div *ngFor="let transaction of transactions" class="py-3 border-b border-gray-200 last:border-0">
        <div class="flex justify-between items-center">
          <div class="flex flex-col">
            <span class="text-sm text-gray-500">{{ formatDate(transaction.created_at) }}</span>
            <span [ngClass]="{'text-[var(--success)]': getTransactionType(transaction) === 'add', 'text-[var(--error)]': getTransactionType(transaction) === 'remove'}" class="font-medium">
              {{ getFormattedAmount(transaction) }}
            </span>
            <span class="text-xs text-gray-500 mt-1">{{ transaction.note }}</span>
          </div>
          <div class="text-gray-500 text-sm">
            <span class="px-2 py-1 rounded-full text-xs"
                  [ngClass]="{
                    'bg-[var(--success-light)] text-[var(--success)]': transaction.source === 'BONUS',
                    'bg-[var(--warning-light)] text-[var(--warning)]': transaction.source === 'MANUAL',
                    'bg-[var(--primary-light)] text-[var(--primary)]': transaction.source === 'SYSTEM',
                    'bg-[var(--error-light)] text-[var(--error)]': transaction.source === 'REMOVE' || transaction.source === 'PAYMENT',
               
                  }">
              {{ transaction.source }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div *ngIf="transactions.length === 0" class="py-6 text-center text-gray-500">
        No transactions found
      </div>

      <!-- Pagination -->
      <div *ngIf="transactions.length > 0" class="flex justify-between items-center mt-4">
        <div class="text-sm text-gray-500">
          Showing {{ pagination.pageNumber * pagination.pageSize + 1 }} to
          {{ (pagination.pageNumber * pagination.pageSize) + transactions.length }} of
          {{ pagination.totalElements }} transactions
        </div>
        <div class="flex space-x-2">
          <button
            [disabled]="pagination.pageNumber === 0"
            [ngClass]="{'opacity-50 cursor-not-allowed': pagination.pageNumber === 0}"
            (click)="loadPreviousPage()"
            class="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-700 hover:bg-gray-50">
            Previous
          </button>
          <button
            [disabled]="pagination.pageNumber >= pagination.totalPages - 1"
            [ngClass]="{'opacity-50 cursor-not-allowed': pagination.pageNumber >= pagination.totalPages - 1}"
            (click)="loadNextPage()"
            class="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-700 hover:bg-gray-50">
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
