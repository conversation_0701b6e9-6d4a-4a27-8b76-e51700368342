/**
 * File system utilities
 */

const fs = require('fs');
const path = require('path');
const logger = require('./logger');

class FileUtils {
  /**
   * Ensure directory exists, create if it doesn't
   * @param {string} dirPath - Directory path to ensure
   */
  static ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      logger.info(`Creating directory: ${dirPath}`);
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  /**
   * Create a safe filename from domain name
   * @param {string} domain - Domain name
   * @returns {string} Safe filename
   */
  static createSafeFileName(domain) {
    return domain.replace(/[^a-zA-Z0-9.-]/g, '_').replace(/\./g, '-');
  }

  /**
   * Clean domain name by removing prefixes
   * @param {string} domain - Domain name
   * @returns {string} Clean domain name
   */
  static cleanDomainName(domain) {
    return domain.replace(/^(success-|failure-)/, '');
  }

  /**
   * Check if file exists
   * @param {string} filePath - File path to check
   * @returns {boolean} True if file exists
   */
  static fileExists(filePath) {
    return fs.existsSync(filePath);
  }

  /**
   * Check if multiple files exist
   * @param {string[]} filePaths - Array of file paths to check
   * @returns {boolean} True if all files exist
   */
  static filesExist(filePaths) {
    return filePaths.every(filePath => fs.existsSync(filePath));
  }

  /**
   * Write file with error handling
   * @param {string} filePath - File path
   * @param {string} content - File content
   * @param {string} description - Description for logging
   */
  static writeFile(filePath, content, description = 'file') {
    try {
      fs.writeFileSync(filePath, content);
      logger.success(`${description} written successfully: ${filePath}`);
    } catch (error) {
      logger.error(`Error writing ${description}: ${error.message}`, { filePath });
      throw error;
    }
  }

  /**
   * Append to file with error handling
   * @param {string} filePath - File path
   * @param {string} content - Content to append
   * @param {string} description - Description for logging
   */
  static appendFile(filePath, content, description = 'content') {
    try {
      fs.appendFileSync(filePath, content);
      logger.success(`${description} appended successfully: ${filePath}`);
    } catch (error) {
      logger.error(`Error appending ${description}: ${error.message}`, { filePath });
      throw error;
    }
  }

  /**
   * Read file with error handling
   * @param {string} filePath - File path
   * @param {string} encoding - File encoding (default: utf8)
   * @returns {string} File content
   */
  static readFile(filePath, encoding = 'utf8') {
    try {
      return fs.readFileSync(filePath, encoding);
    } catch (error) {
      logger.error(`Error reading file: ${error.message}`, { filePath });
      throw error;
    }
  }

  /**
   * Delete file with error handling
   * @param {string} filePath - File path
   * @param {string} description - Description for logging
   */
  static deleteFile(filePath, description = 'file') {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        logger.success(`${description} deleted successfully: ${filePath}`);
      } else {
        logger.warn(`${description} not found: ${filePath}`);
      }
    } catch (error) {
      logger.error(`Error deleting ${description}: ${error.message}`, { filePath });
      throw error;
    }
  }

  /**
   * Copy file with error handling
   * @param {string} sourcePath - Source file path
   * @param {string} destPath - Destination file path
   * @param {string} description - Description for logging
   */
  static copyFile(sourcePath, destPath, description = 'file') {
    try {
      fs.copyFileSync(sourcePath, destPath);
      logger.success(`${description} copied successfully: ${sourcePath} -> ${destPath}`);
    } catch (error) {
      logger.error(`Error copying ${description}: ${error.message}`, { sourcePath, destPath });
      throw error;
    }
  }

  /**
   * Delete directory recursively with error handling
   * @param {string} dirPath - Directory path
   * @param {string} description - Description for logging
   */
  static deleteDirectory(dirPath, description = 'directory') {
    try {
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        files.forEach(file => {
          const filePath = path.join(dirPath, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            logger.debug(`Deleted file: ${filePath}`);
          }
        });
        fs.rmdirSync(dirPath);
        logger.success(`${description} deleted successfully: ${dirPath}`);
      } else {
        logger.warn(`${description} not found: ${dirPath}`);
      }
    } catch (error) {
      logger.error(`Error deleting ${description}: ${error.message}`, { dirPath });
      throw error;
    }
  }
}

module.exports = FileUtils;
