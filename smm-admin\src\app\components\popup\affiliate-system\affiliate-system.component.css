.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg overflow-hidden;
}

.modal-header {
  @apply flex justify-between items-center p-6 border-b border-gray-200;
}

.icon-container {
  @apply w-10 h-10 rounded-full bg-amber-500 flex items-center justify-center mr-3;
}

.modal-title {
  @apply text-xl font-semibold text-gray-800;
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
}

.description-section {
  @apply px-6 py-4 bg-gray-50 text-gray-600 text-sm;
}

.settings-form {
  @apply p-6 space-y-6;
}

.setting-item {
  @apply flex justify-between items-center;
}

.setting-label {
  @apply text-sm font-medium text-gray-700;
}

/* Toggle Switch */
.toggle-switch {
  @apply relative inline-block;
}

.switch {
  @apply relative inline-block w-12 h-6;
}

.switch input {
  @apply opacity-0 w-0 h-0;
}

.slider {
  @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 transition-all duration-300 rounded-full;
}

.slider:before {
  @apply absolute content-[''] h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full;
}

input:checked + .slider {
  @apply bg-green-500;
}

input:checked + .slider:before {
  @apply transform translate-x-6;
}

/* Percentage Input */
.input-container {
  @apply relative;
}

.percentage-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent;
  max-width: 100px;
}

/* Footer */
.modal-footer {
  @apply p-6 border-t border-gray-200 flex justify-end;
}

.save-button {
  @apply px-6 py-2 bg-[var(--primary)] text-white rounded-lg text-sm font-medium hover:bg-[var(--primary-hover)] transition-colors;
}
