.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.special-prices-table table {
  @apply w-full border-collapse;
}

.special-prices-table th {
  @apply text-sm font-medium text-gray-700;
}

.action-menu-button {
  @apply p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors;
}

.dropdown-menu {
  @apply shadow-lg rounded-lg overflow-hidden;
}

.add-special-price-button {
  @apply w-full flex items-center justify-center bg-[#0095f6] text-white font-medium py-3 px-4 rounded-lg hover:bg-blue-600 active:bg-blue-700 transition-colors duration-300;
}

.loading-spinner {
  @apply inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}
