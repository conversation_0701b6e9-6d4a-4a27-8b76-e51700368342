import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, finalize, switchMap, tap } from 'rxjs';
import { MyTransactionRes, TransactionSearchReq } from '../../model/response/my-transaction.model';
import { TransactionPageRes } from '../../model/response/transaction.model';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class TransactionService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _transactions$ = new BehaviorSubject<MyTransactionRes[]>([]);
  private _search$ = new Subject<TransactionSearchReq>();
  private _pagination$ = new BehaviorSubject<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Set up subscription for search
    this._search$
      .pipe(
        tap(() => this._loading$.next(true)),
        switchMap(filter => this.searchTransactions(filter))
      )
      .subscribe({
        next: (result: TransactionPageRes) => {
          this._transactions$.next(result.content as MyTransactionRes[]);
          this._pagination$.next({
            pageNumber: result.number,
            pageSize: result.size,
            totalElements: result.total_elements,
            totalPages: result.total_pages
          });
          this._loading$.next(false);
        },
        error: (error) => {
          console.error('Error searching transactions:', error);
          this._loading$.next(false);
          this._transactions$.next([]);
        }
      });
  }

  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  get transactions$(): Observable<MyTransactionRes[]> {
    return this._transactions$.asObservable();
  }

  get pagination$(): Observable<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }> {
    return this._pagination$.asObservable();
  }

  /**
   * Trigger a search for transactions with filters
   * @param filter The search filter
   */
  search(filter: TransactionSearchReq = {}): void {
    this._search$.next(filter);
  }

  /**
   * Search transactions with filters
   * @param filter Search filter
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Observable of paginated transaction results
   */
  searchTransactions(filter: TransactionSearchReq = {}, page: number = 0, size: number = 10): Observable<TransactionPageRes> {
    this._loading$.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (filter.from) {
      params = params.set('from', filter.from);
    }

    if (filter.to) {
      params = params.set('to', filter.to);
    }

    if (filter.orderId) {
      params = params.set('orderId', filter.orderId.toString());
    }

    if (filter.types && filter.types.length > 0) {
      filter.types.forEach(type => {
        params = params.append('types', type);
      });
    }

    return this.http.get<TransactionPageRes>(
      `${this.configService.apiUrl}/users/me/transactions`,
      { params }
    ).pipe(
      finalize(() => this._loading$.next(false))
    );
  }

  /**
   * Load transactions with pagination
   * @param page Page number (0-based)
   * @param size Page size
   */
  loadTransactions(page: number = 0, size: number = 10): void {
    const filter: TransactionSearchReq = {};
    this.searchTransactions(filter, page, size).subscribe({
      next: (result: TransactionPageRes) => {
        this._transactions$.next(result.content as MyTransactionRes[]);
        this._pagination$.next({
          pageNumber: result.number,
          pageSize: result.size,
          totalElements: result.total_elements,
          totalPages: result.total_pages
        });
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        this._transactions$.next([]);
      }
    });
  }

  /**
   * Change page
   * @param page New page number (0-based)
   */
  changePage(page: number): void {
    const currentPagination = this._pagination$.value;
    this.loadTransactions(page, currentPagination.pageSize);
  }

  /**
   * Change page size
   * @param size New page size
   */
  changePageSize(size: number): void {
    this.loadTransactions(0, size);
  }
}
