# User Notification Integration Test Guide

## Tổng quan
Hướng dẫn test chức năng thông báo user khi admin nạp tiền hoặc khi có hoàn tiền.

## Các trường hợp test

### 1. Admin nạp tiền cho user
**Endpoint**: `PUT /users/{userId}/add-fund`

**Request Body**:
```json
{
  "amount": 100.00,
  "source": "BONUS",
  "note": "Added by admin for user testing"
}
```

**Expected Result**:
- User balance tăng $100.00
- Tạo transaction record với type=Bonus, source=BONUS
- **Gửi notification**: "Thưởng: Bạn đã nhận được thưởng $100.00. Added by admin for user testing"

### 2. Admin nạp tiền qua BANK source
**Request Body**:
```json
{
  "amount": 50.00,
  "source": "BANK",
  "note": "Bank deposit via PayPal"
}
```

**Expected Result**:
- **Gửi notification**: "Nạp tiền thành công: Tà<PERSON> khoản của bạn đã được nạp thêm $50.00. Bank deposit via PayPal"

### 3. Hoàn tiền khi cancel order
**Trigger**: Khi order status chuyển thành CANCELED

**Expected Result**:
- User balance tăng theo số tiền hoàn
- **Gửi notification**: "Hoàn tiền: Bạn đã được hoàn $XX.XX. Cancel refund for order #123"

### 4. Hoàn tiền khi order failed
**Trigger**: Khi order status chuyển thành FAILED

**Expected Result**:
- User balance tăng theo số tiền hoàn
- **Gửi notification**: "Hoàn tiền: Bạn đã được hoàn $XX.XX. Failed order refund for order #123"

## Test Steps

### Frontend Test (@smm-dashboard)

1. **Login vào dashboard**
2. **Kiểm tra notification bell icon** - phải hiển thị số lượng unread
3. **Click vào notification dropdown** - phải hiển thị danh sách notifications
4. **Test filter** - chuyển đổi giữa "Tất cả" và "Chưa đọc"
5. **Test mark as read** - click vào notification để đánh dấu đã đọc
6. **Test mark all as read** - click button "Đánh dấu đã đọc"

### Backend Test (@smm-system)

1. **Test API endpoints**:
   ```bash
   # Get unread count
   GET /user-notifications/unread/count
   
   # Get unread notifications
   GET /user-notifications/unread
   
   # Get all notifications (paginated)
   GET /user-notifications?page=0&size=20
   
   # Mark as read
   POST /user-notifications/{id}/read
   
   # Mark all as read
   POST /user-notifications/read-all
   ```

2. **Test balance operations**:
   ```bash
   # Admin add funds
   PUT /users/{userId}/add-fund
   {
     "amount": 100.00,
     "source": "BONUS",
     "note": "Test notification"
   }
   ```

### Database Verification

1. **Check user_notification table**:
   ```sql
   SELECT * FROM user_notification 
   WHERE user_id = {userId} 
   ORDER BY created_at DESC;
   ```

2. **Check transaction table**:
   ```sql
   SELECT * FROM g_transaction 
   WHERE user_id = {userId} 
   ORDER BY created_at DESC;
   ```

## Expected Notification Messages

| Action | Source | Title | Content Pattern |
|--------|--------|-------|-----------------|
| Admin Add (BONUS) | BONUS | Thưởng | Bạn đã nhận được thưởng $XX.XX. {note} |
| Admin Add (BANK) | BANK | Nạp tiền thành công | Tài khoản của bạn đã được nạp thêm $XX.XX. {note} |
| Order Refund | CANCELED | Hoàn tiền | Bạn đã được hoàn $XX.XX. {note} |
| Order Failed | CANCELED | Hoàn tiền | Bạn đã được hoàn $XX.XX. {note} |
| Other Add | OTHER | Số dư tăng | Tài khoản của bạn đã được cộng thêm $XX.XX. {note} |

## Troubleshooting

### Notification không hiển thị
1. Check database - có record trong user_notification table không?
2. Check tenant_id - đúng tenant không?
3. Check user_id - đúng user không?
4. Check API response - có lỗi không?

### Notification hiển thị sai format
1. Check NumberFormat.getCurrencyInstance(Locale.US)
2. Check TransactionSource mapping
3. Check note content

### Frontend không update real-time
1. Check polling interval (30 seconds)
2. Check unread count API
3. Check subscription cleanup

## Performance Notes

- Notifications được cache trong Redis
- Auto-polling mỗi 30 giây
- Database có indexes tối ưu
- Exception handling không ảnh hưởng main transaction

## Security Notes

- Notifications chỉ hiển thị cho đúng user
- Tenant isolation được đảm bảo
- API endpoints có authentication
- Sensitive data không lưu trong notification content
