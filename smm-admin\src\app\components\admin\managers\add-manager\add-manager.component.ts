import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AddManagerReq } from '../../../../model/request/add-manager-req.model';
import { ManagerRes } from '../../../../model/response/manager-res.model';
import { ManagerService } from '../../../../core/services/manager.service';
import { ToastService } from '../../../../core/services/toast.service';
import { TenantService, TenantInfo } from '../../../../core/services/tenant.service';
import { finalize } from 'rxjs/operators';

interface PanelOption {
  id: string;
  name: string;
  checked: boolean;
}

@Component({
  selector: 'app-add-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule
  ],
  templateUrl: './add-manager.component.html',
  styleUrls: ['./add-manager.component.css']
})
export class AddManagerComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() managerAdded = new EventEmitter<ManagerRes>();

  // Form fields
  login: string = '';
  password: string = '';
  selectedRole: string = 'Admin';

  // Available roles
  roles = [
    { id: 'Admin', name: 'Admin', description: 'Same rights as of main account', enabled: true },
    { id: 'Moderator', name: 'Moderator', description: 'Cant see balance, providers and panel settings. Can work with orders, edit services and answer in support.', enabled: false },
    { id: 'Agent', name: 'Agent', description: 'Can only answer in support, see orders and services but cant edit. Doesn\'t see providers and panel settings.', enabled: false }
  ];

  // Available panels (loaded from accessible tenants)
  panels: PanelOption[] = [];

  // Loading state
  loading: boolean = false;
  errorMessage: string = '';

  constructor(
    private managerService: ManagerService,
    private toastService: ToastService,
    private tenantService: TenantService
  ) {}

  ngOnInit(): void {
    // Disable scrolling on the body when the popup is opened
    document.body.style.overflow = 'hidden';

    // Load accessible tenants
    this.loadAccessibleTenants();
  }

  private loadAccessibleTenants(): void {
    this.tenantService.getAccessibleTenants().subscribe({
      next: (tenants: TenantInfo[]) => {
        this.panels = tenants.map(tenant => ({
          id: tenant.id,
          name: tenant.domain,
          checked: false
        }));
      },
      error: (error) => {
        console.error('Error loading accessible tenants:', error);
        this.toastService.showError('Failed to load accessible panels');
      }
    });
  }

  selectRole(roleId: string): void {
    const role = this.roles.find(r => r.id === roleId);
    if (role && role.enabled) {
      this.selectedRole = roleId;
    }
  }

  togglePanel(panelId: string): void {
    const panel = this.panels.find(p => p.id === panelId);
    if (panel) {
      panel.checked = !panel.checked;
    }
  }

  addManager(): void {
    if (!this.validateForm()) {
      return;
    }

    this.loading = true;
    this.errorMessage = '';

    const selectedPanels = this.panels
      .filter(panel => panel.checked)
      .map(panel => panel.id);

    const request: AddManagerReq = {
      login: this.login,
      password: this.password,
      role: this.selectedRole,
      accessiblePanels: selectedPanels
    };

    this.managerService.addManager(request)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.toastService.showSuccess('Manager added successfully');
            this.managerAdded.emit(response.data);
            this.closeModal();
          } else {
            this.errorMessage = response.message || 'Failed to add manager';
          }
        },
        error: (error) => {
          console.error('Error adding manager:', error);
          this.errorMessage = error?.message || 'Failed to add manager. Please try again.';
          this.toastService.showError(this.errorMessage);
        }
      });
  }

  private validateForm(): boolean {
    if (!this.login.trim()) {
      this.errorMessage = 'Login is required';
      return false;
    }

    if (!this.password.trim()) {
      this.errorMessage = 'Password is required';
      return false;
    }

    if (this.password.length < 6) {
      this.errorMessage = 'Password must be at least 6 characters';
      return false;
    }

    const selectedPanels = this.panels.filter(panel => panel.checked);
    if (selectedPanels.length === 0) {
      this.errorMessage = 'Please select at least one accessible panel';
      return false;
    }

    return true;
  }

  closeModal(): void {
    // Enable scrolling on the body when the popup is closed
    document.body.style.overflow = 'auto';
    this.close.emit();
  }
}
