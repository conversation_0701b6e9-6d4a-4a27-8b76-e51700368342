package tndung.vnfb.smm.constant.enums;

import lombok.Getter;

@Getter
public enum OrderTag {
    REFILL("Refill"),
    CANCEL("Cancel");

    private final String value;

    OrderTag(final String value) {
        this.value = value;
    }

    public static OrderTag findByValue(String value) {
        for(OrderTag e : values()) {
            if(e.value.equals(value)) return e;
        }
        return null;
    }
}
