<div ngClass="relative inline-block text-left w-full {{customClassDropdown}}" >
  <!-- Button -->
  <button
   type="button"
   ngClass="w-full dropdown-container p-4 {{customClassButton}}"
    (click)="toggleDropdown($event)">

    <div class="flex items-center">
      <!-- Hiển thị icon đã chọn -->

      <app-social-icon *ngIf="selectedOption?.icon" zClass="mr-2" [icon]="selectedOption?.icon  || 'facebook'" ></app-social-icon>
      <span>{{ (selectedOption?.label || placeholder) | translate }}</span>
    </div>

    <!-- Dropdown Arrow -->
    <fa-icon class="absolute right-5 top-1/2 transform -translate-y-1/2 w-5 h-5"
    [icon]='["fas", "angle-down"]'></fa-icon>
  </button>

  <!-- Dropdown list - Using fixed positioning to render outside of parent container -->
  <div *ngIf="isOpen" class="dropdown-menu-container" (click)="$event.stopPropagation()">
    <ul>
      <li *ngFor="let option of options"
          (click)="selectOption(option, $event)"
          class="px-4 py-4 flex items-center cursor-pointer hover:bg-gray-100">

        <!-- Icon bên trái -->

        <app-social-icon *ngIf="option?.icon" zClass="mr-2" [icon]="option?.icon  || 'facebook'"></app-social-icon>
        <span>{{ option.label | translate }}</span>

      </li>
    </ul>
  </div>

  <!-- This is a hidden element that will be used to create a portal for the dropdown -->
  <div class="dropdown-portal-anchor" style="display: none;"></div>
</div>
