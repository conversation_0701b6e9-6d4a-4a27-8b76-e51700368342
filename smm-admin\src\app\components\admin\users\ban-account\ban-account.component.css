.overlay-black {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal, 1000);
}

.modal-container {
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

button {
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.98);
}

/* Custom scrollbar */
.modal-container::-webkit-scrollbar {
  width: 6px;
}

.modal-container::-webkit-scrollbar-track {
  background: var(--background, #f5f7fc);
  border-radius: 10px;
}

.modal-container::-webkit-scrollbar-thumb {
  background: var(--gray-300, #dae4fc);
  border-radius: 10px;
}

.modal-container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400, #dae4fc);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-container {
    width: 95%;
    max-width: 95%;
    border-radius: 16px;
  }

  .flex.gap-4 {
    flex-direction: column;
  }

  button {
    width: 100%;
    margin-top: 8px;
  }
}