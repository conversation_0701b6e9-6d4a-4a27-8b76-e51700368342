# NonUniqueResultException Fix Guide

## Vấn đề
Lỗi `javax.persistence.NonUniqueResultException: query did not return a unique result: 26` x<PERSON>y ra khi query `findTopByUserIdOrderByCreatedAtDesc` trả về nhiều kết quả thay vì 1 kết quả duy nhất.

## Nguyên nhân
1. **Method name không đúng**: `findTopByUserIdOrderByCreatedAtDesc` không có `Limit` trong Spring Data JPA
2. **Duplicate timestamps**: Nhiều transactions có cùng `created_at` timestamp
3. **Query không unique**: Thiếu secondary sort để đảm bảo unique result

## Giải pháp đã áp dụng

### 1. Sửa TransactionRepository
**Trước**:
```java
@Query("SELECT t FROM GTransaction t WHERE t.userId = :userId AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt DESC")
Optional<GTransaction> findTopByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);
```

**Sau**:
```java
// Method 1: Sử dụng Pageable để limit kết quả
@Query("SELECT t FROM GTransaction t WHERE t.userId = :userId AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt DESC, t.id DESC")
List<GTransaction> findTopByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

// Method 2: Sử dụng native query với LIMIT 1
@Query(value = "SELECT * FROM g_transaction WHERE user_id = :userId AND tenant_id = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY created_at DESC, id DESC LIMIT 1", nativeQuery = true)
Optional<GTransaction> findLatestByUserId(@Param("userId") Long userId);
```

### 2. Cập nhật GUserServiceImpl
**Trước**:
```java
final GTransaction latestTransaction = transactionRepository
    .findTopByUserIdOrderByCreatedAtDesc(updatedUser.getId())
    .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TRANSACTION_NOT_FOUND));
```

**Sau**:
```java
// Sử dụng method mới với native query
final GTransaction latestTransaction = transactionRepository
    .findLatestByUserId(updatedUser.getId())
    .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TRANSACTION_NOT_FOUND));
```

## Cải tiến chính

### 1. Secondary Sort
- Thêm `t.id DESC` để đảm bảo unique ordering
- Tránh trường hợp nhiều records có cùng `created_at`

### 2. Native Query với LIMIT
- Sử dụng `LIMIT 1` để đảm bảo chỉ trả về 1 record
- Performance tốt hơn so với Pageable

### 3. Error Handling
- Giữ nguyên exception handling
- Đảm bảo backward compatibility

## Testing

### 1. Test Case 1: Normal scenario
```java
@Test
void findLatestByUserId_ShouldReturnLatestTransaction() {
    // Given: User có nhiều transactions
    Long userId = 1L;
    
    // When: Gọi findLatestByUserId
    Optional<GTransaction> result = transactionRepository.findLatestByUserId(userId);
    
    // Then: Trả về transaction mới nhất
    assertTrue(result.isPresent());
    assertEquals(latestTransactionId, result.get().getId());
}
```

### 2. Test Case 2: Multiple transactions with same timestamp
```java
@Test
void findLatestByUserId_WithSameTimestamp_ShouldReturnHighestId() {
    // Given: Nhiều transactions có cùng created_at
    // When: Gọi findLatestByUserId
    // Then: Trả về transaction có ID cao nhất
}
```

### 3. Test Case 3: No transactions
```java
@Test
void findLatestByUserId_NoTransactions_ShouldReturnEmpty() {
    // Given: User không có transactions
    // When: Gọi findLatestByUserId
    // Then: Trả về Optional.empty()
}
```

## Performance Impact

### Before Fix
- Query có thể trả về nhiều results
- Hibernate phải process tất cả results rồi throw exception
- Waste memory và CPU

### After Fix
- Native query với LIMIT 1
- Database chỉ trả về 1 record
- Optimal performance

## Database Indexes
Để tối ưu performance, đảm bảo có indexes:

```sql
-- Composite index cho query performance
CREATE INDEX idx_transaction_user_tenant_created 
ON g_transaction(user_id, tenant_id, created_at DESC, id DESC);

-- Hoặc separate indexes
CREATE INDEX idx_transaction_user_id ON g_transaction(user_id);
CREATE INDEX idx_transaction_tenant_id ON g_transaction(tenant_id);
CREATE INDEX idx_transaction_created_at ON g_transaction(created_at);
```

## Best Practices

### 1. Always use secondary sort
```java
// BAD: Có thể có duplicate timestamps
ORDER BY t.createdAt DESC

// GOOD: Đảm bảo unique ordering
ORDER BY t.createdAt DESC, t.id DESC
```

### 2. Use LIMIT for single result queries
```java
// BAD: Có thể trả về nhiều results
@Query("SELECT t FROM Table t WHERE condition ORDER BY t.date DESC")
Optional<Table> findLatest();

// GOOD: Đảm bảo chỉ 1 result
@Query(value = "SELECT * FROM table WHERE condition ORDER BY date DESC LIMIT 1", nativeQuery = true)
Optional<Table> findLatest();
```

### 3. Handle edge cases
```java
// Always check for empty results
Optional<GTransaction> result = repository.findLatest();
if (result.isEmpty()) {
    throw new NotFoundException("No transactions found");
}
```

## Monitoring
- Monitor query performance sau khi fix
- Check application logs để đảm bảo không còn NonUniqueResultException
- Verify notification functionality hoạt động bình thường

## Rollback Plan
Nếu có vấn đề, có thể rollback về:
1. Sử dụng Pageable approach thay vì native query
2. Thêm additional filtering conditions
3. Revert về original method với proper error handling
