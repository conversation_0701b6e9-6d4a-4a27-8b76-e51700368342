<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-2xl w-full max-w-md p-6 shadow-lg">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">
        <ng-container *ngIf="changeKeyMode">Change API Key</ng-container>
        <ng-container *ngIf="!changeKeyMode">{{ isEdit ? 'Edit Provider' : 'Add Provider' }}</ng-container>
      </h2>
      <button class="text-gray-500 hover:text-gray-700" (click)="closePopup()">
        <fa-icon [icon]="['fas', 'times']" class="text-lg"></fa-icon>
      </button>
    </div>

    <!-- Form -->
    <form (ngSubmit)="saveProvider()">
      <!-- Provider URL - Only show if not in changeKeyMode -->
      <div class="mb-4" *ngIf="!changeKeyMode">
        <label for="url" class="block text-sm font-medium text-gray-700 mb-1">API URL</label>
        <input
          type="text"
          id="url"
          name="url"
          [(ngModel)]="url"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="https://example.com/api"
          required>
      </div>

      <!-- API Key -->
      <div class="mb-6">
        <label for="secretKey" class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
        <input
          type="text"
          id="secretKey"
          name="secretKey"
          [(ngModel)]="secretKey"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter API key"
          required>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <!-- Buttons -->
      <div class="flex justify-end gap-3">
        <button
          type="button"
          class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
          [disabled]="isLoading"
          (click)="closePopup()">
          Cancel
        </button>
        <button
          type="submit"
          [disabled]="isLoading"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center">
          <span *ngIf="isLoading" class="mr-2 animate-spin">
            <fa-icon [icon]="['fas', 'spinner']"></fa-icon>
          </span>
          <ng-container *ngIf="changeKeyMode">Update Key</ng-container>
          <ng-container *ngIf="!changeKeyMode">{{ isEdit ? 'Update' : 'Add' }}</ng-container>
        </button>
      </div>
    </form>
  </div>
</div>
