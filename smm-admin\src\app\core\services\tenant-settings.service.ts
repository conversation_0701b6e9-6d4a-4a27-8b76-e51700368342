import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { TenantLanguageSettingsReq } from '../../model/request/tenant-language-settings-req.model';
import { TenantLanguageSettingsRes } from '../../model/response/tenant-language-settings-res.model';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class TenantSettingsService {


  constructor(private http: HttpClient, private configService: ConfigService) {}

  /**
   * Get current tenant language settings
   */
  getLanguageSettings(): Observable<TenantLanguageSettingsRes> {
    return this.http.get<TenantLanguageSettingsRes>(`${this.configService.apiUrl}/tenant-settings/language`);
  }

  /**
   * Update tenant language settings
   */
  updateLanguageSettings(request: TenantLanguageSettingsReq): Observable<TenantLanguageSettingsRes> {
    return this.http.put<TenantLanguageSettingsRes>(`${this.configService.apiUrl}/tenant-settings/language`, request);
  }

  /**
   * Get tenant default language (public endpoint, no auth required)
   */
  getTenantDefaultLanguage(): Observable<{default_language: string}> {
    return this.http.get<{default_language: string}>(`${this.configService.apiUrl}/tenant-settings/language/default`);
  }

  /**
   * Get tenant available languages (public endpoint, no auth required)
   */
  getTenantAvailableLanguages(): Observable<string[]> {
    return this.http.get<string[]>(`${this.configService.apiUrl}/tenant-settings/language/available`);
  }
}
