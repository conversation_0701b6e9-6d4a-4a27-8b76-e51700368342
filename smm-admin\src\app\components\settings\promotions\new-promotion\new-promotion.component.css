.page-container {
  @apply max-w-full mx-auto md:p-6 ;
}

.content-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  @apply md:p-6 py-6;
  border-bottom: 1px solid #e5e7eb;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  background-color: #f3f4f6;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #e5e7eb;
}

.page-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.promotion-form-container {
  @apply md:p-6 py-4;
}

.promotion-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  transition: border-color 0.2s;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 1px #4f46e5;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.input-error {
  border-color: #ef4444 !important;
}

.validation-error {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.date-input-container {
  flex: 1;
}

.date-separator {
  font-size: 0.875rem;
  color: #6b7280;
}

.percentage-input-container {
  position: relative;
  max-width: 200px;
}

.percentage-input {
  padding-right: 2rem;
}

.percentage-symbol {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 0.875rem;
}

.save-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
  align-self: flex-start;
}

.save-button:hover:not(:disabled) {
  background-color: #4338ca;
}

.save-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .date-range-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .date-input-container {
    width: 100%;
  }
  
  .date-separator {
    margin-left: 0.5rem;
  }
  
  .save-button {
    width: 100%;
  }
}
