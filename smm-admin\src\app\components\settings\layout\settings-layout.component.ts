import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SettingsSidebarService } from '../../../core/services/settings-sidebar.service';
import { SettingsSidebarComponent } from '../sidebar/settings-sidebar.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
  selector: 'app-settings-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SettingsSidebarComponent,
    FontAwesomeModule
  ],
  templateUrl: './settings-layout.component.html',
  styleUrl: './settings-layout.component.css'
})
export class SettingsLayoutComponent implements OnInit {
  isOpen: boolean = false;

  constructor(private sidebarService: SettingsSidebarService) {}

  ngOnInit() {
    // Initialize sidebar state based on screen size
    this.isOpen = window.innerWidth >= 768;

    // Subscribe to sidebar service state changes
    this.sidebarService.sidebarOpen$.subscribe(state => {
      this.isOpen = state;
    });

    // Set initial state in service
    this.sidebarService.setSidebarState(this.isOpen);
  }

  toggleSidebar() {
    this.isOpen = !this.isOpen;
    this.sidebarService.setSidebarState(this.isOpen);
  }

  closeSidebar() {
    this.isOpen = false;
    this.sidebarService.setSidebarState(false);
  }
}
