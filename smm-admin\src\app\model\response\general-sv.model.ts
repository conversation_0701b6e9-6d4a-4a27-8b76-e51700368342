/**
 * Base model for service response with default values
 */
export interface GeneralSvRes {
  id: number;
  name: string;
  description: string;
  price: number;
  add_type: string;
  type: string;
  min: number;
  max: number;
  icon: string;
  hide: boolean;
  is_favorite: boolean;
  sort: number;
  category_id: number;
  sample_link?: string;
  speed_per_day?: number;
  cancel_button: boolean;
}

/**
 * Factory for creating GeneralSvRes instances with default values
 */
export class GeneralSvResFactory {
  /**
   * Creates a new GeneralSvRes with default values
   * @returns A new GeneralSvRes instance with default values
   */
  static createDefault(): GeneralSvRes {
    return {
      id: 0,
      name: '',
      description: '',
      price: 0,
      add_type: 'MANUAL',
      type: '',
      min: 1000,
      max: 5000,
      icon: '',
      hide: false,
      is_favorite: false,
      sort: 0,
      category_id: 0,
      sample_link: '',
      cancel_button: false,
    };
  }

  /**
   * Creates a new GeneralSvRes with custom values
   * @param data Partial data to override default values
   * @returns A new GeneralSvRes instance with custom values
   */
  static create(data: Partial<GeneralSvRes>): GeneralSvRes {
    return { ...this.createDefault(), ...data };
  }
}
