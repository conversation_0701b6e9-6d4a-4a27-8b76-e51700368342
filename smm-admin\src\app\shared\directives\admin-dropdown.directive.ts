import { Directive, ElementRef, Input, OnInit, OnDestroy, Renderer2, HostListener, Output, EventEmitter } from '@angular/core';
import { AdminDropdownService, DropdownOptions } from '../../core/services/admin-dropdown.service';
import { Subscription } from 'rxjs';

@Directive({
  selector: '[appAdminDropdown]',
  standalone: true
})
export class AdminDropdownDirective implements OnInit, OnDestroy {
  @Input() dropdownId: string = '';
  @Input() dropdownTrigger: ElementRef | HTMLElement | null = null;
  @Input() dropdownOptions: DropdownOptions = {};
  @Input() isOpen: boolean = false;

  @Output() dropdownClosed = new EventEmitter<void>();

  private closeSubscription: Subscription | null = null;
  private dropdownElement: HTMLElement | null = null;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private adminDropdownService: AdminDropdownService
  ) {}

  ngOnInit() {
    // Generate a dropdown ID if not provided
    if (!this.dropdownId) {
      this.dropdownId = this.adminDropdownService.generateDropdownId();
    }

    // Store reference to the dropdown element
    this.dropdownElement = this.el.nativeElement;

    // Subscribe to close all dropdowns event
    this.closeSubscription = this.adminDropdownService.closeAllDropdowns.subscribe(() => {
      this.close();
    });

    // Set initial position if dropdown is open
    if (this.isOpen && this.dropdownTrigger) {
      this.updatePosition();
    }

    // Add class for styling
    this.renderer.addClass(this.dropdownElement, 'admin-dropdown-menu');

    // Ensure high z-index
    this.renderer.setStyle(this.dropdownElement, 'z-index', '99999');

    // Ensure the dropdown is positioned correctly
    this.renderer.setStyle(this.dropdownElement, 'position', 'absolute');
  }

  ngOnDestroy() {
    if (this.closeSubscription) {
      this.closeSubscription.unsubscribe();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Check if click is outside the dropdown and its trigger
    const target = event.target as HTMLElement;
    const isInsideDropdown = this.dropdownElement && this.dropdownElement.contains(target);
    const triggerEl = this.dropdownTrigger instanceof ElementRef
      ? this.dropdownTrigger.nativeElement
      : this.dropdownTrigger;
    const isInsideTrigger = triggerEl && triggerEl.contains(target);

    if (!isInsideDropdown && !isInsideTrigger) {
      this.close();
    }
  }

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    // Prevent clicks inside the dropdown from closing it
    event.stopPropagation();
  }

  @HostListener('window:resize')
  onWindowResize() {
    if (this.isOpen && this.dropdownTrigger) {
      this.updatePosition();
    }
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    if (this.isOpen && this.dropdownTrigger) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        this.updatePosition();
      });
    }
  }

  open() {
    if (!this.isOpen) {
      this.isOpen = true;

      // Register with the dropdown service
      this.adminDropdownService.openDropdown(this.dropdownId, {
        close: () => this.close(),
        updatePosition: () => this.updatePosition()
      });

      // Update position
      if (this.dropdownTrigger) {
        // Use requestAnimationFrame to ensure the dropdown is rendered before positioning
        requestAnimationFrame(() => {
          // Move the dropdown to the document body to prevent clipping
          if (this.dropdownElement && this.dropdownElement.parentElement !== document.body) {
            document.body.appendChild(this.dropdownElement);
          }

          this.updatePosition();
        });
      }
    }
  }

  close() {
    if (this.isOpen) {
      this.isOpen = false;
      this.adminDropdownService.closeDropdown(this.dropdownId);
      this.dropdownClosed.emit();

      // Remove the dropdown from the document body if it was moved there
      if (this.dropdownElement && this.dropdownElement.parentElement === document.body) {
        document.body.removeChild(this.dropdownElement);
      }
    }
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  updatePosition() {
    if (!this.dropdownElement || !this.dropdownTrigger) return;

    // Add a class to the dropdown element to ensure it's positioned correctly
    this.renderer.addClass(this.dropdownElement, 'admin-dropdown-menu');

    // Ensure high z-index
    this.renderer.setStyle(this.dropdownElement, 'z-index', '99999');

    // Ensure the dropdown is positioned correctly
    this.renderer.setStyle(this.dropdownElement, 'position', 'absolute');

    // Add the dropdown to the document body to ensure it's not clipped by parent containers
    if (this.dropdownElement.parentElement !== document.body) {
      const triggerEl = this.dropdownTrigger instanceof ElementRef
        ? this.dropdownTrigger.nativeElement
        : this.dropdownTrigger;

      const triggerRect = triggerEl.getBoundingClientRect();
      const scrollX = window.scrollX || window.pageXOffset;
      const scrollY = window.scrollY || window.pageYOffset;

      // Position relative to the trigger element but in document coordinates
      this.renderer.setStyle(this.dropdownElement, 'top', `${triggerRect.bottom + scrollY}px`);
      this.renderer.setStyle(this.dropdownElement, 'left', `${triggerRect.left + scrollX}px`);
      this.renderer.setStyle(this.dropdownElement, 'width', `${triggerRect.width}px`);
    } else {
      // If already in the body, use the service to update position
      this.adminDropdownService.updateDropdownPosition(
        this.dropdownId,
        this.dropdownTrigger,
        this.dropdownElement,
        this.dropdownOptions
      );
    }
  }
}
