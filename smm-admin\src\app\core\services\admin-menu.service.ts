import { Injectable, ElementRef } from '@angular/core';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AdminDropdownService, DropdownOptions } from './admin-dropdown.service';

export interface MenuAction {
  id: string;
  label: string;
  icon: IconName;
  iconColor: string;
  divider?: boolean;
  disabled?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AdminMenuService {
  // User menu actions
  private userMenuActions: MenuAction[] = [
    { id: 'manage-balance', label: 'Manage balance', icon: 'wallet', iconColor: 'text-blue-500' },
    { id: 'edit-user', label: 'Edit user', icon: 'pen', iconColor: 'text-blue-500' },
    { id: 'user-orders', label: 'User orders', icon: 'shopping-cart', iconColor: 'text-blue-500' },
    { id: 'payments-history', label: 'Payments history', icon: 'history', iconColor: 'text-blue-500' },
    { id: 'referral-system', label: 'Referral system', icon: 'users', iconColor: 'text-blue-500' },
    { id: 'discount-prices', label: 'Discount and prices', icon: 'tag', iconColor: 'text-blue-500' },
    { id: 'reset-password', label: 'Reset password', icon: 'key', iconColor: 'text-blue-500' },
    { id: 'login-as-user', label: 'Login as user', icon: 'sign-in-alt', iconColor: 'text-blue-500' },
    { id: 'ban-account', label: 'Ban account', icon: 'ban', iconColor: 'text-red-500' }
  ];

  // Order menu actions
  private orderMenuActions: MenuAction[] = [
    { id: 'mark-completed', label: 'Mark as completed', icon: 'check', iconColor: 'text-green-500' },
    { id: 'resend-provider', label: 'Resend to provider', icon: 'paper-plane', iconColor: 'text-blue-500' },
    { id: 'set-start-count', label: 'Set start count', icon: 'play', iconColor: 'text-blue-500' },
    { id: 'edit-order-link', label: 'Edit order link', icon: 'pen', iconColor: 'text-blue-500' },
    { id: 'cancel-partially', label: 'Cancel partially', icon: 'ban', iconColor: 'text-red-500' },
    { id: 'cancel-refund', label: 'Cancel with refund', icon: 'times-circle', iconColor: 'text-red-500' }
  ];

  // Support ticket menu actions
  private ticketMenuActions: MenuAction[] = [
    { id: 'view-details', label: 'View details', icon: 'eye', iconColor: 'text-blue-500' },
    { id: 'mark-accepted', label: 'Mark as accepted', icon: 'check', iconColor: 'text-green-500' },
    { id: 'mark-solved', label: 'Mark as solved', icon: 'check-circle', iconColor: 'text-green-500' },
    { id: 'mark-closed', label: 'Mark as closed', icon: 'times-circle', iconColor: 'text-red-500' }
  ];

  // Service menu actions
  private serviceMenuActions: MenuAction[] = [
    { id: 'edit-service', label: 'Edit service', icon: 'pen', iconColor: 'text-blue-500' },
    { id: 'duplicate-service', label: 'Duplicate service', icon: 'copy', iconColor: 'text-blue-500' },
    { id: 'toggle-status', label: 'Toggle status', icon: 'toggle-on', iconColor: 'text-blue-500' },
    { id: 'delete-service', label: 'Delete service', icon: 'trash', iconColor: 'text-red-500' }
  ];

  constructor(private adminDropdownService: AdminDropdownService) { }

  getUserMenuActions(): MenuAction[] {
    return this.userMenuActions;
  }

  getOrderMenuActions(): MenuAction[] {
    return this.orderMenuActions;
  }

  getTicketMenuActions(): MenuAction[] {
    return this.ticketMenuActions;
  }

  getServiceMenuActions(): MenuAction[] {
    return this.serviceMenuActions;
  }

  // Calculate menu position based on button element using the AdminDropdownService
  calculateMenuPosition(
    triggerElement: ElementRef | HTMLElement,
    dropdownElement: HTMLElement,
    options: DropdownOptions = {}
  ): { top: number, left: number } {
    // Default options for admin menus
    const defaultOptions: DropdownOptions = {
      placement: 'bottom-right',
      offset: { x: 0, y: 5 },
      width: '224px',
      maxHeight: '400px'
    };

    // Merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };

    return this.adminDropdownService.calculatePosition(triggerElement, dropdownElement, mergedOptions);
  }
}
