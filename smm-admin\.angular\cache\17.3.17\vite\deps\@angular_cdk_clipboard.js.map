{"version": 3, "sources": ["../../../../../node_modules/@angular/cdk/fesm2022/clipboard.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, EventEmitter, Directive, Optional, Input, Output, NgModule } from '@angular/core';\n\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\nclass PendingCopy {\n  constructor(text, _document) {\n    this._document = _document;\n    const textarea = this._textarea = this._document.createElement('textarea');\n    const styles = textarea.style;\n    // Hide the element for display and accessibility. Set a fixed position so the page layout\n    // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n    // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n    styles.position = 'fixed';\n    styles.top = styles.opacity = '0';\n    styles.left = '-999em';\n    textarea.setAttribute('aria-hidden', 'true');\n    textarea.value = text;\n    // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n    textarea.readOnly = true;\n    // The element needs to be inserted into the fullscreen container, if the page\n    // is in fullscreen mode, otherwise the browser won't execute the copy command.\n    (this._document.fullscreenElement || this._document.body).appendChild(textarea);\n  }\n  /** Finishes copying the text. */\n  copy() {\n    const textarea = this._textarea;\n    let successful = false;\n    try {\n      // Older browsers could throw if copy is not supported.\n      if (textarea) {\n        const currentFocus = this._document.activeElement;\n        textarea.select();\n        textarea.setSelectionRange(0, textarea.value.length);\n        successful = this._document.execCommand('copy');\n        if (currentFocus) {\n          currentFocus.focus();\n        }\n      }\n    } catch {\n      // Discard error.\n      // Initial setting of {@code successful} will represent failure here.\n    }\n    return successful;\n  }\n  /** Cleans up DOM changes used to perform the copy operation. */\n  destroy() {\n    const textarea = this._textarea;\n    if (textarea) {\n      textarea.remove();\n      this._textarea = undefined;\n    }\n  }\n}\n\n/**\n * A service for copying text to the clipboard.\n */\nclass Clipboard {\n  constructor(document) {\n    this._document = document;\n  }\n  /**\n   * Copies the provided text into the user's clipboard.\n   *\n   * @param text The string to copy.\n   * @returns Whether the operation was successful.\n   */\n  copy(text) {\n    const pendingCopy = this.beginCopy(text);\n    const successful = pendingCopy.copy();\n    pendingCopy.destroy();\n    return successful;\n  }\n  /**\n   * Prepares a string to be copied later. This is useful for large strings\n   * which take too long to successfully render and be copied in the same tick.\n   *\n   * The caller must call `destroy` on the returned `PendingCopy`.\n   *\n   * @param text The string to copy.\n   * @returns the pending copy operation.\n   */\n  beginCopy(text) {\n    return new PendingCopy(text, this._document);\n  }\n  static {\n    this.ɵfac = function Clipboard_Factory(t) {\n      return new (t || Clipboard)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Clipboard,\n      factory: Clipboard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Clipboard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\nconst CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken('CDK_COPY_TO_CLIPBOARD_CONFIG');\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\nclass CdkCopyToClipboard {\n  constructor(_clipboard, _ngZone, config) {\n    this._clipboard = _clipboard;\n    this._ngZone = _ngZone;\n    /** Content to be copied. */\n    this.text = '';\n    /**\n     * How many times to attempt to copy the text. This may be necessary for longer text, because\n     * the browser needs time to fill an intermediate textarea element and copy the content.\n     */\n    this.attempts = 1;\n    /**\n     * Emits when some text is copied to the clipboard. The\n     * emitted value indicates whether copying was successful.\n     */\n    this.copied = new EventEmitter();\n    /** Copies that are currently being attempted. */\n    this._pending = new Set();\n    if (config && config.attempts != null) {\n      this.attempts = config.attempts;\n    }\n  }\n  /** Copies the current text to the clipboard. */\n  copy(attempts = this.attempts) {\n    if (attempts > 1) {\n      let remainingAttempts = attempts;\n      const pending = this._clipboard.beginCopy(this.text);\n      this._pending.add(pending);\n      const attempt = () => {\n        const successful = pending.copy();\n        if (!successful && --remainingAttempts && !this._destroyed) {\n          // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n          this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n        } else {\n          this._currentTimeout = null;\n          this._pending.delete(pending);\n          pending.destroy();\n          this.copied.emit(successful);\n        }\n      };\n      attempt();\n    } else {\n      this.copied.emit(this._clipboard.copy(this.text));\n    }\n  }\n  ngOnDestroy() {\n    if (this._currentTimeout) {\n      clearTimeout(this._currentTimeout);\n    }\n    this._pending.forEach(copy => copy.destroy());\n    this._pending.clear();\n    this._destroyed = true;\n  }\n  static {\n    this.ɵfac = function CdkCopyToClipboard_Factory(t) {\n      return new (t || CdkCopyToClipboard)(i0.ɵɵdirectiveInject(Clipboard), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(CDK_COPY_TO_CLIPBOARD_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCopyToClipboard,\n      selectors: [[\"\", \"cdkCopyToClipboard\", \"\"]],\n      hostBindings: function CdkCopyToClipboard_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkCopyToClipboard_click_HostBindingHandler() {\n            return ctx.copy();\n          });\n        }\n      },\n      inputs: {\n        text: [i0.ɵɵInputFlags.None, \"cdkCopyToClipboard\", \"text\"],\n        attempts: [i0.ɵɵInputFlags.None, \"cdkCopyToClipboardAttempts\", \"attempts\"]\n      },\n      outputs: {\n        copied: \"cdkCopyToClipboardCopied\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCopyToClipboard, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCopyToClipboard]',\n      host: {\n        '(click)': 'copy()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: Clipboard\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_COPY_TO_CLIPBOARD_CONFIG]\n    }]\n  }], {\n    text: [{\n      type: Input,\n      args: ['cdkCopyToClipboard']\n    }],\n    attempts: [{\n      type: Input,\n      args: ['cdkCopyToClipboardAttempts']\n    }],\n    copied: [{\n      type: Output,\n      args: ['cdkCopyToClipboardCopied']\n    }]\n  });\n})();\nclass ClipboardModule {\n  static {\n    this.ɵfac = function ClipboardModule_Factory(t) {\n      return new (t || ClipboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ClipboardModule,\n      imports: [CdkCopyToClipboard],\n      exports: [CdkCopyToClipboard]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkCopyToClipboard],\n      exports: [CdkCopyToClipboard]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_COPY_TO_CLIPBOARD_CONFIG, CdkCopyToClipboard, Clipboard, ClipboardModule, PendingCopy };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,MAAM,WAAW;AAC3B,SAAK,YAAY;AACjB,UAAM,WAAW,KAAK,YAAY,KAAK,UAAU,cAAc,UAAU;AACzE,UAAM,SAAS,SAAS;AAIxB,WAAO,WAAW;AAClB,WAAO,MAAM,OAAO,UAAU;AAC9B,WAAO,OAAO;AACd,aAAS,aAAa,eAAe,MAAM;AAC3C,aAAS,QAAQ;AAEjB,aAAS,WAAW;AAGpB,KAAC,KAAK,UAAU,qBAAqB,KAAK,UAAU,MAAM,YAAY,QAAQ;AAAA,EAChF;AAAA;AAAA,EAEA,OAAO;AACL,UAAM,WAAW,KAAK;AACtB,QAAI,aAAa;AACjB,QAAI;AAEF,UAAI,UAAU;AACZ,cAAM,eAAe,KAAK,UAAU;AACpC,iBAAS,OAAO;AAChB,iBAAS,kBAAkB,GAAG,SAAS,MAAM,MAAM;AACnD,qBAAa,KAAK,UAAU,YAAY,MAAM;AAC9C,YAAI,cAAc;AAChB,uBAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF,QAAQ;AAAA,IAGR;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,eAAS,OAAO;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACF;AAKA,IAAM,aAAN,MAAM,WAAU;AAAA,EACd,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,MAAM;AACT,UAAM,cAAc,KAAK,UAAU,IAAI;AACvC,UAAM,aAAa,YAAY,KAAK;AACpC,gBAAY,QAAQ;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,MAAM;AACd,WAAO,IAAI,YAAY,MAAM,KAAK,SAAS;AAAA,EAC7C;AAaF;AAXI,WAAK,OAAO,SAAS,kBAAkB,GAAG;AACxC,SAAO,KAAK,KAAK,YAAc,SAAS,QAAQ,CAAC;AACnD;AAGA,WAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,WAAU;AAAA,EACnB,YAAY;AACd,CAAC;AAtCL,IAAM,YAAN;AAAA,CAyCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,+BAA+B,IAAI,eAAe,8BAA8B;AAKtF,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,YAAY,SAAS,QAAQ;AACvC,SAAK,aAAa;AAClB,SAAK,UAAU;AAEf,SAAK,OAAO;AAKZ,SAAK,WAAW;AAKhB,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,WAAW,oBAAI,IAAI;AACxB,QAAI,UAAU,OAAO,YAAY,MAAM;AACrC,WAAK,WAAW,OAAO;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,KAAK,WAAW,KAAK,UAAU;AAC7B,QAAI,WAAW,GAAG;AAChB,UAAI,oBAAoB;AACxB,YAAM,UAAU,KAAK,WAAW,UAAU,KAAK,IAAI;AACnD,WAAK,SAAS,IAAI,OAAO;AACzB,YAAM,UAAU,MAAM;AACpB,cAAM,aAAa,QAAQ,KAAK;AAChC,YAAI,CAAC,cAAc,EAAE,qBAAqB,CAAC,KAAK,YAAY;AAE1D,eAAK,kBAAkB,KAAK,QAAQ,kBAAkB,MAAM,WAAW,SAAS,CAAC,CAAC;AAAA,QACpF,OAAO;AACL,eAAK,kBAAkB;AACvB,eAAK,SAAS,OAAO,OAAO;AAC5B,kBAAQ,QAAQ;AAChB,eAAK,OAAO,KAAK,UAAU;AAAA,QAC7B;AAAA,MACF;AACA,cAAQ;AAAA,IACV,OAAO;AACL,WAAK,OAAO,KAAK,KAAK,WAAW,KAAK,KAAK,IAAI,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,iBAAiB;AACxB,mBAAa,KAAK,eAAe;AAAA,IACnC;AACA,SAAK,SAAS,QAAQ,UAAQ,KAAK,QAAQ,CAAC;AAC5C,SAAK,SAAS,MAAM;AACpB,SAAK,aAAa;AAAA,EACpB;AA2BF;AAzBI,oBAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,SAAO,KAAK,KAAK,qBAAuB,kBAAkB,SAAS,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,8BAA8B,CAAC,CAAC;AAC9J;AAGA,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EAC1C,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,8CAA8C;AAC5E,eAAO,IAAI,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAI,WAAa,MAAM,sBAAsB,MAAM;AAAA,IACzD,UAAU,CAAI,WAAa,MAAM,8BAA8B,UAAU;AAAA,EAC3E;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AACd,CAAC;AA7EL,IAAM,qBAAN;AAAA,CAgFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,iBAAgB;AAgBtB;AAdI,iBAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,SAAO,KAAK,KAAK,kBAAiB;AACpC;AAGA,iBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,kBAAkB;AAAA,EAC5B,SAAS,CAAC,kBAAkB;AAC9B,CAAC;AAGD,iBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAdrD,IAAM,kBAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB;AAAA,MAC5B,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}