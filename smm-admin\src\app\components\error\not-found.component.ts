import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  template: `
    <div class="error-container">
      <div class="error-card">
        <div class="error-icon">404</div>
        <h1 class="error-title">{{ 'error.not_found_title' | translate }}</h1>
        <p class="error-message">{{ 'error.not_found_message' | translate }}</p>
        <div class="error-actions">
          <button (click)="goBack()" class="btn-secondary">
            {{ 'error.go_back' | translate }}
          </button>
          <button (click)="goHome()" class="btn-primary">
            {{ 'error.go_home' | translate }}
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .error-container {
      @apply flex items-center justify-center min-h-[80vh] w-full;
    }
    
    .error-card {
      @apply bg-white rounded-xl shadow-md p-8 max-w-md w-full text-center;
    }
    
    .error-icon {
      @apply text-[var(--primary)] text-8xl font-bold mb-4;
    }
    
    .error-title {
      @apply text-2xl font-bold text-gray-800 mb-2;
    }
    
    .error-message {
      @apply text-gray-600 mb-8;
    }
    
    .error-actions {
      @apply flex flex-col sm:flex-row gap-4 justify-center;
    }
    
    .btn-primary {
      @apply bg-[var(--primary)] text-white px-6 py-3 rounded-lg font-medium hover:bg-[var(--primary-hover)] transition-colors;
    }
    
    .btn-secondary {
      @apply bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors;
    }
  `]
})
export class NotFoundComponent {
  constructor(private router: Router) {}

  goBack(): void {
    window.history.back();
  }

  goHome(): void {
    this.router.navigate(['/panel']);
  }
}
