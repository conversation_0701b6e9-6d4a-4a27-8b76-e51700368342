package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.UserNotificationReq;
import tndung.vnfb.smm.dto.UserNotificationRes;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.UserNotification;
import tndung.vnfb.smm.mapper.UserNotificationMapper;
import tndung.vnfb.smm.repository.tenant.UserNotificationRepository;
import tndung.vnfb.smm.service.AuthenticationFacade;
import tndung.vnfb.smm.service.UserNotificationService;


import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@Slf4j
public class UserNotificationServiceImpl implements UserNotificationService {

    private final UserNotificationRepository userNotificationRepository;
    private final UserNotificationMapper userNotificationMapper;
    private final RedisTemplate<String, String> redisTemplate;
    private final AuthenticationFacade authenticationFacade;

    private static final String REDIS_KEY_PREFIX = "user_notification:read:";

    @Override
    public UserNotificationRes createNotification(UserNotificationReq req) {
        UserNotification notification = userNotificationMapper.toEntity(req);
        notification.setTenantId(TenantContext.getSiteTenant());

        UserNotification saved = userNotificationRepository.save(notification);
        return userNotificationMapper.toDto(saved);
    }

    @Override
    public Page<UserNotificationRes> getNotifications(Pageable pageable) {
        String tenantId = TenantContext.getSiteTenant();
        Optional<GUser> currentUser = authenticationFacade.getCurrentUser();
        
        if (currentUser.isPresent()) {
            Page<UserNotification> notifications = userNotificationRepository
                    .findByTenantIdAndUserIdOrderByCreatedAtDesc(tenantId, currentUser.get().getId(), pageable);
            return notifications.map(userNotificationMapper::toDto);
        } else {
            Page<UserNotification> notifications = userNotificationRepository
                    .findByTenantIdOrderByCreatedAtDesc(tenantId, pageable);
            return notifications.map(userNotificationMapper::toDto);
        }
    }

    @Override
    public List<UserNotificationRes> getUnreadNotifications() {
        String tenantId = TenantContext.getSiteTenant();
        Optional<GUser> currentUser = authenticationFacade.getCurrentUser();
        
        if (currentUser.isPresent()) {
            List<UserNotification> notifications = userNotificationRepository
                    .findUnreadByTenantIdAndUserId(tenantId, currentUser.get().getId());
            return userNotificationMapper.toDto(notifications);
        } else {
            List<UserNotification> notifications = userNotificationRepository
                    .findUnreadByTenantId(tenantId);
            return userNotificationMapper.toDto(notifications);
        }
    }

    @Override
    public long getUnreadCount() {
        String tenantId = TenantContext.getSiteTenant();
        Optional<GUser> currentUser = authenticationFacade.getCurrentUser();

        return currentUser.map(user -> userNotificationRepository.countUnreadByTenantIdAndUserId(tenantId, user.getId())).orElseGet(() -> userNotificationRepository.countUnreadByTenantId(tenantId));
    }

    @Override
    public void markAsRead(Long notificationId) {
        Optional<UserNotification> notificationOpt = userNotificationRepository.findById(notificationId);
        if (notificationOpt.isPresent()) {
            UserNotification notification = notificationOpt.get();
            notification.setIsRead(true);
            userNotificationRepository.save(notification);
            
            // Also store in Redis for caching
            Optional<GUser> currentUser = authenticationFacade.getCurrentUser();
            if (currentUser.isPresent()) {
                String redisKey = REDIS_KEY_PREFIX + currentUser.get().getId();
                redisTemplate.opsForSet().add(redisKey, notificationId.toString());
            }
        }
    }

    @Override
    public void markAllAsRead() {
        String tenantId = TenantContext.getSiteTenant();
        Optional<GUser> currentUser = authenticationFacade.getCurrentUser();
        
        if (currentUser.isPresent()) {
            List<UserNotification> unreadNotifications = userNotificationRepository
                    .findUnreadByTenantIdAndUserId(tenantId, currentUser.get().getId());
            
            unreadNotifications.forEach(notification -> {
                notification.setIsRead(true);
                userNotificationRepository.save(notification);
            });
        }
    }

    @Override
    public void createOrderNotification(GUser user, String orderDetails) {
        UserNotificationReq req = new UserNotificationReq();
        req.setTitle("Đơn hàng mới");
        req.setContent(orderDetails);
        req.setType(UserNotification.NotificationType.INFO);
        req.setCategory(UserNotification.NotificationCategory.ORDER);
        req.setUserId(user.getId());
        
        createNotification(req);
    }

    @Override
    public void createBalanceNotification(GUser user, String balanceDetails) {
        UserNotificationReq req = new UserNotificationReq();
        req.setTitle("Thay đổi số dư");
        req.setContent(balanceDetails);
        req.setType(UserNotification.NotificationType.SUCCESS);
        req.setCategory(UserNotification.NotificationCategory.BALANCE);
        req.setUserId(user.getId());
        
        createNotification(req);
    }

    @Override
    public void createSystemNotification(GUser user, String message) {
        UserNotificationReq req = new UserNotificationReq();
        req.setTitle("Thông báo hệ thống");
        req.setContent(message);
        req.setType(UserNotification.NotificationType.INFO);
        req.setCategory(UserNotification.NotificationCategory.SYSTEM);
        req.setUserId(user.getId());
        
        createNotification(req);
    }
}
