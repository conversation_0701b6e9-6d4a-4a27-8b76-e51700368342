package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.dto.UserNotificationReq;
import tndung.vnfb.smm.dto.UserNotificationRes;
import tndung.vnfb.smm.service.UserNotificationService;

import java.util.List;

@RestController
@RequestMapping("/v1/user-notifications")
@RequiredArgsConstructor
public class UserNotificationController {

    private final UserNotificationService userNotificationService;

    @PostMapping
    public ResponseEntity<UserNotificationRes> createNotification(@RequestBody UserNotificationReq req) {
        UserNotificationRes notification = userNotificationService.createNotification(req);
        return ResponseEntity.ok(notification);
    }

    @GetMapping
    public ResponseEntity<Page<UserNotificationRes>> getNotifications(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<UserNotificationRes> notifications = userNotificationService.getNotifications(pageable);
        return ResponseEntity.ok(notifications);
    }

    @GetMapping("/unread")
    public ResponseEntity<List<UserNotificationRes>> getUnreadNotifications() {
        List<UserNotificationRes> notifications = userNotificationService.getUnreadNotifications();
        return ResponseEntity.ok(notifications);
    }

    @GetMapping("/unread/count")
    public ResponseEntity<Long> getUnreadCount() {
        long count = userNotificationService.getUnreadCount();
        return ResponseEntity.ok(count);
    }

    @PostMapping("/{id}/read")
    public ResponseEntity<String> markAsRead(@PathVariable Long id) {
        userNotificationService.markAsRead(id);
        return ResponseEntity.ok("Notification marked as read");
    }

    @PostMapping("/read-all")
    public ResponseEntity<String> markAllAsRead() {
        userNotificationService.markAllAsRead();
        return ResponseEntity.ok("All notifications marked as read");
    }
}
