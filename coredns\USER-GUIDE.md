# Domain Nameserver Setup Guide

This guide explains how to set up your domain to use our nameservers for your child panel.

## Step 1: Purchase a Domain

If you don't already have a domain, you'll need to purchase one from a domain registrar such as:

- Namecheap
- GoDaddy
- Google Domains
- Cloudflare Registrar
- Name.com

## Step 2: Change Your Domain's Nameservers

After purchasing your domain, you need to change its nameservers to point to our system:

1. Log in to your domain registrar's website
2. Find the domain management or DNS settings section
3. Look for "Nameservers" or "DNS Servers" settings
4. Change the nameservers to:
   - ns1.autovnfb.com
   - ns2.autovnfb.com
5. Save your changes

## Step 3: Wait for DNS Propagation

After changing your nameservers, you need to wait for the changes to propagate across the internet. This can take anywhere from a few minutes to 48 hours, depending on your domain registrar and various internet factors.

## Step 4: Create Your Child Panel

Once you've set up your domain's nameservers:

1. Log in to our platform
2. Navigate to the Child Panel section
3. Enter your domain name and other required information
4. Complete the setup process

## Step 5: Verify Your Domain is Working

After DNS propagation is complete, you can verify your domain is working by:

1. Visiting your domain in a web browser
2. If your site loads correctly, your domain is properly configured

## Troubleshooting

If your domain isn't working after 48 hours, check the following:

1. **Verify nameserver settings**: Double-check that you've correctly set the nameservers to ns1.autovnfb.com and ns2.autovnfb.com
2. **Check domain lock**: Some registrars have a domain lock feature that prevents changes. Make sure your domain is unlocked.
3. **Contact your registrar**: Some registrars have additional steps to change nameservers.
4. **Contact our support**: If you've verified all the above and still have issues, contact our support team.

## Common Questions

### How long does DNS propagation take?
DNS propagation typically takes 24-48 hours, but can sometimes be faster.

### Can I use my existing DNS settings?
No, for our child panel system to work correctly, you must use our nameservers.

### Will changing nameservers affect my email?
If you're using email services tied to your domain, changing nameservers may affect them. Contact our support for assistance with email configuration.

### Can I point a subdomain instead of my main domain?
Yes, you can create a CNAME record for a subdomain at your current DNS provider that points to your child panel. Contact support for specific instructions.

## Need Help?

If you need assistance with setting up your domain, please contact our support team.
