import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize, tap } from 'rxjs';
import { CustomReferralReq, ReferralRes, ReferralStatsRes } from '../../model/response/referral-res.model';
import { PageResponse } from '../../model/response/page-response.model';
import { GUserSuperRes } from '../../model/response/g-user-super-res.model';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class ReferralService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _referrals$ = new BehaviorSubject<ReferralRes[]>([]);
  private _stats$ = new BehaviorSubject<ReferralStatsRes | null>(null);
  private _pagination$ = new BehaviorSubject<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });

  constructor(private http: HttpClient, private configService: ConfigService) {}

  get loading$(): Observable<boolean> {
    return this._loading$.asObservable();
  }

  get referrals$(): Observable<ReferralRes[]> {
    return this._referrals$.asObservable();
  }

  get stats$(): Observable<ReferralStatsRes | null> {
    return this._stats$.asObservable();
  }

  get pagination$(): Observable<{
    pageNumber: number;
    pageSize: number;
    totalElements: number;
    totalPages: number;
  }> {
    return this._pagination$.asObservable();
  }

  /**
   * Get referrals for a specific user
   * @param userId User ID to get referrals for
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Observable of paginated referral results
   */
  getUserReferrals(userId: number, page: number = 0, size: number = 10): Observable<PageResponse<ReferralRes>> {
    this._loading$.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<PageResponse<ReferralRes>>(`${this.configService.apiUrl}/users/${userId}/referrals`, { params })
      .pipe(
        tap(response => {
          this._referrals$.next(response.content);
          this._pagination$.next({
            pageNumber: response.pageable.page_number,
            pageSize: response.pageable.page_size,
            totalElements: response.total_elements,
            totalPages: response.total_pages
          });
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  /**
   * Update custom referral rate for a user
   * @param userId User ID to update
   * @param rate New referral rate
   * @returns Observable of the updated user data
   */
  updateCustomReferralRate(userId: number, rate: number): Observable<GUserSuperRes> {
    this._loading$.next(true);

    const requestBody: CustomReferralReq = {
      custom_rate: rate
    };

    return this.http.patch<GUserSuperRes>(
      `${this.configService.apiUrl}/users/${userId}/custom-referral`,
      requestBody
    ).pipe(
      finalize(() => this._loading$.next(false))
    );
  }
}
