import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { AdminOrderService } from '../../../core/services/admin-order.service';
import { ToastService } from '../../../core/services/toast.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-set-count',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './set-count.component.html',
  styleUrl: './set-count.component.css'
})
export class SetCountComponent {
  @Input() startCount: number = 0;
  @Input() orderId: number = 0;
  @Output() close = new EventEmitter<void>();
  @Output() countUpdated = new EventEmitter<number>();

  isLoading: boolean = false;

  constructor(
    private adminOrderService: AdminOrderService,
    private toastService: ToastService
  ) {}

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  saveCount(): void {
    // Ensure startCount is a valid number and orderId is provided
    if (this.startCount !== null && this.startCount !== undefined && this.orderId) {
      this.isLoading = true;

      // Call the API to update the start count
      this.adminOrderService.updateOrderStartCount(this.orderId, this.startCount)
        .pipe(
          finalize(() => this.isLoading = false)
        )
        .subscribe({
          next: (updatedOrder) => {
            this.toastService.showSuccess('Start count updated successfully');
            this.countUpdated.emit(this.startCount);
            this.onClose();
          },
          error: (error) => {
            console.error('Error updating start count:', error);
            this.toastService.showError('Failed to update start count');
          }
        });
    } else if (!this.orderId) {
      this.toastService.showError('Order ID is required');
    }
  }
}
