import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { SpecialPriceRes, ServiceInfo } from '../../../model/response/special-price-res.model';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { UIStateService } from '../../../core/services/ui-state.service';
import { finalize } from 'rxjs';
import { NewSpecialPricesComponent } from '../new-special-prices/new-special-prices.component';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { ToastService } from '../../../core/services/toast.service';
import { GUserSuperRes } from '../../../model/response/g-user-super-res.model';
import { CurrencyService } from '../../../core/services/currency.service';


@Component({
  selector: 'app-special-prices-user',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    NewSpecialPricesComponent
  ],
  templateUrl: './special-prices-user.component.html',
  styleUrls: ['./special-prices-user.component.css']
})
export class SpecialPricesUserComponent implements OnInit, OnDestroy {
  @Input() userIdNumber: number = 0; // This is the numeric user ID used for API calls
  @Input() user: GUserSuperRes | null = null; // This is the user ID string used for display purposes
  @Input() selectedServices: Set<number> = new Set<number>();
  @Input() selectedService: ServiceInfo | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() specialPriceAdded = new EventEmitter<SpecialPriceRes[]>();

  discountType: string = 'Fix price';
  newPrice: number | null = null;
  isSubmitting = false;
  errorMessage: string = '';

  // Global discount for the user
  globalDiscount: number | null = null;

  // For displaying existing special prices
  specialPrices: SpecialPriceRes[] = [];
  isLoadingPrices = false;

  // For action menu
  activeActionMenu: number | null = null;

  // For nested popups
  showNewSpecialPricesPopup = false;
  fromUserView = true; // Flag to indicate that we're opening the new-special-prices from user view

  discountTypes = [
    'Fix price',
    'Percentage discount'
  ];

  // For edit mode
  isEditMode = false;
  specialPriceToEdit: SpecialPriceRes | null = null;

  constructor(
    private adminService: AdminServiceService,
    public uiStateService: UIStateService,
    private toastService: ToastService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit(): void {
    // Load existing special prices for this user
    this.loadSpecialPrices();

    // Load user data to get the global discount
    this.loadUserData();

    // Add global click event listener to close dropdown when clicking outside
    document.addEventListener('click', this.handleDocumentClick);
  }

  /**
   * Loads user data to get the custom_discount value
   */
  loadUserData(): void {
    this.globalDiscount = this.user?.custom_discount || 0 ;

    // Show loading indicator
    //this.isLoadingPrices = true;

    // Use the service to get user data
    // this.adminService.getUserById(this.userIdNumber)
    //   .pipe(
    //     finalize(() => {
    //       this.isLoadingPrices = false;
    //     })
    //   )
    //   .subscribe({
    //     next: (response) => {
    //       console.log('User data loaded:', response);

    //       // Set the global discount from the user data
    //       if (response && response.custom_discount !== undefined) {
    //         this.globalDiscount = response.custom_discount;
    //       } else {
    //         // Default value if not found
    //         this.globalDiscount = 0;
    //       }
    //     },
    //     error: (error) => {
    //       console.error('Error loading user data:', error);

    //       // Default value if there's an error
    //       this.globalDiscount = 0;
    //     }
    //   });
  }

  ngOnDestroy(): void {
    // Remove the event listener when component is destroyed
    document.removeEventListener('click', this.handleDocumentClick);
  }

  saveGlobalDiscount(): void {
    if (this.globalDiscount === null) {
      return;
    }

    // Show loading indicator
    this.isLoadingPrices = true;

    // Use the service to save global discount
    this.adminService.saveGlobalDiscount(this.userIdNumber, this.globalDiscount)
      .pipe(
        finalize(() => {
          this.isLoadingPrices = false;
        })
      )
      .subscribe({
        next: (response) => {
          console.log('Global discount saved successfully:', response);

          // Show success toast notification
          this.toastService.showSuccess('Discount saved successfully');
        },
        error: (error) => {
          console.error('Error saving global discount:', error);

          // Show error toast notification
          this.toastService.showError('Failed to save discount');
        }
      });
  }

  loadSpecialPrices(): void {
    this.isLoadingPrices = true;

    // Use the service to get special prices for the user
    this.adminService.getSpecialPricesForUser(this.userIdNumber)
      .pipe(
        finalize(() => {
          this.isLoadingPrices = false;
        })
      )
      .subscribe({
        next: (response) => {
          this.specialPrices = response;
        },
        error: (error) => {
          console.error('Error loading special prices:', error);

          // Show empty array
          this.specialPrices = [];

          // Show error toast
          this.toastService.showError('Failed to load special prices');
        }
      });
  }

  // Method removed

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onDiscountTypeChange(type: string): void {
    this.discountType = type;
    // Reset price when changing discount type
    this.newPrice = null;
  }

  openAddNewSpecialPrice(): void {
    console.log('Opening new special price form for user ID:', this.userIdNumber);

    // Reset edit mode
    this.isEditMode = false;
    this.specialPriceToEdit = null;

    // Set fromUserView to true to show the service dropdown and hide user ID
    // This will make the new mode interface similar to edit mode
    this.fromUserView = true;

    // Show the nested popup
    this.showNewSpecialPricesPopup = true;
  }

  closeNewSpecialPricesPopup(): void {
    this.showNewSpecialPricesPopup = false;
    this.isEditMode = false;
    this.specialPriceToEdit = null;

    // Refresh the special prices list after adding a new price
    this.loadSpecialPrices();
  }

  onSpecialPriceAdded(specialPrices: SpecialPriceRes[]): void {
    console.log('Special price added:', specialPrices);

    // Close the nested popup
    this.showNewSpecialPricesPopup = false;

    // Refresh the special prices list
    this.loadSpecialPrices();

    // Emit the event to notify parent components
    this.specialPriceAdded.emit(specialPrices);
  }

  toggleActionMenu(priceId: number, event: MouseEvent): void {
    event.stopPropagation();

    // Close if already open for this price
    if (this.activeActionMenu === priceId) {
      this.closeActionMenu();
      return;
    }

    // Open the menu for this price
    this.activeActionMenu = priceId;
  }

  closeActionMenu(): void {
    this.activeActionMenu = null;
  }

  editSpecialPrice(priceId: number): void {
    console.log('Edit special price:', priceId);

    // Find the special price to edit
    const specialPrice = this.specialPrices.find(price => price.id === priceId);
    if (!specialPrice) {
      console.error('Special price not found:', priceId);
      return;
    }

    // Close the action menu
    this.closeActionMenu();

    // Set edit mode and store the special price to edit
    this.isEditMode = true;
    this.specialPriceToEdit = specialPrice;

    // Show the nested popup
    this.showNewSpecialPricesPopup = true;
  }

  deleteSpecialPrice(priceId: number): void {
    console.log('Deleting special price:', priceId);

    // Show loading indicator
    this.isLoadingPrices = true;

    // Call the API to delete the special price
    this.adminService.deleteSpecialPrice(priceId)
      .pipe(
        finalize(() => {
          this.isLoadingPrices = false;
        })
      )
      .subscribe({
        next: () => {
          console.log('Special price deleted successfully');
          // Remove the deleted price from the local array
          this.specialPrices = this.specialPrices.filter(price => price.id !== priceId);
          // Emit event to notify parent component
          this.specialPriceAdded.emit(this.specialPrices);
        },
        error: (error: any) => {
          console.error('Error deleting special price:', error);
          // You could add error handling here, such as displaying an error message
        }
      });

    this.closeActionMenu();
  }

  handleDocumentClick = (): void => {
    if (this.activeActionMenu !== null) {
      this.closeActionMenu();
    }
  }

  getFormattedPrice(price: SpecialPriceRes): string {
    if (price.discount_type === 'FIXED') {
      return `$${this.currencyService.formatBalance(price.discount_value)}`;
    } else {
      return `${price.discount_value}%`;
    }
  }

  getServiceName(serviceId: number): string {
    // This method is no longer needed since we directly access price.service.name in the template
    // But we'll keep it for backward compatibility
    const specialPrice = this.specialPrices.find(p => p.service.id === serviceId);
    if (specialPrice) {
      return specialPrice.service.name;
    }
    return `Service #${serviceId}`;
  }

  /**
   * Converts a ServiceInfo object to a SuperGeneralSvRes object
   * @param serviceInfo The ServiceInfo object to convert
   * @returns A SuperGeneralSvRes object with the same properties as the ServiceInfo object
   */
  convertToSuperGeneralSvRes(serviceInfo: ServiceInfo): SuperGeneralSvRes | null {
    if (!serviceInfo) return null;

    // Convert refill_days to string if it's a number


    // Convert labels to the correct type
    const labels = serviceInfo.labels ?
      serviceInfo.labels.map(label => {
        // Ensure the type is either 'Green' or 'Red'
        let labelType: 'Green' | 'Red' = 'Green'; // Default to Green

        if (typeof label.type === 'string') {
          if (label.type === 'Green' || label.type === 'Red') {
            labelType = label.type as 'Green' | 'Red';
          } else if (label.type.toLowerCase().includes('green')) {
            labelType = 'Green';
          } else {
            labelType = 'Red';
          }
        }

        return {
          text: label.text,
          type: labelType
        };
      }) :
      [];

    // Create a new SuperGeneralSvRes object with the properties from ServiceInfo using the factory method
    return SuperGeneralSvRes.create({
      id: serviceInfo.id,
      name: serviceInfo.name,
      description: serviceInfo.description,
      price: serviceInfo.price,
      add_type: serviceInfo.add_type,
      type: serviceInfo.type,
      min: serviceInfo.min,
      max: serviceInfo.max,
      average_time: serviceInfo.average_time,
      refill: serviceInfo.refill,
      refill_days: serviceInfo.refill_days || 30,
      sort: serviceInfo.sort,
      labels: labels,
      // Override specific price values
      price1: serviceInfo.price,
      price2: serviceInfo.price,
      original_price: serviceInfo.price
    });
  }
}
