#!/bin/bash

# Script to remove a domain from CoreDNS
# Usage: ./remove-domain.sh example.com

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 domain_name"
    echo "Example: $0 example.com"
    exit 1
fi

DOMAIN=$1
ZONE_FILE="./zones/${DOMAIN}.db"

# Remove zone file if it exists
if [ -f "$ZONE_FILE" ]; then
    rm "$ZONE_FILE"
    echo "Zone file for ${DOMAIN} removed."
else
    echo "Zone file for ${DOMAIN} not found."
fi

# Remove domain block from Corefile
if grep -q "${DOMAIN}" ./Corefile; then
    # Find the start and end lines of the domain block
    START_LINE=$(grep -n "${DOMAIN} {" ./Corefile | cut -d: -f1)
    if [ -n "$START_LINE" ]; then
        # Find the matching closing brace
        END_LINE=$((START_LINE + 5))  # Assuming standard block size
        
        # Delete the block
        sed -i "${START_LINE},${END_LINE}d" ./Corefile
        echo "Domain ${DOMAIN} removed from Corefile."
    else
        echo "Domain block for ${DOMAIN} not found in Corefile."
    fi
else
    echo "Domain ${DOMAIN} not found in Corefile."
fi

echo "Domain ${DOMAIN} removed from CoreDNS configuration."
echo "Restart CoreDNS or reload configuration for changes to take effect."
