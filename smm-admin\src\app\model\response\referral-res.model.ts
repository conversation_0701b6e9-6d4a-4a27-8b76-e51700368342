/**
 * Model representing a referral from the API
 */
export interface ReferralRes {
  id: number;
  user_name: string;
  email: string;
  avatar: string;
  balance: number;
  status: string;
  last_login_at: string;
  created_at: string;
  custom_referral_rate?: number;
}

/**
 * Model representing referral statistics
 */
export interface ReferralStatsRes {
  total_referrals: number;
  total_earnings: number;
  custom_referral_rate: number;
  default_referral_rate: number;
}

/**
 * Request model for updating custom referral rate
 */
export interface CustomReferralReq {
  custom_rate: number;
}
