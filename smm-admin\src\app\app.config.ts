import { ApplicationConfig, importProv<PERSON>s<PERSON>rom, APP_INITIALIZER } from '@angular/core';
import { provideRouter } from '@angular/router';
import { HttpClient, provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';

import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';


import { unwrapInterceptorFn } from './core/interceptors/unwrap.interceptor';
import { errorInterceptorFn } from './core/interceptors/error.interceptor';
import { jwtInterceptorFn } from './core/interceptors/jwt.interceptor';
import { corsInterceptorFn } from './core/interceptors/cors.interceptor';
import { JWT_OPTIONS } from '@auth0/angular-jwt';
import { JwtHelperService } from '@auth0/angular-jwt';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { LanguageService } from './core/services/language.service';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
export function initializeApp(lang: LanguageService) {
  return () => {
    // Return the promise from init() to ensure the app waits for translations to load
    return lang.init();
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideClientHydration(),
    provideHttpClient(
      withFetch(),
      withInterceptors([corsInterceptorFn, jwtInterceptorFn, unwrapInterceptorFn, errorInterceptorFn])
    ),
   importProvidersFrom( TranslateModule),
    { provide: JWT_OPTIONS, useValue: JWT_OPTIONS },
    JwtHelperService,

    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      defaultLanguage: 'vi',
      isolate: false
    }).providers!,
      {
        provide: APP_INITIALIZER,
        useFactory: initializeApp,
        deps: [LanguageService],
        multi: true
      }
  ]
};
