<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ isEditMode ? ('Edit network' | translate) : ('Add network' | translate) }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form [formGroup]="platformForm" (ngSubmit)="onSubmit()" class="platform-form">
        <!-- Error message -->
        <div *ngIf="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>

        <!-- Name field -->
        <div class="form-group">
          <label for="name" class="form-label">{{ 'Name' | translate }}</label>
          <input
            type="text"
            id="name"
            formControlName="name"
            class="form-input"
            [ngClass]="{'input-error': platformForm.get('name')?.invalid && platformForm.get('name')?.touched}"
          >
          <div *ngIf="platformForm.get('name')?.invalid && platformForm.get('name')?.touched" class="validation-error">
            <span *ngIf="platformForm.get('name')?.errors?.['required']">{{ 'Name is required' | translate }}</span>
            <span *ngIf="platformForm.get('name')?.errors?.['minlength']">{{ 'Name must be at least 2 characters' | translate }}</span>
            <span *ngIf="platformForm.get('name')?.errors?.['maxlength']">{{ 'Name cannot exceed 50 characters' | translate }}</span>
          </div>
        </div>

        <!-- Icon selection -->
        <div class="form-group">
          <label class="form-label">{{ 'Icon' | translate }}</label>
          <div class="icon-selector">
            <div class="selected-icon" (click)="toggleIconGrid($event); $event.stopPropagation()">
              <div class="icon-display">
                <span *ngIf="selectedIcon.id === 0" class="no-icon-text">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="folder-icon">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                  </svg>
                  {{ selectedIcon.value }}
                </span>
                <span *ngIf="selectedIcon.id !== 0" class="icon-name">
                  <fa-icon  [icon]="['fab', selectedIcon.value]" [ngStyle]="{ color: getIconColor(selectedIcon.name) }" class="mr-2"></fa-icon>
                  {{ selectedIcon.value }}
                </span>
              </div>
              <div class="dropdown-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>
            </div>

            <!-- Icon Grid -->
            <div *ngIf="showIconGrid" class="icon-grid-container" (click)="$event.stopPropagation()">
              <div class="icon-grid-header">
                <h3>Select an icon</h3>
              </div>
              <div class="icon-grid">
                <div
                  *ngFor="let icon of brandIcons"
                  class="icon-grid-item"
                  [class.selected]="selectedIcon.value === icon.value"
                  (click)="selectIcon(icon); $event.stopPropagation()">
                  <fa-icon [icon]="['fab', icon.value]" [ngStyle]="{ color: getIconColor(icon.name) }"></fa-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit button -->
        <button
          type="submit"
          class="save-button"
          [disabled]="isSubmitting">
          {{ 'Save' | translate }}
        </button>
      </form>
    </div>
  </div>
</div>
