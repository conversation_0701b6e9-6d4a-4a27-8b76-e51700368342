<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Set new extra charge for services</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Global Extra Charge Input -->
      <div class="global-charge-section">
        <label for="extraCharge" class="charge-label">Extra charge in %</label>
        <div class="input-container">
          <input
            type="number"
            id="extraCharge"
            [(ngModel)]="extraCharge"
            (ngModelChange)="updateAllExtraCharges()"
            class="charge-input"
            min="0"
            max="1000"
          >
        </div>
      </div>

      <!-- Services Table -->
      <div class="services-table-container">
        <table >
          <thead>
            <tr>
              <th class="service-column">Service</th>
              <th class="price-column">Provider price</th>
              <th class="price-column">Final price</th>
              <th class="charge-column">Extra charge in %</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let service of services">
              <td class="service-cell">
                <div class="service-id">{{ service.id }}</div>
                <div class="service-name">{{ service.name }}</div>
              </td>
              <td class="price-cell">${{ formatBalance(updatedServices[service.id].providerPrice) }}</td>
              <td class="price-cell">${{ formatBalance(updatedServices[service.id].finalPrice) }}</td>
              <td class="charge-cell">
                <input
                  type="number"
                  [(ngModel)]="updatedServices[service.id].extraCharge"
                  (ngModelChange)="updateServiceExtraCharge(service.id, updatedServices[service.id].extraCharge)"
                  class="charge-input-small"
                  min="0"
                  max="1000"
                >
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Error/Success Messages -->
      <div *ngIf="errorMessage" class="error-message">
        <fa-icon [icon]="['fas', 'exclamation-circle']" class="mr-2"></fa-icon>
        {{ errorMessage }}
      </div>

      <div *ngIf="successMessage" class="success-message">
        <fa-icon [icon]="['fas', 'check-circle']" class="mr-2"></fa-icon>
        {{ successMessage }}
      </div>

      <!-- Save Button -->
      <button
        (click)="saveNewPrices()"
        class="save-button"
        [disabled]="isSubmitting"
      >
        <span *ngIf="!isSubmitting">Save new prices</span>
        <span *ngIf="isSubmitting" class="loading-spinner"></span>
      </button>
    </div>
  </div>
</div>

