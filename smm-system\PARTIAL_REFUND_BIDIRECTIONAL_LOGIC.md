# Bidirectional Partial Refund Logic - Complete Solution

## 🎯 Problem Solved

**Your Requirements:**
1. **<PERSON><PERSON><PERSON> giảm** (30 → 20): Refund chênh lệch (10 items)
2. **Remains tăng** (20 → 30): Deduct chênh lệch (10 items)

## 📊 Detailed Examples

### Scenario A: Remains Decreasing (Refund)

**Initial Order:**
```
Order ID: 12345
Quantity: 100 items
Price: $5.00 per 1000 items
Total Charge: $0.50
Status: IN_PROGRESS
```

**Step 1: First Partial (remains = 30)**
```
Previous remains = 100 (original quantity)
Current remains = 30
Difference = 100 - 30 = 70 items → REFUND
Refund amount = 70 * $5.00 / 1000 = $0.35
```
**Result:** User balance +$0.35, Transaction: type='Partial', change=+$0.35

**Step 2: Second Partial (remains = 20)**
```
Previous remains = 30 (from last transaction)
Current remains = 20
Difference = 30 - 20 = 10 items → REFUND
Refund amount = 10 * $5.00 / 1000 = $0.05
```
**Result:** User balance +$0.05, Transaction: type='Partial', change=+$0.05

---

### Scenario B: Remains Increasing (Deduct)

**Continuing from Scenario A...**

**Step 3: Third Partial (remains = 30)**
```
Previous remains = 20 (from last transaction)
Current remains = 30
Difference = 20 - 30 = -10 items → DEDUCT
Deduct amount = 10 * $5.00 / 1000 = $0.05
```
**Result:** User balance -$0.05, Transaction: type='Partial', change=-$0.05

**Step 4: Fourth Partial (remains = 50)**
```
Previous remains = 30 (from last transaction)
Current remains = 50
Difference = 30 - 50 = -20 items → DEDUCT
Deduct amount = 20 * $5.00 / 1000 = $0.10
```
**Result:** User balance -$0.10, Transaction: type='Partial', change=-$0.10

## 🔧 Technical Implementation

### Key Methods:

1. **`processPartialRefund()`** - Main orchestrator
   - Determines if refund or deduct is needed
   - Handles insufficient balance validation
   - Creates appropriate transactions

2. **`calculatePartialAdjustmentAmount()`** - Core calculation
   - Returns positive value for refund
   - Returns negative value for deduct
   - Extracts previous remains from transaction notes

3. **`addBalanceWithCustomType()`** - For refunds
   - Adds money to user balance
   - Creates positive transaction record

4. **`deductBalanceWithCustomType()`** - For deductions
   - Subtracts money from user balance
   - Creates negative transaction record
   - Validates sufficient balance

### Transaction Note Formats:
```
Refund: "Partial refund for order #12345 (remains: 20): $0.05"
Deduct: "Partial deduction for order #12345 (remains: 30): $0.05"
```

## 🛡️ Safety Features

### 1. Insufficient Balance Protection
```java
if (!hasSufficientBalance(order.getUser(), deductAmount)) {
    throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
}
```

### 2. Fallback Calculation
If transaction note parsing fails, system calculates from transaction history:
```java
BigDecimal totalAdjustment = previousPartialTransactions.stream()
    .map(GTransaction::getChange)
    .reduce(BigDecimal.ZERO, BigDecimal::add);
```

### 3. Complete Audit Trail
Every adjustment is logged with:
- Transaction type: 'Partial'
- Change amount: positive (refund) or negative (deduct)
- Detailed note with remains value
- Order reference

## 🧪 Test Scenarios

### Complete Flow Test:
```
Initial: 100 items, $0.50 total

1. Partial remains=30 → Refund $0.35 (70 items)
   Balance: +$0.35, Total refunded: $0.35

2. Partial remains=20 → Refund $0.05 (10 items)
   Balance: +$0.05, Total refunded: $0.40

3. Partial remains=30 → Deduct $0.05 (10 items)
   Balance: -$0.05, Total refunded: $0.35

4. Partial remains=50 → Deduct $0.10 (20 items)
   Balance: -$0.10, Total refunded: $0.25

5. Partial remains=10 → Refund $0.20 (40 items)
   Balance: +$0.20, Total refunded: $0.45
```

## ✅ Benefits

1. **Bidirectional Logic**: Handles both refund and deduct scenarios
2. **Accurate Calculations**: Only processes the actual difference
3. **Balance Protection**: Prevents overdraft situations
4. **Complete Audit**: Full transaction history with clear notes
5. **Robust Parsing**: Fallback calculation if note parsing fails
6. **Consistent Interface**: Same method handles all partial adjustments

This implementation ensures your partial refund system works exactly as specified, handling both increasing and decreasing remains scenarios with proper balance management and complete audit trails.
