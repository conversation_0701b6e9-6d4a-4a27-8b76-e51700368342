package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.AutoRenewalRequest;
import tndung.vnfb.smm.dto.RenewalRequest;
import tndung.vnfb.smm.dto.RenewalResponse;
import tndung.vnfb.smm.dto.TenantSubscriptionDto;
import tndung.vnfb.smm.service.TenantSubscriptionService;

import java.util.List;
@RestController
@RequestMapping("/v1/tenant-subscriptions")
@RequiredArgsConstructor
public class TenantSubscriptionController {

    private final TenantSubscriptionService subscriptionService;

    @PostMapping("/renew")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<RenewalResponse> renewSubscription(@RequestBody RenewalRequest request) {
        RenewalResponse response = subscriptionService.renewSubscription(request);
        return ApiResponseEntity.success(response);
    }

//    @GetMapping
//    @PreAuthorize("hasRole('ROLE_PANEL')")
//    @TenantCheck
//    public ApiResponseEntity<List<TenantSubscriptionDto>> getAllSubscriptions() {
//        List<TenantSubscriptionDto> subscriptions = subscriptionService.getAllTenantSubscriptions();
//        return ApiResponseEntity.success(subscriptions);
//    }

    @GetMapping("/{tenantId}")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TenantSubscriptionDto> getSubscription(@PathVariable String tenantId) {
        return ApiResponseEntity.success(subscriptionService.getTenantSubscription(tenantId));

    }

    @GetMapping("/expiring-soon")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ResponseEntity<List<TenantSubscriptionDto>> getExpiringSoon(
            @RequestParam(defaultValue = "7") int days) {
        List<TenantSubscriptionDto> expiring = subscriptionService.getTenantsExpiringSoon(days);
        return ResponseEntity.ok(expiring);
    }

    @PostMapping("/update-expired")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ResponseEntity<String> updateExpiredTenants() {
        int updatedCount = subscriptionService.updateExpiredTenants();
        return ResponseEntity.ok("Updated " + updatedCount + " expired tenants");
    }

    @PostMapping("/send-notifications")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ResponseEntity<String> sendRenewalNotifications() {
        subscriptionService.sendRenewalNotifications();
        return ResponseEntity.ok("Renewal notifications sent");
    }

    @PutMapping("/{tenantId}/auto-renewal")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<String> toggleAutoRenewal(
            @PathVariable String tenantId,
            @RequestBody AutoRenewalRequest request) {
        subscriptionService.updateAutoRenewal(tenantId, request.isAutoRenewal());
        return ApiResponseEntity.success("Auto-renewal updated successfully");
    }
}