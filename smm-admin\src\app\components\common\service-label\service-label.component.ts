import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TagLabelComponent } from '../tag-label/tag-label.component';
import { IconsModule } from '../../../icons/icons.module';
import { SocialIconComponent } from "../social-icon/social-icon.component";
import { TranslateModule } from '@ngx-translate/core';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { CurrencyService } from '../../../core/services/currency.service';
import { CurrencyConvertPipe } from '../../../core/pipes/currency-convert.pipe';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';
import { ToastService } from '../../../core/services/toast.service';

@Component({
  selector: 'app-service-label',
  standalone: true,
  imports: [CommonModule, TagLabelComponent, IconsModule, SocialIconComponent, TranslateModule],
  templateUrl: './service-label.component.html',
  styleUrl: './service-label.component.css'
})
export class ServiceLabelComponent implements OnInit, OnDestroy {
  @Input() service: SuperGeneralSvRes = {} as SuperGeneralSvRes;
  @Input() lite: boolean = false;

  // User data
  user: UserRes | undefined;
  private userSubscription: Subscription | undefined;
  userCustomDiscount: number = 0;

  constructor(
    private currencyService: CurrencyService,
    private userService: UserService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    // Subscribe to user data
    this.userSubscription = this.userService.user$.subscribe(user => {
      this.user = user;

      // Get custom discount from user data
      this.userCustomDiscount = user && (user as any).custom_discount ? (user as any).custom_discount : 0;

      // If user is not loaded yet, trigger a fetch
      if (!user) {
        this.userService.get$.next();
      }
    });
  }

  ngOnDestroy(): void {
    // Clean up subscription when component is destroyed
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  // Format price with currency conversion
  formatPrice(price: number): string {
    return this.currencyService.formatPrice(price);
  }

  // Check if service has special prices
  hasSpecialPrices(): boolean {
    return !!this.service && !!this.service.special_prices && this.service.special_prices.length > 0;
  }

  // Get special price count
  getSpecialPriceCount(): number {
    return this.service && this.service.special_prices ? this.service.special_prices.length : 0;
  }

  // Check if service has any discount (special price or custom discount)
  hasDiscount(): boolean {
    // Check for special prices first (priority)
    if (this.hasSpecialPrices()) {
      return true;
    }

    // Check for user's custom discount
    if (this.userCustomDiscount > 0) {
      return true;
    }

    return false;
  }

  copyToClipboard(text: string, event: MouseEvent): void {
    event.stopPropagation();
    const element = event.currentTarget as HTMLElement;
    navigator.clipboard.writeText(text)
      .then(() => {
        // Show a temporary success indicator
        const originalColor = element.style.color;

        // Change color to indicate success
        element.style.color = '#10b981'; // Green color

        // Reset after a short delay
        setTimeout(() => {
          element.style.color = originalColor;
        }, 1000);
        this.toastService.showSuccess('Copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy text: ', err);
        this.toastService.showError('Failed to copy text');
      });
  }

  // Get the original price before discount
  getOriginalPrice(): number {
    return this.service ? this.service.original_price || this.service.price : 0;
  }

  // Get the discount percentage
  getDiscountPercent(): number {
    // Check for special prices with PERCENT type (priority)
    if (this.hasSpecialPrices() && this.service.special_prices[0].discount_type === 'PERCENT') {
      return this.service.special_prices[0].discount_value;
    }


    if (this.hasSpecialPrices() && this.service.special_prices[0].discount_type === 'FIXED') {
      return 0;
    }

    // Return user's custom discount if available
    if (this.userCustomDiscount > 0) {
      return this.userCustomDiscount;
    }

    return 0;
  }

}
