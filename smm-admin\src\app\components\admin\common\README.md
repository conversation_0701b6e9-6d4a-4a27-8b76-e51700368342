# Admin Menu System

This directory contains common components and services for the admin menu system.

## AdminMenuService

The `AdminMenuService` provides a centralized way to manage menu actions for different admin components.

### Usage

1. Import the service in your component:

```typescript
import { AdminMenuService, MenuAction } from '../../../core/services/admin-menu.service';
```

2. Inject the service in your constructor:

```typescript
constructor(private adminMenuService: AdminMenuService) {}
```

3. Get the menu actions in ngOnInit:

```typescript
ngOnInit() {
  // For user menu
  this.menuActions = this.adminMenuService.getUserMenuActions();
  
  // OR for order menu
  this.menuActions = this.adminMenuService.getOrderMenuActions();
}
```

4. Use the menu actions in your template:

```html
<div *ngIf="activeActionMenu !== null" class="fixed dropdown-menu z-50 w-56"
     [style.top.px]="menuPosition.top"
     [style.left.px]="menuPosition.left"
     appClickOutside
     (clickOutside)="closeActionMenu()">
  <div class="py-1 divide-y divide-gray-100">
    <a *ngFor="let action of menuActions"
       (click)="handleMenuAction(action.id, activeActionMenu!); $event.stopPropagation()" 
       class="flex items-center px-4 py-2 text-sm cursor-pointer">
      <fa-icon [icon]="['fas', action.icon]" class="mr-3 {{action.iconColor}} w-4"></fa-icon>
      <span>{{action.label}}</span>
    </a>
  </div>
</div>
```

5. Implement the handleMenuAction method:

```typescript
handleMenuAction(actionId: string, itemId: number): void {
  console.log(`Action ${actionId} for item:`, itemId);
  
  switch (actionId) {
    case 'action-1':
      this.doAction1(itemId);
      break;
    case 'action-2':
      this.doAction2(itemId);
      break;
    // Add more cases as needed
  }
  
  this.closeActionMenu();
}
```

## Styling

Import the admin-menu.css file in your component's styleUrls to use the common styling:

```typescript
@Component({
  selector: 'app-your-component',
  standalone: true,
  imports: [...],
  templateUrl: './your-component.component.html',
  styleUrls: ['./your-component.component.css', '../common/admin-menu.css']
})
```
