package tndung.vnfb.smm.service;

import java.util.List;

/**
 * Service for managing user-tenant relationships
 */
public interface UserTenantAccessService {
    
    /**
     * Get list of tenant IDs that a user has access to
     * 
     * @param userId The user ID
     * @return List of tenant IDs
     */
    List<String> getAccessibleTenantIds(Long userId);
    
    /**
     * Check if a user has access to a specific tenant
     * 
     * @param userId The user ID
     * @param tenantId The tenant ID
     * @return true if the user has access, false otherwise
     */
    boolean hasAccessToTenant(Long userId, String tenantId);
    
    /**
     * Grant a user access to a tenant
     * 
     * @param userId The user ID
     * @param tenantId The tenant ID
     */
    void grantTenantAccess(Long userId, String tenantId);
    
    /**
     * Revoke a user's access to a tenant
     * 
     * @param userId The user ID
     * @param tenantId The tenant ID
     */
    void revokeTenantAccess(Long userId, String tenantId);
}
