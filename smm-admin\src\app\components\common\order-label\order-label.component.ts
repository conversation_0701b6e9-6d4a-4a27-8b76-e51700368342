import { Component, inject, Input } from '@angular/core';
import { Order } from '../../../model/g-service';
import { TagLabelComponent } from "../tag-label/tag-label.component";
import { CommonModule } from '@angular/common';
import { Clipboard } from '@angular/cdk/clipboard';
@Component({
  selector: 'app-order-label',
  standalone: true,
  imports: [TagLabelComponent, CommonModule],
  templateUrl: './order-label.component.html',
  styleUrl: './order-label.component.css'
})
export class OrderLabelComponent {
  @Input() order: Order | Order = {} as Order;
  @Input() showCopyId: boolean = false;
  private clipboard = inject(Clipboard);

  copyId() {
    this.clipboard.copy(this.order.id);
  }
}
