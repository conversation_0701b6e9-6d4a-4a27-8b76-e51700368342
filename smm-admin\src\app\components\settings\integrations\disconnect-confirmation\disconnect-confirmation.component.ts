import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconsModule } from '../../../../icons/icons.module';

@Component({
  selector: 'app-disconnect-confirmation',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    TranslateModule,
    FaIconComponent
  ],
  templateUrl: './disconnect-confirmation.component.html',
  styleUrl: './disconnect-confirmation.component.css'
})
export class DisconnectConfirmationComponent {
  @Input() itemName: string = '';
  @Input() isLoading: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<void>();

  closePopup(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.closePopup();
    }
  }

  confirmDisconnect(): void {
    this.confirm.emit();
  }
}
