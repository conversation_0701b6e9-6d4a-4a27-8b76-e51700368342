import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/fontawesome-svg-core';

@Component({
  selector: 'app-url-badge',
  standalone: true,
  imports: [CommonModule, IconsModule],
  template: `
    <div class="url-badge-container" [ngStyle]="{'background-color': backgroundColor}">
      <div class="count-container">
        <span class="count" [ngStyle]="{'color': countColor}">{{ count }}</span>
        <fa-icon [icon]="['fas', icon]" class="count-icon" [ngStyle]="{'color': iconColor}"></fa-icon>
      </div>
      <div class="url-container">
        <span class="url-text" [ngStyle]="{'color': urlColor}">{{ url }}</span>
      </div>
      <div class="action-container">
        <div class="action-circle" [ngStyle]="{'background-color': actionBgColor}">
          <fa-icon [icon]="['fas', actionIcon]" class="action-icon"></fa-icon>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .url-badge-container {
      display: flex;
      align-items: center;
      border-radius: 6px;
      padding: 6px 10px;
      max-width: 100%;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .count-container {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-right: 10px;
    }

    .count {
      font-weight: 600;
      font-size: 14px;
    }

    .count-icon {
      font-size: 12px;
    }

    .url-container {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .url-text {
      font-size: 14px;
      font-weight: 500;
    }

    .action-container {
      margin-left: 10px;
    }

    .action-circle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      border-radius: 50%;
    }

    .action-icon {
      color: white;
      font-size: 10px;
    }
  `]
})
export class UrlBadgeComponent {
  @Input() count: number = 0;
  @Input() url: string = '';
  @Input() icon: IconName = 'file-alt' as IconName;
  @Input() actionIcon: IconName = 'arrow-right' as IconName;

  // Colors
  @Input() backgroundColor: string = '#f8f9fa';
  @Input() countColor: string = '#333333';
  @Input() iconColor: string = '#666666';
  @Input() urlColor: string = '#3498db';
  @Input() actionBgColor: string = '#3498db';
}
