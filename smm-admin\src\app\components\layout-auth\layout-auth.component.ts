import { Component, OnInit, OnD<PERSON>roy, Inject, PLATFORM_ID, HostListener } from '@angular/core';
import { FooterComponent } from "./footer/footer.component";
import { HeaderComponent } from "./header/header.component";
import { RouterModule, Router } from '@angular/router';
import { AuthUtilsService } from '../../core/services/auth-utils.service';
import { isPlatformBrowser, CommonModule } from '@angular/common';
import { FloatingContactButtonsComponent } from '../common/floating-contact-buttons/floating-contact-buttons.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconsModule } from '../../icons/icons.module';
import { AppAssetsService } from '../../core/services/app-assets.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-layout-auth',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, IconsModule],
  templateUrl: './layout-auth.component.html',
  styleUrl: './layout-auth.component.css'
})
export class LayoutAuthComponent implements OnInit, OnDestroy {
  // Language switcher
  currentLanguage = 'en';
  showLanguageDropdown = false;
  languages = [
    { code: 'en', name: 'English' },
    { code: 'vi', name: 'Tiếng Việt' }
  ];

  // Logo
  logoUrl: string = 'assets/images/logo.png';
  private subscription = new Subscription();

  constructor(
    private authUtils: AuthUtilsService,
    private router: Router,
    private translateService: TranslateService,
    @Inject(PLATFORM_ID) private platformId: Object,
   // private appAssetsService: AppAssetsService
  ) {}

  ngOnInit(): void {
    // Check if user is already authenticated
    if (isPlatformBrowser(this.platformId)) {
      if (this.authUtils.isAuthenticated() && !this.authUtils.isTokenExpired()) {
        console.log('LayoutAuth Component - User is already authenticated, redirecting');
        const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/';
        this.router.navigate([redirectUrl]);
      }

      // Get the browser language or use default
      const browserLang = this.translateService.getBrowserLang();
      const defaultLang = browserLang && ['en', 'vi'].includes(browserLang) ? browserLang : 'en';

      // Use the language from localStorage if available, otherwise use the default
      const savedLang = localStorage.getItem('language');
      this.currentLanguage = savedLang || defaultLang;

      // Set the current language
      this.translateService.use(this.currentLanguage);
    }

    // Subscribe to logo URL changes
    // this.subscription.add(
    //   this.appAssetsService.logoUrl$.subscribe(url => {
    //     this.logoUrl = url;
    //   })
    // );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  // Toggle language dropdown
  toggleLanguageDropdown(): void {
    this.showLanguageDropdown = !this.showLanguageDropdown;
  }

  // Change language
  changeLanguage(langCode: string): void {
    this.currentLanguage = langCode;
    this.translateService.use(langCode);
    localStorage.setItem('language', langCode);
    this.showLanguageDropdown = false;
  }

  // Close language dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
      this.showLanguageDropdown = false;
    }
  }


    // Navigate to login page
  navigateToLogin() {
    this.router.navigate(['/auth/login']);
  }

  // Navigate to register page
  navigateToRegister() {
    this.router.navigate(['/auth/register']);
  }

  // Navigate to services page
  navigateToServices() {
    this.router.navigate(['/panel']);
  }

  // Navigate to dashboard
  navigateToDashboard() {
    this.router.navigate(['/panel']);
  }
}
