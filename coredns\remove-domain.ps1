param (
    [Parameter(Mandatory=$true)]
    [string]$Domain
)

# Script to remove a domain from CoreDNS
# Usage: .\remove-domain.ps1 -Domain example.com

$ZoneFile = ".\zones\$Domain.db"

# Remove zone file if it exists
if (Test-Path $ZoneFile) {
    Remove-Item $ZoneFile
    Write-Host "Zone file for $Domain removed."
} else {
    Write-Host "Zone file for $Domain not found."
}

# Remove domain block from Corefile
$CorefileContent = Get-Content -Path .\Corefile -Raw
$DomainBlockPattern = "$Domain {[^}]*}"

if ($CorefileContent -match $DomainBlockPattern) {
    $UpdatedContent = $CorefileContent -replace "$DomainBlockPattern`r`n", ""
    Set-Content -Path .\Corefile -Value $UpdatedContent
    Write-Host "Domain $Domain removed from Corefile."
} else {
    Write-Host "Domain block for $Domain not found in Corefile."
}

Write-Host "Domain $Domain removed from CoreDNS configuration."
Write-Host "Restart CoreDNS or reload configuration for changes to take effect."
