import { Directive, ElementRef, EventEmitter, HostListener, Output, OnInit, OnDestroy } from '@angular/core';

@Directive({
  selector: '[clickOutside]',
  standalone: true
})
export class ClickOutsideDirective implements OnInit, OnDestroy {
  @Output() clickOutside = new EventEmitter<void>();
  private listening = false;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    setTimeout(() => {
      this.listening = true;
    }, 100);
  }

  ngOnDestroy() {
    this.listening = false;
  }

  @HostListener('document:click', ['$event'])
  public onClick(event: MouseEvent) {
    if (!this.listening) return;

    const target = event.target as HTMLElement;
    const clickedInside = this.elementRef.nativeElement.contains(target);

    if (!clickedInside) {
      this.clickOutside.emit();
    }
  }
}
