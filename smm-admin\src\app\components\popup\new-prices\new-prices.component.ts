import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { CurrencyService } from '../../../core/services/currency.service';

@Component({
  selector: 'app-new-prices',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './new-prices.component.html',
  styleUrls: ['./new-prices.component.css']
})
export class NewPricesComponent implements OnInit {
  @Input() selectedServices: Set<number> = new Set<number>();
  @Output() close = new EventEmitter<void>();
  @Output() pricesUpdated = new EventEmitter<SuperGeneralSvRes[]>();

  extraCharge: number = 30; // Default extra charge percentage
  services: SuperGeneralSvRes[] = [];
  updatedServices: { [id: number]: { providerPrice: number, finalPrice: number, extraCharge: number } } = {};
  isSubmitting = false;
  errorMessage: string = '';
  successMessage: string = '';

  constructor(
    private adminService: AdminServiceService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit(): void {
    this.loadSelectedServices();

    // Set the default extraCharge to the average of the selected services' percent values
    if (this.services.length > 0) {
      let totalPercent = 0;
      let servicesWithPercent = 0;

      this.services.forEach(service => {
        if (service.percent !== undefined) {
          totalPercent += service.percent;
          servicesWithPercent++;
        }
      });

      if (servicesWithPercent > 0) {
        this.extraCharge = Math.round(totalPercent / servicesWithPercent);
      }
    }
  }

  loadSelectedServices(): void {
    if (this.selectedServices.size === 0) {
      return;
    }

    // Get current services from the BehaviorSubject value
    const allServices = this.adminService.getServiceValue();

    // Filter only the selected services
    this.services = allServices.filter((service: SuperGeneralSvRes) => this.selectedServices.has(service.id));

    // Initialize the updated services with current values
    this.services.forEach(service => {
      // Use service.percent as the extraCharge value if available, otherwise use the default extraCharge
      const serviceExtraCharge = service.percent !== undefined ? service.percent : this.extraCharge;

      this.updatedServices[service.id] = {
        providerPrice: service.original_price || service.price,
        finalPrice: service.price,
        extraCharge: serviceExtraCharge
      };
    });
  }

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  updateAllExtraCharges(): void {
    // Update all services with the global extra charge
    this.services.forEach(service => {
      const providerPrice = this.updatedServices[service.id].providerPrice;
      this.updatedServices[service.id] = {
        ...this.updatedServices[service.id],
        extraCharge: this.extraCharge,
        finalPrice: this.calculateFinalPrice(providerPrice, this.extraCharge)
      };
    });
  }

  updateServiceExtraCharge(serviceId: number, extraCharge: number): void {
    if (this.updatedServices[serviceId]) {
      this.updatedServices[serviceId] = {
        ...this.updatedServices[serviceId],
        extraCharge: extraCharge,
        finalPrice: this.calculateFinalPrice(this.updatedServices[serviceId].providerPrice, extraCharge)
      };
    }
  }

  calculateFinalPrice(providerPrice: number, extraCharge: number): number {
    return providerPrice * (1 + extraCharge / 100);
  }

  saveNewPrices(): void {
    this.isSubmitting = true;

    // Prepare the updated services data
    const updatedServicesData = this.services.map(service => {
      const updatedData = this.updatedServices[service.id];
      return {
        ...service,
        price: updatedData.finalPrice,
        percent: updatedData.extraCharge
      };
    });

    // Track progress of all API calls
    let completedCount = 0;
    let errorCount = 0;
    const totalServices = this.services.length;

    // Process each service
    this.services.forEach(service => {
      const updatedData = this.updatedServices[service.id];

      // Call the API to update the price
      this.adminService.changePrice(service.id, updatedData.extraCharge)
        .subscribe({
          next: () => {
            completedCount++;
            checkCompletion();
          },
          error: (error) => {
            console.error(`Error updating price for service ${service.id}:`, error);
            errorCount++;
            checkCompletion();
          }
        });
    });

    // Helper function to check if all operations are complete
    const checkCompletion = () => {
      if (completedCount + errorCount === totalServices) {
        this.isSubmitting = false;

        // If all operations were successful, emit the updated services
        if (errorCount === 0) {
          this.successMessage = 'All prices updated successfully!';
          this.pricesUpdated.emit(updatedServicesData);

          // Close after a short delay to show success message
          setTimeout(() => {
            this.onClose();
          }, 1500);
        } else {
          // If there were errors, still emit the updated services but don't close immediately
          this.pricesUpdated.emit(updatedServicesData);

          // Show error message
          this.errorMessage = `Failed to update ${errorCount} out of ${totalServices} services`;
          console.error(this.errorMessage);

          // Close after a delay to show error message
          setTimeout(() => {
            this.onClose();
          }, 3000);
        }
      }
    };

    // If there are no services to update, just close the modal
    if (totalServices === 0) {
      this.isSubmitting = false;
      this.onClose();
    }
  }

  /**
   * Format balance using the common currency service
   */
  formatBalance(balance: number | undefined): string {
    return this.currencyService.formatBalance(balance);
  }
}


