import { Directive, ElementRef, forwardRef, HostListener, Input, On<PERSON>estroy, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { isPlatformBrowser } from '@angular/common';

@Directive({
  selector: '[appQuillEditor]',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => QuillEditorDirective),
      multi: true
    }
  ]
})
export class QuillEditorDirective implements OnInit, OnDestroy, ControlValueAccessor {
  quill: any;
  value: string = '';
  disabled: boolean = false;
  private isBrowser: boolean;

  @Input() placeholder: string = 'Enter content here...';

  private onChange: (value: string) => void = () => {};
  private onTouched: () => void = () => {};

  constructor(
    private elementRef: ElementRef,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngOnInit() {
    if (!this.isBrowser) {
      return; // Skip initialization on server-side
    }

    // Dynamically import Quill only in browser environment
    import('quill').then(Quill => {
      // Initialize Quill
      this.quill = new Quill.default(this.elementRef.nativeElement, {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'color': [] }, { 'background': [] }],
            ['link'],
            ['clean']
          ]
        },
        placeholder: this.placeholder,
        theme: 'snow'
      });

      // Set initial value if any
      if (this.value) {
        this.quill.clipboard.dangerouslyPasteHTML(this.value);
      }

      // Listen for changes
      this.quill.on('text-change', () => {
        const html = this.elementRef.nativeElement.querySelector('.ql-editor').innerHTML;
        this.onChange(html);
      });

      this.quill.on('selection-change', () => {
        this.onTouched();
      });
    });
  }

  ngOnDestroy() {
    if (this.isBrowser && this.quill) {
      this.quill = null;
    }
  }

  writeValue(value: string): void {
    this.value = value || '';
    if (this.isBrowser && this.quill) {
      if (value) {
        this.quill.clipboard.dangerouslyPasteHTML(value);
      } else {
        this.quill.setText('');
      }
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (this.isBrowser && this.quill) {
      this.quill.enable(!isDisabled);
    }
  }
}
