.layout-container {
  @apply max-w-full mx-auto;
}

/* Card styling */
.stat-card {
  @apply bg-gray-800 rounded-lg p-6 shadow-lg;
}

.card-icon {
  @apply p-3 rounded-full bg-gray-700 text-[var(--primary)] text-xl;
}

.card-value {
  @apply text-2xl font-bold text-white;
}

/* Table styling */
table {
  @apply min-w-full divide-y divide-gray-700;
}

thead {
  @apply bg-gray-900;
}

th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider;
}

tbody {
  @apply bg-gray-800 divide-y divide-gray-700;
}

tr {
  @apply hover:bg-gray-700;
}

td {
  @apply px-6 py-4 whitespace-nowrap text-sm;
}

/* Status badges */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.status-completed {
  @apply bg-green-100 text-green-800;
}

/* Custom scrollbar styling */
.max-h-\[350px\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[350px\]::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.max-h-\[350px\]::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.max-h-\[350px\]::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.status-in-progress {
  @apply bg-blue-100 text-blue-800;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-failed {
  @apply bg-red-100 text-red-800;
}

/* Activity icons */
.activity-icon {
  @apply flex items-center justify-center h-10 w-10 rounded-md text-white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layout-container {
    @apply px-2;
  }

  th, td {
    @apply px-2 py-2;
  }
}
