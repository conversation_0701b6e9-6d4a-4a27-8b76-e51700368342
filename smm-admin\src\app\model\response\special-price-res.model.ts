import { DiscountType } from "../request/custom-discount-service-req.model";

export interface UserInfo {
    id: number;
    user_name: string;
    email: string;
    avatar: string;
    balance: number;
    status: string;
    last_login_at: string;
    custom_discount: number;
}

export interface ServiceInfo {
    id: number;
    name: string;
    description: string;
    price: number;
    add_type: string;
    labels: {
        text: string;
        type: string;
    }[];
    type: string;
    min: number;
    max: number;
    average_time: number;
    refill: boolean;
    refill_days?: number;
    sort: number;
}

export interface SpecialPriceRes {
    id: number;
    user: UserInfo;
    service: ServiceInfo;
    discount_type: string;
    discount_value: number;

    // Compatibility properties
    userId?: number;
    serviceId?: number;
    type?: DiscountType;
    customDiscount?: number;
    createdAt?: string;
    updatedAt?: string;
}

// Legacy class for backward compatibility
export class LegacySpecialPriceRes implements SpecialPriceRes {
    public user: UserInfo;
    public service: ServiceInfo;
    public discount_type: string;
    public discount_value: number;

    constructor(
        public id: number,
        public userId: number,
        public serviceId: number,
        public type: DiscountType,
        public customDiscount: number,
        public createdAt: string,
        public updatedAt: string
    ) {
        // Initialize the interface properties
        this.discount_type = type;
        this.discount_value = customDiscount;

        // Create minimal user and service objects
        this.user = {
            id: userId,
            user_name: '',
            email: '',
            avatar: '',
            balance: 0,
            status: '',
            last_login_at: '',
            custom_discount: 0
        };

        this.service = {
            id: serviceId,
            name: '',
            description: '',
            price: 0,
            add_type: '',
            labels: [],
            type: '',
            min: 0,
            max: 0,
            average_time: 0,
            refill: false,
            sort: 0
        };
    }
}
