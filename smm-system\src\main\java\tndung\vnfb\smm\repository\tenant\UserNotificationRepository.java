package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.UserNotification;

import java.util.List;

@Repository
public interface UserNotificationRepository extends TenantAwareRepository<UserNotification, Long> {
    
    @Query("SELECT un FROM UserNotification un WHERE un.tenantId = :tenantId ORDER BY un.createdAt DESC")
    Page<UserNotification> findByTenantIdOrderByCreatedAtDesc(@Param("tenantId") String tenantId, Pageable pageable);
    
    @Query("SELECT un FROM UserNotification un WHERE un.tenantId = :tenantId AND un.isRead = false ORDER BY un.createdAt DESC")
    List<UserNotification> findUnreadByTenantId(@Param("tenantId") String tenantId);
    
    @Query("SELECT COUNT(un) FROM UserNotification un WHERE un.tenantId = :tenantId AND un.isRead = false")
    long countUnreadByTenantId(@Param("tenantId") String tenantId);
    
    @Query("SELECT un FROM UserNotification un WHERE un.tenantId = :tenantId AND un.userId = :userId ORDER BY un.createdAt DESC")
    Page<UserNotification> findByTenantIdAndUserIdOrderByCreatedAtDesc(
            @Param("tenantId") String tenantId, 
            @Param("userId") Long userId, 
            Pageable pageable);
    
    @Query("SELECT un FROM UserNotification un WHERE un.tenantId = :tenantId AND un.userId = :userId AND un.isRead = false ORDER BY un.createdAt DESC")
    List<UserNotification> findUnreadByTenantIdAndUserId(@Param("tenantId") String tenantId, @Param("userId") Long userId);
    
    @Query("SELECT COUNT(un) FROM UserNotification un WHERE un.tenantId = :tenantId AND un.userId = :userId AND un.isRead = false")
    long countUnreadByTenantIdAndUserId(@Param("tenantId") String tenantId, @Param("userId") Long userId);
}
