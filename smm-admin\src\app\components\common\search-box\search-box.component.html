
    <div [ngClass]="['relative rounded-md border border-gray-300 overflow-hidden inline-block', containerClass]">
      <input
        type="text"
        [(ngModel)]="searchValue"
        (keyup.enter)="search()"
        [placeholder]="placeholder"
        [ngClass]="[
          'py-3 px-4 focus:outline-none border-0',
          'transition duration-300 ease-in-out',
          inputClass,
          buttonPosition === 'right' ? 'pr-28' : 'pl-28'
        ]"
      />
      
      <button
        (click)="search()"
        [ngClass]="[
          'absolute top-0 h-full px-6 flex items-center justify-center',
          buttonClass,
          buttonPosition === 'right' ? 'right-0' : 'left-0'
        ]"
      >
        <span *ngIf="buttonIcon === 'search'" class="flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </span>
        <span *ngIf="buttonIcon === 'edit'" class="flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </span>
        <span *ngIf="!['search', 'edit'].includes(buttonIcon) || showButtonText">{{ buttonText }}</span>
      </button>
    </div>