/* Modern Landing Page Styles */

/* Background blob animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Shake animation for errors */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  20%, 60% {
    transform: translateX(-8px);
  }
  40%, 80% {
    transform: translateX(8px);
  }
}

.animate-shake {
  animation: shake 0.4s ease-in-out;
}

/* Fade in animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

/* Glassmorphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-card-strong {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Hover effects for cards */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
}

/* Button animations */
.btn-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
}

/* Text gradient effects */
.text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Feature card animations */
.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Dashboard preview animations */
.dashboard-preview {
  transition: all 0.5s ease;
}

.dashboard-preview:hover {
  transform: scale(1.02) rotateY(5deg);
  box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.4);
}

/* Payment method cards */
.payment-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.payment-card:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.2);
}

/* Design template cards */
.design-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.design-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.3);
}

/* Pricing card animations */
.pricing-card {
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Stats counter animation */
.stats-counter {
  transition: all 0.3s ease;
}

.stats-counter:hover {
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 1024px) {
  .text-5xl {
    font-size: 2.5rem;
  }

  .text-6xl {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .text-4xl {
    font-size: 1.875rem;
  }

  .text-5xl {
    font-size: 2.25rem;
  }

  .py-20 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .gap-12 {
    gap: 2rem;
  }

  .gap-16 {
    gap: 2rem;
  }

  /* Mobile-first login form styling */
  .order-1 {
    order: 1;
  }

  .order-2 {
    order: 2;
  }

  /* Ensure login form is prominently displayed on mobile */
  .min-h-screen {
    min-height: auto;
  }

  /* Adjust grid layout for mobile */
  .grid.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  /* Mobile login form container */
  .order-1.lg\\:order-2 {
    margin-bottom: 2rem;
  }

  /* Enhance mobile form visibility */
  .bg-white {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
  }
}

@media (max-width: 640px) {
  .text-8xl {
    font-size: 4rem;
  }

  .text-9xl {
    font-size: 5rem;
  }

  .px-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .py-4 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  /* Mobile-specific adjustments */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .py-16 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  /* Optimize login form for small screens */
  .rounded-3xl {
    border-radius: 1.5rem;
  }

  .p-8 {
    padding: 1.5rem;
  }

  /* Improve touch targets on mobile */
  .py-3 {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
  }

  /* Better spacing for mobile features grid */
  .grid.grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Mobile stats layout */
  .flex.space-x-8 {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
}

/* Loading spinner */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}