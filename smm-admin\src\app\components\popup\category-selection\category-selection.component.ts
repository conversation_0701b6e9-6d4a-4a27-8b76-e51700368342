import { Component, EventEmitter, Output, Input, OnInit, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { CategoriesService } from '../../../core/services/categories.service';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { IconBaseModel } from '../../../model/base-model';
import { IconDropdownComponent } from '../../common/icon-dropdown/icon-dropdown.component';
import { AdminServiceService } from '../../../core/services/admin-service.service';

@Component({
  selector: 'app-category-selection',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    IconDropdownComponent
  ],
  templateUrl: './category-selection.component.html',
  styleUrl: './category-selection.component.css'
})
export class CategorySelectionComponent implements OnInit, AfterViewInit {
  @Output() close = new EventEmitter<void>();
  @Output() categorySelected = new EventEmitter<SuperCategoryRes>();
  @Input() selectedCategoryId: number | null = null;
  @Input() serviceId: number | null = null;

  platforms: SuperPlatformRes[] = [];
  categories: SuperCategoryRes[] = [];
  categoryOptions: IconBaseModel[] = [];
  selectedCategoryOption: IconBaseModel | undefined;
  loading = true; // Start with loading true
  changingCategory = false;

  constructor(
    private categoriesService: CategoriesService,
    private adminService: AdminServiceService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // We already set loading to true in the property initialization
    this.loadCategories();
  }

  ngAfterViewInit(): void {
    // This will run after the view has been initialized
    this.cdr.detectChanges();
  }

  loadCategories(): void {
    // loading is already set to true in the property initialization
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms) => {
        this.platforms = platforms.filter(platform => !platform.hide);

        // Extract all categories from all platforms
        this.categories = [];
        this.platforms.forEach(platform => {
          platform.categories
            .filter(category => !category.hide)
            .forEach(category => {
              // Add platform information to the category
              this.categories.push({
                ...category,
                platformIcon: platform.icon as IconName,
                platformName: platform.name
              });
            });
        });

        // Convert categories to IconBaseModel format for the dropdown
        this.categoryOptions = this.categories.map(category => ({
          id: category.id.toString(),
          sort: category.sort,
          label: `${category.name} (${category.platformName})`,
          icon: category.platformIcon || 'facebook'
        }));

        // Set selected category if available
        if (this.selectedCategoryId) {
          this.selectedCategoryOption = this.categoryOptions.find(
            option => option.id === this.selectedCategoryId?.toString()
          );
        }

        this.loading = false;
        // Manually trigger change detection to avoid ExpressionChangedAfterItHasBeenCheckedError
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.loading = false;
        // Manually trigger change detection to avoid ExpressionChangedAfterItHasBeenCheckedError
        this.cdr.detectChanges();
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }


  onCategorySelect(selectedCategoryId: number | null): void {
    if (this.changingCategory) return;

    const category = this.categories.find(c => c.id === selectedCategoryId);
    if (category && selectedCategoryId) {
      if (this.serviceId) {
        // If we have a service ID, change the category
        this.changingCategory = true;
        this.loading = true;
        // Manually trigger change detection to avoid ExpressionChangedAfterItHasBeenCheckedError
        this.cdr.detectChanges();

        this.adminService.changeServiceCategory(this.serviceId, selectedCategoryId).subscribe({
          next: () => {
            console.log(`Service ${this.serviceId} category changed to ${selectedCategoryId}`);
            this.categorySelected.emit(category);
            this.changingCategory = false;
            this.loading = false;
            this.cdr.detectChanges();
            this.close.emit(); // Close after successful category change
          },
          error: (error) => {
            console.error('Error changing service category:', error);
            this.changingCategory = false;
            this.loading = false;
            this.cdr.detectChanges();
          }
        });
      } else {
        // If no service ID, just emit the selected category
        this.categorySelected.emit(category);
      }
    }
  }

  onDropdownSelect(selected: IconBaseModel): void {
    const categoryId = parseInt(selected.id);
    this.selectedCategoryId = categoryId;
    // Don't automatically emit - wait for the save button to be clicked
    // Manually trigger change detection to avoid ExpressionChangedAfterItHasBeenCheckedError
    this.cdr.detectChanges();
  }
}
