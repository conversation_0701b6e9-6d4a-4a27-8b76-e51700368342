### V<PERSON> dụ sử dụng API Create Token For User

### 1. Tạo token cho user với tenant mặc định
POST http://localhost:8080/v1/access/create-token
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "userId": 123
}

### 2. Tạo token cho user với tenant cụ thể
POST http://localhost:8080/v1/access/create-token
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "userId": 123,
  "tenantId": "550e8400-e29b-41d4-a716-446655440000"
}

### 3. Test với user không tồn tại (sẽ trả về lỗi)
POST http://localhost:8080/v1/access/create-token
Content-Type: application/json
Authorization: Bearer ey<PERSON><PERSON>b<PERSON>ciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "userId": 99999
}

### 4. Test với tenant không có quyền truy cập (sẽ trả về lỗi)
POST http://localhost:8080/v1/access/create-token
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "userId": 123,
  "tenantId": "unauthorized-tenant-id"
}

### 5. Test với request không hợp lệ (thiếu userId)
POST http://localhost:8080/v1/access/create-token
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "tenantId": "550e8400-e29b-41d4-a716-446655440000"
}

### Response mẫu thành công:
# {
#   "success": true,
#   "data": {
#     "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "clientId": "550e8400-e29b-41d4-a716-446655440000"
#   },
#   "message": null,
#   "errorCode": null
# }

### Response mẫu lỗi:
# {
#   "success": false,
#   "data": null,
#   "message": "User not found",
#   "errorCode": "USER_NOT_FOUND"
# }
