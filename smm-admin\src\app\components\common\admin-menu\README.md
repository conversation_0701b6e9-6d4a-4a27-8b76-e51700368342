# Admin Dropdown Menu System

This directory contains components and services for the admin dropdown menu system, which provides a consistent and improved dropdown menu experience across all admin components.

## Features

- Centralized dropdown menu management
- Proper positioning of menus (even when scrolling)
- Consistent styling and behavior
- Mobile-friendly design
- High z-index to prevent menus from being obscured by other elements
- Automatic closing of menus when clicking outside

## Components

### AdminMenuComponent

A reusable component for displaying dropdown menus in admin components.

#### Usage

1. Import the component in your component:

```typescript
import { AdminMenuComponent } from '../../common/admin-menu/admin-menu.component';
```

2. Add it to your component's imports:

```typescript
@Component({
  // ...
  imports: [
    // ...
    AdminMenuComponent
  ]
})
```

3. Use it in your template:

```html
<app-admin-menu 
  [menuItems]="menuActions" 
  (menuItemClicked)="handleMenuAction($event, item.id)">
  <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
</app-admin-menu>
```

4. Define menu items in your component:

```typescript
menuActions: MenuAction[] = [
  { id: 'edit', label: 'Edit', icon: 'pen', iconColor: 'text-blue-500' },
  { id: 'delete', label: 'Delete', icon: 'trash', iconColor: 'text-red-500' }
];
```

5. Handle menu item clicks:

```typescript
handleMenuAction(actionId: string, itemId: number): void {
  switch (actionId) {
    case 'edit':
      this.editItem(itemId);
      break;
    case 'delete':
      this.deleteItem(itemId);
      break;
  }
}
```

## Services

### AdminDropdownService

A service for managing dropdown menus across the application.

#### Usage

1. Import the service in your component:

```typescript
import { AdminDropdownService, DropdownOptions } from '../../../core/services/admin-dropdown.service';
```

2. Inject it in your constructor:

```typescript
constructor(private adminDropdownService: AdminDropdownService) {}
```

3. Use it to calculate menu positions:

```typescript
const position = this.adminDropdownService.calculatePosition(
  triggerElement,
  dropdownElement,
  { placement: 'bottom-right', offset: { x: 0, y: 5 } }
);
```

### AdminMenuService

A service that provides menu actions for different admin components.

#### Usage

1. Import the service in your component:

```typescript
import { AdminMenuService, MenuAction } from '../../../core/services/admin-menu.service';
```

2. Inject it in your constructor:

```typescript
constructor(private adminMenuService: AdminMenuService) {}
```

3. Get menu actions for a specific component:

```typescript
// For user menu
this.menuActions = this.adminMenuService.getUserMenuActions();

// For order menu
this.menuActions = this.adminMenuService.getOrderMenuActions();

// For ticket menu
this.menuActions = this.adminMenuService.getTicketMenuActions();

// For service menu
this.menuActions = this.adminMenuService.getServiceMenuActions();
```

## Directives

### AdminDropdownDirective

A directive for handling dropdown menu positioning and behavior.

#### Usage

1. Import the directive in your component:

```typescript
import { AdminDropdownDirective } from '../../../shared/directives/admin-dropdown.directive';
```

2. Add it to your component's imports:

```typescript
@Component({
  // ...
  imports: [
    // ...
    AdminDropdownDirective
  ]
})
```

3. Use it in your template:

```html
<div *ngIf="isOpen"
  appAdminDropdown
  [dropdownId]="dropdownId"
  [dropdownTrigger]="triggerElement"
  [dropdownOptions]="dropdownOptions"
  [isOpen]="isOpen"
  (dropdownClosed)="onDropdownClosed()">
  <!-- Dropdown content -->
</div>
```

## Styling

The admin dropdown menu system uses a consistent set of styles defined in `admin-dropdown.css`. Import this file in your component to ensure consistent styling:

```typescript
@Component({
  // ...
  styleUrls: ['./your-component.css', '../common/admin-dropdown.css']
})
```
