import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { isPlatformBrowser } from '@angular/common';
import { ConfigService } from './config.service';
import { GeneralSettingsReq } from '../../model/request/general-settings-req.model';
import { GeneralSettingsRes } from '../../model/response/general-settings-res.model';

// Constants
const GENERAL_SETTINGS_ID = '1';
const GENERAL_SETTINGS_STORAGE_KEY = 'general_settings';

@Injectable({
  providedIn: 'root'
})
export class GeneralSettingsService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  /**
   * Get general settings
   */
  getGeneralSettings(): Observable<GeneralSettingsRes> {
    return this.http.get<GeneralSettingsRes>(`${this.configService.apiUrl}/api/v1/general-settings/${GENERAL_SETTINGS_ID}`)
      .pipe(
        tap(settings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(GENERAL_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
          }
        }),
        catchError(error => {
          console.error('Error fetching general settings:', error);

          // Try to get from localStorage if available
          if (isPlatformBrowser(this.platformId)) {
            const storedSettings = localStorage.getItem(GENERAL_SETTINGS_STORAGE_KEY);
            if (storedSettings) {
              try {
                return of(JSON.parse(storedSettings));
              } catch (e) {
                console.error('Error parsing stored settings:', e);
              }
            }
          }

          // Return mock data as fallback
          return of(this.getMockGeneralSettings());
        })
      );
  }

  /**
   * Update general settings
   */
  updateGeneralSettings(settings: GeneralSettingsReq): Observable<GeneralSettingsRes> {
    // Add the ID to the settings
    const settingsWithId = {
      ...settings,
      id: GENERAL_SETTINGS_ID
    };

    return this.http.post<GeneralSettingsRes>(`${this.configService.apiUrl}/api/v1/general-settings`, settingsWithId)
      .pipe(
        tap(updatedSettings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(GENERAL_SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
          }
        }),
        catchError(error => {
          console.error('Error updating general settings:', error);
          // Return mock data for now
          return of(this.getMockGeneralSettings());
        })
      );
  }

  /**
   * Get mock general settings (fallback)
   */
  private getMockGeneralSettings(): GeneralSettingsRes {
    return {
      id: GENERAL_SETTINGS_ID,
      site_name: 'SMM Panel',
      site_description: 'Social Media Marketing Panel',
      contact_email: '<EMAIL>',
      support_email: '<EMAIL>',
      allow_registration: true,
      require_email_verification: true,
      enable_captcha: true,
      enable_2fa: false,
      min_order_amount: 1000,
      max_order_amount: 1000000,
      default_user_balance: 0,
      maintenance_mode: false,
      maintenance_message: 'Site is under maintenance. Please check back later.',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
}
