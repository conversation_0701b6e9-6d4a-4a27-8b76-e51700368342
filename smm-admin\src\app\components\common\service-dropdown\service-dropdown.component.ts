import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, OnInit, On<PERSON><PERSON>roy, ElementRef, HostListener, OnChanges, SimpleChanges, Renderer2 } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { ServiceLabelComponent } from "../service-label/service-label.component";
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { DropdownService } from '../../../core/services/dropdown.service';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-service-dropdown',
  standalone: true,
  imports: [CommonModule, IconsModule, ServiceLabelComponent],
  templateUrl: './service-dropdown.component.html',
  styleUrl: './service-dropdown.component.css'
})
export class ServiceDropdownComponent implements OnInit, OnDestroy, OnChanges {
  private dropdownMenuElement: HTMLElement | null = null;
  // Flag to track if we just opened the dropdown
  private justOpened = false;
  // Unique ID for this dropdown instance
  private dropdownId: string = `service-dropdown-${Math.random().toString(36).substring(2, 9)}`;
  // Subscription to the closeAllDropdowns observable
  private closeAllSubscription: Subscription;

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private dropdownService: DropdownService
  ) {
    // Subscribe to the closeAllDropdowns observable
    this.closeAllSubscription = this.dropdownService.closeAllDropdowns.subscribe(() => {
      if (this.isOpen) {
        this.isOpen = false;
        this.removeDropdownFromDOM();
        document.body.classList.remove('service-dropdown-open');
      }
    });
  }

  ngOnDestroy() {
    // Clean up any dropdown menu that might be in the DOM
    this.removeDropdownFromDOM();

    // Remove the class from the body if it was added
    document.body.classList.remove('service-dropdown-open');

    // Unsubscribe from the closeAllDropdowns observable
    if (this.closeAllSubscription) {
      this.closeAllSubscription.unsubscribe();
    }
  }

  @Input() lite: boolean = false;
  @Input() options: SuperGeneralSvRes[] = []; // Danh sách option
  @Input() placeholder: string = 'Chọn một tùy chọn';
  @Output() selected = new EventEmitter<SuperGeneralSvRes>(); // Emit giá trị đã chọn
  isOpen = false;

  // Track the selected option with a setter to handle external changes
  private _selectedOption: SuperGeneralSvRes = {} as SuperGeneralSvRes;

  get selectedOption(): SuperGeneralSvRes {
    return this._selectedOption;
  }

  @Input()
  set selectedOption(value: SuperGeneralSvRes | undefined) {
    if (value && value.id && (!this._selectedOption || this._selectedOption.id !== value.id)) {
      console.log('Service dropdown selectedOption changed externally:', value.id, value.name);
      this._selectedOption = value;
      // Don't emit here to avoid circular updates
    }
  }

  @Input() customClassButton: string = '';
  @Input() customClassDropdown : string = '';


  ngOnInit(): void {
    if (this.options.length > 0 && (!this._selectedOption || !this._selectedOption.id)) {
      // Only set the selected option if it's not already set
      this._selectedOption = this.options[0];
      this.selected.emit(this.options[0]);
      console.log('Service dropdown initialized with first option:', this._selectedOption.id, this._selectedOption.name);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // If options array changes, update the selected option to the first one only if not already set
    if (changes['options'] && changes['options'].currentValue && changes['options'].currentValue.length > 0) {
      const newOptions = changes['options'].currentValue;

      // If we have a selected option, try to find it in the new options array
      if (this._selectedOption && this._selectedOption.id) {
        const existingOption = newOptions.find((o: SuperGeneralSvRes) => o.id === this._selectedOption.id);
        if (existingOption) {
          // If the selected option exists in the new options, keep it selected
          console.log('Service dropdown options changed, keeping current selection:', this._selectedOption.id);
          return;
        }
      }

      // Otherwise, select the first option
      this._selectedOption = newOptions[0];
      this.selected.emit(this._selectedOption);
      console.log('Service dropdown options changed, selected first option:', this._selectedOption.id, this._selectedOption.name);
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // If we just opened the dropdown, ignore this click event
    if (this.justOpened) {
      this.justOpened = false;
      return;
    }

    // Check if the click is outside the dropdown
    const target = event.target as HTMLElement;
    const isInsideDropdown = this.elementRef.nativeElement.contains(target);
    const isInsideDropdownMenu = this.dropdownMenuElement && this.dropdownMenuElement.contains(target);

    if (!isInsideDropdown && !isInsideDropdownMenu) {
      this.isOpen = false;
      this.removeDropdownFromDOM();

      // Remove the class from the body
      document.body.classList.remove('service-dropdown-open');
    }
  }

  @HostListener('window:resize')
  onWindowResize() {
    if (this.isOpen) {
      this.updateDropdownPosition();
    }
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    if (this.isOpen) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        this.updateDropdownPosition();
      });
    }
  }

  toggleDropdown(event?: MouseEvent) {
    if (event) {
      // Stop the event from propagating to prevent document click handler from firing
      event.stopPropagation();
      event.preventDefault();
    }

    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      // Notify the dropdown service that this dropdown is now open
      this.dropdownService.openDropdown(this.dropdownId, this);

      // Set the flag to indicate we just opened the dropdown
      this.justOpened = true;

      // Add a small delay to ensure the dropdown is rendered before positioning
      setTimeout(() => {
        this.updateDropdownPosition();

        // Add a class to the body to prevent scrolling while dropdown is open
        document.body.classList.add('service-dropdown-open');

        // Force a reflow to ensure the dropdown is properly positioned
        const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.service-dropdown-menu-container');
        if (dropdownMenuElement) {
          void dropdownMenuElement.offsetHeight;
        }
      }, 0);
    } else {
      this.removeDropdownFromDOM();

      // Remove the class from the body
      document.body.classList.remove('service-dropdown-open');

      // Notify the dropdown service that this dropdown is now closed
      this.dropdownService.closeDropdown();
    }
  }

  selectOption(option: SuperGeneralSvRes, event?: MouseEvent) {
    if (event) {
      // Stop the event from propagating
      event.stopPropagation();
    }

    // Make sure we have a valid option
    if (!option || !option.id) {
      console.warn('Attempted to select an invalid service option:', option);
      return;
    }

    console.log('Service dropdown selecting option:', option.id, option.name);

    // Update the selected option using the setter
    this._selectedOption = option;

    // Emit the selected option
    this.selected.emit(option);

    // Close the dropdown
    this.isOpen = false;
    this.removeDropdownFromDOM();

    // Remove the class from the body
    document.body.classList.remove('service-dropdown-open');

    // Notify the dropdown service that this dropdown is now closed
    this.dropdownService.closeDropdown();
  }

  private updateDropdownPosition() {
    if (!this.isOpen) return;

    // Get the button element
    const buttonElement = this.elementRef.nativeElement.querySelector('button');
    if (!buttonElement) return;

    // Get the dropdown menu element
    const dropdownMenuElement = this.elementRef.nativeElement.querySelector('.service-dropdown-menu-container');
    if (!dropdownMenuElement) return;

    // Store reference to the dropdown menu for click detection
    this.dropdownMenuElement = dropdownMenuElement;

    // Get the button's position
    const buttonRect = buttonElement.getBoundingClientRect();

    // Calculate the position for the dropdown
    const top = buttonRect.bottom;
    const left = buttonRect.left;
    const width = buttonRect.width;

    // Set the position and width of the dropdown
    this.renderer.setStyle(dropdownMenuElement, 'position', 'fixed');
    this.renderer.setStyle(dropdownMenuElement, 'top', `${top}px`);
    this.renderer.setStyle(dropdownMenuElement, 'left', `${left}px`);
    this.renderer.setStyle(dropdownMenuElement, 'width', `${width}px`);
    this.renderer.setStyle(dropdownMenuElement, 'z-index', '99999');

    // Check if the dropdown would go off the bottom of the screen
    const dropdownHeight = dropdownMenuElement.offsetHeight;
    const viewportHeight = window.innerHeight;

    if (top + dropdownHeight > viewportHeight) {
      // Position the dropdown above the button instead
      const newTop = buttonRect.top - dropdownHeight;
      if (newTop >= 0) {
        this.renderer.setStyle(dropdownMenuElement, 'top', `${newTop}px`);
      } else {
        // If there's not enough space above either, limit the height and add scrolling
        this.renderer.setStyle(dropdownMenuElement, 'top', '10px');
        this.renderer.setStyle(dropdownMenuElement, 'max-height', `${viewportHeight - 20}px`);
        this.renderer.setStyle(dropdownMenuElement, 'overflow-y', 'auto');
      }
    }
  }

  private removeDropdownFromDOM() {
    // Just clear the reference, the actual DOM element will be removed by Angular
    // when isOpen becomes false
    this.dropdownMenuElement = null;
  }
}
