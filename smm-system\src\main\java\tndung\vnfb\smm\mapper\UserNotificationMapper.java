package tndung.vnfb.smm.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import tndung.vnfb.smm.dto.UserNotificationReq;
import tndung.vnfb.smm.dto.UserNotificationRes;
import tndung.vnfb.smm.entity.UserNotification;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserNotificationMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isRead", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    UserNotification toEntity(UserNotificationReq req);
    
    UserNotificationRes toDto(UserNotification entity);
    
    List<UserNotificationRes> toDto(List<UserNotification> entities);
}
