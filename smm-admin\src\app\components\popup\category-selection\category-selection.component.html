<div class="overlay-black" >
  <div class="modal-container">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'Select category' | translate }}</h2>
        <button type="button" class="close-button" (click)="onClose()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>



      <!-- Loading indicator -->
      <div *ngIf="loading" class="loading-spinner">
        <div class="spinner"></div>
      </div>

      <!-- Categories dropdown -->
      <div *ngIf="!loading" class="dropdown-container">
        <app-icon-dropdown
          [options]="categoryOptions"
          [selectedOption]="selectedCategoryOption"
          [placeholder]="'Select a category' | translate"
          (selected)="onDropdownSelect($event)"
          [customClassDropdown]="'bg-white rounded-lg border'"
          customClassDropdown="category-dropdown-container"
        ></app-icon-dropdown>
      </div>

      <!-- Empty state -->
      <div *ngIf="!loading && categories.length === 0" class="empty-state">
        <p>{{ 'No categories found' | translate }}</p>
      </div>

      <!-- Save button -->
      <button
        class="save-button"
        [attr.disabled]="(loading || changingCategory || !selectedCategoryId) ? true : null"
        (click)="onCategorySelect(selectedCategoryId)"
      >
        <span *ngIf="!loading && !changingCategory">{{ 'Save' | translate }}</span>
        <span *ngIf="loading || changingCategory">{{ 'Processing' | translate }}...</span>
      </button>
    </div>
  </div>
</div>

