/**
 * Centralized logging utility
 */

const config = require('../config/constants');

class Logger {
  constructor() {
    this.isDevelopment = config.NODE_ENV === 'development';
  }

  _formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  info(message, meta = {}) {
    console.log(this._formatMessage('info', message, meta));
  }

  warn(message, meta = {}) {
    console.warn(this._formatMessage('warn', message, meta));
  }

  error(message, meta = {}) {
    console.error(this._formatMessage('error', message, meta));
  }

  debug(message, meta = {}) {
    if (this.isDevelopment) {
      console.log(this._formatMessage('debug', message, meta));
    }
  }

  success(message, meta = {}) {
    console.log(this._formatMessage('success', message, meta));
  }
}

module.exports = new Logger();
