/* Using standardized component styles from the main CSS */
.search-container {
  @apply flex items-center px-4 bg-white rounded-lg border border-[var(--gray-200)];
  height: var(--input-height);
}

.search-input {
  @apply flex-grow border-none outline-none font-roboto text-base text-[var(--gray-700)];
}

.search-button {
  @apply bg-[var(--primary)] text-white font-medium text-sm px-4 py-2 rounded-md cursor-pointer transition-colors duration-300;
  height: calc(var(--input-height) - 8px);
}

.search-button:hover {
  @apply bg-[var(--primary-hover)];
}

.search-button:active {
  @apply bg-[var(--primary-active)];
}