/* Drag and drop styles */
.service-row {
  transition: all 0.2s ease;
  position: relative;
}

.service-row.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.service-row.drop-target {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.05);
}

.service-row.drag-enter {
  background-color: rgba(0, 149, 246, 0.1);
}

/* Cursor styles */
.service-row {
  cursor: grab;
}

.service-row:active {
  cursor: grabbing;
}

/* Empty drop zone styles */
.empty-drop-zone {
  transition: all 0.2s ease;
}

.empty-drop-zone.drop-target {
  border: 2px dashed #0095f6;
  background-color: rgba(0, 149, 246, 0.1);
}

/* Category column styles */
.category-column {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ensure the services list takes up all available space */
.services-list {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
}
