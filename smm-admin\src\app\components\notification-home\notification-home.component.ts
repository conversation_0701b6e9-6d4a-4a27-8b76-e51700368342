import { Component, Input, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SafeHtml } from '@angular/platform-browser';
import { IconsModule } from '../../icons/icons.module';

@Component({
  selector: 'app-notification-home',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './notification-home.component.html',
  styleUrl: './notification-home.component.css'
})
export class NotificationHomeComponent implements OnChanges {
  @Input() title: string = '';
  @Input() content: SafeHtml = null!;
  

  constructor() {}

  ngOnChanges(): void {
    // Check if we have content and title
   

    if (this.content) {
      console.log('NotificationHomeComponent - Received fixed notification content');
    }
  }
}
