<div class="notification-dropdown-container">
  <!-- Notification Bell Icon -->
  <div class="notification-bell cursor-pointer" (click)="toggleDropdown()">
    <fa-icon [icon]="faBell" class="text-lg text-[var(--primary)]"></fa-icon>
    <span *ngIf="unreadCount > 0" class="notification-badge">
      {{ unreadCount > 99 ? '99+' : unreadCount }}
    </span>
  </div>

  <!-- Dropdown Menu -->
  <div *ngIf="isOpen" class="notification-dropdown">
    <!-- Triangle pointer -->
    <div class="triangle-pointer"></div>

    <!-- Header -->
    <div class="dropdown-header">
      <div class="header-left">
        <h3 class="header-title">Thông báo</h3>
        <span *ngIf="unreadCount > 0" class="unread-count">({{ unreadCount }} chưa đọc)</span>
      </div>
      <div class="header-actions">
        <button 
          class="filter-btn" 
          (click)="toggleNotificationFilter()"
          [class.active]="!showAllNotifications">
          {{ showAllNotifications ? 'Chưa đọc' : 'Tất cả' }}
        </button>
        <button 
          *ngIf="unreadCount > 0" 
          class="mark-all-btn"
          (click)="markAllAsRead()">
          Đánh dấu đã đọc
        </button>
      </div>
    </div>

    <!-- Loading -->
    <div *ngIf="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <span>Đang tải...</span>
    </div>

    <!-- Notifications List -->
    <div *ngIf="!loading" class="notifications-list">
      <div 
        *ngFor="let notification of notifications" 
        class="notification-item"
        [class.unread]="!notification.is_read"
        (click)="markAsRead(notification)">
        
        <div class="notification-icon">
          <fa-icon 
            [icon]="getNotificationIcon(notification.type)" 
            [class]="getNotificationColor(notification.type)">
          </fa-icon>
        </div>
        
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.content }}</div>
          <div class="notification-time">{{ formatDate(notification.created_at) }}</div>
        </div>
        
        <div *ngIf="!notification.is_read" class="unread-indicator"></div>
      </div>

      <!-- Empty State -->
      <div *ngIf="notifications.length === 0" class="empty-state">
        <fa-icon [icon]="faBell" class="empty-icon"></fa-icon>
        <p class="empty-message">
          {{ showAllNotifications ? 'Không có thông báo nào' : 'Không có thông báo chưa đọc' }}
        </p>
      </div>
    </div>

    <!-- Footer -->
    <div class="dropdown-footer">
      <button class="view-all-btn">Xem tất cả thông báo</button>
    </div>
  </div>
</div>

<!-- Overlay -->
<div *ngIf="isOpen" class="overlay" (click)="toggleDropdown()"></div>
