# Balance Management System - Implementation Guide

## 📋 Overview

This document describes the new **Balance Management System** that ensures all user balance changes are properly logged in `GTransaction` table. The system provides a centralized, consistent, and audit-friendly approach to handling balance operations.

## 🎯 Problem Solved

**Before**: Balance was modified directly in multiple places without consistent transaction logging:
```java
// ❌ OLD WAY - Direct balance modification
user.setBalance(user.getBalance().add(amount));
gUserRepository.save(user);
// Transaction logging was manual and inconsistent
```

**After**: All balance changes go through `BalanceService` with automatic transaction logging:
```java
// ✅ NEW WAY - Centralized balance management
balanceService.addBalance(user, amount, TransactionSource.BONUS, "Reason for change");
// Transaction is automatically created and logged
```

## 🔧 Core Components

### 1. BalanceService Interface
- `addBalance()` - Add funds with transaction logging
- `deductBalance()` - Deduct funds with transaction logging  
- `processRefund()` - Handle order refunds automatically
- `hasSufficientBalance()` - Check balance before operations
- `transferBalance()` - Transfer between users (future use)

### 2. BalanceServiceImpl
- Implements all balance operations with automatic transaction logging
- Handles validation, error checking, and audit trails
- Ensures data consistency with `@Transactional` annotations

## 📚 Usage Examples

### Adding Balance (Deposits, Bonuses, etc.)
```java
@Autowired
private BalanceService balanceService;

// Add deposit
balanceService.addBalance(user, depositAmount, TransactionSource.DEPOSIT, 
    "Deposit via PayPal: $100.00");

// Add bonus
balanceService.addBalance(user, bonusAmount, TransactionSource.BONUS, 
    "Welcome bonus for new user");

// Add commission
balanceService.addBalance(user, commissionAmount, TransactionSource.BONUS, 
    "Commission from referral: user123");
```

### Deducting Balance (Orders, Penalties, etc.)
```java
// Check balance first
if (!balanceService.hasSufficientBalance(user, orderAmount)) {
    throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
}

// Deduct for order
balanceService.deductBalance(user, orderAmount, TransactionSource.ADD_ORDER, 
    "Order #12345: Instagram Followers", order);

// Deduct penalty
balanceService.deductBalance(user, penaltyAmount, TransactionSource.REMOVE, 
    "Penalty for policy violation");
```

### Processing Refunds
```java
// Automatic refund calculation and processing
balanceService.processRefund(order);
// This handles CANCELED (full refund) and PARTIAL (proportional refund) automatically
```

## 🔄 Migration Guide

### Step 1: Replace Direct Balance Modifications

**Before:**
```java
user.setBalance(user.getBalance().add(amount));
gUserRepository.save(user);

// Manual transaction creation
GTransaction transaction = new GTransaction();
transaction.setUserId(user.getId());
transaction.setChange(amount);
transaction.setBalance(user.getBalance());
transaction.setType(TransactionType.Bonus);
transaction.setSource(TransactionSource.BONUS);
transactionRepository.save(transaction);
```

**After:**
```java
balanceService.addBalance(user, amount, TransactionSource.BONUS, "Reason");
// Transaction is automatically created
```

### Step 2: Update Service Dependencies

Add `BalanceService` to your service constructors:
```java
@Service
@RequiredArgsConstructor
public class YourService {
    private final BalanceService balanceService;
    // ... other dependencies
}
```

### Step 3: Update Method Implementations

Replace balance modification logic with BalanceService calls:
```java
// ❌ Remove this pattern
user.setBalance(user.getBalance().subtract(orderAmount));
gUserRepository.save(user);

// ✅ Replace with this
balanceService.deductBalance(user, orderAmount, TransactionSource.ADD_ORDER, 
    "Order description", order);
```

## ✅ Benefits

1. **Consistency**: All balance changes follow the same pattern
2. **Audit Trail**: Every balance change is automatically logged
3. **Error Prevention**: Built-in validation and error handling
4. **Maintainability**: Centralized balance logic is easier to maintain
5. **Transaction Safety**: All operations are properly transactional
6. **Performance**: Optimized database operations

## 🛡️ Error Handling

The system includes comprehensive error handling:

- `BALANCE_NOT_ENOUGH` - Insufficient balance for deduction
- `INVALID_STATUS_TRANSITION` - Invalid order status change
- `TRANSACTION_NOT_FOUND` - Transaction record not found
- Automatic validation of amounts (must be positive)
- Null safety checks for all parameters

## 📊 Transaction Sources

Use appropriate `TransactionSource` values:
- `ADD_ORDER` - Order creation
- `CANCELED` - Order cancellation/refund
- `DEPOSIT` - User deposits
- `BONUS` - Bonuses, commissions, rewards
- `REMOVE` - Penalties, withdrawals, adjustments

## 🔍 Monitoring & Debugging

All operations are logged with detailed information:
```
INFO: Adding balance: userId=123, amount=50.00, source=BONUS
INFO: Balance added successfully: userId=123, oldBalance=100.00, newBalance=150.00
```

## 🚀 Future Enhancements

The system is designed to support future features:
- User-to-user transfers
- Escrow functionality
- Multi-currency support
- Advanced reporting and analytics
- Automated balance reconciliation

## ⚠️ Important Notes

1. **Always use BalanceService** for balance modifications
2. **Never modify balance directly** with `user.setBalance()`
3. **Check sufficient balance** before deductions
4. **Use appropriate TransactionSource** for proper categorization
5. **Provide meaningful notes** for audit purposes

This system ensures that every balance change in your application is properly tracked, validated, and logged for complete financial transparency and auditability.
