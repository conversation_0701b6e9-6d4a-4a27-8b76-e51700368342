FROM certbot/certbot:latest

# Install jq for JSON parsing
RUN apk add --no-cache jq

# Create directories
RUN mkdir -p /etc/letsencrypt/renewal-requests /etc/letsencrypt/renewal-requests/processed /var/log/letsencrypt

# Copy the request watcher script
COPY certbot-request-watcher.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/certbot-request-watcher.sh

# Set the entrypoint
ENTRYPOINT ["/bin/sh", "-c", "chmod +x /usr/local/bin/certbot-request-watcher.sh && /usr/local/bin/certbot-request-watcher.sh & trap exit TERM; while :; do certbot renew; sleep 12h & wait; done;"]
