.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
}

.modal-title {
  @apply text-xl font-semibold text-gray-800;
}

.close-button {
  @apply p-2 rounded-full text-gray-500 hover:text-gray-700 ;
}

.form-section {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-select {
  @apply w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-700 appearance-none;
  padding-right: 2.5rem;
}

.select-icon {
  @apply absolute right-0 top-0 h-full px-3 flex items-center pointer-events-none text-gray-500;
}

.search-container {
  @apply relative border border-gray-300 rounded-lg overflow-hidden;
}

.search-input {
  @apply w-full px-4 py-3 pl-10 bg-white border-none text-gray-700;
}

.search-icon {
  @apply absolute left-0 top-0 h-full px-3 flex items-center pointer-events-none text-gray-500;
}

.services-container {
  @apply mt-4 border border-gray-200 rounded-lg overflow-hidden;
  max-height: 300px;
  overflow-y: auto;
}

.loading-indicator {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.empty-state {
  @apply flex flex-col items-center justify-center p-8 text-center text-gray-500;
}

.select-all-row {
  @apply bg-gray-50;
}

.select-all-row input[type="checkbox"] {
  @apply rounded-sm;
}

.service-list {
  @apply divide-y divide-gray-200;
}

.service-item {
  @apply flex items-center p-3 hover:bg-gray-50 transition-colors duration-150;
  cursor: pointer;
}

.service-item:hover {
  @apply bg-blue-50;
}

.service-item:active {
  @apply bg-blue-100;
}

.service-checkbox {
  @apply relative flex items-center justify-center mr-3;
  min-width: 24px;
}

.service-checkbox input {
  @apply absolute opacity-0 h-0 w-0;
}

.checkmark {
  @apply h-5 w-5 border border-gray-300 rounded bg-white;
  display: inline-block;
  position: relative;
}

.service-checkbox input:checked ~ .checkmark {
  @apply bg-[#0095f6] border-[#0095f6];
}

.service-checkbox input:checked ~ .checkmark:after {
  content: "";
  position: absolute;
  left: 8px;
  top: 4px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: translate(-50%, -50%) rotate(45deg);
}

.service-info {
  @apply flex-1;
}

.service-id {
  @apply text-sm font-medium text-gray-900;
}

.service-name {
  @apply text-sm text-gray-500;
}

.action-container {
  @apply mt-6;
}

.import-button {
  @apply w-full py-3 px-4 bg-[#0095f6] text-white font-medium text-sm rounded-lg transition-colors duration-300;
}

.import-button:hover {
  @apply bg-blue-600;
}

.import-button:active {
  @apply bg-blue-700;
}

.import-button.disabled {
  @apply bg-gray-300 text-gray-500 cursor-not-allowed;
}
