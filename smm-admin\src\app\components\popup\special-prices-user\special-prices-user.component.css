.overlay-black {
  @apply flex justify-center items-center fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[var(--z-modal)];
}

.modal-container {
  @apply w-full max-w-md mx-auto;
}

.modal-content {
  @apply bg-white rounded-xl shadow-lg overflow-hidden p-6;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
  position: relative;
}

.modal-title {
  @apply text-xl font-semibold text-[var(--gray-800)];
}

.close-button {
  @apply p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors;
  position: absolute;
  top: 0;
  right: 0;
}

.user-info {
  @apply text-lg font-bold text-gray-800;
}

.section-title {
  @apply text-base font-semibold text-gray-700 mb-3;
}

.set-discount-container {
  @apply w-full;
}

.set-discount-title {
  @apply text-base font-semibold text-gray-700;
}

.set-discount-input {
  @apply w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#30B0C7] focus:border-transparent;
}

.special-prices-section {
  @apply mb-6;
}

.loading-spinner {
  @apply inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Special prices table styles */
.special-prices-table table {
  @apply w-full border-collapse;
}

.special-prices-table th {
  @apply text-sm font-medium text-gray-700;
  
  top: 0;
  background-color: white;
  z-index: 10;
  border-bottom: 1px solid #e5e7eb;
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
  border-radius: 0.5rem;
  box-shadow: 0 0 0 1px #e5e7eb;
}

.action-menu-button {
  @apply p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors;
}

.dropdown-menu {
  @apply shadow-lg rounded-lg overflow-hidden;
}

.add-special-price-button {
  @apply w-full flex items-center justify-center bg-[#0095f6] text-white font-medium py-3 px-4 rounded-lg hover:bg-blue-600 active:bg-blue-700 transition-colors duration-300;
}

.add-new-form {
  @apply mt-4;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#30B0C7] focus:border-transparent;
}

.form-buttons {
  @apply flex gap-3 mt-4;
}

.cancel-button {
  @apply flex-1 bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors;
}

.save-button {
  @apply flex-1 bg-[#30B0C7] text-white font-medium py-2 px-4 rounded-lg hover:bg-[#2599a8] active:bg-[#1e7f8a] transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed;
}



.error-message {
  @apply text-red-500 text-sm mb-4 p-3 bg-red-50 border border-red-200 rounded-lg;
}

.user-info,
.selected-service-info,
.selected-services-count {
  @apply text-sm;
}
