import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { catchError, finalize, map, tap } from 'rxjs/operators';
import { ConfigService } from './config.service';

interface SpecialPriceCountResponse {

    service_id: number;
    special_price_count: number;
}

@Injectable({
  providedIn: 'root'
})
export class SpecialPriceService {
  private _loading$ = new BehaviorSubject<boolean>(false);
  private _specialPriceCounts$ = new BehaviorSubject<Map<number, number>>(new Map<number, number>());

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  // Expose observables for components to subscribe to
  get loading$() {
    return this._loading$.asObservable();
  }

  get specialPriceCounts$() {
    return this._specialPriceCounts$.asObservable();
  }

  // Get special price counts for all services
  getSpecialPriceCounts(): Observable<Map<number, number>> {
    this._loading$.next(true);
    
    return this.http.get<SpecialPriceCountResponse[]>(`${this.configService.apiUrl}/users/services/custom-discount/count`)
      .pipe(
        map(response => {
          const countsMap = new Map<number, number>();
          
          if (response) {
            response.forEach(item => {
              countsMap.set(item.service_id, item.special_price_count);
            });
          }
          
          // Update the BehaviorSubject
          this._specialPriceCounts$.next(countsMap);
          
          return countsMap;
        }),
        catchError(error => {
          console.error('Error loading special price counts:', error);
          return of(new Map<number, number>());
        }),
        finalize(() => this._loading$.next(false))
      );
  }

  // Get special price count for a specific service
  getSpecialPriceCountForService(serviceId: number): number {
    const countsMap = this._specialPriceCounts$.value;
    return countsMap.get(serviceId) || 0;
  }

  // Update special price counts after adding/removing special prices
  updateSpecialPriceCounts(): void {
    this.getSpecialPriceCounts().subscribe();
  }
}
