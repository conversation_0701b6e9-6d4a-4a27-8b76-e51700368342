/**
 * Test example demonstrating the bidirectional partial refund logic
 * This shows how the system handles both refund and deduct scenarios
 */
public class PartialRefundTestExample {

    /**
     * Example test case for your specific scenarios
     */
    public void testBidirectionalPartialRefund() {
        // Setup test order
        GOrder order = createTestOrder();
        order.setId(12345L);
        order.setQuantity(100);
        order.setPrice(new BigDecimal("5.00")); // $5 per 1000 items
        order.setCharge(new BigDecimal("0.50")); // 100 * $5 / 1000
        order.setStatus(OrderStatus.IN_PROGRESS);
        
        GUser user = order.getUser();
        user.setBalance(new BigDecimal("10.00")); // Starting balance
        
        System.out.println("=== Initial State ===");
        System.out.println("Order: 100 items, $5/1000, Total: $0.50");
        System.out.println("User balance: $10.00");
        System.out.println();

        // Test Case 1: First partial remains = 30 (should refund 70 items)
        System.out.println("=== Test Case 1: Partial remains = 30 ===");
        order.setRemains(30);
        order.setStatus(OrderStatus.PARTIAL);
        
        // Expected: Refund 70 items = 70 * $5 / 1000 = $0.35
        balanceService.processRefund(order);
        
        System.out.println("Previous remains: 100 (original)");
        System.out.println("Current remains: 30");
        System.out.println("Difference: 100 - 30 = 70 items → REFUND");
        System.out.println("Expected refund: 70 * $5 / 1000 = $0.35");
        System.out.println("User balance after: $10.35");
        System.out.println();

        // Test Case 2: Second partial remains = 20 (should refund 10 items)
        System.out.println("=== Test Case 2: Partial remains = 20 ===");
        order.setRemains(20);
        
        // Expected: Refund 10 items = 10 * $5 / 1000 = $0.05
        balanceService.processRefund(order);
        
        System.out.println("Previous remains: 30 (from last transaction)");
        System.out.println("Current remains: 20");
        System.out.println("Difference: 30 - 20 = 10 items → REFUND");
        System.out.println("Expected refund: 10 * $5 / 1000 = $0.05");
        System.out.println("User balance after: $10.40");
        System.out.println();

        // Test Case 3: Third partial remains = 30 (should deduct 10 items)
        System.out.println("=== Test Case 3: Partial remains = 30 ===");
        order.setRemains(30);
        
        // Expected: Deduct 10 items = 10 * $5 / 1000 = $0.05
        balanceService.processRefund(order);
        
        System.out.println("Previous remains: 20 (from last transaction)");
        System.out.println("Current remains: 30");
        System.out.println("Difference: 20 - 30 = -10 items → DEDUCT");
        System.out.println("Expected deduct: 10 * $5 / 1000 = $0.05");
        System.out.println("User balance after: $10.35");
        System.out.println();

        // Test Case 4: Fourth partial remains = 50 (should deduct 20 items)
        System.out.println("=== Test Case 4: Partial remains = 50 ===");
        order.setRemains(50);
        
        // Expected: Deduct 20 items = 20 * $5 / 1000 = $0.10
        balanceService.processRefund(order);
        
        System.out.println("Previous remains: 30 (from last transaction)");
        System.out.println("Current remains: 50");
        System.out.println("Difference: 30 - 50 = -20 items → DEDUCT");
        System.out.println("Expected deduct: 20 * $5 / 1000 = $0.10");
        System.out.println("User balance after: $10.25");
        System.out.println();

        // Test Case 5: Fifth partial remains = 10 (should refund 40 items)
        System.out.println("=== Test Case 5: Partial remains = 10 ===");
        order.setRemains(10);
        
        // Expected: Refund 40 items = 40 * $5 / 1000 = $0.20
        balanceService.processRefund(order);
        
        System.out.println("Previous remains: 50 (from last transaction)");
        System.out.println("Current remains: 10");
        System.out.println("Difference: 50 - 10 = 40 items → REFUND");
        System.out.println("Expected refund: 40 * $5 / 1000 = $0.20");
        System.out.println("User balance after: $10.45");
        System.out.println();

        System.out.println("=== Final Summary ===");
        System.out.println("Starting balance: $10.00");
        System.out.println("Final balance: $10.45");
        System.out.println("Net refund: $0.45");
        System.out.println("Final remains: 10 items");
        System.out.println("Items refunded: 90 items (100 - 10)");
        System.out.println("Expected total refund: 90 * $5 / 1000 = $0.45 ✓");
    }

    /**
     * Test insufficient balance scenario
     */
    public void testInsufficientBalanceScenario() {
        System.out.println("=== Insufficient Balance Test ===");
        
        GOrder order = createTestOrder();
        order.setQuantity(100);
        order.setPrice(new BigDecimal("5.00"));
        order.setRemains(20); // Previous state
        
        GUser user = order.getUser();
        user.setBalance(new BigDecimal("0.02")); // Very low balance
        
        System.out.println("User balance: $0.02");
        System.out.println("Attempting to set remains from 20 to 50 (need to deduct $0.15)");
        
        order.setRemains(50);
        
        try {
            balanceService.processRefund(order);
            System.out.println("ERROR: Should have thrown insufficient balance exception!");
        } catch (InvalidParameterException e) {
            System.out.println("✓ Correctly threw insufficient balance exception");
            System.out.println("Error code: " + e.getErrorCode());
        }
    }

    /**
     * Helper method to create test order
     */
    private GOrder createTestOrder() {
        GOrder order = new GOrder();
        GUser user = new GUser();
        user.setId(1L);
        order.setUser(user);
        return order;
    }
}
