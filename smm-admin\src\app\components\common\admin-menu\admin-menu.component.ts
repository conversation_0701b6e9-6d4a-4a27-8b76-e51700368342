import { Component, Input, Output, EventEmitter, ElementRef, ViewChild, AfterViewInit, OnDestroy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { AdminDropdownService, DropdownOptions } from '../../../core/services/admin-dropdown.service';
import { IconName } from '@fortawesome/fontawesome-svg-core';

export interface AdminMenuItem {
  id: string;
  label: string;
  icon?: IconName;
  iconColor?: string;
  divider?: boolean;
  disabled?: boolean;
}

@Component({
  selector: 'app-admin-menu',
  standalone: true,
  imports: [CommonModule, IconsModule],
  template: `
    <div class="relative inline-block text-left">
      <!-- Trigger button -->
      <button #menuTrigger
        type="button"
        class="dropdown-container action-menu-button"
        [class.menu-open]="isOpen"
        (click)="toggleMenu($event)">
        <ng-content></ng-content>
      </button>
    </div>

    <!-- Dropdown menu -->
    <div *ngIf="isOpen"
      #menuContent
      class="dropdown-menu menu-visible"
      (click)="$event.stopPropagation()">
      <div class="py-1 divide-y divide-gray-100">
        <ng-container *ngFor="let item of menuItems">
          <!-- Divider -->
          <div *ngIf="item.divider" class="menu-divider"></div>

          <!-- Menu item -->
          <a *ngIf="!item.divider"
            [class.disabled]="item.disabled"
            (click)="onMenuItemClick(item, $event)"
            class="menu-item">
            <fa-icon *ngIf="item.icon"
              [icon]="['fas', item.icon]"
              class="menu-item-icon"
              [ngClass]="item.iconColor || 'text-gray-600'">
            </fa-icon>
            <span class="menu-item-label">{{ item.label }}</span>
          </a>
        </ng-container>
      </div>
    </div>
  `,
  styles: [`
    .relative {
      position: relative;
    }

    .dropdown-menu {
      background-color: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      width: auto; /* Allow width to adjust based on content */
      min-width: 224px; /* Minimum width to maintain consistency */
      max-width: 400px; /* Maximum width to prevent overly wide menus */
      z-index: 99999; /* Increased z-index to ensure it appears above all other elements */
      position: fixed; /* Use fixed positioning for proper placement */
      opacity: 0;
      transform: translateY(-5px);
      transition: opacity 0.2s ease-out, transform 0.2s ease-out;
      visibility: hidden;
    }

    .dropdown-menu.menu-visible {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }

    .menu-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      color: #374151;
      cursor: pointer;
      transition: background-color 0.2s;
      user-select: none; /* Prevent text selection */
      -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
      box-sizing: border-box; /* Ensure padding is included in height calculation */
      min-height: 44px; /* Ensure minimum height for touch targets */
      text-decoration: none; /* Remove underline from links */
      width: 100%; /* Ensure the entire width is clickable */
    }

    .menu-item:hover {
      background-color: #f3f4f6;
    }

    .menu-item:active {
      background-color: #e5e7eb; /* Darker background when clicked */
    }

    .menu-item.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none; /* Prevent clicks on disabled items */
    }

    .menu-item.disabled:hover {
      background-color: transparent;
    }

    .menu-item-icon {
      margin-right: 0.75rem;
      width: 1rem;
      flex-shrink: 0;
    }

    .menu-item-label {
      white-space: nowrap; /* Keep text on single line to maintain clean menu appearance */
    }

    .menu-divider {
      border-top: 1px solid #e5e7eb;
      margin: 0.25rem 0;
    }

    .action-menu-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      background-color: transparent;
      border: none;
      cursor: pointer;
      transition: background-color 0.2s;
      position: relative; /* Ensure position context for the menu */
    }

    .action-menu-button:hover {
      background-color: #f3f4f6;
    }

    .action-menu-button.menu-open {
      background-color: #f3f4f6;
    }

    @media (max-width: 768px) {
      .action-menu-button {
        min-width: 44px;
        min-height: 44px;
      }

      .menu-item {
        padding: 12px 16px;
        min-height: 44px;
      }

      .dropdown-menu {
        max-width: 90vw; /* Limit width on mobile to prevent overflow */
        min-width: 200px; /* Smaller minimum width on mobile */
      }
    }
  `]
})
export class AdminMenuComponent implements AfterViewInit, OnDestroy {
  @Input() menuItems: AdminMenuItem[] = [];
  @Input() dropdownOptions: DropdownOptions = {};
  @Input() isOpen = false;

  @Output() menuItemClicked = new EventEmitter<string>();
  @Output() menuClosed = new EventEmitter<void>();
  @Output() menuOpened = new EventEmitter<void>();

  @ViewChild('menuTrigger') menuTrigger!: ElementRef;
  @ViewChild('menuContent') menuContent!: ElementRef;

  dropdownId: string;

  // Handle window resize events
  @HostListener('window:resize')
  onResize() {
    if (this.isOpen) {
      this.updatePosition();
    }
  }

  // Handle window scroll events
  @HostListener('window:scroll', ['$event'])
  onScroll() {
    if (this.isOpen) {
      this.updatePosition();
    }
  }

  // Handle document clicks to close the menu when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Skip if menu is not open
    if (!this.isOpen) return;

    // Get the clicked element
    const target = event.target as HTMLElement;

    // Check if click is inside the menu or trigger
    if (this.menuContent && this.menuTrigger) {
      const clickedInMenu = this.menuContent.nativeElement.contains(target);
      const clickedOnTrigger = this.menuTrigger.nativeElement.contains(target);

      // If clicked inside menu, let the menu item click handler handle it
      if (clickedInMenu) {
        return;
      }

      // If clicked on trigger, let the toggle handler handle it
      if (clickedOnTrigger) {
        return;
      }

      // If clicked outside both menu and trigger, close the menu
      this.isOpen = false;
      this.adminDropdownService.closeDropdown(this.dropdownId);
      this.menuClosed.emit();
    }
  }

  constructor(private adminDropdownService: AdminDropdownService) {
    this.dropdownId = this.adminDropdownService.generateDropdownId('admin-menu');
  }

  ngAfterViewInit() {
    // Set default dropdown options if not provided
    if (!this.dropdownOptions.placement) {
      this.dropdownOptions.placement = 'bottom-right';
    }

    if (!this.dropdownOptions.offset) {
      this.dropdownOptions.offset = { x: 0, y: 5 };
    }
  }

  toggleMenu(event: MouseEvent) {
    // Prevent default behavior and stop propagation
    event.stopPropagation();
    event.preventDefault();

    // Toggle the menu state
    this.isOpen = !this.isOpen;
    console.log('Menu toggled:', this.isOpen);

    if (this.isOpen) {
      // Register with the dropdown service when opening
      this.adminDropdownService.openDropdown(this.dropdownId, {
        close: () => this.onDropdownClosed(),
        updatePosition: () => this.updatePosition()
      });

      // Emit the opened event
      this.menuOpened.emit();

      // Position the menu immediately after it's rendered
      requestAnimationFrame(() => {
        if (this.menuContent && this.menuTrigger) {
          this.updatePosition();
        }
      });
    } else {
      // Close the dropdown
      this.adminDropdownService.closeDropdown(this.dropdownId);
      this.menuClosed.emit();
    }
  }

  updatePosition() {
    if (this.menuContent && this.menuTrigger) {
      // Get the dropdown element
      const dropdownElement = this.menuContent.nativeElement;

      // Get the trigger element
      const triggerElement = this.menuTrigger.nativeElement;

      // Get the trigger's position
      const rect = triggerElement.getBoundingClientRect();

      // Get viewport dimensions
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Get dropdown dimensions
      const dropdownHeight = dropdownElement.offsetHeight;
      const dropdownWidth = dropdownElement.offsetWidth;

      // Determine if there's enough space below
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;

      // Set position to fixed for proper positioning
      dropdownElement.style.position = 'fixed';

      // Set z-index to ensure it appears above other elements
      dropdownElement.style.zIndex = '99999';

      // Calculate position
      let top, left;

      // Determine if menu should appear above or below
      if (spaceBelow < dropdownHeight && spaceAbove >= dropdownHeight) {
        // Position above
        top = rect.top - dropdownHeight - 5;
      } else {
        // Position below
        top = rect.bottom + 5;
      }

      // Determine horizontal position (right-aligned by default)
      left = rect.right - dropdownWidth;

      // Ensure menu doesn't go off screen
      if (left < 10) {
        left = 10;
      }

      if (left + dropdownWidth > viewportWidth - 10) {
        left = viewportWidth - dropdownWidth - 10;
      }

      // Apply position
      dropdownElement.style.top = `${top}px`;
      dropdownElement.style.left = `${left}px`;
    }
  }

  onMenuItemClick(item: AdminMenuItem, event: MouseEvent) {
    // Stop event propagation to prevent document click handler from firing
    event.stopPropagation();
    event.preventDefault();

    // Skip if item is disabled
    if (item.disabled) return;

    console.log('Menu item clicked:', item.id);

    // Close the menu immediately
    this.isOpen = false;
    this.adminDropdownService.closeDropdown(this.dropdownId);
    this.menuClosed.emit();

    // Emit the menu item click event immediately
    this.menuItemClicked.emit(item.id);
  }

  onDropdownClosed() {
    console.log('Dropdown closed');
    this.isOpen = false;
    this.menuClosed.emit();
  }

  ngOnDestroy() {
    // Clean up by closing the dropdown
    if (this.isOpen) {
      this.isOpen = false;
      this.adminDropdownService.closeDropdown(this.dropdownId);
    }
  }
}
