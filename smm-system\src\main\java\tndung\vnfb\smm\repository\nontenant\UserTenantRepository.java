package tndung.vnfb.smm.repository.nontenant;

import org.springframework.data.jpa.repository.JpaRepository;
import tndung.vnfb.smm.entity.UserTenant;

import java.util.List;


public interface UserTenantRepository extends JpaRepository<UserTenant, Long> {

    /**
     * Check if a user belongs to a specific tenant
     *
     * @param userId The user ID
     * @param tenantId The tenant ID
     * @return true if the user belongs to the tenant, false otherwise
     */
    boolean existsByUserIdAndTenantId(Long userId, String tenantId);

    /**
     * Find all users belonging to a specific tenant
     *
     * @param tenantId The tenant ID
     * @return List of UserTenant entities
     */
    List<UserTenant> findByTenantId(String tenantId);

    List<UserTenant> findByUserId(Long userId);
}
