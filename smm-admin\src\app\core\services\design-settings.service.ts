import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { DesignSettingsReq } from '../../model/request/design-settings-req.model';
import { DesignSettingsRes } from '../../model/response/design-settings-res.model';
import { isPlatformBrowser } from '@angular/common';

// Fixed ID for design settings
const DESIGN_SETTINGS_ID = '5d4efe10-cc87-4557-830e-9057fbff78b7';
const DESIGN_SETTINGS_STORAGE_KEY = 'app_design_settings';
const TENANT_ID_KEY = 'tenant_id';

@Injectable({
  providedIn: 'root'
})
export class DesignSettingsService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  /**
   * Get design settings
   */
  getDesignSettings(): Observable<DesignSettingsRes> {
    return this.http.get<DesignSettingsRes>(`${this.configService.apiUrl}/design-settings/current`)
      .pipe(
        tap(settings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
            // Also save the tenant ID
            if (settings.id) {
              localStorage.setItem(TENANT_ID_KEY, settings.id);
            }
          }
        }),
        catchError(error => {
          console.error('Error fetching design settings:', error);

          // Try to get from localStorage if available
          if (isPlatformBrowser(this.platformId)) {
            const storedSettings = localStorage.getItem(DESIGN_SETTINGS_STORAGE_KEY);
            if (storedSettings) {
              try {
                return of(JSON.parse(storedSettings));
              } catch (e) {
                console.error('Error parsing stored settings:', e);
              }
            }
          }

          // Return mock data as fallback
          return of(this.getMockDesignSettings());
        })
      );
  }

  /**
   * Create/Update design settings (all at once)
   */
  createSettings(settings: DesignSettingsReq): Observable<DesignSettingsRes> {
    return this.http.post<DesignSettingsRes>(`${this.configService.apiUrl}/design-settings`, settings)
      .pipe(
        tap(updatedSettings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
            // Also save the tenant ID
            if (updatedSettings.id) {
              localStorage.setItem(TENANT_ID_KEY, updatedSettings.id);
            }
          }
        })
      );
  }

  /**
   * Update design settings (legacy method)
   * @deprecated Use createSettings instead
   */
  updateDesignSettings(settings: DesignSettingsReq): Observable<DesignSettingsRes> {
    return this.createSettings(settings);
  }

  /**
   * Update color scheme
   */
  updateColorScheme(colorScheme: any): Observable<DesignSettingsRes> {
    return this.http.put<DesignSettingsRes>(`${this.configService.apiUrl}/design-settings/current/color-scheme`, colorScheme)
      .pipe(
        tap(updatedSettings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
          }
        }),
        catchError(error => {
          console.error('Error updating color scheme:', error);
          return of(this.getMockDesignSettings());
        })
      );
  }

  /**
   * Update header settings
   */
  updateHeaderSettings(headerSettings: any): Observable<DesignSettingsRes> {
    return this.http.put<DesignSettingsRes>(`${this.configService.apiUrl}/design-settings/current/header-settings`, headerSettings)
      .pipe(
        tap(updatedSettings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
          }
        }),
        catchError(error => {
          console.error('Error updating header settings:', error);
          return of(this.getMockDesignSettings());
        })
      );
  }

  /**
   * Update sidebar settings
   */
  updateSidebarSettings(sidebarSettings: any): Observable<DesignSettingsRes> {
    return this.http.put<DesignSettingsRes>(`${this.configService.apiUrl}/design-settings/current/sidebar-settings`, sidebarSettings)
      .pipe(
        tap(updatedSettings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
          }
        }),
        catchError(error => {
          console.error('Error updating sidebar settings:', error);
          return of(this.getMockDesignSettings());
        })
      );
  }

  /**
   * Update landing settings
   */
  updateLandingSettings(landingSettings: any): Observable<DesignSettingsRes> {
    return this.http.put<DesignSettingsRes>(`${this.configService.apiUrl}/design-settings/current/landing-settings`, landingSettings)
      .pipe(
        tap(updatedSettings => {
          // Save to localStorage for persistence
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
          }
        }),
        catchError(error => {
          console.error('Error updating landing settings:', error);
          return of(this.getMockDesignSettings());
        })
      );
  }

  /**
   * Upload logo
   */
  uploadLogo(file: File, isDarkLogo: boolean = false): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<any>(
      `${this.configService.apiUrl}/uploads/logo`,
      formData
    ).pipe(
      tap(response => {
        // Update the stored settings with the new logo URL
        if (isPlatformBrowser(this.platformId)) {
          const storedSettings = localStorage.getItem(DESIGN_SETTINGS_STORAGE_KEY);
          if (storedSettings) {
            try {
              const settings = JSON.parse(storedSettings);
              // Update the logo in the new structure
              if (!settings.logo) {
                settings.logo = { url: '', file_type: '' };
              }
              settings.logo.url = response.data.url;
              settings.logo.file_type = response.data.fileType;

              localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
            } catch (e) {
              console.error('Error updating stored settings with new logo:', e);
            }
          }
        }
      }),
      catchError(error => {
        console.error('Error uploading logo:', error);
        // Return mock URL for now
        return of({ data: { url: 'assets/images/logo.png', fileType: 'png' } });
      })
    );
  }

  /**
   * Upload favicon
   */
  uploadFavicon(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<any>(
      `${this.configService.apiUrl}/uploads/favicon`,
      formData
    ).pipe(
      tap(response => {
        // Update the stored settings with the new favicon URL
        if (isPlatformBrowser(this.platformId)) {
          const storedSettings = localStorage.getItem(DESIGN_SETTINGS_STORAGE_KEY);
          if (storedSettings) {
            try {
              const settings = JSON.parse(storedSettings);
              // Update the favicon in the new structure
              if (!settings.favicon) {
                settings.favicon = { url: '', file_type: '' };
              }
              settings.favicon.url = response.data.url;
              settings.favicon.file_type = response.data.fileType;

              localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
            } catch (e) {
              console.error('Error updating stored settings with new favicon:', e);
            }
          }
        }
      }),
      catchError(error => {
        console.error('Error uploading favicon:', error);
        // Return mock URL for now
        return of({ data: { url: 'assets/images/favicon.ico', fileType: 'ico' } });
      })
    );
  }

  /**
   * Update landing text
   */
  updateLandingText(text: string): Observable<{ success: boolean }> {
    return this.http.patch<{ success: boolean }>(
      `${this.configService.apiUrl}/api/v1/design-settings/${DESIGN_SETTINGS_ID}/landing-text`,
      { text }
    ).pipe(
      tap(() => {
        // Update the stored settings with the new landing text
        if (isPlatformBrowser(this.platformId)) {
          const storedSettings = localStorage.getItem(DESIGN_SETTINGS_STORAGE_KEY);
          if (storedSettings) {
            try {
              const settings = JSON.parse(storedSettings);
              settings.landing_text = text;
              localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
            } catch (e) {
              console.error('Error updating stored settings with new landing text:', e);
            }
          }
        }
      }),
      catchError(error => {
        console.error('Error updating landing text:', error);
        return of({ success: true });
      })
    );
  }

  /**
   * Get settings from localStorage
   */
  getSettingsFromLocalStorage(): DesignSettingsRes | null {
    if (isPlatformBrowser(this.platformId)) {
      const storedSettings = localStorage.getItem(DESIGN_SETTINGS_STORAGE_KEY);
      if (storedSettings) {
        try {
          return JSON.parse(storedSettings);
        } catch (e) {
          console.error('Error parsing stored settings:', e);
        }
      }
    }
    return null;
  }

  /**
   * Save settings to localStorage
   */
  saveSettingsToLocalStorage(settings: DesignSettingsRes): void {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem(DESIGN_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    }
  }

  /**
   * Get tenant ID for file uploads
   */
  getTenantId(): string {
    // First try to get from localStorage
    if (isPlatformBrowser(this.platformId)) {
      const storedTenantId = localStorage.getItem(TENANT_ID_KEY);
      if (storedTenantId) {
        return storedTenantId;
      }
    }

    // If not found, use the fixed ID
    return DESIGN_SETTINGS_ID;
  }

  /**
   * Mock design settings for development
   */
  private getMockDesignSettings(): DesignSettingsRes {
    return {
      id: DESIGN_SETTINGS_ID,
      logo: {
        url: 'assets/images/logo.png',
        file_type: 'png'
      },
      favicon: {
        url: 'assets/images/favicon.ico',
        file_type: 'ico'
      },
      color_scheme: {
        primary: '#30B0C7',
        text_on_buttons: '#FFFFFF'
      },
      landing_settings: {
        type: 'standard'
      },
      header_settings: {
        type: 'standard'
      },
      sidebar_settings: {
        type: 'standard'
      }
    };
  }
}
