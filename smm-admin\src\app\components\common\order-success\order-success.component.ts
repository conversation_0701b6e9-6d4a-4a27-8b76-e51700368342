import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderRes } from '../../../model/response/order-res.model';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-order-success',
  standalone: true,
  imports: [CommonModule, IconsModule],
  templateUrl: './order-success.component.html',
  styleUrl: './order-success.component.css'
})
export class OrderSuccessComponent {
  @Input() order: OrderRes | null = null;
  @Output() newOrder = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  get orderDetails() {
    if (!this.order) {
      return {
        orderId: '',
        service: '',
        link: '',
        quantity: 0,
        fee: 0,
        balance: 0
      };
    }

    return {
      orderId: this.order.id.toString(),
      service: this.order.service?.name || '',
      link: this.order.link || '',
      quantity: this.order.quantity || 0,
      fee: this.order.charge || 0,
      balance: 0 // This would need to be passed from the parent component
    };
  }

  onNewOrder() {
    this.newOrder.emit();
  }

  onClose() {
    this.close.emit();
  }
}
