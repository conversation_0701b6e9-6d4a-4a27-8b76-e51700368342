package tndung.vnfb.smm.aop;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.anotation.TenantAccessCheck;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.service.AuthenticationFacade;
import tndung.vnfb.smm.service.UserTenantAccessService;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;

/**
 * Aspect that handles tenant access validation for methods annotated with @TenantAccessCheck
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class TenantAccessCheckAspect {

    private final UserTenantAccessService userTenantAccessService;
    private final AuthenticationFacade authenticationFacade;

    /**
     * Advice that checks tenant access before executing methods annotated with @TenantAccessCheck
     */
    @Before("@annotation(tndung.vnfb.smm.anotation.TenantAccessCheck)")
    public void checkTenantAccess(JoinPoint joinPoint) {
        log.debug("Checking tenant access for method: {}", joinPoint.getSignature().getName());

        // Get the current user
        Long currentUserId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();

        // Extract tenant ID from method parameters
        String tenantId = extractTenantId(joinPoint);
        
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.warn("Tenant ID not found in method parameters for: {}", joinPoint.getSignature().getName());
            throw new InvalidParameterException(IdErrorCode.TENANT_ACCESS_DENIED);
        }

        // Get accessible tenant IDs for the current user
        List<String> accessibleTenants = userTenantAccessService.getAccessibleTenantIds(currentUserId);

        // Check if user has access to the requested tenant
        if (!accessibleTenants.contains(tenantId)) {
            log.warn("User {} does not have access to tenant: {}", currentUserId, tenantId);
            throw new InvalidParameterException(IdErrorCode.TENANT_ACCESS_DENIED);
        }

        log.debug("Tenant access check passed for user: {} and tenant: {}", currentUserId, tenantId);
    }

    /**
     * Extract tenant ID from method parameters
     */
    private String extractTenantId(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        Parameter[] parameters = method.getParameters();
        
        // Get the annotation to check for custom parameter name
        TenantAccessCheck annotation = method.getAnnotation(TenantAccessCheck.class);
        String customParameterName = annotation != null ? annotation.parameterName() : "";

        // Strategy 1: Look for custom parameter name if specified
        if (!customParameterName.isEmpty()) {
            for (int i = 0; i < parameters.length; i++) {
                if (parameters[i].getName().equals(customParameterName) && args[i] instanceof String) {
                    return (String) args[i];
                }
            }
        }

        // Strategy 2: Look for parameter named "tenantId"
        for (int i = 0; i < parameters.length; i++) {
            if ("tenantId".equals(parameters[i].getName()) && args[i] instanceof String) {
                return (String) args[i];
            }
        }

        // Strategy 3: Look for request objects with getTenantId() method
        for (Object arg : args) {
            if (arg != null) {
                try {
                    Method getTenantIdMethod = arg.getClass().getMethod("getTenantId");
                    Object result = getTenantIdMethod.invoke(arg);
                    if (result instanceof String) {
                        return (String) result;
                    }
                } catch (Exception e) {
                    // Method doesn't exist or failed to invoke, continue to next parameter
                }
            }
        }

        return null;
    }
}
