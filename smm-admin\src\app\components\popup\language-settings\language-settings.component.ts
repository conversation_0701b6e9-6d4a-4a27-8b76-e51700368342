import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

import { ToastService } from '../../../core/services/toast.service';
import { TenantSettingsService } from '../../../core/services/tenant-settings.service';
import { TenantLanguageSettingsRes } from '../../../model/response/tenant-language-settings-res.model';
import { TenantLanguageSettingsReq } from '../../../model/request/tenant-language-settings-req.model';
import { LanguageSelectorComponent } from '../language-selector/language-selector.component';

@Component({
  selector: 'app-language-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,
    LanguageSelectorComponent
  ],
  templateUrl: './language-settings.component.html',
  styleUrl: './language-settings.component.css'
})
export class LanguageSettingsComponent implements OnInit {
  @Output() close = new EventEmitter<void>();

  isLoading = false;
  selectedLanguage = 'vi';
  selectedAvailableLanguages: string[] = ['vi', 'en'];
  showLanguageSelector = false;

  allLanguages = [
    { code: 'vi', name: 'Tiếng Việt', flag: 'fi fi-vn' },
    { code: 'en', name: 'English', flag: 'fi fi-us' },
    { code: 'cn', name: 'China', flag: 'fi fi-cn' }
  ];

  constructor(
    private tenantSettingsService: TenantSettingsService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadCurrentSettings();
  }

  loadCurrentSettings(): void {
    this.isLoading = true;
    this.tenantSettingsService.getLanguageSettings().subscribe({
      next: (response: TenantLanguageSettingsRes) => {
        this.selectedLanguage = response.default_language || 'vi';
        this.selectedAvailableLanguages = response.available_languages || ['vi', 'en'];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading language settings:', error);
        this.toastService.showError(error?.message || 'Failed to load language settings');
        this.isLoading = false;
      }
    });
  }

  selectLanguage(languageCode: string): void {
    this.selectedLanguage = languageCode;
  }

  addLanguage(languageCode: string): void {
    if (!this.selectedAvailableLanguages.includes(languageCode)) {
      this.selectedAvailableLanguages.push(languageCode);
      // If this is the first language, make it default
      if (this.selectedAvailableLanguages.length === 1) {
        this.selectedLanguage = languageCode;
      }
    }
    this.showLanguageSelector = false;
  }

  removeLanguage(languageCode: string): void {
    if (this.selectedAvailableLanguages.length <= 1) {
      this.toastService.showError('At least one language must be selected');
      return;
    }

    const index = this.selectedAvailableLanguages.indexOf(languageCode);
    if (index > -1) {
      this.selectedAvailableLanguages.splice(index, 1);
      // If removed language was the default, reset default to first available
      if (this.selectedLanguage === languageCode && this.selectedAvailableLanguages.length > 0) {
        this.selectedLanguage = this.selectedAvailableLanguages[0];
      }
    }
  }

  getLanguageByCode(code: string) {
    return this.allLanguages.find(lang => lang.code === code);
  }

  saveSettings(): void {
    if (this.selectedAvailableLanguages.length === 0) {
      this.toastService.showError('Please select at least one available language');
      return;
    }

    if (!this.selectedAvailableLanguages.includes(this.selectedLanguage)) {
      this.toastService.showError('Default language must be one of the available languages');
      return;
    }

    this.isLoading = true;

    const request: TenantLanguageSettingsReq = {
      default_language: this.selectedLanguage,
      available_languages: this.selectedAvailableLanguages
    };

    this.tenantSettingsService.updateLanguageSettings(request).subscribe({
      next: () => {
        this.toastService.showSuccess('Language settings updated successfully');
        this.isLoading = false;
        this.close.emit();
      },
      error: (error) => {
        console.error('Error updating language settings:', error);
        this.toastService.showError(error?.message || 'Failed to update language settings');
        this.isLoading = false;
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }
}
