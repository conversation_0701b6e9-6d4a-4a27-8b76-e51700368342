import { Role } from '../../constant/role';


export enum CommonStatus {
  ACTIVATED = 'ACTIVATED',
  DEACTIVATED = 'DEACTIVATED'
}

export interface GUserSuperRes {
  api_key: string;
  id: number;
  name: string;
  user_name: string;
  email: string;
  phone: string;
  avatar: string;
  telegram: string;
  balance: number;
  status: CommonStatus;
  is_verify: boolean;
  last_login_at: string; // ZonedDateTime as string
  mfa_enabled: boolean;
  language: string;
  secret_key: string;
  roles: Role[];
  time_zone: string;
  custom_discount: number;
  special_price_count: number;
  custom_referral_rate: number;
  created_at: string; // ZonedDateTime as string
  total_spend: number;
}
