.container {
    @apply flex flex-col items-center bg-white p-6 rounded-lg shadow-md w-[500px] gap-4;
}

app-icon-section {
    @apply mb-4;
}

app-header-section {
    @apply mb-4;
}

app-social-media-list {
    @apply mb-4;
}

app-add-social-media-button {
    @apply mt-4;
}

@media (max-width: 640px) {
    .container {
        @apply w-full;
    }
}

.add-social-media-btn {
    @apply flex items-center justify-center bg-[#30b0c7] text-white font-bold py-2 px-4 rounded-lg min-w-[150px] h-[40px] transition-colors duration-300 w-full;
}

.add-social-media-btn:hover {
    @apply bg-[#2599a8];
}

.icon {
    @apply mr-2 w-5 h-5;
}

.social-media-list {
    @apply w-full min-w-[402px] bg-[#f5f7fc] p-4 rounded-lg;
}

.social-media-item {
    @apply flex flex-row justify-between items-center py-2 px-4 mb-4 hover:bg-gray-50;
}

.item-content {
    @apply flex flex-row items-center gap-2;
}

.more-icon {
    @apply w-5 h-5 cursor-pointer;
}

.social-icon {
    @apply w-5 h-5;
}

.item-name {
    @apply text-base font-semibold text-black ml-2;
}

.item-actions {
    @apply flex flex-row gap-3;
}

.action-btn {
    @apply p-1 hover:bg-gray-100 rounded-full transition-colors;
}

.action-icon {
    @apply w-5 h-5;
}

.icon-container {
    @apply flex justify-center items-center min-w-[86px] h-[86px] w-full max-w-full;
}

.education-icon {
    @apply w-[86px] h-[86px] object-contain transition-transform duration-200;
}

.education-icon:hover {
    @apply transform scale-105;
}

.header-section {
    @apply flex flex-col items-center text-center w-full max-w-[438px] mx-auto;
}

.title {
    @apply font-bold text-2xl leading-[22px] ;
}

.description {
    @apply text-base font-normal leading-[22px];
}