/**
 * Subdomain management controller
 */

const express = require('express');
const DomainService = require('../services/domain-service');
const { asyncHandler } = require('../middleware/error-handler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Create a subdomain
 * POST /subdomains/:subdomain
 */
router.post('/:subdomain', asyncHandler(async (req, res) => {
  const subdomain = req.params.subdomain;
  logger.info(`Received request to create subdomain: ${subdomain}`);

  const result = await DomainService.createSubdomain(subdomain);
  
  res.status(201).json(result);
}));

module.exports = router;
