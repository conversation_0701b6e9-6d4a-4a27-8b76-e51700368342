import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';

interface PanelInfo {
  domain: string;
  currency: string;
  currencyCode: string;
}

@Component({
  selector: 'app-new-panel-step3',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './new-panel-step3.component.html',
  styleUrl: './new-panel-step3.component.css'
})
export class NewPanelStep3Component {
  @Input() domain: string = '';
  @Output() close = new EventEmitter<void>();
  @Output() back = new EventEmitter<void>();
  @Output() createPanel = new EventEmitter<PanelInfo>();

  selectedCurrency: string = 'USD';
  selectedCurrencyName: string = 'US Dollar';

  getCurrencyName(code: string): string {
    const currency = this.currencies.find(c => c.code === code);
    return currency ? currency.name : this.selectedCurrencyName;
  }

  // This would typically come from a service
  currencies = [
    { code: 'USD', name: 'US Dollar' },
  ];

  onClose(): void {
    this.close.emit();
  }

  onBack(): void {
    this.back.emit();
  }

  onCreatePanel(): void {
    const panelInfo: PanelInfo = {
      domain: this.domain,
      currency: this.selectedCurrencyName,
      currencyCode: this.selectedCurrency
    };
    this.createPanel.emit(panelInfo);
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }
}
