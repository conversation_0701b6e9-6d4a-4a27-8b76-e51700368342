import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';
import { Tenant } from '../../model/tenant.model';

export interface ChildPanelReq {
  domain: string;
}

@Injectable({
  providedIn: 'root'
})
export class PanelService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Create a new panel
   * @param domain The domain for the new panel
   */
  createPanel(domain: string): Observable<Tenant> {
    const req: ChildPanelReq = { domain };
    return this.http.post<Tenant>(`${this.configService.apiUrl}/panels`, req);
  }
}
