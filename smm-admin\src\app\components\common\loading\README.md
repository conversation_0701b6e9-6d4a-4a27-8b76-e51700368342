# Loading Component

A standardized loading component for the SMM application that uses the `--primary` color variable.

## Features

- Consistent loading spinner across the application
- Uses the `--primary` color variable for branding consistency
- Multiple size options (small, medium, large)
- Support for overlay and full-screen modes
- Optional message display

## Usage

### Basic Usage

```html
<!-- Simple inline loading spinner -->
<app-loading></app-loading>
```

### Size Options

```html
<!-- Small loading spinner -->
<app-loading [size]="'sm'"></app-loading>

<!-- Medium loading spinner (default) -->
<app-loading [size]="'md'"></app-loading>

<!-- Large loading spinner -->
<app-loading [size]="'lg'"></app-loading>
```

### Overlay Mode

```html
<!-- Loading spinner with overlay (for container elements) -->
<div class="relative">
  <!-- Your content here -->
  <app-loading [overlay]="true"></app-loading>
</div>
```

### Full Screen Mode

```html
<!-- Full screen loading spinner (for page loading) -->
<app-loading [fullScreen]="true"></app-loading>
```

### With Message

```html
<!-- Loading spinner with message -->
<app-loading [message]="'Loading data...'"></app-loading>

<!-- Full screen loading with message -->
<app-loading 
  [fullScreen]="true" 
  [message]="'Processing your request...'"
  [size]="'lg'">
</app-loading>
```

### Transparent Overlay

```html
<!-- Loading spinner with transparent overlay -->
<div class="relative">
  <!-- Your content here -->
  <app-loading [overlay]="true" [transparent]="true"></app-loading>
</div>
```

## Using with Loading Service

The application includes a `LoadingService` that can be used to manage loading states globally:

```typescript
import { Component } from '@angular/core';
import { LoadingService } from '../../../core/services/loading.service';

@Component({
  selector: 'app-my-component',
  template: `
    <!-- Your component content -->
  `
})
export class MyComponent {
  constructor(private loadingService: LoadingService) {}

  loadData() {
    // Show loading
    this.loadingService.show('Loading data...');
    
    // Your async operation
    this.dataService.getData().subscribe({
      next: (data) => {
        // Process data
        this.loadingService.hide();
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.loadingService.hide();
      }
    });
  }
}
```

## Using with Loading Directive

For more control over loading states in specific elements, use the `appLoading` directive:

```html
<div class="data-container" [appLoading]="isLoading">
  <!-- Content here -->
</div>
```

```typescript
import { Component } from '@angular/core';

@Component({
  selector: 'app-my-component',
  template: `
    <div class="data-container" [appLoading]="isLoading">
      <!-- Content here -->
    </div>
    <button (click)="loadData()">Load Data</button>
  `
})
export class MyComponent {
  isLoading = false;

  loadData() {
    this.isLoading = true;
    
    // Your async operation
    setTimeout(() => {
      this.isLoading = false;
    }, 2000);
  }
}
```
