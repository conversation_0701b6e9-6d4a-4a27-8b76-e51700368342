.layout-container {
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Status badge styling */
.status-badge {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

/* Table styling */
table {
  width: 100%;
  font-size: 0.875rem;
  text-align: left;
  color: #d1d5db;
}

thead {
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
}

th, td {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

tbody tr {
  border-bottom-width: 1px;
  border-color: #1f2937;
}

/* Filter tabs */
.filter-tab {
  display: inline-block;
  padding: 1rem;
  border-color: transparent;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.filter-tab.active {
  color: var(--primary);
  border-bottom-width: 2px;
  border-color: var(--primary);
}

.filter-tab:not(.active) {
  color: #9ca3af;
}

.filter-tab:not(.active):hover {
  border-bottom-width: 2px;
  color: #e5e7eb;
  border-color: #374151;
}

/* Search box */
.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  background-color: #1f2937;
  color: white;
  border-width: 1px;
  border-color: #374151;
  border-radius: 0.5rem;
}

.search-input:focus {
  outline: none;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: var(--primary);
}

.search-button {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  background-color: var(--primary);
  color: white;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.search-button:hover {
  opacity: 0.9;
}

/* Filter button */
.filter-button {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  height: 100%;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  border-width: 1px;
  border-color: #374151;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
}

.filter-button:hover {
  background-color: #1f2937;
}

.filter-button:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: #374151;
}

/* Action buttons and menu */
.action-button {
  padding: 0.25rem;
  border-radius: 9999px;
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.action-button:hover {
  background-color: #374151;
}

.edit-button {
  color: var(--primary);
}

.edit-button:hover {
  color: var(--primary-dark);
}

.delete-button {
  color: #ef4444;
}

.delete-button:hover {
  color: #b91c1c;
}

/* Action menu dropdown */
.dropdown-menu {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateZ(0); /* Force hardware acceleration */
  position: fixed; /* Fixed position so it doesn't move when scrolling */
  z-index: 1000; /* Ensure it's above other elements */
}

/* Position the dropdown menu */
.menu-container {
  position: relative;
}

/* Ensure the dropdown doesn't get cut off at the right edge of the screen */
@media (max-width: 768px) {
  .dropdown-menu {
    position: fixed !important; /* Ensure fixed positioning on mobile */
    right: auto !important;
    margin-top: 4px !important;
  }
}

.dropdown-menu a {
  color: #374151;
  transition: background-color 0.2s;
  display: block;
  width: 100%;
}

.dropdown-menu a:hover {
  background-color: #f3f4f6;
}

.dropdown-menu .py-1.divide-y.divide-gray-100 a {
  border-bottom: 1px solid #e5e7eb;
}

.dropdown-menu .py-1.divide-y.divide-gray-100 a:last-child {
  border-bottom: none;
}

/* Ensure the action menu button has a proper clickable area */
.action-menu-button {
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-menu-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Ensure table is scrollable on small screens */
.overflow-x-auto {
  width: 100%;
  -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar but allow scrolling */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

/* Mobile card view styling */
.mobile-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 1rem;
  transition: all 0.2s;
}

.mobile-card:active {
  background-color: #f9fafb;
}

.mobile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.mobile-card-content {
  margin-bottom: 0.75rem;
}

.mobile-card-footer {
  display: flex;
  justify-content: flex-end;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layout-container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  th, td {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .filter-tab {
    padding: 0.5rem;
  }

  /* Header and filter adjustments */
  .mb-6.flex.flex-row.w-full.gap-4 {
    flex-direction: column;
  }

  .mb-6.flex.flex-row.w-full.gap-4 > div:first-child {
    margin-bottom: 0.5rem;
  }

  /* Improve filter tabs on mobile */
  .flex.flex-wrap.-mb-px.mt-4 {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 0.5rem;
  }

  /* Pagination adjustments for mobile */
  .pagination {
    flex-direction: column;
    align-items: flex-start;
  }

  /* Mobile card styling - using direct CSS instead of @apply */
  .bg-white.border.border-gray-200.rounded-lg.p-4.shadow-sm {
    border: 1px solid #e5e7eb;
  }

  /* Improve spacing in mobile cards */
  .grid-cols-1.gap-4 {
    row-gap: 0.75rem;
  }

  /* Improve touch targets for mobile */
  button {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Ensure text is readable on mobile */
  .font-medium {
    font-size: 0.875rem;
  }

  /* Ensure proper spacing between cards */
  .mb-4 {
    margin-bottom: 0.75rem;
  }
}
